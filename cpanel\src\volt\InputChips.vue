<template>
    <div :class="containerClass" @click="focusInput">
        <div v-for="(value, index) in model" :key="index" :class="tokenClass">
            <span :class="labelClass">{{ value }}</span>
            <span :class="removeIconClass" @click.stop="removeValue(index)">
                <TimesIcon />
            </span>
        </div>
        <input
            ref="inputRef"
            type="text"
            v-model="inputValue"
            :class="inputClass"
            @keydown.enter.prevent="addValue"
            @keydown.backspace="handleBackspace"
        />
    </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue';
import TimesIcon from '@primevue/icons/times';

const props = defineProps<{
    modelValue: string[];
    separator?: string;
    addOnBlur?: boolean;
    allowDuplicate?: boolean;
    max?: number;
}>();

const emit = defineEmits(['update:modelValue']);

const model = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
});

const inputValue = ref('');
const inputRef = ref<HTMLInputElement | null>(null);

const addValue = () => {
    if (inputValue.value.trim() !== '') {
        if (props.max && model.value.length >= props.max) {
            return;
        }
        if (!props.allowDuplicate && model.value.includes(inputValue.value.trim())) {
            inputValue.value = '';
            return;
        }
        model.value = [...model.value, inputValue.value.trim()];
        inputValue.value = '';
    }
};

const removeValue = (index: number) => {
    model.value = model.value.filter((_, i) => i !== index);
};

const handleBackspace = () => {
    if (inputValue.value === '' && model.value.length > 0) {
        removeValue(model.value.length - 1);
    }
};

const focusInput = async () => {
    await nextTick();
    inputRef.value?.focus();
};

const containerClass = `m-0 py-1.5 px-3 list-none cursor-text overflow-hidden flex items-center flex-wrap
        w-full text-surface-900 dark:text-surface-0 bg-surface-0 dark:bg-surface-950 
        border border-surface-300 dark:border-surface-600 rounded-md 
        transition-colors duration-200 appearance-none
        hover:border-surface-400 dark:hover:border-surface-500
        focus-within:outline-none focus-within:outline-offset-0 focus-within:ring-1 focus-within:ring-primary-500 focus-within:border-primary-500
        p-invalid:border-red-500 p-invalid:focus-within:ring-red-500 p-invalid:focus-within:border-red-500`;

const tokenClass = `py-1 px-2 mr-2 bg-surface-200 dark:bg-surface-700 text-surface-700 dark:text-surface-300 rounded-md 
        inline-flex items-center`;

const labelClass = `leading-none`;

const removeIconClass = `ml-2 w-4 h-4 cursor-pointer`;

const inputClass = `border-0 outline-none bg-transparent m-0 p-0 shadow-none rounded-none w-full
        text-surface-700 dark:text-surface-200 placeholder:text-surface-400 dark:placeholder:text-surface-500 flex-1 inline-flex`;

</script> 