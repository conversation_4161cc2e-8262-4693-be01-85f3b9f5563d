import { TRPCError } from '@trpc/server'
import { DomainError, isDomainError } from './errors'

export function toTRPCError(error: unknown): TRPCError {
  if (error instanceof TRPCError) return error
  if (isDomainError(error)) {
    return new TRPCError({ code: (error as DomainError).code as any, message: (error as DomainError).message })
  }
  const message = (error as any)?.message || 'Внутренняя ошибка сервера'
  return new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message })
}



