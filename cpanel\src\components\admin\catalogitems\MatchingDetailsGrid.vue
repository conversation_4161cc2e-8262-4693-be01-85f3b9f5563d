<template>
  <div class="space-y-2">
    <div v-if="controls" class="flex flex-wrap items-center justify-between gap-2">
      <div class="flex items-center gap-3">
        <label class="text-surface-600 inline-flex items-center gap-2 text-xs">
          <VToggleSwitch v-model="showOnlyImportant" />
          <span>Только важное</span>
        </label>
        <label class="text-surface-600 inline-flex items-center gap-2 text-xs">
          <VToggleSwitch v-model="showNotes" />
          <span>Показывать заметки</span>
        </label>
        <div class="hidden items-center gap-2 md:flex">
          <span class="text-surface-500 text-xs">Сортировка</span>
          <VSelect v-model="sortMode" :options="sortOptions" optionLabel="label" optionValue="value" class="w-40" />
        </div>
      </div>
      <div class="flex w-full max-w-xs items-center gap-2 md:max-w-sm">
        <VInputText v-model="search" placeholder="Поиск по атрибуту" class="w-full" />
      </div>
      <div class="text-surface-500 hidden items-center gap-1 text-xs md:flex">
        <VTag size="small" :value="`Всего ${summary.total}`" severity="secondary" />
        <VTag size="small" :value="`EXACT ${summary.exact}`" severity="success" />
        <VTag size="small" :value="`NEAR ${summary.near}`" severity="info" />
        <VTag size="small" :value="`TOL ${summary.tol}`" severity="info" />
        <VTag size="small" :value="`LEGACY ${summary.legacy}`" severity="warning" />
      </div>
    </div>

    <!-- Desktop layout -->
    <div class="hidden md:block">
      <div class="text-surface-500 grid grid-cols-12 px-2 py-2 text-[11px] tracking-wide uppercase">
        <div class="col-span-3">Атрибут</div>
        <div class="col-span-3">Значение в item</div>
        <div class="col-span-3">Значение в part</div>
        <div class="col-span-2">Результат</div>
        <div class="col-span-1" v-if="showNotes">Заметки</div>
      </div>
      <div class="divide-surface-border divide-y rounded border">
        <div
          v-for="d in preparedDetails"
          :key="d.templateId + ':' + d.kind + ':' + d.itemValue + ':' + d.partValue"
          class="grid grid-cols-12 items-center border-l-2 px-2 py-2 text-sm"
          :class="accentClass(d)"
        >
          <div class="col-span-3 flex min-w-0 items-center gap-2">
            <Icon :name="getKindIcon(d.kind)" class="text-surface-400 h-4 w-4" />
            <span class="truncate" :title="d.templateTitle || 'template #' + d.templateId">
              {{ d.templateTitle || 'template #' + d.templateId }}
            </span>
          </div>
          <div class="col-span-3 flex items-center gap-2 font-mono break-words" :class="valueClass(d)">
            <span>{{ d.itemValue }}</span>
            <span v-if="isNumeric(d) && d.delta !== undefined" class="text-surface-500 ml-1 text-xs">Δ={{ Number(d.delta).toFixed(3) }}</span>
            <VButton v-if="!isNumeric(d) && d.templateId && d.itemValue" size="small" severity="secondary" outlined @click="toggleSynonyms(d, 'item')"
              >
              <InfoIcon class="h-4 w-4"
            />
          </VButton>
          </div>
          <div class="col-span-3 flex items-center gap-2 font-mono break-words" :class="valueClass(d)">
            <span>{{ d.partValue }}</span>
            <span v-if="isNumeric(d) && d.toleranceUsed !== undefined" class="text-surface-500 ml-1 text-xs">tol={{ d.toleranceUsed }}</span>
            <span v-if="isStringSynonym(d) && d.synonymLevel" class="ml-1 inline-flex">
              <VTag size="small" :value="String(d.synonymLevel)" :severity="synonymSeverity(String(d.synonymLevel))" />
            </span>
            <span v-if="isStringSynonym(d) && d.canonical" class="ml-1 inline-flex">
              <VTag size="small" :value="`canonical: ${d.canonical}`" severity="secondary" />
            </span>
            <VButton v-if="!isNumeric(d) && d.templateId && d.partValue" size="small" severity="secondary" outlined @click="toggleSynonyms(d, 'part')"
              ><InfoIcon class="h-4 w-4"
            /></VButton>
          </div>
          <div class="col-span-2">
            <VTag size="small" :value="getKindLabel(d.kind)" :severity="getDetailSeverity(d.kind)" />
          </div>
          <div class="text-surface-500 col-span-1 text-xs" v-if="showNotes">
            <span v-if="d.notes">{{ d.notes }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Mobile layout -->
    <div class="grid grid-cols-1 gap-2 md:hidden">
      <div
        v-for="d in preparedDetails"
        :key="d.templateId + ':' + d.kind + ':' + d.itemValue + ':' + d.partValue"
        class="bg-surface-50 dark:bg-surface-900 rounded border border-l-2 p-2"
        :class="accentClass(d)"
      >
        <div class="flex items-center justify-between">
          <div class="text-surface-700 dark:text-surface-300 flex items-center gap-2 text-xs">
            <Icon :name="getKindIcon(d.kind)" class="text-surface-400 h-4 w-4" />
            <span class="font-medium">{{ d.templateTitle || 'template #' + d.templateId }}</span>
          </div>
          <VTag :value="getKindLabel(d.kind)" :severity="getDetailSeverity(d.kind)" size="small" />
        </div>
        <div class="text-surface-500 mt-1 text-xs">
          <template v-if="isNumeric(d)">
            item: <span class="font-mono" :class="valueClass(d)">{{ d.itemValue }}</span> → part:
            <span class="font-mono" :class="valueClass(d)">{{ d.partValue }}</span>
            <span v-if="d.delta !== undefined"> | Δ={{ d.delta }}</span>
            <span v-if="d.toleranceUsed !== undefined"> | tol={{ d.toleranceUsed }}</span>
          </template>
          <template v-else>
            <div class="flex items-center gap-2">
              item: <span class="font-mono" :class="valueClass(d)">{{ d.itemValue }}</span>
              <VButton v-if="d.templateId && d.itemValue" size="small" severity="secondary" outlined @click="toggleSynonyms(d, 'item')">Синонимы</VButton>
            </div>
            <div class="flex items-center gap-2">
              → part: <span class="font-mono" :class="valueClass(d)">{{ d.partValue }}</span>
              <VButton v-if="d.templateId && d.partValue" size="small" severity="secondary" outlined @click="toggleSynonyms(d, 'part')">Синонимы</VButton>
            </div>
            <span v-if="d.synonymLevel" class="ml-1 inline-flex align-middle">
              <VTag size="small" :value="String(d.synonymLevel)" :severity="synonymSeverity(String(d.synonymLevel))" />
            </span>
            <span v-if="d.canonical" class="ml-1 inline-flex align-middle">
              <VTag size="small" :value="`canonical: ${d.canonical}`" severity="secondary" />
            </span>
          </template>
        </div>
        <div v-if="showNotes && d.notes" class="text-surface-500 mt-1 text-xs">{{ d.notes }}</div>
      </div>
    </div>
    <!-- Synonym details modal -->
    <SynonymDetailsModal v-model="showSynonymModal" :data="synonymModalData" />
  </div>
</template>

<script setup lang="ts">
import VTag from '@/volt/Tag.vue'
import VToggleSwitch from '@/volt/ToggleSwitch.vue'
import VSelect from '@/volt/Select.vue'
import VInputText from '@/volt/InputText.vue'
import Icon from '@/components/ui/Icon.vue'
import { computed, ref } from 'vue'
import { useMatchingLabels } from '@/composables/useMatchingLabels'
import SynonymDetailsModal from './SynonymDetailsModal.vue'
import VButton from '@/volt/Button.vue'
import { useTrpc } from '@/composables/useTrpc'
import { InfoIcon } from 'lucide-vue-next'

interface Props {
  details: any[]
  controls?: boolean
}
const props = withDefaults(defineProps<Props>(), { controls: true })
const synonymSeverity = (level: string) => (level === 'EXACT' ? 'success' : level === 'NEAR' ? 'info' : 'warning')

const { getKindLabel, getDetailSeverity } = useMatchingLabels()

const showOnlyImportant = ref(false)
const showNotes = ref(true)
const search = ref('')
const sortMode = ref<'importance' | 'name'>('importance')
const sortOptions = [
  { label: 'По важности', value: 'importance' },
  { label: 'По атрибуту', value: 'name' }
]

const isNumeric = (d: any) => String(d?.kind || '').startsWith('NUMBER')
const isStringSynonym = (d: any) => String(d?.kind || '').startsWith('STRING_SYNONYM')
const isExact = (d: any) => String(d?.kind || '').includes('EXACT') && !String(d?.kind || '').includes('WITHIN_TOLERANCE')
const isImportant = (d: any) => {
  const kind = String(d?.kind || '')
  if (d?.notes) return true
  if (kind.includes('NEAR') || kind.includes('LEGACY') || kind.includes('WITHIN_TOLERANCE')) return true
  if (isNumeric(d) && (d.delta !== undefined || d.toleranceUsed !== undefined)) return true
  return !isExact(d)
}

const filteredDetails = computed(() => {
  let src = Array.isArray(props.details) ? props.details : []
  if (showOnlyImportant.value) src = src.filter(isImportant)
  const q = search.value.trim().toLowerCase()
  if (q)
    src = src.filter((d: any) =>
      String(d?.templateTitle || d?.templateId || '')
        .toLowerCase()
        .includes(q)
    )
  return src
})

const importanceRank = (d: any) => {
  const kind = String(d?.kind || '')
  if (kind.includes('LEGACY')) return 0
  if (kind.includes('NEAR') || kind.includes('WITHIN_TOLERANCE')) return 1
  if (kind.includes('EXACT')) return 2
  return 3
}

const preparedDetails = computed(() => {
  const list = [...filteredDetails.value]
  if (sortMode.value === 'name') {
    list.sort((a: any, b: any) => String(a?.templateTitle || a?.templateId).localeCompare(String(b?.templateTitle || b?.templateId)))
  } else {
    list.sort((a: any, b: any) => importanceRank(a) - importanceRank(b))
  }
  return list
})

const summary = computed(() => {
  let exact = 0,
    near = 0,
    legacy = 0,
    tol = 0
  const list = Array.isArray(props.details) ? props.details : []
  for (const d of list) {
    const kind = String(d?.kind || '')
    if (kind.includes('EXACT')) exact++
    if (kind.includes('NEAR')) near++
    if (kind.includes('LEGACY')) legacy++
    if (kind.includes('WITHIN_TOLERANCE')) tol++
  }
  return { total: list.length, exact, near, legacy, tol }
})

const getKindIcon = (kind: string): string => {
  const k = String(kind || '')
  if (k.includes('EXACT') && !k.includes('WITHIN_TOLERANCE')) return 'pi pi-check-circle'
  if (k.includes('WITHIN_TOLERANCE') || k.includes('NEAR')) return 'pi pi-info-circle'
  if (k.includes('LEGACY')) return 'pi pi-exclamation-triangle'
  return 'pi pi-circle'
}

const accentClass = (d: any) => {
  const k = String(d?.kind || '')
  if (k.includes('LEGACY')) return 'border-yellow-400/60 dark:border-yellow-500/60'
  if (k.includes('WITHIN_TOLERANCE') || k.includes('NEAR')) return 'border-sky-400/60 dark:border-sky-500/60'
  if (k.includes('EXACT')) return 'border-emerald-400/60 dark:border-emerald-500/60'
  return 'border-surface-200 dark:border-surface-600'
}

const valueClass = (d: any) => {
  const k = String(d?.kind || '')
  if (k.includes('LEGACY')) return 'text-yellow-700 dark:text-yellow-300'
  if (k.includes('WITHIN_TOLERANCE') || k.includes('NEAR')) return 'text-sky-700 dark:text-sky-300'
  if (k.includes('EXACT')) return 'text-emerald-700 dark:text-emerald-300'
  return 'text-surface-800 dark:text-surface-200'
}
const showSynonymModal = ref(false)
const synonymModalData = ref<any | null>(null)
const { attributeSynonyms } = useTrpc()

const toggleSynonyms = async (d: any, source: 'item' | 'part') => {
  const templateId = d?.templateId
  const value = source === 'item' ? d?.itemValue : d?.partValue
  if (!templateId || !value) return
  try {
    const result = await attributeSynonyms.utils.findGroupSynonymsByValue({ templateId, value })
    if (result) {
      synonymModalData.value = result
      showSynonymModal.value = true
    } else {
      // нет группы — просто ничего не открываем; при желании можно добавить тост
    }
  } catch (e) {
    console.error('Не удалось получить синонимы:', e)
  }
}
</script>
