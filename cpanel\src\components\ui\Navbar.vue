<template>
  <nav class="bg-[--p-surface-card] border-b border-[--color-border] shadow-sm">
    <div class="container mx-auto px-4">
      <div class="flex justify-between items-center h-16">
        <!-- Логотип и название -->
        <div class="flex items-center">
          <img class="h-8 w-8" src="/favicon.svg" alt="PartTec" />
          <span class="ml-3 text-xl font-semibold text-[--color-foreground]">
            PartTec
          </span>
        </div>

        <!-- Навигация -->
        <div class="hidden md:flex items-center space-x-6">
          <a
            href="/"
            class="text-[--color-muted] hover:text-[--color-primary] transition-colors"
            :class="{ 'text-[--color-primary] font-medium': isActive('/') }"
          >
            Главная
          </a>
          <a
            href="/catalog"
            class="text-[--color-muted] hover:text-[--color-primary] transition-colors"
            :class="{ 'text-[--color-primary] font-medium': isActive('/catalog') }"
          >
            Каталог
          </a>
          <a
            href="/search"
            class="text-[--color-muted] hover:text-[--color-primary] transition-colors"
            :class="{ 'text-[--color-primary] font-medium': isActive('/search') }"
          >
            Поиск
          </a>
          <a
            href="/stats"
            class="text-[--color-muted] hover:text-[--color-primary] transition-colors"
            :class="{ 'text-[--color-primary] font-medium': isActive('/stats') }"
          >
            Статистика
          </a>
          <a
            href="/admin"
            class="text-[--color-muted] hover:text-[--color-primary] transition-colors"
            :class="{ 'text-[--color-primary] font-medium': isActive('/admin') }"
          >
            Админ панель
          </a>
        </div>

        <!-- Переключатель тем и мобильное меню -->
        <div class="flex items-center space-x-4">
          <ThemeToggle mode="toggle" />
          
          <!-- Мобильное меню кнопка -->
          <button
            @click="toggleMobileMenu"
            class="md:hidden p-2 rounded-md text-[--color-muted] hover:bg-[--p-content-hover-background] transition-colors"
          >
            <Menu :size="20" />
          </button>
        </div>
      </div>

      <!-- Мобильное меню -->
      <div
        v-if="showMobileMenu"
        class="md:hidden border-t border-[--color-border] py-4"
      >
        <div class="space-y-2">
          <a
            href="/"
            class="block px-3 py-2 rounded-md text-[--color-muted] hover:bg-[--p-content-hover-background] transition-colors"
            :class="{ 'bg-[--p-highlight-background] text-[--p-primary-color]': isActive('/') }"
            @click="closeMobileMenu"
          >
            Главная
          </a>
          <a
            href="/catalog"
            class="block px-3 py-2 rounded-md text-[--color-muted] hover:bg-[--p-content-hover-background] transition-colors"
            :class="{ 'bg-[--p-highlight-background] text-[--p-primary-color]': isActive('/catalog') }"
            @click="closeMobileMenu"
          >
            Каталог
          </a>
          <a
            href="/search"
            class="block px-3 py-2 rounded-md text-[--color-muted] hover:bg-[--p-content-hover-background] transition-colors"
            :class="{ 'bg-[--p-highlight-background] text-[--p-primary-color]': isActive('/search') }"
            @click="closeMobileMenu"
          >
            Поиск
          </a>
          <a
            href="/stats"
            class="block px-3 py-2 rounded-md text-[--color-muted] hover:bg-[--p-content-hover-background] transition-colors"
            :class="{ 'bg-[--p-highlight-background] text-[--p-primary-color]': isActive('/stats') }"
            @click="closeMobileMenu"
          >
            Статистика
          </a>
          <a
            href="/admin"
            class="block px-3 py-2 rounded-md text-[--color-muted] hover:bg-[--p-content-hover-background] transition-colors"
            :class="{ 'bg-[--p-highlight-background] text-[--p-primary-color]': isActive('/admin') }"
            @click="closeMobileMenu"
          >
            Админ панель
          </a>
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import ThemeToggle from './ThemeToggle.vue'
import { Menu } from 'lucide-vue-next'

// Локальное состояние
const showMobileMenu = ref(false)

// Получаем текущий путь
const currentPath = computed(() => {
  if (typeof window !== 'undefined') {
    return window.location.pathname
  }
  return ''
})

// Методы
const isActive = (path: string): boolean => {
  if (path === '/') {
    return currentPath.value === '/'
  }
  return currentPath.value.startsWith(path)
}

const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value
}

const closeMobileMenu = () => {
  showMobileMenu.value = false
}
</script>