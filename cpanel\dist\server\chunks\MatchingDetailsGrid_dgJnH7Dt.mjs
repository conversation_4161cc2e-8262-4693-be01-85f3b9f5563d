import { defineComponent, useSSRContext, ref, watch, mergeProps, withCtx, createBlock, createCommentVNode, openBlock, createVNode, toDisplayString, Fragment, renderList, computed, createTextVNode } from 'vue';
import { T as Tag } from './Tag_DnSNoHBb.mjs';
import { V as VToggleSwitch } from './ToggleSwitch_C8GWpSWf.mjs';
import { S as Select } from './Select_Bk34xTKM.mjs';
import { I as InputText } from './InputText_CtReD0EA.mjs';
import { I as Icon } from './AdminLayout_b20tykPC.mjs';
import { D as Dialog } from './Dialog_g9jHgXMZ.mjs';
import { ssrRenderComponent, ssrInterpolate, ssrRenderList, ssrRenderClass, ssrRenderAttrs, ssrRenderAttr } from 'vue/server-renderer';
import { _ as _export_sfc } from './ClientRouter_B8Zzhk9G.mjs';
import { V as VButton } from './Button_CuwpNmer.mjs';
import { u as useTrpc } from './useTrpc_B-zNiBto.mjs';
import { InfoIcon } from 'lucide-vue-next';

const useMatchingLabels = () => {
  const getAccuracyLabel = (accuracy) => ({
    EXACT_MATCH: "Точное совпадение",
    MATCH_WITH_NOTES: "С примечаниями",
    REQUIRES_MODIFICATION: "Требует доработки",
    PARTIAL_MATCH: "Частичное совпадение"
  })[accuracy] || accuracy;
  const getAccuracySeverity = (accuracy) => ({
    EXACT_MATCH: "success",
    MATCH_WITH_NOTES: "info",
    REQUIRES_MODIFICATION: "warning",
    PARTIAL_MATCH: "secondary"
  })[accuracy] || "secondary";
  const getDetailSeverity = (kind) => {
    if (kind.includes("EXACT")) return "success";
    if (kind.includes("WITHIN_TOLERANCE") || kind.includes("NEAR")) return "info";
    if (kind.includes("LEGACY")) return "warning";
    return "secondary";
  };
  const getKindLabel = (kind) => ({
    NUMBER_EXACT: "Число: точное",
    NUMBER_WITHIN_TOLERANCE: "Число: в допуске",
    STRING_EXACT: "Строка: точное",
    STRING_SYNONYM_EXACT: "Строка: группа EXACT",
    STRING_SYNONYM_NEAR: "Строка: группа NEAR",
    STRING_SYNONYM_LEGACY: "Строка: группа LEGACY",
    EXACT_STRING: "Точное совпадение"
  })[kind] || kind;
  return { getAccuracyLabel, getAccuracySeverity, getDetailSeverity, getKindLabel };
};

const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "SynonymDetailsModal",
  props: {
    modelValue: { type: Boolean },
    data: {}
  },
  emits: ["update:modelValue"],
  setup(__props, { expose: __expose, emit: __emit }) {
    __expose();
    const props = __props;
    const emit = __emit;
    const visible = ref(false);
    watch(() => props.modelValue, (v) => {
      visible.value = v;
    });
    watch(visible, (v) => emit("update:modelValue", v));
    const getSeverity = (level) => level === "EXACT" ? "success" : level === "NEAR" ? "info" : "warning";
    const __returned__ = { props, emit, visible, getSeverity, VDialog: Dialog, VTag: Tag };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$1(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(ssrRenderComponent($setup["VDialog"], mergeProps({
    visible: $setup.visible,
    "onUpdate:visible": ($event) => $setup.visible = $event,
    modal: "",
    header: "\u0421\u0438\u043D\u043E\u043D\u0438\u043C\u044B \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u0430",
    class: "w-auto"
  }, _attrs), {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        if ($props.data) {
          _push2(`<div class="p-2 space-y-3"${_scopeId}><div class="flex items-center justify-between"${_scopeId}><div${_scopeId}><div class="text-sm text-surface-500"${_scopeId}>\u0413\u0440\u0443\u043F\u043F\u0430</div><div class="text-base font-medium"${_scopeId}>${ssrInterpolate($props.data.group.name)}</div></div>`);
          _push2(ssrRenderComponent($setup["VTag"], {
            value: $props.data.group.compatibilityLevel,
            severity: $setup.getSeverity($props.data.group.compatibilityLevel)
          }, null, _parent2, _scopeId));
          _push2(`</div>`);
          if ($props.data.group.description) {
            _push2(`<div class="text-xs text-surface-500"${_scopeId}>${ssrInterpolate($props.data.group.description)}</div>`);
          } else {
            _push2(`<!---->`);
          }
          _push2(`<div class="grid grid-cols-1 gap-1 max-h-64 overflow-auto border rounded p-2"${_scopeId}><!--[-->`);
          ssrRenderList($props.data.synonyms, (s) => {
            _push2(`<div class="${ssrRenderClass([s.id === $props.data.currentSynonymId ? "bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300" : "", "flex items-center justify-between text-sm p-1 rounded"])}"${_scopeId}><div class="flex items-center gap-2"${_scopeId}><span class="font-mono"${_scopeId}>${ssrInterpolate(s.value)}</span>`);
            if (s.compatibilityLevel && s.compatibilityLevel !== $props.data.group.compatibilityLevel) {
              _push2(ssrRenderComponent($setup["VTag"], {
                size: "small",
                value: s.compatibilityLevel,
                severity: $setup.getSeverity(s.compatibilityLevel)
              }, null, _parent2, _scopeId));
            } else {
              _push2(`<!---->`);
            }
            _push2(`</div>`);
            if (s.brand) {
              _push2(`<div class="text-xs text-surface-500"${_scopeId}>${ssrInterpolate(s.brand.name)}</div>`);
            } else {
              _push2(`<!---->`);
            }
            _push2(`</div>`);
          });
          _push2(`<!--]--></div>`);
          if ($props.data.group.canonicalValue) {
            _push2(`<div class="text-xs"${_scopeId}><span class="text-surface-500"${_scopeId}>\u041A\u0430\u043D\u043E\u043D\u0438\u0447\u0435\u0441\u043A\u043E\u0435:</span><span class="font-mono"${_scopeId}>${ssrInterpolate($props.data.group.canonicalValue)}</span></div>`);
          } else {
            _push2(`<!---->`);
          }
          if ($props.data.group.notes) {
            _push2(`<div class="text-xs text-surface-600"${_scopeId}>${ssrInterpolate($props.data.group.notes)}</div>`);
          } else {
            _push2(`<!---->`);
          }
          _push2(`</div>`);
        } else {
          _push2(`<!---->`);
        }
      } else {
        return [
          $props.data ? (openBlock(), createBlock("div", {
            key: 0,
            class: "p-2 space-y-3"
          }, [
            createVNode("div", { class: "flex items-center justify-between" }, [
              createVNode("div", null, [
                createVNode("div", { class: "text-sm text-surface-500" }, "\u0413\u0440\u0443\u043F\u043F\u0430"),
                createVNode("div", { class: "text-base font-medium" }, toDisplayString($props.data.group.name), 1)
              ]),
              createVNode($setup["VTag"], {
                value: $props.data.group.compatibilityLevel,
                severity: $setup.getSeverity($props.data.group.compatibilityLevel)
              }, null, 8, ["value", "severity"])
            ]),
            $props.data.group.description ? (openBlock(), createBlock("div", {
              key: 0,
              class: "text-xs text-surface-500"
            }, toDisplayString($props.data.group.description), 1)) : createCommentVNode("", true),
            createVNode("div", { class: "grid grid-cols-1 gap-1 max-h-64 overflow-auto border rounded p-2" }, [
              (openBlock(true), createBlock(Fragment, null, renderList($props.data.synonyms, (s) => {
                return openBlock(), createBlock("div", {
                  key: s.id,
                  class: ["flex items-center justify-between text-sm p-1 rounded", s.id === $props.data.currentSynonymId ? "bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300" : ""]
                }, [
                  createVNode("div", { class: "flex items-center gap-2" }, [
                    createVNode("span", { class: "font-mono" }, toDisplayString(s.value), 1),
                    s.compatibilityLevel && s.compatibilityLevel !== $props.data.group.compatibilityLevel ? (openBlock(), createBlock($setup["VTag"], {
                      key: 0,
                      size: "small",
                      value: s.compatibilityLevel,
                      severity: $setup.getSeverity(s.compatibilityLevel)
                    }, null, 8, ["value", "severity"])) : createCommentVNode("", true)
                  ]),
                  s.brand ? (openBlock(), createBlock("div", {
                    key: 0,
                    class: "text-xs text-surface-500"
                  }, toDisplayString(s.brand.name), 1)) : createCommentVNode("", true)
                ], 2);
              }), 128))
            ]),
            $props.data.group.canonicalValue ? (openBlock(), createBlock("div", {
              key: 1,
              class: "text-xs"
            }, [
              createVNode("span", { class: "text-surface-500" }, "\u041A\u0430\u043D\u043E\u043D\u0438\u0447\u0435\u0441\u043A\u043E\u0435:"),
              createVNode("span", { class: "font-mono" }, toDisplayString($props.data.group.canonicalValue), 1)
            ])) : createCommentVNode("", true),
            $props.data.group.notes ? (openBlock(), createBlock("div", {
              key: 2,
              class: "text-xs text-surface-600"
            }, toDisplayString($props.data.group.notes), 1)) : createCommentVNode("", true)
          ])) : createCommentVNode("", true)
        ];
      }
    }),
    _: 1
  }, _parent));
}
const _sfc_setup$1 = _sfc_main$1.setup;
_sfc_main$1.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/catalogitems/SynonymDetailsModal.vue");
  return _sfc_setup$1 ? _sfc_setup$1(props, ctx) : void 0;
};
const SynonymDetailsModal = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["ssrRender", _sfc_ssrRender$1]]);

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "MatchingDetailsGrid",
  props: {
    details: {},
    controls: { type: Boolean, default: true }
  },
  setup(__props, { expose: __expose }) {
    __expose();
    const props = __props;
    const synonymSeverity = (level) => level === "EXACT" ? "success" : level === "NEAR" ? "info" : "warning";
    const { getKindLabel, getDetailSeverity } = useMatchingLabels();
    const showOnlyImportant = ref(false);
    const showNotes = ref(true);
    const search = ref("");
    const sortMode = ref("importance");
    const sortOptions = [
      { label: "\u041F\u043E \u0432\u0430\u0436\u043D\u043E\u0441\u0442\u0438", value: "importance" },
      { label: "\u041F\u043E \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u0443", value: "name" }
    ];
    const isNumeric = (d) => String(d?.kind || "").startsWith("NUMBER");
    const isStringSynonym = (d) => String(d?.kind || "").startsWith("STRING_SYNONYM");
    const isExact = (d) => String(d?.kind || "").includes("EXACT") && !String(d?.kind || "").includes("WITHIN_TOLERANCE");
    const isImportant = (d) => {
      const kind = String(d?.kind || "");
      if (d?.notes) return true;
      if (kind.includes("NEAR") || kind.includes("LEGACY") || kind.includes("WITHIN_TOLERANCE")) return true;
      if (isNumeric(d) && (d.delta !== void 0 || d.toleranceUsed !== void 0)) return true;
      return !isExact(d);
    };
    const filteredDetails = computed(() => {
      let src = Array.isArray(props.details) ? props.details : [];
      if (showOnlyImportant.value) src = src.filter(isImportant);
      const q = search.value.trim().toLowerCase();
      if (q)
        src = src.filter(
          (d) => String(d?.templateTitle || d?.templateId || "").toLowerCase().includes(q)
        );
      return src;
    });
    const importanceRank = (d) => {
      const kind = String(d?.kind || "");
      if (kind.includes("LEGACY")) return 0;
      if (kind.includes("NEAR") || kind.includes("WITHIN_TOLERANCE")) return 1;
      if (kind.includes("EXACT")) return 2;
      return 3;
    };
    const preparedDetails = computed(() => {
      const list = [...filteredDetails.value];
      if (sortMode.value === "name") {
        list.sort((a, b) => String(a?.templateTitle || a?.templateId).localeCompare(String(b?.templateTitle || b?.templateId)));
      } else {
        list.sort((a, b) => importanceRank(a) - importanceRank(b));
      }
      return list;
    });
    const summary = computed(() => {
      let exact = 0, near = 0, legacy = 0, tol = 0;
      const list = Array.isArray(props.details) ? props.details : [];
      for (const d of list) {
        const kind = String(d?.kind || "");
        if (kind.includes("EXACT")) exact++;
        if (kind.includes("NEAR")) near++;
        if (kind.includes("LEGACY")) legacy++;
        if (kind.includes("WITHIN_TOLERANCE")) tol++;
      }
      return { total: list.length, exact, near, legacy, tol };
    });
    const getKindIcon = (kind) => {
      const k = String(kind || "");
      if (k.includes("EXACT") && !k.includes("WITHIN_TOLERANCE")) return "pi pi-check-circle";
      if (k.includes("WITHIN_TOLERANCE") || k.includes("NEAR")) return "pi pi-info-circle";
      if (k.includes("LEGACY")) return "pi pi-exclamation-triangle";
      return "pi pi-circle";
    };
    const accentClass = (d) => {
      const k = String(d?.kind || "");
      if (k.includes("LEGACY")) return "border-yellow-400/60 dark:border-yellow-500/60";
      if (k.includes("WITHIN_TOLERANCE") || k.includes("NEAR")) return "border-sky-400/60 dark:border-sky-500/60";
      if (k.includes("EXACT")) return "border-emerald-400/60 dark:border-emerald-500/60";
      return "border-surface-200 dark:border-surface-600";
    };
    const valueClass = (d) => {
      const k = String(d?.kind || "");
      if (k.includes("LEGACY")) return "text-yellow-700 dark:text-yellow-300";
      if (k.includes("WITHIN_TOLERANCE") || k.includes("NEAR")) return "text-sky-700 dark:text-sky-300";
      if (k.includes("EXACT")) return "text-emerald-700 dark:text-emerald-300";
      return "text-surface-800 dark:text-surface-200";
    };
    const showSynonymModal = ref(false);
    const synonymModalData = ref(null);
    const { attributeSynonyms } = useTrpc();
    const toggleSynonyms = async (d, source) => {
      const templateId = d?.templateId;
      const value = source === "item" ? d?.itemValue : d?.partValue;
      if (!templateId || !value) return;
      try {
        const result = await attributeSynonyms.utils.findGroupSynonymsByValue({ templateId, value });
        if (result) {
          synonymModalData.value = result;
          showSynonymModal.value = true;
        } else {
        }
      } catch (e) {
        console.error("\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u043F\u043E\u043B\u0443\u0447\u0438\u0442\u044C \u0441\u0438\u043D\u043E\u043D\u0438\u043C\u044B:", e);
      }
    };
    const __returned__ = { props, synonymSeverity, getKindLabel, getDetailSeverity, showOnlyImportant, showNotes, search, sortMode, sortOptions, isNumeric, isStringSynonym, isExact, isImportant, filteredDetails, importanceRank, preparedDetails, summary, getKindIcon, accentClass, valueClass, showSynonymModal, synonymModalData, attributeSynonyms, toggleSynonyms, VTag: Tag, VToggleSwitch, VSelect: Select, VInputText: InputText, Icon, SynonymDetailsModal, VButton, get InfoIcon() {
      return InfoIcon;
    } };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "space-y-2" }, _attrs))}>`);
  if ($props.controls) {
    _push(`<div class="flex flex-wrap items-center justify-between gap-2"><div class="flex items-center gap-3"><label class="text-surface-600 inline-flex items-center gap-2 text-xs">`);
    _push(ssrRenderComponent($setup["VToggleSwitch"], {
      modelValue: $setup.showOnlyImportant,
      "onUpdate:modelValue": ($event) => $setup.showOnlyImportant = $event
    }, null, _parent));
    _push(`<span>\u0422\u043E\u043B\u044C\u043A\u043E \u0432\u0430\u0436\u043D\u043E\u0435</span></label><label class="text-surface-600 inline-flex items-center gap-2 text-xs">`);
    _push(ssrRenderComponent($setup["VToggleSwitch"], {
      modelValue: $setup.showNotes,
      "onUpdate:modelValue": ($event) => $setup.showNotes = $event
    }, null, _parent));
    _push(`<span>\u041F\u043E\u043A\u0430\u0437\u044B\u0432\u0430\u0442\u044C \u0437\u0430\u043C\u0435\u0442\u043A\u0438</span></label><div class="hidden items-center gap-2 md:flex"><span class="text-surface-500 text-xs">\u0421\u043E\u0440\u0442\u0438\u0440\u043E\u0432\u043A\u0430</span>`);
    _push(ssrRenderComponent($setup["VSelect"], {
      modelValue: $setup.sortMode,
      "onUpdate:modelValue": ($event) => $setup.sortMode = $event,
      options: $setup.sortOptions,
      optionLabel: "label",
      optionValue: "value",
      class: "w-40"
    }, null, _parent));
    _push(`</div></div><div class="flex w-full max-w-xs items-center gap-2 md:max-w-sm">`);
    _push(ssrRenderComponent($setup["VInputText"], {
      modelValue: $setup.search,
      "onUpdate:modelValue": ($event) => $setup.search = $event,
      placeholder: "\u041F\u043E\u0438\u0441\u043A \u043F\u043E \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u0443",
      class: "w-full"
    }, null, _parent));
    _push(`</div><div class="text-surface-500 hidden items-center gap-1 text-xs md:flex">`);
    _push(ssrRenderComponent($setup["VTag"], {
      size: "small",
      value: `\u0412\u0441\u0435\u0433\u043E ${$setup.summary.total}`,
      severity: "secondary"
    }, null, _parent));
    _push(ssrRenderComponent($setup["VTag"], {
      size: "small",
      value: `EXACT ${$setup.summary.exact}`,
      severity: "success"
    }, null, _parent));
    _push(ssrRenderComponent($setup["VTag"], {
      size: "small",
      value: `NEAR ${$setup.summary.near}`,
      severity: "info"
    }, null, _parent));
    _push(ssrRenderComponent($setup["VTag"], {
      size: "small",
      value: `TOL ${$setup.summary.tol}`,
      severity: "info"
    }, null, _parent));
    _push(ssrRenderComponent($setup["VTag"], {
      size: "small",
      value: `LEGACY ${$setup.summary.legacy}`,
      severity: "warning"
    }, null, _parent));
    _push(`</div></div>`);
  } else {
    _push(`<!---->`);
  }
  _push(`<div class="hidden md:block"><div class="text-surface-500 grid grid-cols-12 px-2 py-2 text-[11px] tracking-wide uppercase"><div class="col-span-3">\u0410\u0442\u0440\u0438\u0431\u0443\u0442</div><div class="col-span-3">\u0417\u043D\u0430\u0447\u0435\u043D\u0438\u0435 \u0432 item</div><div class="col-span-3">\u0417\u043D\u0430\u0447\u0435\u043D\u0438\u0435 \u0432 part</div><div class="col-span-2">\u0420\u0435\u0437\u0443\u043B\u044C\u0442\u0430\u0442</div>`);
  if ($setup.showNotes) {
    _push(`<div class="col-span-1">\u0417\u0430\u043C\u0435\u0442\u043A\u0438</div>`);
  } else {
    _push(`<!---->`);
  }
  _push(`</div><div class="divide-surface-border divide-y rounded border"><!--[-->`);
  ssrRenderList($setup.preparedDetails, (d) => {
    _push(`<div class="${ssrRenderClass([$setup.accentClass(d), "grid grid-cols-12 items-center border-l-2 px-2 py-2 text-sm"])}"><div class="col-span-3 flex min-w-0 items-center gap-2">`);
    _push(ssrRenderComponent($setup["Icon"], {
      name: $setup.getKindIcon(d.kind),
      class: "text-surface-400 h-4 w-4"
    }, null, _parent));
    _push(`<span class="truncate"${ssrRenderAttr("title", d.templateTitle || "template #" + d.templateId)}>${ssrInterpolate(d.templateTitle || "template #" + d.templateId)}</span></div><div class="${ssrRenderClass([$setup.valueClass(d), "col-span-3 flex items-center gap-2 font-mono break-words"])}"><span>${ssrInterpolate(d.itemValue)}</span>`);
    if ($setup.isNumeric(d) && d.delta !== void 0) {
      _push(`<span class="text-surface-500 ml-1 text-xs">\u0394=${ssrInterpolate(Number(d.delta).toFixed(3))}</span>`);
    } else {
      _push(`<!---->`);
    }
    if (!$setup.isNumeric(d) && d.templateId && d.itemValue) {
      _push(ssrRenderComponent($setup["VButton"], {
        size: "small",
        severity: "secondary",
        outlined: "",
        onClick: ($event) => $setup.toggleSynonyms(d, "item")
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(ssrRenderComponent($setup["InfoIcon"], { class: "h-4 w-4" }, null, _parent2, _scopeId));
          } else {
            return [
              createVNode($setup["InfoIcon"], { class: "h-4 w-4" })
            ];
          }
        }),
        _: 2
      }, _parent));
    } else {
      _push(`<!---->`);
    }
    _push(`</div><div class="${ssrRenderClass([$setup.valueClass(d), "col-span-3 flex items-center gap-2 font-mono break-words"])}"><span>${ssrInterpolate(d.partValue)}</span>`);
    if ($setup.isNumeric(d) && d.toleranceUsed !== void 0) {
      _push(`<span class="text-surface-500 ml-1 text-xs">tol=${ssrInterpolate(d.toleranceUsed)}</span>`);
    } else {
      _push(`<!---->`);
    }
    if ($setup.isStringSynonym(d) && d.synonymLevel) {
      _push(`<span class="ml-1 inline-flex">`);
      _push(ssrRenderComponent($setup["VTag"], {
        size: "small",
        value: String(d.synonymLevel),
        severity: $setup.synonymSeverity(String(d.synonymLevel))
      }, null, _parent));
      _push(`</span>`);
    } else {
      _push(`<!---->`);
    }
    if ($setup.isStringSynonym(d) && d.canonical) {
      _push(`<span class="ml-1 inline-flex">`);
      _push(ssrRenderComponent($setup["VTag"], {
        size: "small",
        value: `canonical: ${d.canonical}`,
        severity: "secondary"
      }, null, _parent));
      _push(`</span>`);
    } else {
      _push(`<!---->`);
    }
    if (!$setup.isNumeric(d) && d.templateId && d.partValue) {
      _push(ssrRenderComponent($setup["VButton"], {
        size: "small",
        severity: "secondary",
        outlined: "",
        onClick: ($event) => $setup.toggleSynonyms(d, "part")
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(ssrRenderComponent($setup["InfoIcon"], { class: "h-4 w-4" }, null, _parent2, _scopeId));
          } else {
            return [
              createVNode($setup["InfoIcon"], { class: "h-4 w-4" })
            ];
          }
        }),
        _: 2
      }, _parent));
    } else {
      _push(`<!---->`);
    }
    _push(`</div><div class="col-span-2">`);
    _push(ssrRenderComponent($setup["VTag"], {
      size: "small",
      value: $setup.getKindLabel(d.kind),
      severity: $setup.getDetailSeverity(d.kind)
    }, null, _parent));
    _push(`</div>`);
    if ($setup.showNotes) {
      _push(`<div class="text-surface-500 col-span-1 text-xs">`);
      if (d.notes) {
        _push(`<span>${ssrInterpolate(d.notes)}</span>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div>`);
    } else {
      _push(`<!---->`);
    }
    _push(`</div>`);
  });
  _push(`<!--]--></div></div><div class="grid grid-cols-1 gap-2 md:hidden"><!--[-->`);
  ssrRenderList($setup.preparedDetails, (d) => {
    _push(`<div class="${ssrRenderClass([$setup.accentClass(d), "bg-surface-50 dark:bg-surface-900 rounded border border-l-2 p-2"])}"><div class="flex items-center justify-between"><div class="text-surface-700 dark:text-surface-300 flex items-center gap-2 text-xs">`);
    _push(ssrRenderComponent($setup["Icon"], {
      name: $setup.getKindIcon(d.kind),
      class: "text-surface-400 h-4 w-4"
    }, null, _parent));
    _push(`<span class="font-medium">${ssrInterpolate(d.templateTitle || "template #" + d.templateId)}</span></div>`);
    _push(ssrRenderComponent($setup["VTag"], {
      value: $setup.getKindLabel(d.kind),
      severity: $setup.getDetailSeverity(d.kind),
      size: "small"
    }, null, _parent));
    _push(`</div><div class="text-surface-500 mt-1 text-xs">`);
    if ($setup.isNumeric(d)) {
      _push(`<!--[--> item: <span class="${ssrRenderClass([$setup.valueClass(d), "font-mono"])}">${ssrInterpolate(d.itemValue)}</span> \u2192 part: <span class="${ssrRenderClass([$setup.valueClass(d), "font-mono"])}">${ssrInterpolate(d.partValue)}</span>`);
      if (d.delta !== void 0) {
        _push(`<span> | \u0394=${ssrInterpolate(d.delta)}</span>`);
      } else {
        _push(`<!---->`);
      }
      if (d.toleranceUsed !== void 0) {
        _push(`<span> | tol=${ssrInterpolate(d.toleranceUsed)}</span>`);
      } else {
        _push(`<!---->`);
      }
      _push(`<!--]-->`);
    } else {
      _push(`<!--[--><div class="flex items-center gap-2"> item: <span class="${ssrRenderClass([$setup.valueClass(d), "font-mono"])}">${ssrInterpolate(d.itemValue)}</span>`);
      if (d.templateId && d.itemValue) {
        _push(ssrRenderComponent($setup["VButton"], {
          size: "small",
          severity: "secondary",
          outlined: "",
          onClick: ($event) => $setup.toggleSynonyms(d, "item")
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(`\u0421\u0438\u043D\u043E\u043D\u0438\u043C\u044B`);
            } else {
              return [
                createTextVNode("\u0421\u0438\u043D\u043E\u043D\u0438\u043C\u044B")
              ];
            }
          }),
          _: 2
        }, _parent));
      } else {
        _push(`<!---->`);
      }
      _push(`</div><div class="flex items-center gap-2"> \u2192 part: <span class="${ssrRenderClass([$setup.valueClass(d), "font-mono"])}">${ssrInterpolate(d.partValue)}</span>`);
      if (d.templateId && d.partValue) {
        _push(ssrRenderComponent($setup["VButton"], {
          size: "small",
          severity: "secondary",
          outlined: "",
          onClick: ($event) => $setup.toggleSynonyms(d, "part")
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(`\u0421\u0438\u043D\u043E\u043D\u0438\u043C\u044B`);
            } else {
              return [
                createTextVNode("\u0421\u0438\u043D\u043E\u043D\u0438\u043C\u044B")
              ];
            }
          }),
          _: 2
        }, _parent));
      } else {
        _push(`<!---->`);
      }
      _push(`</div>`);
      if (d.synonymLevel) {
        _push(`<span class="ml-1 inline-flex align-middle">`);
        _push(ssrRenderComponent($setup["VTag"], {
          size: "small",
          value: String(d.synonymLevel),
          severity: $setup.synonymSeverity(String(d.synonymLevel))
        }, null, _parent));
        _push(`</span>`);
      } else {
        _push(`<!---->`);
      }
      if (d.canonical) {
        _push(`<span class="ml-1 inline-flex align-middle">`);
        _push(ssrRenderComponent($setup["VTag"], {
          size: "small",
          value: `canonical: ${d.canonical}`,
          severity: "secondary"
        }, null, _parent));
        _push(`</span>`);
      } else {
        _push(`<!---->`);
      }
      _push(`<!--]-->`);
    }
    _push(`</div>`);
    if ($setup.showNotes && d.notes) {
      _push(`<div class="text-surface-500 mt-1 text-xs">${ssrInterpolate(d.notes)}</div>`);
    } else {
      _push(`<!---->`);
    }
    _push(`</div>`);
  });
  _push(`<!--]--></div>`);
  _push(ssrRenderComponent($setup["SynonymDetailsModal"], {
    modelValue: $setup.showSynonymModal,
    "onUpdate:modelValue": ($event) => $setup.showSynonymModal = $event,
    data: $setup.synonymModalData
  }, null, _parent));
  _push(`</div>`);
}
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/catalogitems/MatchingDetailsGrid.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const MatchingDetailsGrid = /* @__PURE__ */ _export_sfc(_sfc_main, [["ssrRender", _sfc_ssrRender]]);

export { MatchingDetailsGrid as M, useMatchingLabels as u };
