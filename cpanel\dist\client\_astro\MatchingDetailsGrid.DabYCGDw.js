import{T as Y}from"./Tag.DvN1X7lb.js";import{V as J}from"./ToggleSwitch.7R1jWLlg.js";import{S as Z}from"./Select.CKfyRLxl.js";import{I as $}from"./InputText.COaPodMV.js";import{I as ee}from"./Icon.Ci-mb2Ee.js";import{D as te}from"./Dialog.Dcz8Sg9i.js";import{_ as z}from"./_plugin-vue_export-helper.BX07OBiL.js";import{d as X,g as C,o as i,j as S,c as o,b as u,a as l,e as c,F as A,r as b,w as H,f as T,h as N}from"./index.BglzLLgy.js";import{t as d,n as f,r as p}from"./reactivity.esm-bundler.D5IypM4U.js";import se from"./Button.oEwD-lSq.js";import{u as le}from"./useTrpc.CAkGIEe7.js";import{I as ne}from"./info.NGiRJ8VE.js";const ae=()=>({getAccuracyLabel:m=>({EXACT_MATCH:"Точное совпадение",MATCH_WITH_NOTES:"С примечаниями",REQUIRES_MODIFICATION:"Требует доработки",PARTIAL_MATCH:"Частичное совпадение"})[m]||m,getAccuracySeverity:m=>({EXACT_MATCH:"success",MATCH_WITH_NOTES:"info",REQUIRES_MODIFICATION:"warning",PARTIAL_MATCH:"secondary"})[m]||"secondary",getDetailSeverity:m=>m.includes("EXACT")?"success":m.includes("WITHIN_TOLERANCE")||m.includes("NEAR")?"info":m.includes("LEGACY")?"warning":"secondary",getKindLabel:m=>({NUMBER_EXACT:"Число: точное",NUMBER_WITHIN_TOLERANCE:"Число: в допуске",STRING_EXACT:"Строка: точное",STRING_SYNONYM_EXACT:"Строка: группа EXACT",STRING_SYNONYM_NEAR:"Строка: группа NEAR",STRING_SYNONYM_LEGACY:"Строка: группа LEGACY",EXACT_STRING:"Точное совпадение"})[m]||m}),ie=X({__name:"SynonymDetailsModal",props:{modelValue:{type:Boolean},data:{}},emits:["update:modelValue"],setup(E,{expose:s,emit:r}){s();const t=E,m=r,_=p(!1);H(()=>t.modelValue,v=>{_.value=v}),H(_,v=>m("update:modelValue",v));const g={props:t,emit:m,visible:_,getSeverity:v=>v==="EXACT"?"success":v==="NEAR"?"info":"warning",VDialog:te,VTag:Y};return Object.defineProperty(g,"__isScriptSetup",{enumerable:!1,value:!0}),g}}),oe={key:0,class:"p-2 space-y-3"},re={class:"flex items-center justify-between"},ue={class:"text-base font-medium"},ce={key:0,class:"text-xs text-surface-500"},de={class:"grid grid-cols-1 gap-1 max-h-64 overflow-auto border rounded p-2"},me={class:"flex items-center gap-2"},ye={class:"font-mono"},ve={key:0,class:"text-xs text-surface-500"},fe={key:1,class:"text-xs"},ge={class:"font-mono"},_e={key:2,class:"text-xs text-surface-600"};function xe(E,s,r,t,m,_){return i(),C(t.VDialog,{visible:t.visible,"onUpdate:visible":s[0]||(s[0]=e=>t.visible=e),modal:"",header:"Синонимы атрибута",class:"w-auto"},{default:S(()=>[r.data?(i(),o("div",oe,[l("div",re,[l("div",null,[s[1]||(s[1]=l("div",{class:"text-sm text-surface-500"},"Группа",-1)),l("div",ue,d(r.data.group.name),1)]),c(t.VTag,{value:r.data.group.compatibilityLevel,severity:t.getSeverity(r.data.group.compatibilityLevel)},null,8,["value","severity"])]),r.data.group.description?(i(),o("div",ce,d(r.data.group.description),1)):u("",!0),l("div",de,[(i(!0),o(A,null,b(r.data.synonyms,e=>(i(),o("div",{key:e.id,class:f(["flex items-center justify-between text-sm p-1 rounded",e.id===r.data.currentSynonymId?"bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300":""])},[l("div",me,[l("span",ye,d(e.value),1),e.compatibilityLevel&&e.compatibilityLevel!==r.data.group.compatibilityLevel?(i(),C(t.VTag,{key:0,size:"small",value:e.compatibilityLevel,severity:t.getSeverity(e.compatibilityLevel)},null,8,["value","severity"])):u("",!0)]),e.brand?(i(),o("div",ve,d(e.brand.name),1)):u("",!0)],2))),128))]),r.data.group.canonicalValue?(i(),o("div",fe,[s[2]||(s[2]=l("span",{class:"text-surface-500"},"Каноническое:",-1)),l("span",ge,d(r.data.group.canonicalValue),1)])):u("",!0),r.data.group.notes?(i(),o("div",_e,d(r.data.group.notes),1)):u("",!0)])):u("",!0)]),_:1},8,["visible"])}const pe=z(ie,[["render",xe]]),Ee=X({__name:"MatchingDetailsGrid",props:{details:{},controls:{type:Boolean,default:!0}},setup(E,{expose:s}){s();const r=E,t=n=>n==="EXACT"?"success":n==="NEAR"?"info":"warning",{getKindLabel:m,getDetailSeverity:_}=ae(),e=p(!1),g=p(!0),v=p(""),w=p("importance"),U=[{label:"По важности",value:"importance"},{label:"По атрибуту",value:"name"}],L=n=>String(n?.kind||"").startsWith("NUMBER"),W=n=>String(n?.kind||"").startsWith("STRING_SYNONYM"),h=n=>String(n?.kind||"").includes("EXACT")&&!String(n?.kind||"").includes("WITHIN_TOLERANCE"),D=n=>{const a=String(n?.kind||"");return n?.notes||a.includes("NEAR")||a.includes("LEGACY")||a.includes("WITHIN_TOLERANCE")||L(n)&&(n.delta!==void 0||n.toleranceUsed!==void 0)?!0:!h(n)},R=N(()=>{let n=Array.isArray(r.details)?r.details:[];e.value&&(n=n.filter(D));const a=v.value.trim().toLowerCase();return a&&(n=n.filter(y=>String(y?.templateTitle||y?.templateId||"").toLowerCase().includes(a))),n}),V=n=>{const a=String(n?.kind||"");return a.includes("LEGACY")?0:a.includes("NEAR")||a.includes("WITHIN_TOLERANCE")?1:a.includes("EXACT")?2:3},F=N(()=>{const n=[...R.value];return w.value==="name"?n.sort((a,y)=>String(a?.templateTitle||a?.templateId).localeCompare(String(y?.templateTitle||y?.templateId))):n.sort((a,y)=>V(a)-V(y)),n}),K=N(()=>{let n=0,a=0,y=0,k=0;const x=Array.isArray(r.details)?r.details:[];for(const q of x){const I=String(q?.kind||"");I.includes("EXACT")&&n++,I.includes("NEAR")&&a++,I.includes("LEGACY")&&y++,I.includes("WITHIN_TOLERANCE")&&k++}return{total:x.length,exact:n,near:a,legacy:y,tol:k}}),j=n=>{const a=String(n||"");return a.includes("EXACT")&&!a.includes("WITHIN_TOLERANCE")?"pi pi-check-circle":a.includes("WITHIN_TOLERANCE")||a.includes("NEAR")?"pi pi-info-circle":a.includes("LEGACY")?"pi pi-exclamation-triangle":"pi pi-circle"},P=n=>{const a=String(n?.kind||"");return a.includes("LEGACY")?"border-yellow-400/60 dark:border-yellow-500/60":a.includes("WITHIN_TOLERANCE")||a.includes("NEAR")?"border-sky-400/60 dark:border-sky-500/60":a.includes("EXACT")?"border-emerald-400/60 dark:border-emerald-500/60":"border-surface-200 dark:border-surface-600"},Q=n=>{const a=String(n?.kind||"");return a.includes("LEGACY")?"text-yellow-700 dark:text-yellow-300":a.includes("WITHIN_TOLERANCE")||a.includes("NEAR")?"text-sky-700 dark:text-sky-300":a.includes("EXACT")?"text-emerald-700 dark:text-emerald-300":"text-surface-800 dark:text-surface-200"},M=p(!1),O=p(null),{attributeSynonyms:B}=le(),G={props:r,synonymSeverity:t,getKindLabel:m,getDetailSeverity:_,showOnlyImportant:e,showNotes:g,search:v,sortMode:w,sortOptions:U,isNumeric:L,isStringSynonym:W,isExact:h,isImportant:D,filteredDetails:R,importanceRank:V,preparedDetails:F,summary:K,getKindIcon:j,accentClass:P,valueClass:Q,showSynonymModal:M,synonymModalData:O,attributeSynonyms:B,toggleSynonyms:async(n,a)=>{const y=n?.templateId,k=a==="item"?n?.itemValue:n?.partValue;if(!(!y||!k))try{const x=await B.utils.findGroupSynonymsByValue({templateId:y,value:k});x&&(O.value=x,M.value=!0)}catch(x){console.error("Не удалось получить синонимы:",x)}},VTag:Y,VToggleSwitch:J,VSelect:Z,VInputText:$,Icon:ee,SynonymDetailsModal:pe,VButton:se,get InfoIcon(){return ne}};return Object.defineProperty(G,"__isScriptSetup",{enumerable:!1,value:!0}),G}}),Te={class:"space-y-2"},Ce={key:0,class:"flex flex-wrap items-center justify-between gap-2"},ke={class:"flex items-center gap-3"},Se={class:"text-surface-600 inline-flex items-center gap-2 text-xs"},Ae={class:"text-surface-600 inline-flex items-center gap-2 text-xs"},Ie={class:"hidden items-center gap-2 md:flex"},Ve={class:"flex w-full max-w-xs items-center gap-2 md:max-w-sm"},Ne={class:"text-surface-500 hidden items-center gap-1 text-xs md:flex"},be={class:"hidden md:block"},we={class:"text-surface-500 grid grid-cols-12 px-2 py-2 text-[11px] tracking-wide uppercase"},Le={key:0,class:"col-span-1"},he={class:"divide-surface-border divide-y rounded border"},De={class:"col-span-3 flex min-w-0 items-center gap-2"},Re=["title"],Me={key:0,class:"text-surface-500 ml-1 text-xs"},Oe={key:0,class:"text-surface-500 ml-1 text-xs"},Be={key:1,class:"ml-1 inline-flex"},Ge={key:2,class:"ml-1 inline-flex"},He={class:"col-span-2"},Ye={key:0,class:"text-surface-500 col-span-1 text-xs"},ze={key:0},Xe={class:"grid grid-cols-1 gap-2 md:hidden"},Ue={class:"flex items-center justify-between"},We={class:"text-surface-700 dark:text-surface-300 flex items-center gap-2 text-xs"},Fe={class:"font-medium"},Ke={class:"text-surface-500 mt-1 text-xs"},je={key:0},Pe={key:1},Qe={class:"flex items-center gap-2"},qe={class:"flex items-center gap-2"},Je={key:0,class:"ml-1 inline-flex align-middle"},Ze={key:1,class:"ml-1 inline-flex align-middle"},$e={key:0,class:"text-surface-500 mt-1 text-xs"};function et(E,s,r,t,m,_){return i(),o("div",Te,[r.controls?(i(),o("div",Ce,[l("div",ke,[l("label",Se,[c(t.VToggleSwitch,{modelValue:t.showOnlyImportant,"onUpdate:modelValue":s[0]||(s[0]=e=>t.showOnlyImportant=e)},null,8,["modelValue"]),s[5]||(s[5]=l("span",null,"Только важное",-1))]),l("label",Ae,[c(t.VToggleSwitch,{modelValue:t.showNotes,"onUpdate:modelValue":s[1]||(s[1]=e=>t.showNotes=e)},null,8,["modelValue"]),s[6]||(s[6]=l("span",null,"Показывать заметки",-1))]),l("div",Ie,[s[7]||(s[7]=l("span",{class:"text-surface-500 text-xs"},"Сортировка",-1)),c(t.VSelect,{modelValue:t.sortMode,"onUpdate:modelValue":s[2]||(s[2]=e=>t.sortMode=e),options:t.sortOptions,optionLabel:"label",optionValue:"value",class:"w-40"},null,8,["modelValue"])])]),l("div",Ve,[c(t.VInputText,{modelValue:t.search,"onUpdate:modelValue":s[3]||(s[3]=e=>t.search=e),placeholder:"Поиск по атрибуту",class:"w-full"},null,8,["modelValue"])]),l("div",Ne,[c(t.VTag,{size:"small",value:`Всего ${t.summary.total}`,severity:"secondary"},null,8,["value"]),c(t.VTag,{size:"small",value:`EXACT ${t.summary.exact}`,severity:"success"},null,8,["value"]),c(t.VTag,{size:"small",value:`NEAR ${t.summary.near}`,severity:"info"},null,8,["value"]),c(t.VTag,{size:"small",value:`TOL ${t.summary.tol}`,severity:"info"},null,8,["value"]),c(t.VTag,{size:"small",value:`LEGACY ${t.summary.legacy}`,severity:"warning"},null,8,["value"])])])):u("",!0),l("div",be,[l("div",we,[s[8]||(s[8]=l("div",{class:"col-span-3"},"Атрибут",-1)),s[9]||(s[9]=l("div",{class:"col-span-3"},"Значение в item",-1)),s[10]||(s[10]=l("div",{class:"col-span-3"},"Значение в part",-1)),s[11]||(s[11]=l("div",{class:"col-span-2"},"Результат",-1)),t.showNotes?(i(),o("div",Le,"Заметки")):u("",!0)]),l("div",he,[(i(!0),o(A,null,b(t.preparedDetails,e=>(i(),o("div",{key:e.templateId+":"+e.kind+":"+e.itemValue+":"+e.partValue,class:f(["grid grid-cols-12 items-center border-l-2 px-2 py-2 text-sm",t.accentClass(e)])},[l("div",De,[c(t.Icon,{name:t.getKindIcon(e.kind),class:"text-surface-400 h-4 w-4"},null,8,["name"]),l("span",{class:"truncate",title:e.templateTitle||"template #"+e.templateId},d(e.templateTitle||"template #"+e.templateId),9,Re)]),l("div",{class:f(["col-span-3 flex items-center gap-2 font-mono break-words",t.valueClass(e)])},[l("span",null,d(e.itemValue),1),t.isNumeric(e)&&e.delta!==void 0?(i(),o("span",Me,"Δ="+d(Number(e.delta).toFixed(3)),1)):u("",!0),!t.isNumeric(e)&&e.templateId&&e.itemValue?(i(),C(t.VButton,{key:1,size:"small",severity:"secondary",outlined:"",onClick:g=>t.toggleSynonyms(e,"item")},{default:S(()=>[c(t.InfoIcon,{class:"h-4 w-4"})]),_:2},1032,["onClick"])):u("",!0)],2),l("div",{class:f(["col-span-3 flex items-center gap-2 font-mono break-words",t.valueClass(e)])},[l("span",null,d(e.partValue),1),t.isNumeric(e)&&e.toleranceUsed!==void 0?(i(),o("span",Oe,"tol="+d(e.toleranceUsed),1)):u("",!0),t.isStringSynonym(e)&&e.synonymLevel?(i(),o("span",Be,[c(t.VTag,{size:"small",value:String(e.synonymLevel),severity:t.synonymSeverity(String(e.synonymLevel))},null,8,["value","severity"])])):u("",!0),t.isStringSynonym(e)&&e.canonical?(i(),o("span",Ge,[c(t.VTag,{size:"small",value:`canonical: ${e.canonical}`,severity:"secondary"},null,8,["value"])])):u("",!0),!t.isNumeric(e)&&e.templateId&&e.partValue?(i(),C(t.VButton,{key:3,size:"small",severity:"secondary",outlined:"",onClick:g=>t.toggleSynonyms(e,"part")},{default:S(()=>[c(t.InfoIcon,{class:"h-4 w-4"})]),_:2},1032,["onClick"])):u("",!0)],2),l("div",He,[c(t.VTag,{size:"small",value:t.getKindLabel(e.kind),severity:t.getDetailSeverity(e.kind)},null,8,["value","severity"])]),t.showNotes?(i(),o("div",Ye,[e.notes?(i(),o("span",ze,d(e.notes),1)):u("",!0)])):u("",!0)],2))),128))])]),l("div",Xe,[(i(!0),o(A,null,b(t.preparedDetails,e=>(i(),o("div",{key:e.templateId+":"+e.kind+":"+e.itemValue+":"+e.partValue,class:f(["bg-surface-50 dark:bg-surface-900 rounded border border-l-2 p-2",t.accentClass(e)])},[l("div",Ue,[l("div",We,[c(t.Icon,{name:t.getKindIcon(e.kind),class:"text-surface-400 h-4 w-4"},null,8,["name"]),l("span",Fe,d(e.templateTitle||"template #"+e.templateId),1)]),c(t.VTag,{value:t.getKindLabel(e.kind),severity:t.getDetailSeverity(e.kind),size:"small"},null,8,["value","severity"])]),l("div",Ke,[t.isNumeric(e)?(i(),o(A,{key:0},[s[12]||(s[12]=T(" item: ")),l("span",{class:f(["font-mono",t.valueClass(e)])},d(e.itemValue),3),s[13]||(s[13]=T(" → part: ")),l("span",{class:f(["font-mono",t.valueClass(e)])},d(e.partValue),3),e.delta!==void 0?(i(),o("span",je," | Δ="+d(e.delta),1)):u("",!0),e.toleranceUsed!==void 0?(i(),o("span",Pe," | tol="+d(e.toleranceUsed),1)):u("",!0)],64)):(i(),o(A,{key:1},[l("div",Qe,[s[15]||(s[15]=T(" item: ")),l("span",{class:f(["font-mono",t.valueClass(e)])},d(e.itemValue),3),e.templateId&&e.itemValue?(i(),C(t.VButton,{key:0,size:"small",severity:"secondary",outlined:"",onClick:g=>t.toggleSynonyms(e,"item")},{default:S(()=>s[14]||(s[14]=[T("Синонимы")])),_:2,__:[14]},1032,["onClick"])):u("",!0)]),l("div",qe,[s[17]||(s[17]=T(" → part: ")),l("span",{class:f(["font-mono",t.valueClass(e)])},d(e.partValue),3),e.templateId&&e.partValue?(i(),C(t.VButton,{key:0,size:"small",severity:"secondary",outlined:"",onClick:g=>t.toggleSynonyms(e,"part")},{default:S(()=>s[16]||(s[16]=[T("Синонимы")])),_:2,__:[16]},1032,["onClick"])):u("",!0)]),e.synonymLevel?(i(),o("span",Je,[c(t.VTag,{size:"small",value:String(e.synonymLevel),severity:t.synonymSeverity(String(e.synonymLevel))},null,8,["value","severity"])])):u("",!0),e.canonical?(i(),o("span",Ze,[c(t.VTag,{size:"small",value:`canonical: ${e.canonical}`,severity:"secondary"},null,8,["value"])])):u("",!0)],64))]),t.showNotes&&e.notes?(i(),o("div",$e,d(e.notes),1)):u("",!0)],2))),128))]),c(t.SynonymDetailsModal,{modelValue:t.showSynonymModal,"onUpdate:modelValue":s[4]||(s[4]=e=>t.showSynonymModal=e),data:t.synonymModalData},null,8,["modelValue","data"])])}const vt=z(Ee,[["render",et]]);export{vt as M,ae as u};
