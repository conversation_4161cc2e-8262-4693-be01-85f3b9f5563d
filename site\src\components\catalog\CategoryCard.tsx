import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import type { PartCategoryItem } from '@/types/catalog';
import { navigate } from 'astro:transitions/client';

interface CategoryCardProps {
  category: PartCategoryItem;
  onClick?: () => void;
}

export function CategoryCard({ category, onClick }: CategoryCardProps) {
  const handleClick = () => {
    if (onClick) {
      onClick();
    } else {
      navigate(`/catalog/categories/${category.slug}`);
    }
  };

  return (
    <Card
      className="cursor-pointer transition-all hover:shadow-md hover:scale-[1.02]"
      onClick={handleClick}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg line-clamp-2 flex items-center gap-2">
              {category.icon && (
                <span className="text-xl">{category.icon}</span>
              )}
              {category.name}
            </CardTitle>
            {category.description && (
              <CardDescription className="mt-1 line-clamp-2">
                {category.description}
              </CardDescription>
            )}
          </div>
          {category.image && (
            <img
              src={category.image.url}
              alt={category.name}
              className="w-16 h-16 object-cover rounded-md ml-3"
            />
          )}
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Badge variant="secondary">
              Уровень {category.level}
            </Badge>
            {category._count && (
              <Badge variant="outline">
                {category._count.parts} запчастей
              </Badge>
            )}
          </div>
        </div>

        {/* Подкатегории */}
        {category.children && category.children.length > 0 && (
          <div className="mt-3 pt-3 border-t">
            <div className="text-xs text-muted-foreground mb-2">Подкатегории:</div>
            <div className="flex flex-wrap gap-1">
              {category.children.slice(0, 3).map((child) => (
                <Badge key={child.id} variant="outline" className="text-xs">
                  {child.name}
                </Badge>
              ))}
              {category.children.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{category.children.length - 3}
                </Badge>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
