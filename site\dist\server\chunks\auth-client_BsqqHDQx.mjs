import { createAuthClient } from 'better-auth/react';
import { inferAdditionalFields } from 'better-auth/client/plugins';

const baseURL = "http://localhost:3000";
const authClient = createAuthClient({
  baseURL,
  // Включаем передачу куков к стороннему домену API
  fetchOptions: {
    credentials: "include",
    timeout: 1e4
  },
  plugins: [
    // Типизируем дополнительные поля пользователя из server config (role)
    inferAdditionalFields({
      user: {
        role: { type: "string" }
      }
    })
  ]
});

export { authClient as a };
