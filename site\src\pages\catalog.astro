---
import MainLayout from "../layouts/MainLayout.astro";
import SearchHeaderIsland from "@/components/catalog/islands/SearchHeaderIsland";
import FiltersIsland from "@/components/catalog/islands/FiltersIsland";
import ResultsIsland from "@/components/catalog/islands/ResultsIsland";
import AIAssistantIsland from "@/components/catalog/islands/AIAssistantIsland";
import ItemDetailsIsland from "@/components/catalog/islands/ItemDetailsIsland";
---

<MainLayout title="Каталог запчастей" description="Поиск и просмотр каталога взаимозаменяемых запчастей">
  <div class="min-h-screen bg-background">
    <SearchHeaderIsland client:load />
    <div class="flex">
      <FiltersIsland client:visible />
      <ResultsIsland client:load />
    </div>
    <AIAssistantIsland client:idle />
    <ItemDetailsIsland client:idle />
  </div>
</MainLayout>
