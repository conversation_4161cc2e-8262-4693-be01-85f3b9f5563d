import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"
import { Badge } from "./badge"

const statusBadgeVariants = cva(
  "inline-flex items-center gap-1 font-medium",
  {
    variants: {
      status: {
        EXACT_MATCH: "bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800",
        MATCH_WITH_NOTES: "bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800",
        REQUIRES_MODIFICATION: "bg-orange-100 text-orange-800 border-orange-200 dark:bg-orange-900/20 dark:text-orange-400 dark:border-orange-800",
        PARTIAL_MATCH: "bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800",
      },
      size: {
        sm: "text-xs px-2 py-1",
        default: "text-sm px-2.5 py-1.5",
        lg: "text-base px-3 py-2",
      },
    },
    defaultVariants: {
      status: "EXACT_MATCH",
      size: "default",
    },
  }
)

const statusLabels = {
  EXACT_MATCH: "Точное совпадение",
  MATCH_WITH_NOTES: "С примечаниями",
  REQUIRES_MODIFICATION: "Требует доработки",
  PARTIAL_MATCH: "Частичное совпадение",
}

const statusIcons = {
  EXACT_MATCH: "●",
  MATCH_WITH_NOTES: "◐",
  REQUIRES_MODIFICATION: "◑",
  PARTIAL_MATCH: "○",
}

export interface StatusBadgeProps
  extends React.HTMLAttributes<HTMLSpanElement>,
    VariantProps<typeof statusBadgeVariants> {
  status: keyof typeof statusLabels
}

const StatusBadge = React.forwardRef<HTMLSpanElement, StatusBadgeProps>(
  ({ className, status, size, ...props }, ref) => {
    return (
      <Badge
        ref={ref}
        className={cn(statusBadgeVariants({ status, size, className }))}
        variant="outline"
        {...props}
      >
        <span className="text-current">{statusIcons[status]}</span>
        {statusLabels[status]}
      </Badge>
    )
  }
)
StatusBadge.displayName = "StatusBadge"

export { StatusBadge }