import { Hono } from "hono";
import { trpcServer } from "@hono/trpc-server";
import { appRouter } from "./router";
import { auth } from "./auth";
import { cors } from "hono/cors";
import { serveStatic } from "hono/bun";
import { createHonoHandler } from "@zenstackhq/server/hono";
import { getEnhancedDB, getSystemDB, getPublicDB } from "./db";

const app = new Hono<{
  Variables: {
    user: typeof auth.$Infer.Session.user | null;
    session: typeof auth.$Infer.Session.session | null;
  };
}>();

// Включаем CORS до всех остальных middleware
app.use(
  "*",
  cors({
    origin: [
      "http://localhost:3000",
      "http://localhost:3001",
      "http://localhost:4321",
      "http://localhost:4322",
      "http://localhost:4323",
      "http://localhost:4444",
    ], // добавляем все нужные origins
    allowMethods: ["POST", "GET", "OPTIONS", "HEAD"],
    allowHeaders: ["Content-Type", "Authorization", "Cookie", "X-Requested-With", "x-better-auth-csrf"],
    exposeHeaders: ["Set-Cookie"],
    credentials: true, // важно для работы с куками
    maxAge: 600,
  })
);

// Middleware аутентификации - должен быть перед API роутами
app.use("*", async (c, next) => {
  const session = await auth.api.getSession({ headers: c.req.raw.headers });

  if (!session) {
    c.set("user", null);
    c.set("session", null);
    return next();
  }

  c.set("user", session.user);
  c.set("session", session.session);
  return next();
});

// Статический middleware для загруженных файлов
app.use("/api/uploads/*", serveStatic({ root: "./uploads" }));

app.use(
  "/trpc/*",
  trpcServer({
    router: appRouter,
    createContext: async (opts, c) => {
      // Получаем пользователя из контекста Hono для tRPC
      const user = c.get("user");
      
      // Создаем контекст с правильной типизацией
      return {
        auth,
        prisma: getSystemDB(), // Системный DB для совместимости
        user: user as any, // Приводим к правильному типу
        db: user ? getEnhancedDB(user) : getPublicDB(), // Enhanced DB с авторизацией
        req: opts.req
      };
    },
  })
);

// Добавляем собственный роут для получения сессии ПЕРЕД общим auth handler
app.get("/api/auth/session", async (c) => {
  const session = await auth.api.getSession({ headers: c.req.raw.headers });

  if (!session) {
    return c.json({ user: null, session: null }, 200);
  }

  return c.json({
    user: session.user,
    session: session.session,
  });
});

// Обёртка для auth.handler с корректными CORS заголовками и обработкой preflight
app.use("/api/auth/*", async (c) => {
  // Обрабатываем preflight до передачи в auth.handler
  if (c.req.method === "OPTIONS") {
    return c.text("", 204);
  }

  const res = await auth.handler(c.req.raw);

  // Клонируем ответ и добавляем CORS заголовки (иначе они не проставляются на внешнем Response)
  const origin = c.req.header("origin");
  const headers = new Headers(res.headers);
  if (origin) {
    headers.set("Access-Control-Allow-Origin", origin);
    headers.append("Vary", "Origin");
  }
  headers.set("Access-Control-Allow-Credentials", "true");

  return new Response(res.body, {
    status: res.status,
    statusText: res.statusText,
    headers,
  });
});

// ZenStack API endpoints - добавляем REST API для всех моделей
app.use(
  "/api/model/*",
  createHonoHandler({
    getPrisma: (ctx) => {
      // Получаем пользователя из контекста Hono
      const user = ctx.get("user");
      return getEnhancedDB(user);
    },
    // Включаем Zod валидацию (используем уже сгенерированные схемы)
    zodSchemas: true,
  })
);

app.get("/session", async (c) => {
  const session = c.get("session");
  const user = c.get("user");

  if (!user) return c.body(null, 401);

  return c.json({
    session,
    user,
  });
});

export default {
  port: 3000,
  fetch: app.fetch,
};
