<template>
  <div class="space-y-8">
    <!-- Заголовок страницы -->
    <div>
      <h1 class="text-2xl font-bold text-surface-900">Дашборд</h1>
      <p class="mt-1 text-sm text-surface-600">
        Обзор системы управления каталогом запчастей
      </p>
    </div>

    <!-- Скелетон/ошибка -->
    <div v-if="loading" class="text-sm text-surface-500">Загрузка...</div>
    <div v-else-if="error" class="text-sm text-red-600">{{ error }}</div>

    <template v-else>
      <!-- Статистические карточки -->
      <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <Card>
          <template #content>
            <button type="button" aria-label="Пользователи" @click="navigateTo('/admin/users')" class="flex items-center w-full text-left hover:bg-surface-50 dark:hover:bg-surface-800/60 p-2 rounded-md transition-colors cursor-pointer">
              <div class="flex-shrink-0">
                <Icon name="pi pi-users" class="text-2xl text-primary-600" />
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-surface-500 truncate">Пользователи</dt>
                  <dd class="text-lg font-medium text-surface-900">{{ stats?.totals.users.toLocaleString() }}</dd>
                </dl>
              </div>
            </button>
          </template>
        </Card>

        <Card>
          <template #content>
            <button type="button" aria-label="Магазины" @click="navigateTo('/admin/users')" class="flex items-center w-full text-left hover:bg-surface-50 dark:hover:bg-surface-800/60 p-2 rounded-md transition-colors cursor-pointer">
              <div class="flex-shrink-0">
                <Icon name="pi pi-building" class="text-2xl text-primary-600" />
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-surface-500 truncate">Магазины</dt>
                  <dd class="text-lg font-medium text-surface-900">{{ stats?.totals.shops.toLocaleString() }}</dd>
                </dl>
              </div>
            </button>
          </template>
        </Card>

        <Card>
          <template #content>
            <button type="button" aria-label="Запчасти" @click="navigateTo('/admin/parts')" class="flex items-center w-full text-left hover:bg-surface-50 dark:hover:bg-surface-800/60 p-2 rounded-md transition-colors cursor-pointer">
              <div class="flex-shrink-0">
                <Icon name="pi pi-box" class="text-2xl text-primary-600" />
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-surface-500 truncate">Запчасти</dt>
                  <dd class="text-lg font-medium text-surface-900">{{ stats?.totals.parts.toLocaleString() }}</dd>
                </dl>
              </div>
            </button>
          </template>
        </Card>

        <Card>
          <template #content>
            <button type="button" aria-label="Активные сессии" @click="navigateTo('/admin/access-control')" class="flex items-center w-full text-left hover:bg-surface-50 dark:hover:bg-surface-800/60 p-2 rounded-md transition-colors cursor-pointer">
              <div class="flex-shrink-0">
                <Icon name="pi pi-bolt" class="text-2xl text-primary-600" />
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-surface-500 truncate">Активные сессии</dt>
                  <dd class="text-lg font-medium text-surface-900">{{ stats?.totals.sessionsActive.toLocaleString() }}</dd>
                </dl>
              </div>
            </button>
          </template>
        </Card>
      </div>

      <!-- Дополнительные метрики -->
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5">
        <Card>
          <template #content>
            <button type="button" aria-label="Бренды" @click="navigateTo('/admin/brands')" class="flex items-center justify-between w-full hover:bg-surface-50 dark:hover:bg-surface-800/60 p-2 rounded-md transition-colors cursor-pointer">
              <div class="flex items-center">
                <Icon name="pi pi-tags" class="text-xl text-primary-600" />
                <span class="ml-3 text-sm text-surface-600">Бренды</span>
              </div>
              <span class="text-base font-semibold">{{ stats?.totals.brands.toLocaleString() }}</span>
            </button>
          </template>
        </Card>
        <Card>
          <template #content>
            <button type="button" aria-label="Категории" @click="navigateTo('/admin/categories')" class="flex items-center justify-between w-full hover:bg-surface-50 dark:hover:bg-surface-800/60 p-2 rounded-md transition-colors cursor-pointer">
              <div class="flex items-center">
                <Icon name="pi pi-sitemap" class="text-xl text-primary-600" />
                <span class="ml-3 text-sm text-surface-600">Категории</span>
              </div>
              <span class="text-base font-semibold">{{ stats?.totals.partCategories.toLocaleString() }}</span>
            </button>
          </template>
        </Card>
        <Card>
          <template #content>
            <button type="button" aria-label="Каталог" @click="navigateTo('/admin/catalogitems')" class="flex items-center justify-between w-full hover:bg-surface-50 dark:hover:bg-surface-800/60 p-2 rounded-md transition-colors cursor-pointer">
              <div class="flex items-center">
                <Icon name="pi pi-list" class="text-xl text-primary-600" />
                <span class="ml-3 text-sm text-surface-600">Каталог</span>
              </div>
              <span class="text-base font-semibold">{{ stats?.totals.catalogItems.toLocaleString() }}</span>
            </button>
          </template>
        </Card>
        <Card>
          <template #content>
            <button type="button" aria-label="Предложения" @click="navigateTo('/admin/proposals')" class="flex items-center justify-between w-full hover:bg-surface-50 dark:hover:bg-surface-800/60 p-2 rounded-md transition-colors cursor-pointer">
              <div class="flex items-center">
                <Icon name="pi pi-inbox" class="text-xl text-primary-600" />
                <span class="ml-3 text-sm text-surface-600">Предложения (ожидают)</span>
              </div>
              <span class="text-base font-semibold">{{ stats?.totals.proposals.pending.toLocaleString() }}</span>
            </button>
          </template>
        </Card>
      </div>

      <!-- Основной контент -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Регистрации/тренды за 30 дней -->
        <Card class="lg:col-span-2">
          <template #header>
            <h3 class="text-lg leading-6 font-medium text-surface-900">Активность за {{ rangeDays }} дней</h3>
          </template>
          <template #content>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div class="p-3 rounded-lg bg-surface-50 dark:bg-surface-800">
                <div class="text-xs text-surface-500 mb-1">Новые пользователи</div>
                <div class="text-xl font-semibold">{{ sumSeries(stats!.trends.usersDaily).toLocaleString() }}</div>
              </div>
              <div class="p-3 rounded-lg bg-surface-50 dark:bg-surface-800">
                <div class="text-xs text-surface-500 mb-1">Новые запчасти</div>
                <div class="text-xl font-semibold">{{ sumSeries(stats!.trends.partsDaily).toLocaleString() }}</div>
              </div>
              <div class="p-3 rounded-lg bg-surface-50 dark:bg-surface-800">
                <div class="text-xs text-surface-500 mb-1">Новые предложения</div>
                <div class="text-xl font-semibold">{{ sumSeries(stats!.trends.proposalsDaily).toLocaleString() }}</div>
              </div>
            </div>
            <div class="mt-4 grid grid-cols-7 gap-2 text-xs text-surface-500">
              <div v-for="d in last7(stats!.trends.usersDaily)" :key="d.date" class="flex flex-col items-center">
                <div class="h-10 w-8 flex items-end">
                  <div class="w-full bg-primary-500/20" :style="{ height: `${barHeight(d.count, stats!.trends.usersDaily)}%` }"></div>
                </div>
                <div class="mt-1">{{ shortDate(d.date) }}</div>
              </div>
            </div>
          </template>
        </Card>

        <!-- Топ категорий -->
        <Card>
          <template #header>
            <h3 class="text-lg leading-6 font-medium text-surface-900">Популярные категории</h3>
          </template>
          <template #content>
            <div class="space-y-3">
              <div v-for="category in stats?.top.categories" :key="category.id" class="flex justify-between items-center">
                <span class="text-sm font-medium text-surface-900">{{ category.name }}</span>
                <span class="text-sm text-surface-500">{{ category.count.toLocaleString() }}</span>
              </div>
            </div>
          </template>
        </Card>
      </div>

      <!-- Недавние события -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <Card>
          <template #header>
            <h3 class="text-lg leading-6 font-medium text-surface-900">Последние действия (аудит)</h3>
          </template>
          <template #content>
            <div v-if="(stats?.recent.audit?.length || 0) === 0" class="text-sm text-surface-500">Нет записей</div>
            <div v-else class="space-y-3">
              <div v-for="log in stats!.recent.audit" :key="log.id" class="flex items-center p-3 bg-surface-50 dark:bg-surface-800 rounded-lg">
                <Icon name="pi pi-history" class="text-surface-500 mr-3" />
                <div class="flex-1">
                  <div class="text-sm text-surface-900">{{ log.action }}</div>
                  <div class="text-xs text-surface-500">{{ formatDate(log.createdAt) }} • {{ log.adminEmail }}</div>
                </div>
              </div>
            </div>
          </template>
        </Card>

        <Card>
          <template #header>
            <h3 class="text-lg leading-6 font-medium text-surface-900">Новые предложения эквивалентов</h3>
          </template>
          <template #content>
            <div v-if="(stats?.recent.pendingProposals?.length || 0) === 0" class="text-sm text-surface-500">Нет новых предложений</div>
            <div v-else class="space-y-3">
              <div v-for="p in stats!.recent.pendingProposals" :key="p.id" class="flex items-center p-3 bg-surface-50 dark:bg-surface-800 rounded-lg">
                <Icon name="pi pi-link" class="text-primary mr-3" />
                <div class="flex-1">
                  <div class="text-sm text-surface-900">
                    {{ p.catalogItem?.brand?.name || 'Бренд' }} • {{ p.catalogItem?.sku }} → {{ p.part?.name || `Part #${p.part?.id}` }}
                  </div>
                  <div class="text-xs text-surface-500">{{ p.accuracySuggestion }} • {{ formatDate(p.createdAt) }}</div>
                </div>
              </div>
            </div>
          </template>
        </Card>
      </div>
    </template>
  </div>
  
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import Card from '@/volt/Card.vue'
import Icon from '@/components/ui/Icon.vue'
import { trpc } from '@/lib/trpc'
import { navigate } from 'astro:transitions/client'

interface DailyPoint { date: string; count: number }

interface DashboardData {
  totals: {
    users: number
    shops: number
    brands: number
    oemBrands: number
    parts: number
    partCategories: number
    catalogItems: number
    equipmentModels: number
    attributes: { partAttributes: number; catalogItemAttributes: number; equipmentModelAttributes: number }
    synonyms: { groups: number; values: number }
    templates: { total: number; numeric: number; numericWithTolerance: number }
    sessionsActive: number
    proposals: { pending: number; approved: number; rejected: number }
  }
  trends: { usersDaily: DailyPoint[]; partsDaily: DailyPoint[]; proposalsDaily: DailyPoint[] }
  top: { categories: Array<{ id: number; name: string; count: number }>; brands: Array<{ id: number; name: string; count: number }> }
  recent: {
    audit: Array<{ id: string; action: string; createdAt: string; adminEmail: string; targetUserEmail?: string | null }>
    pendingProposals: Array<{ id: number; createdAt: string; accuracySuggestion: string; notesSuggestion?: string | null; catalogItem: { id: number; sku: string; brand: { id: number; name: string } | null } | null; part: { id: number; name: string | null } | null }>
  }
}

const loading = ref(true)
const error = ref<string | null>(null)
const stats = ref<DashboardData | null>(null)
const rangeDays = 30

onMounted(async () => {
  try {
    const res = await trpc.admin.getDashboard.query({ rangeDays })
    stats.value = res as unknown as DashboardData
  } catch (e: any) {
    error.value = e?.message ?? 'Не удалось загрузить дашборд'
  } finally {
    loading.value = false
  }
})

function sumSeries(series: DailyPoint[]): number {
  return series.reduce((acc, p) => acc + p.count, 0)
}

function last7(series: DailyPoint[]): DailyPoint[] {
  return series.slice(-7)
}

function maxInSeries(series: DailyPoint[]): number {
  return series.reduce((m, p) => Math.max(m, p.count), 0) || 1
}

function barHeight(value: number, series: DailyPoint[]): number {
  const max = maxInSeries(series)
  return Math.round((value / max) * 100)
}

function shortDate(iso: string): string {
  const d = new Date(iso)
  return `${d.getDate().toString().padStart(2, '0')}.${(d.getMonth()+1).toString().padStart(2, '0')}`
}

function formatDate(iso: string): string {
  const d = new Date(iso)
  return d.toLocaleString()
}

function navigateTo(path: string) {
  navigate(path)
}
</script>

