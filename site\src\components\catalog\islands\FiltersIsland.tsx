"use client"

import { useMemo } from "react"
import { ModernFiltersPanel } from "../pro/ModernFiltersPanel"
import { useCatalogSearch } from "../pro/useCatalogSearch"
import { useCatalogMetadata } from "@/hooks/useCatalogData"

import { TrpcBoundary } from "@/components/providers/TrpcBoundary"

export default function FiltersIsland() {
  return (
    <TrpcBoundary>
      <FiltersIslandInner />
    </TrpcBoundary>
  )
}

function FiltersIslandInner() {
  const { filters, setFilters, clearFilters, availableAttributeValues } = useCatalogSearch()
  const { categories, brands, templates } = useCatalogMetadata()

  const activeFiltersCount = useMemo(() => {
    return filters.categoryIds.length +
      filters.brandIds.length +
      Object.keys(filters.attributeFilters).length +
      filters.accuracyLevels.length +
      (filters.isOemOnly ? 1 : 0)
  }, [filters])

  return (
    <ModernFiltersPanel
      filters={filters}
      setFilters={setFilters}
      activeFiltersCount={activeFiltersCount}
      onClearAll={clearFilters}
      mockCategories={categories}
      mockBrands={brands}
      mockAttributeTemplates={templates}
      availableAttributeValues={availableAttributeValues}
    />
  )
}
