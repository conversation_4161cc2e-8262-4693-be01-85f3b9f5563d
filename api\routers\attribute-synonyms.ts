import { z } from 'zod'
import { router, procedure, expertProcedure } from '../trpc'
import { AttributeSynonymsService } from '../services/attribute-synonyms.service'
import { SynonymCompatibilityLevelSchema } from '../generated/zod/enums/SynonymCompatibilityLevel.schema'

// Вспомогательные схемы
const PaginationInput = z.object({
  limit: z.number().min(1).max(100).default(50),
  offset: z.number().min(0).default(0)
})

const GroupFindManyInput = z.object({
  templateId: z.number(),
  search: z.string().optional().nullable(),
}).merge(PaginationInput)

const GroupCreateInput = z.object({
  templateId: z.number(),
  name: z.string().min(1).max(100),
  canonicalValue: z.string().min(1).optional(),
  parentId: z.number().optional().nullable(),
  description: z.string().optional().nullable(),
  compatibilityLevel: SynonymCompatibilityLevelSchema.default('EXACT'),
  notes: z.string().optional().nullable(),
})

const GroupUpdateInput = z.object({
  id: z.number(),
  name: z.string().min(1).max(100).optional(),
  canonicalValue: z.string().optional().nullable(),
  parentId: z.number().optional().nullable(),
  description: z.string().optional().nullable(),
  compatibilityLevel: SynonymCompatibilityLevelSchema.optional(),
  notes: z.string().optional().nullable(),
})

const GroupDeleteInput = z.object({ id: z.number() })

const SynonymFindManyInput = z.object({ groupId: z.number() })

const SynonymUpdateInput = z.object({
  id: z.number(),
  notes: z.string().optional().nullable(),
  brandId: z.number().optional().nullable(),
  compatibilityLevel: SynonymCompatibilityLevelSchema.optional(),
})

const SynonymCreateInput = z.object({

  groupId: z.number(),
  value: z.string().trim().min(1).max(200),
  notes: z.string().optional().nullable(),
  brandId: z.number().optional().nullable(),
  compatibilityLevel: SynonymCompatibilityLevelSchema.optional(),
})

const SynonymDeleteInput = z.object({ id: z.number() })

const FindGroupByValueInput = z.object({
  templateId: z.number(),
  value: z.string().trim().min(1)
})

const FindGroupSynonymsByValueInput = z.object({
  templateId: z.number(),
  value: z.string().trim().min(1)
})

/**
 * Роутер для управления группами синонимов атрибутов (ADMIN-only)
 */
export const attributeSynonymsRouter = router({
  groups: router({
    findMany: expertProcedure
      .input(GroupFindManyInput)
      .query(async ({ input }) => AttributeSynonymsService.findGroups(input)),

    create: expertProcedure
      .input(GroupCreateInput)
      .mutation(async ({ input }) => AttributeSynonymsService.createGroup(input)),

    update: expertProcedure
      .input(GroupUpdateInput)
      .mutation(async ({ input }) => AttributeSynonymsService.updateGroup(input)),

    delete: expertProcedure
      .input(GroupDeleteInput)
      .mutation(async ({ input }) => AttributeSynonymsService.deleteGroup(input))
  }),

  synonyms: router({
    findMany: expertProcedure
      .input(SynonymFindManyInput)
      .query(async ({ input }) => AttributeSynonymsService.findSynonyms(input)),

    create: expertProcedure
      .input(SynonymCreateInput)
      .mutation(async ({ input }) => AttributeSynonymsService.createSynonym(input)),

    update: expertProcedure
      .input(SynonymUpdateInput)
      .mutation(async ({ input }) => AttributeSynonymsService.updateSynonym(input)),

    delete: expertProcedure
      .input(SynonymDeleteInput)
      .mutation(async ({ input }) => AttributeSynonymsService.deleteSynonym(input))
  }),

  utils: router({
    findGroupByValue: expertProcedure
      .input(FindGroupByValueInput)
      .query(async ({ input }) => AttributeSynonymsService.findGroupByValue(input)),

    findGroupSynonymsByValue: expertProcedure
      .input(FindGroupSynonymsByValueInput)
      .query(async ({ input }) => AttributeSynonymsService.findGroupSynonymsByValue(input))
  })
})


