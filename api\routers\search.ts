/**
 * API для расширенного поиска с поддержкой диапазонных запросов
 */

import { z } from 'zod'
import { router, publicProcedure, expertProcedure } from '../trpc'
import { AttributeFilter, SearchCatalogItemsInput, SearchPartsInput, GetAttributeStatsInput } from '../schemas/search'
import { SearchService } from '../services/search.service'

export const searchRouter = router({
  /**
   * Расширенный поиск Parts с поддержкой диапазонных запросов
   */
  searchParts: publicProcedure
    .input(SearchPartsInput)
    .query(async ({ input }: { input: z.infer<typeof SearchPartsInput> }) => SearchService.searchParts(input)),
  
  /**
   * Расширенный поиск CatalogItems с поддержкой диапазонных запросов
   */
  searchCatalogItems: publicProcedure
    .input(SearchCatalogItemsInput)
    .query(async ({ input }: { input: z.infer<typeof SearchCatalogItemsInput> }) => SearchService.searchCatalogItems(input)),
  
  /**
   * Получение статистики по числовым атрибутам
   * Полезно для построения фильтров с диапазонами
   */
  getAttributeStats: expertProcedure
    .input(GetAttributeStatsInput)
    .query(async ({ input }: { input: z.infer<typeof GetAttributeStatsInput> }) => SearchService.getAttributeStats(input))
})
