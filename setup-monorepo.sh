#!/bin/bash

# Скрипт для настройки монорепозитория Parttec

echo "=== Настройка монорепозитория Parttec ==="

# Проверяем, что мы в правильной директории
if [ ! -f ".gitmodules" ]; then
    echo "Ошибка: Запустите скрипт из корня репозитория parttec"
    exit 1
fi

echo "1. Отправляем код cpanel в его репозиторий..."
cd cpanel
if [ ! -d ".git" ]; then
    echo "Ошибка: cpanel не является git репозиторием"
    exit 1
fi

# Проверяем, есть ли удаленный репозиторий
if ! git remote get-url origin > /dev/null 2>&1; then
    echo "Добавляем удаленный репозиторий для cpanel..."
    git remote add origin https://github.com/xferqr/parttec3-cpanel.git
fi

echo "Отправляем cpanel в GitHub..."
git push -u origin master

cd ..

echo "2. Отправляем код site в его репозиторий..."
cd site
if [ ! -d ".git" ]; then
    echo "Ошибка: site не является git репозиторием"
    exit 1
fi

# Проверяем, есть ли удаленный репозиторий
if ! git remote get-url origin > /dev/null 2>&1; then
    echo "Добавляем удаленный репозиторий для site..."
    git remote add origin https://github.com/xferqr/parttec3-site.git
fi

echo "Отправляем site в GitHub..."
git push -u origin master

cd ..

echo "3. Удаляем локальные папки и добавляем как подмодули..."

# Удаляем папки из git индекса, но оставляем файлы
git rm -r --cached cpanel site 2>/dev/null || true

# Временно переименовываем папки
mv cpanel cpanel_temp
mv site site_temp

echo "4. Добавляем подмодули..."
git submodule add https://github.com/xferqr/parttec3-cpanel.git cpanel
git submodule add https://github.com/xferqr/parttec3-site.git site

echo "5. Инициализируем подмодули..."
git submodule update --init --recursive

echo "6. Удаляем временные папки..."
rm -rf cpanel_temp site_temp

echo "7. Коммитим изменения в основном репозитории..."
git add .
git commit -m "Setup monorepo with submodules"

echo "8. Отправляем изменения в основной репозиторий..."
git push origin master

echo "=== Монорепозиторий успешно настроен! ==="
echo ""
echo "Структура:"
echo "  /api/    - API сервер (подмодуль)"
echo "  /cpanel/ - Админ панель (подмодуль)"
echo "  /site/   - Основной сайт (подмодуль)"
echo ""
echo "Для работы с подмодулями используйте:"
echo "  git submodule update --remote  # Обновить все подмодули"
echo "  cd <submodule> && git checkout main  # Переключиться на ветку в подмодуле"
