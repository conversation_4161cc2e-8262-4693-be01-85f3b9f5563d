import { createTRPCReact } from '@trpc/react-query';
import { createTRPCProxyClient, httpBatchLink, loggerLink } from '@trpc/client';
import superjson from 'superjson';

// Типы для tRPC роутера (заглушка для билда)
// В рантайме типы будут получены от API сервера
type AppRouter = any;

// Базовый URL API (совместимо с better-auth клиентом)
const baseURL = import.meta.env.PUBLIC_API_URL || 'http://localhost:3000';

// React Query tRPC клиент для компонентов
export const trpc = createTRPCReact<AppRouter>();

// Proxy tRPC клиент для использования в Astro и вне React (поддерживает .query/.mutate)
export const trpcClient = createTRPCProxyClient<AppRouter>({
  transformer: superjson,
  links: [
    loggerLink({
      enabled: () => import.meta.env.DEV,
    }),
    httpBatchLink({
      url: `${baseURL}/trpc`,
      fetch: (url, options) =>
        fetch(url, {
          ...(options as RequestInit),
          credentials: 'include',
        }),
    }),
  ],
});

// Утилита для получения базового URL в рантайме (SSR/SPA)
export const getBaseUrl = () => {
  if (typeof window !== 'undefined') return '';
  return baseURL;
};
