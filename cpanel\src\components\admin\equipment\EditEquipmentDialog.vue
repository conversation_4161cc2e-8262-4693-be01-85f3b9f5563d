<template>
  <Dialog v-model:visible="isVisible" modal :header="dialogTitle" class="sm:w-100 w-9/10">
    <!-- Форма для редактирования данных -->
    <div class="flex flex-col gap-4 py-4">
      <div class="flex flex-col">
        <label for="name">Наименование модели</label>
        <InputText id="name" v-model="localEquipment.name" placeholder="Например: Экскаватор CAT 320D" />
      </div>
      <div class="flex flex-col">
        <label for="brand">Бренд</label>
        <Dropdown 
          id="brand" 
          v-model="localEquipment.brandId" 
          :options="brandOptions" 
          optionLabel="name" 
          optionValue="id" 
          placeholder="Выберите бренд"
          showClear
        />
      </div>
    </div>
    <div class="flex justify-end gap-2">
      <SecondaryButton type="button" label="Cancel" @click="handleCancel" />
      <Button type="button" label="Save" @click="handleSave" />
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import Button from '@/volt/Button.vue'
import Dialog from '@/volt/Dialog.vue'
import InputText from '@/volt/InputText.vue'
import Dropdown from '@/volt/Dropdown.vue'
import SecondaryButton from '@/volt/SecondaryButton.vue'
import type { EquipmentModelFormData } from '@/types/equipment'
import { trpc } from '@/lib/trpc'

const brandOptions = ref<Array<{ id: number; name: string }>>([])

// v-model для управления видимостью диалога
const isVisible = defineModel<boolean>('isVisible', { required: true })

// Входящие данные модели техники для редактирования
const props = defineProps<{
  equipment: EquipmentModelFormData | null
}>()

// Определяем события, которые компонент может отправлять родителю
const emit = defineEmits<{
  (e: 'save', equipmentData: EquipmentModelFormData): void
  (e: 'cancel'): void
}>()

// Локальное состояние для данных формы.
// Инициализируется пустым объектом или данными входящей модели техники.
const localEquipment = ref<EquipmentModelFormData>({})

// Заголовок диалогового окна
const dialogTitle = ref('Создать модель техники')

// Отслеживаем изменения isVisible.
// При открытии диалога (новое значение true), инициализируем локальные данные
watch(isVisible, (newValue) => {
  if (newValue) {
    // Копируем данные из props в локальное состояние, чтобы избежать прямого изменения props.
    // Если props.equipment не предоставлен (создание новой), localEquipment будет пустым объектом.
    localEquipment.value = { ...(props.equipment || {}) }
    dialogTitle.value = props.equipment?.id ? `Редактировать: ${props.equipment.name}` : 'Создать модель техники'
  }
})

function handleCancel() {
  isVisible.value = false
  emit('cancel')
}

function handleSave() {
  // Отправляем копию локальных данных, чтобы избежать
  // случайных изменений после отправки события.
  emit('save', { ...localEquipment.value })
  isVisible.value = false
}

async function loadBrands() {
  const brands = await trpc.crud.brand.findMany.query({
    select: {
      id: true,
      name: true
    },
    orderBy: {
      name: 'asc'
    }
  })

  brandOptions.value = brands
}

onMounted(() => {
  loadBrands()
})
</script>