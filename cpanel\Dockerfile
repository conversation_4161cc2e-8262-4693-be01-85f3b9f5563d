# Multi-stage build для оптимизации
FROM node:20-alpine AS dependencies

WORKDIR /app

# Копируем package.json и package-lock.json
COPY package*.json ./

# Устанавливаем все зависимости (включая dev)
RUN npm ci

# Build stage
FROM node:20-alpine AS build

WORKDIR /app

# Копируем зависимости из предыдущего stage
COPY --from=dependencies /app/node_modules ./node_modules
COPY package*.json ./

# Копируем исходный код
COPY . .

# Собираем приложение
RUN npm run build

# Production stage
FROM node:20-alpine AS production

WORKDIR /app

# Создаем пользователя для безопасности
RUN addgroup -g 1001 -S nodejs
RUN adduser -S astro -u 1001

# Копируем только production зависимости
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

# Копируем собранное приложение
COPY --from=build /app/dist ./dist

# Устанавливаем права доступа
RUN chown -R astro:nodejs /app

# Переключаемся на непривилегированного пользователя
USER astro

# Открываем порт
EXPOSE 4322

# Устанавливаем переменные окружения
ENV NODE_ENV=production
ENV HOST=0.0.0.0
ENV PORT=4322

# Запускаем приложение
CMD ["node", "./dist/server/entry.mjs"]
