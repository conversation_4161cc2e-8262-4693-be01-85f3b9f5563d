/**
 * Composable для управления фильтрами каталога с сохранением в URL
 */

import { ref, computed } from 'vue';
import type { CatalogFilters, ViewPreferences } from '../utils/filters';
import { filtersToQuery, hasActiveFilters } from '../utils/filters';
import { useUrlParams } from './useUrlParams';

export function useCatalogFilters(initialFilters?: CatalogFilters) {
  // Синхронизация фильтров с URL через универсальный useUrlParams
  const urlSync = useUrlParams({
    search: initialFilters?.search ?? '',
    categoryIds: initialFilters?.categoryIds ?? [],
    brandIds: initialFilters?.brandIds ?? [],
    equipmentModelIds: initialFilters?.equipmentModelIds ?? [],
    priceMin: initialFilters?.priceRange?.[0],
    priceMax: initialFilters?.priceRange?.[1]
  }, {
    prefix: 'catalog_',
    arrayParams: ['categoryIds', 'brandIds', 'equipmentModelIds'],
    numberParams: ['priceMin', 'priceMax'],
    debounceMs: 300
  });

  // Приводим форму из urlSync к нашему интерфейсу CatalogFilters (priceRange)
  const filtersState = computed<CatalogFilters>(() => {
    const f: any = urlSync.filters.value as any;
    const priceRange: [number, number] | undefined = (f.priceMin != null || f.priceMax != null)
      ? [f.priceMin ?? 0, f.priceMax ?? Infinity]
      : undefined;
    return {
      search: f.search || undefined,
      categoryIds: Array.isArray(f.categoryIds) && f.categoryIds.length ? f.categoryIds.map(Number) : undefined,
      brandIds: Array.isArray(f.brandIds) && f.brandIds.length ? f.brandIds.map(Number) : undefined,
      equipmentModelIds: Array.isArray(f.equipmentModelIds) && f.equipmentModelIds.length ? f.equipmentModelIds : undefined,
      priceRange
    };
  });

  // Настройки отображения (не храним в URL)
  const viewPreferences = ref<ViewPreferences>({
    mode: 'grid',
    itemsPerPage: 20,
    sortBy: 'name',
    sortOrder: 'asc'
  });

  // Вычисляемые свойства
  const hasFilters = computed(() => hasActiveFilters(filtersState.value));
  const queryParams = computed(() => filtersToQuery(filtersState.value));

  // Синхронизацией с URL занимается urlSync, здесь ничего не делаем



  // Методы для работы с фильтрами (обновляют urlSync)
  const updateFilters = (updates: Partial<CatalogFilters>) => {
    const patch: Record<string, any> = {};
    if ('search' in updates) patch.search = updates.search || undefined;
    if ('categoryIds' in updates) patch.categoryIds = updates.categoryIds && updates.categoryIds.length ? updates.categoryIds : undefined;
    if ('brandIds' in updates) patch.brandIds = updates.brandIds && updates.brandIds.length ? updates.brandIds : undefined;
    if ('equipmentModelIds' in updates) patch.equipmentModelIds = updates.equipmentModelIds && updates.equipmentModelIds.length ? updates.equipmentModelIds : undefined;
    if ('priceRange' in updates) {
      const pr = updates.priceRange as [number, number] | undefined;
      patch.priceMin = pr?.[0];
      patch.priceMax = pr?.[1];
    }
    urlSync.updateFilters(patch as any);
  };

  const setSearch = (search: string) => {
    updateFilters({ search: search || undefined });
  };

  const setCategoryIds = (categoryIds: number[]) => {
    updateFilters({ categoryIds: categoryIds.length ? categoryIds : undefined });
  };

  const setBrandIds = (brandIds: number[]) => {
    updateFilters({ brandIds: brandIds.length ? brandIds : undefined });
  };

  const setEquipmentModelIds = (equipmentModelIds: string[]) => {
    updateFilters({ equipmentModelIds: equipmentModelIds.length ? equipmentModelIds : undefined });
  };

  const setPriceRange = (priceRange: [number, number] | undefined) => {
    updateFilters({ priceRange });
  };

  const setAttributes = (attributes: any[]) => {
    updateFilters({ attributes: attributes.length ? attributes : undefined });
  };

  const resetFilters = () => {
    urlSync.updateFilters({
      search: undefined,
      categoryIds: undefined,
      brandIds: undefined,
      equipmentModelIds: undefined,
      priceMin: undefined,
      priceMax: undefined
    } as any);
  };

  // Методы для настроек отображения
  const setViewMode = (mode: 'grid' | 'list' | 'table') => {
    viewPreferences.value.mode = mode;
  };

  const setItemsPerPage = (count: number) => {
    viewPreferences.value.itemsPerPage = count;
  };

  const setSorting = (sortBy: string, sortOrder: 'asc' | 'desc' = 'asc') => {
    viewPreferences.value.sortBy = sortBy;
    viewPreferences.value.sortOrder = sortOrder;
  };

  return {
    // Состояние
    filters: computed(() => filtersState.value),
    viewPreferences: computed(() => viewPreferences.value),
    hasFilters,
    queryParams,

    // Методы обновления фильтров
    updateFilters,
    setSearch,
    setCategoryIds,
    setBrandIds,
    setEquipmentModelIds,
    setPriceRange,
    setAttributes,
    resetFilters,

    // Методы настроек отображения
    setViewMode,
    setItemsPerPage,
    setSorting,
  };
}