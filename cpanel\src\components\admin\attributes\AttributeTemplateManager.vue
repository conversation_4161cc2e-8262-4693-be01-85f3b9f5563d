<template>
  <div class="attribute-template-manager">
    <!-- Индикатор загрузки -->
    <div v-if="loading" class="py-12 text-center">
      <Icon name="pi pi-spinner pi-spin" class="text-primary mb-4 inline-block text-4xl" />
      <p class="text-surface-600 dark:text-surface-400">Загрузка шаблонов атрибутов...</p>
    </div>

    <!-- Основной контент -->
    <div v-else>
      <!-- Заголовок и действия -->
      <div class="mb-6 flex items-center justify-between">
        <div>
          <h2 class="text-surface-900 dark:text-surface-0 text-xl font-semibold">Управление атрибутами</h2>
          <p class="text-surface-600 dark:text-surface-400 mt-1 text-sm">Группы и шаблоны атрибутов для запчастей, каталожных позиций и техники</p>
        </div>

        <div class="flex gap-3">
          <VButton @click="refreshData" :disabled="loading" severity="secondary" outlined label="Обновить">
          </VButton>

          <VButton @click="showGroupDialog = true" severity="secondary" outlined label="Создать группу">
          </VButton>

          <VButton @click="createNewTemplate" label="Создать шаблон">
          </VButton>
        </div>
      </div>

      <!-- Дерево групп и управление -->
      <div class="mb-6 grid grid-cols-1 gap-4 lg:grid-cols-3">
        <!-- Левая колонка: дерево групп -->
        <VCard>
          <template #content>
            <div class="p-4 space-y-3">
              <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-surface-900 dark:text-surface-0">Дерево групп</h3>
                <VButton size="small" severity="secondary" outlined @click="refreshGroupData">
                  <RefreshCwIcon class="w-4 h-4"/>
                </VButton>
              </div>
              <VTree
                :value="treeNodes"
                v-model:expandedKeys="expandedKeys"
                filter
                filterPlaceholder="Поиск групп..."
              >
                <template #default="{ node }">
                  <div class="flex items-center justify-between w-full" @click.stop>
                    <div class="flex items-center gap-2 flex-1 cursor-pointer rounded px-2 py-1 transition-colors"
                         :class="{ 'bg-primary text-primary-contrast': selectedTreeGroup?.id === node.data.id, 'hover:bg-surface-100 dark:hover:bg-surface-800': selectedTreeGroup?.id !== node.data.id }"
                         @click="selectTreeGroup(node.data)">
                      <span class="font-medium">{{ node.data.name }}</span>
                      <VTag :value="node.data._count?.templates || 0" severity="secondary" size="small" />
                    </div>
                    <div class="flex gap-1">
                      <VButton size="small" severity="secondary" outlined @click.stop="toggleGroupExpansion(node.data)"
                        :title="isGroupExpanded(node.data) ? 'Свернуть группу' : 'Развернуть группу'">
                        <ChevronDownIcon v-if="isGroupExpanded(node.data)" class="w-3 h-3" />
                        <ChevronRightIcon v-else class="w-3 h-3" />
                      </VButton>
                      <VButton size="small" severity="secondary" outlined @click.stop="editGroup(node.data)">
                        <PencilIcon class="w-3 h-3" />
                      </VButton>
                      <VButton
                        size="small"
                        severity="danger"
                        outlined
                        @click.stop="deleteGroup(node.data)"
                        :disabled="(node.data._count?.templates || 0) > 0 || (node.data._count?.children || 0) > 0"
                      >
                        <TrashIcon class="w-3 h-3" />
                      </VButton>
                    </div>
                  </div>
                </template>
              </VTree>
            </div>
          </template>
        </VCard>

        <!-- Правая колонка: детали выбранной группы -->
        <VCard class="lg:col-span-2">
          <template #content>
            <div class="p-4 space-y-3">
              <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-surface-900 dark:text-surface-0">
                  {{ selectedTreeGroup ? `Группа: ${selectedTreeGroup.name}` : 'Выберите группу' }}
                </h3>
                <div class="flex gap-2" v-if="selectedTreeGroup">
                  <VButton outlined @click="editGroup(selectedTreeGroup)">
                    <PencilLineIcon class="w-4 h-4" />
                    Редактировать
                  </VButton>
                </div>
              </div>

              <div v-if="!selectedTreeGroup" class="text-surface-500 text-center py-8">
                Выберите группу в дереве слева для просмотра деталей и шаблонов
              </div>

              <div v-else class="space-y-4">
                <div class="flex items-center gap-3">
                  <FolderIcon class="text-blue-600 w-5 h-5" />
                  <div class="flex-1">
                    <div class="font-medium">{{ selectedTreeGroup.name }}</div>
                    <div class="text-sm text-surface-500" v-if="selectedTreeGroup.description">{{ selectedTreeGroup.description }}</div>
                  </div>
                  <VTag :value="`${selectedTreeGroup._count?.templates || 0} шаблонов`" severity="secondary" />
                  <VTag v-if="selectedTreeGroup._count?.children" :value="`${selectedTreeGroup._count.children} дочерних`" severity="info" />
                </div>

                <!-- Шаблоны в выбранной группе -->
                <div v-if="selectedTreeGroup._count?.templates > 0" class="border-t border-surface-200 dark:border-surface-700 pt-4">
                  <div class="flex items-center justify-between mb-3">
                    <span class="text-sm font-medium">Шаблоны в группе:</span>
                    <VButton
                      :label="showGroupTemplates ? 'Скрыть' : 'Показать'"
                      @click="toggleGroupTemplates"
                      severity="secondary"
                      text
                      size="small"
                    />
                  </div>

                  <div v-if="showGroupTemplates" class="space-y-2">
                    <div v-if="groupTemplates.length > 0" class="grid gap-2">
                      <div v-for="template in groupTemplates" :key="template.id" class="flex items-center justify-between p-3 bg-surface-50 dark:bg-surface-900 rounded-lg">
                        <div class="flex items-center gap-3">
                          <TagIcon class="text-green-600 w-4 h-4" />
                          <div>
                            <div class="font-medium text-surface-900 dark:text-surface-0">{{ template.title }}</div>
                            <div class="text-sm text-surface-600 dark:text-surface-400">{{ template.name }} • {{ getDataTypeLabel(template.dataType) }}</div>
                          </div>
                        </div>
                        <div class="flex gap-1">
                          <VTag :value="getDataTypeLabel(template.dataType)" severity="info" size="small" />
                          <VTag v-if="template.unit" :value="getUnitLabel(template.unit)" severity="success" size="small" />
                        </div>
                      </div>
                    </div>
                    <div v-else-if="loadingGroupTemplates" class="text-center py-4">
                      <LoaderCircleIcon class="w-4 h-4 animate-spin" />
                      Загрузка...
                    </div>
                    <div v-else class="text-center py-4 text-surface-500">
                      Нет шаблонов в группе
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </VCard>
      </div>

      <!-- Фильтры и поиск -->
      <VCard class="mb-6">
        <template #content>
          <div class="p-4">
            <div class="grid grid-cols-1 gap-4 md:grid-cols-4">
              <!-- Поиск -->
              <div>
                <label class="text-surface-700 dark:text-surface-300 mb-2 block text-sm font-medium"> Поиск </label>
                <VInputText v-model="searchQuery" placeholder="Поиск по названию, имени или описанию..." class="w-full" @input="debouncedSearch" />
              </div>

              <!-- Группа -->
              <div>
                <label class="text-surface-700 dark:text-surface-300 mb-2 block text-sm font-medium"> Группа </label>
                <VAutoComplete
                  v-model="selectedGroup"
                  :suggestions="groupSuggestions"
                  @complete="filterGroups"
                  option-label="name"
                  option-value="id"
                  placeholder="Все группы"
                  class="w-full"
                  dropdown
                  show-clear
                  @change="loadTemplates"
                />
              </div>

              <!-- Тип данных -->
              <div>
                <label class="text-surface-700 dark:text-surface-300 mb-2 block text-sm font-medium"> Тип данных </label>
                <VAutoComplete
                  v-model="selectedDataType"
                  :suggestions="dataTypeSuggestions"
                  @complete="filterDataTypes"
                  option-label="label"
                  option-value="value"
                  placeholder="Все типы"
                  class="w-full"
                  dropdown
                  show-clear
                  @change="loadTemplates"
                />
              </div>

              <!-- Режим отображения -->
            </div>
          </div>
        </template>
      </VCard>

      <!-- Статистика -->
      <div class="mb-6 grid grid-cols-1 gap-4 md:grid-cols-4">
        <VCard>
          <template #content>
            <div class="p-4 text-center">
              <div class="text-primary mb-2 text-2xl font-bold">{{ totalCount }}</div>
              <div class="text-surface-600 dark:text-surface-400 text-sm">Всего шаблонов</div>
            </div>
          </template>
        </VCard>

        <VCard>
          <template #content>
            <div class="p-4 text-center">
              <div class="mb-2 text-2xl font-bold text-green-600">{{ groups.length }}</div>
              <div class="text-surface-600 dark:text-surface-400 text-sm">Групп</div>
            </div>
          </template>
        </VCard>

        <VCard>
          <template #content>
            <div class="p-4 text-center">
              <div class="mb-2 text-2xl font-bold text-blue-600">{{ usedTemplatesCount }}</div>
              <div class="text-surface-600 dark:text-surface-400 text-sm">Используется</div>
            </div>
          </template>
        </VCard>

        <VCard>
          <template #content>
            <div class="p-4 text-center">
              <div class="mb-2 text-2xl font-bold text-orange-600">{{ unusedTemplatesCount }}</div>
              <div class="text-surface-600 dark:text-surface-400 text-sm">Не используется</div>
            </div>
          </template>
        </VCard>
      </div>

      <!-- Табличный режим -->
      <VCard v-if="viewMode === 'table'">
        <template #content>
          <VDataTable
            :value="templates"
            :loading="tableLoading && templates.length === 0"
            paginator
            :rows="pageSize"
            :total-records="totalCount"
            :rows-per-page-options="[10, 25, 50]"
            lazy
            @page="onPageChange"
            table-style="min-width: 50rem"
            class="p-datatable-sm"
            striped-rows
          >
            <!-- ID -->
            <Column field="id" header="ID" sortable style="width: 80px">
              <template #body="{ data }">
                <span class="text-surface-700 dark:text-surface-300 font-mono text-sm">#{{ data.id }}</span>
              </template>
            </Column>

            <!-- Название -->
            <Column field="title" header="Название" sortable>
              <template #body="{ data }">
                <div>
                  <div class="text-surface-900 dark:text-surface-0 font-medium">
                    {{ data.title }}
                  </div>
                  <div class="text-surface-600 dark:text-surface-400 font-mono text-sm">
                    {{ data.name }}
                  </div>
                </div>
              </template>
            </Column>

            <!-- Группа -->
            <Column field="group.name" header="Группа" sortable>
              <template #body="{ data }">
                <VTag v-if="data.group" :value="data.group.name" severity="secondary" />
                <span v-else class="text-surface-400 dark:text-surface-600">—</span>
              </template>
            </Column>

            <!-- Тип данных -->
            <Column field="dataType" header="Тип" sortable>
              <template #body="{ data }">
                <VTag :value="getDataTypeLabel(data.dataType)" severity="info" />
              </template>
            </Column>

            <!-- Единица измерения -->
            <Column field="unit" header="Единица">
              <template #body="{ data }">
                <VTag v-if="data.unit" :value="getUnitLabel(data.unit)" severity="success" />
                <span v-else class="text-surface-400 dark:text-surface-600">—</span>
              </template>
            </Column>

            <!-- Использование -->
            <Column header="Использование" style="width: 120px">
              <template #body="{ data }">
                <div class="text-sm">
                  <div v-if="data._count">
                    <div class="text-surface-700 dark:text-surface-300">{{ getTotalUsage(data._count) }} исп.</div>
                    <div class="text-surface-500 dark:text-surface-400 text-xs">
                      {{ getUsageDetails(data._count) }}
                    </div>
                  </div>
                  <span v-else class="text-surface-400 dark:text-surface-600">—</span>
                </div>
              </template>
            </Column>

            <!-- Действия -->
            <Column header="Действия" style="width: 120px">
              <template #body="{ data }">
                <div class="flex gap-2">
                  <VButton v-if="data.dataType === 'STRING'" @click="openSynonyms(data)" severity="secondary" outlined size="small">
                    Синонимы
                    <TagsIcon class="h-5 w-5" />
                  </VButton>
                  <VButton @click="editTemplate(data)" severity="secondary" outlined size="small">
                    <PencilIcon class="h-5 w-5" />
                  </VButton>
                  <DangerButton @click="deleteTemplate(data)" severity="danger" outlined size="small" :disabled="getTotalUsage(data._count) > 0">
                    <TrashIcon class="h-5 w-5" />
                  </DangerButton>
                </div>
              </template>
            </Column>
          </VDataTable>
        </template>
      </VCard>

      <!-- Карточный режим -->
      <div v-else-if="viewMode === 'cards'" class="grid gap-4">
        <VCard
          v-for="template in templates"
          :key="template.id"
          class="border-surface-200 dark:border-surface-700 hover:border-primary border transition-colors"
        >
          <template #content>
            <div class="p-6">
              <div class="mb-4 flex items-start justify-between">
                <div class="flex-1">
                  <div class="mb-2 flex items-center gap-3">
                    <TagsIcon class="h-5 w-5" />
                    <h3 class="text-surface-900 dark:text-surface-0 text-lg font-semibold">
                      {{ template.title }}
                    </h3>
                    <VTag v-if="template.isRequired" value="Обязательный" severity="danger" size="small" />
                  </div>
                  <div class="text-surface-600 dark:text-surface-400 mb-2 font-mono text-sm">
                    {{ template.name }}
                  </div>
                  <p v-if="template.description" class="text-surface-600 dark:text-surface-400 mb-3">
                    {{ template.description }}
                  </p>
                </div>

                <div class="ml-4 flex gap-2">
                  <VButton @click="editTemplate(template)" severity="secondary" outlined size="small">
                    <PencilIcon class="h-5 w-5" />
                  </VButton>
                  <VButton @click="deleteTemplate(template)" severity="danger" outlined size="small" :disabled="getTotalUsage(template._count) > 0">
                    <TrashIcon class="h-5 w-5" />
                  </VButton>
                </div>
              </div>

              <div class="mb-4 flex items-center gap-4">
                <VTag :value="getDataTypeLabel(template.dataType)" severity="info" />
                <VTag v-if="template.unit" :value="getUnitLabel(template.unit)" severity="success" />
                <VTag v-if="template.group" :value="template.group.name" severity="secondary" />
              </div>

              <!-- Статистика использования -->
              <div v-if="template._count" class="border-surface-200 dark:border-surface-700 border-t pt-4">
                <div class="text-surface-600 dark:text-surface-400 mb-2 text-sm">Использование:</div>
                <div class="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <div class="text-surface-900 dark:text-surface-0 text-lg font-semibold">
                      {{ template._count.partAttributes || 0 }}
                    </div>
                    <div class="text-surface-500 text-xs">Запчасти</div>
                  </div>
                  <div>
                    <div class="text-surface-900 dark:text-surface-0 text-lg font-semibold">
                      {{ template._count.catalogItemAttributes || 0 }}
                    </div>
                    <div class="text-surface-500 text-xs">Каталог</div>
                  </div>
                  <div>
                    <div class="text-surface-900 dark:text-surface-0 text-lg font-semibold">
                      {{ template._count.equipmentAttributes || 0 }}
                    </div>
                    <div class="text-surface-500 text-xs">Техника</div>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </VCard>

        <!-- Пагинация для карточного режима -->
        <VCard v-if="totalCount > pageSize">
          <template #content>
            <div class="p-4">
              <Paginator :rows="pageSize" :total-records="totalCount" :rows-per-page-options="[10, 25, 50]" @page="onPageChange" />
            </div>
          </template>
        </VCard>
      </div>

      <!-- Диалог создания/редактирования шаблона -->
      <VDialog
        v-model:visible="showCreateDialog"
        modal
        :header="editingTemplate ? 'Редактировать шаблон' : 'Создать шаблон'"
        :style="{ width: '50rem' }"
        :breakpoints="{ '1199px': '75vw', '575px': '90vw' }"
      >
        <TemplateForm v-model="templateForm" :groups="groups" :hierarchy-groups="hierarchyGroups" :loading="saving" @save="saveTemplate" @cancel="showCreateDialog = false" @group-created="onGroupCreated" />
      </VDialog>

      <VDialog
        v-model:visible="showSynonymsDialog"
        modal
        header="Управление синонимами"
        :style="{ width: '80rem' }"
        :breakpoints="{ '1199px': '90vw', '575px': '98vw' }"
      >
        <AttributeSynonymManager v-if="selectedTemplateForSynonyms" :template="selectedTemplateForSynonyms" />
      </VDialog>

      <!-- Диалог создания/редактирования группы -->
      <VDialog
        v-model:visible="showGroupDialog"
        modal
        :header="editingGroup ? 'Редактировать группу' : 'Создать группу'"
        style="width: 500px"
      >
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
              Название группы *
            </label>
            <VInputText
              v-model="groupForm.name"
              placeholder="Введите название группы..."
              class="w-full"
              :class="{ 'p-invalid': groupErrors.name }"
            />
            <small v-if="groupErrors.name" class="p-error">{{ groupErrors.name }}</small>
          </div>

          <div>
            <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
              Описание
            </label>
            <VTextarea
              v-model="groupForm.description"
              placeholder="Подробное описание группы атрибутов..."
              rows="3"
              class="w-full"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
              Родительская группа
            </label>
            <VSelect
              v-model="groupForm.parentId"
              :options="parentSelectOptions"
              option-label="name"
              option-value="id"
              class="w-full"
              placeholder="Не выбрано"
            />
          </div>
        </div>

        <template #footer>
          <div class="flex justify-end gap-2">
            <VButton @click="closeGroupDialog" severity="secondary" outlined label="Отмена" />
            <VButton @click="saveGroup" :loading="savingGroup" :label="editingGroup ? 'Сохранить' : 'Создать'" />
          </div>
        </template>
      </VDialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useTrpc } from '@/composables/useTrpc'
import { useUrlParams } from '@/composables/useUrlParams'

// Импорт компонентов
import VCard from '@/volt/Card.vue'
import VButton from '@/volt/Button.vue'
import VInputText from '@/volt/InputText.vue'
import VTextarea from '@/volt/Textarea.vue'
import VSelect from '@/volt/Select.vue'
// VDropdown removed - replaced with VAutoComplete
import VDataTable from '@/volt/DataTable.vue'
import VTag from '@/volt/Tag.vue'
import VDialog from '@/volt/Dialog.vue'
import VTree from '@/volt/Tree.vue'
import Column from 'primevue/column'
import Paginator from 'primevue/paginator'
import TemplateForm from '../TemplateForm.vue'
import VAutoComplete from '@/volt/AutoComplete.vue'
import AttributeSynonymManager from './AttributeSynonymManager.vue'
import Icon from '@/components/ui/Icon.vue'
import { TagsIcon, PencilIcon, TrashIcon, RefreshCwIcon, TagIcon, PencilLineIcon, LoaderCircleIcon, FolderIcon, ChevronDownIcon, ChevronRightIcon } from 'lucide-vue-next'
import DangerButton from '@/volt/DangerButton.vue'

// Composables
const { attributeTemplates, loading } = useTrpc()

// Состояние компонента
const templates = ref<any[]>([])
const groups = ref<any[]>([])
const hierarchyGroups = ref<any[]>([])
const totalCount = ref(0)
const pageSize = ref(25)
const currentPage = ref(0)

// Локальный индикатор загрузки только для таблицы,
// чтобы не задействовать глобальный `loading` tRPC и избежать "моргания".
const tableLoading = ref(false)

// Счётчик для защиты от гонки запросов
let lastRequestId = 0

// Фильтры
const searchQuery = ref('')
const selectedGroup = ref<number | null>(null)
const selectedDataType = ref<string | null>(null)
const viewMode = ref('table')

// Синхронизация фильтров с URL (постепенная миграция на единый механизм)
const urlSync = useUrlParams(
  {
    search: '',
    groupId: undefined as number | undefined,
    dataType: undefined as string | undefined
  },
  {
    prefix: 'attr_',
    numberParams: ['groupId'],
    debounceMs: 300
  }
)

// Поддерживаем URL в актуальном состоянии при изменении локальных фильтров
watch(searchQuery, (val) => {
  urlSync.updateFilter('search', val || undefined)
})
watch(selectedGroup, (val) => {
  const gid = val && typeof val === 'object' ? (val as any).id : val
  urlSync.updateFilter('groupId', gid ?? undefined)
})
watch(selectedDataType, (val) => {
  const dt = val && typeof val === 'object' ? (val as any).value : val
  urlSync.updateFilter('dataType', dt ?? undefined)
})

// Реагируем на изменения URL (назад/вперёд) и применяем к локальному стейту
watch(urlSync.filters, (f) => {
  const nextSearch = (f as any).search || ''
  const nextGroupId = (f as any).groupId ?? null
  const nextDataType = (f as any).dataType ?? null

  if (searchQuery.value !== nextSearch) searchQuery.value = nextSearch
  if (selectedGroup.value !== nextGroupId) selectedGroup.value = nextGroupId
  if (selectedDataType.value !== nextDataType) selectedDataType.value = nextDataType

  currentPage.value = 0
  loadTemplates()
})

// Диалоги
const showCreateDialog = ref(false)
const editingTemplate = ref<any>(null)
const templateForm = ref<any>({})
const saving = ref(false)

// Синонимы
const showSynonymsDialog = ref(false)
const selectedTemplateForSynonyms = ref<any | null>(null)

// Управление группами
const showGroupDialog = ref(false)
const editingGroup = ref<any>(null)
const groupForm = ref({ name: '', description: '', parentId: null as number | null })
const groupErrors = ref<Record<string, string>>({})
const savingGroup = ref(false)

// Состояние дерева групп
const selectedTreeGroup = ref<any | null>(null)
const expandedKeys = ref<Record<string, boolean>>({})
const showGroupTemplates = ref(false)
const groupTemplates = ref<any[]>([])
const loadingGroupTemplates = ref(false)

// Опции для фильтров
const dataTypeOptions = [
  { label: 'Строка', value: 'STRING' },
  { label: 'Число', value: 'NUMBER' },
  { label: 'Логическое', value: 'BOOLEAN' },
  { label: 'Дата', value: 'DATE' },
  { label: 'JSON', value: 'JSON' }
]

const viewModeOptions = [
  { label: 'Таблица', value: 'table' },
  { label: 'Карточки', value: 'cards' }
]

// Данные для автокомплита
const groupSuggestions = ref<any[]>([])
const dataTypeSuggestions = ref<any[]>([])
const viewModeSuggestions = ref<any[]>([])

// Фильтрация для автокомплита
const filterGroups = (event: any) => {
  const query = event.query?.toLowerCase() || ''

  // Создаем специальную опцию "Без группы"
  const noGroupOption = { id: null, name: 'Без группы', isSpecial: true }

  if (!query.trim()) {
    groupSuggestions.value = [noGroupOption, ...groups.value]
  } else {
    const filteredGroups = groups.value.filter((group) => group.name.toLowerCase().includes(query))
    // Добавляем опцию "Без группы" если запрос подходит
    const suggestions = query.includes('без') || query.includes('группы') || query.includes('группа')
      ? [noGroupOption, ...filteredGroups]
      : filteredGroups
    groupSuggestions.value = suggestions
  }
}

const filterDataTypes = (event: any) => {
  const query = event.query?.toLowerCase() || ''
  if (!query.trim()) {
    dataTypeSuggestions.value = [...dataTypeOptions]
  } else {
    dataTypeSuggestions.value = dataTypeOptions.filter((option) => option.label.toLowerCase().includes(query))
  }
}

const filterViewModes = (event: any) => {
  const query = event.query?.toLowerCase() || ''
  if (!query.trim()) {
    viewModeSuggestions.value = [...viewModeOptions]
  } else {
    viewModeSuggestions.value = viewModeOptions.filter((option) => option.label.toLowerCase().includes(query))
  }
}

// Инициализация автокомплитов встроена в loadGroups и onMounted

// Вычисляемые свойства
const usedTemplatesCount = computed(() => {
  return templates.value.filter((template) => getTotalUsage(template._count) > 0).length
})

const unusedTemplatesCount = computed(() => {
  return templates.value.filter((template) => getTotalUsage(template._count) === 0).length
})

// Дерево групп
const idMap = computed(() => {
  const m = new Map<number, any>()
  for (const g of groups.value) m.set(g.id, g)
  return m
})

const treeNodes = computed(() => {
  const map = new Map<number, any>()
  const roots: any[] = []

  // Добавляем специальный узел для шаблонов без группы
  const noGroupNode = {
    key: 'no-group',
    label: 'Без группы',
    data: { id: null, name: 'Без группы', isSpecial: true },
    children: []
  }
  roots.push(noGroupNode)

  // Обрабатываем обычные группы
  for (const g of groups.value) map.set(g.id, { key: String(g.id), label: g.name, data: g, children: [] as any[] })
  for (const g of groups.value) {
    const node = map.get(g.id)
    if (g.parentId && map.has(g.parentId)) map.get(g.parentId).children.push(node)
    else roots.push(node)
  }
  return roots
})

const parentSelectOptions = computed(() => [
  { id: null, name: 'Нет родителя' },
  ...groups.value.filter(g => !editingGroup.value || g.id !== editingGroup.value.id)
])

// Вспомогательные функции
const getDataTypeLabel = (dataType: string) => {
  const option = dataTypeOptions.find((opt) => opt.value === dataType)
  return option?.label || dataType
}

const getUnitLabel = (unit: string) => {
  const labels: Record<string, string> = {
    'MM': 'мм',
    'INCH': 'дюймы',
    'FT': 'футы',
    'G': 'г',
    'KG': 'кг',
    'T': 'т',
    'LB': 'фунты',
    'ML': 'мл',
    'L': 'л',
    'GAL': 'галлоны',
    'PCS': 'шт',
    'SET': 'комплект',
    'PAIR': 'пара',
    'BAR': 'бар',
    'PSI': 'PSI',
    'KW': 'кВт',
    'HP': 'л.с.',
    'NM': 'Н⋅м',
    'RPM': 'об/мин',
    'C': '°C',
    'F': '°F',
    'PERCENT': '%'
  }
  return labels[unit] || unit
}

const getTotalUsage = (count: any) => {
  if (!count) return 0
  return (count.partAttributes || 0) + (count.catalogItemAttributes || 0) + (count.equipmentAttributes || 0)
}

const getUsageDetails = (count: any) => {
  if (!count) return ''
  const parts = count.partAttributes || 0
  const items = count.catalogItemAttributes || 0
  const equipment = count.equipmentAttributes || 0

  const details = []
  if (parts > 0) details.push(`${parts} зап.`)
  if (items > 0) details.push(`${items} кат.`)
  if (equipment > 0) details.push(`${equipment} тех.`)

  return details.join(', ')
}

// Основные функции
const loadTemplates = async () => {
  const current = ++lastRequestId
  tableLoading.value = true
  try {
    // Извлекаем ID из выбранной группы
    let groupId: number | null | undefined = undefined
    if (selectedGroup.value) {
      if (typeof selectedGroup.value === 'object') {
        groupId = (selectedGroup.value as any).id
      } else {
        groupId = selectedGroup.value
      }
    }

    // Извлекаем значение из выбранного типа данных
    const dataType = selectedDataType.value && typeof selectedDataType.value === 'object' ? (selectedDataType.value as any).value : undefined

    const result = await attributeTemplates.findMany({
      groupId: groupId,
      search: searchQuery.value || undefined,
      dataType: dataType,
      limit: pageSize.value,
      offset: currentPage.value * pageSize.value
    })

    if (current === lastRequestId && result && typeof result === 'object') {
      templates.value = (result as any).templates || []
      totalCount.value = (result as any).total || 0
    }
  } catch (error) {
    console.error('Ошибка загрузки шаблонов:', error)
    console.error('Не удалось загрузить шаблоны')
  } finally {
    if (current === lastRequestId) {
      tableLoading.value = false
    }
  }
}

const loadGroups = async () => {
  try {
    const [flatResult, hierarchyResult] = await Promise.all([
      attributeTemplates.findAllGroups(),
      attributeTemplates.findGroupsHierarchy()
    ])

    if (flatResult && Array.isArray(flatResult)) {
      groups.value = flatResult
      // Обновляем данные для автокомплита групп после загрузки с опцией "Без группы"
      const noGroupOption = { id: null, name: 'Без группы', isSpecial: true }
      groupSuggestions.value = [noGroupOption, ...flatResult]
    }

    if (hierarchyResult && Array.isArray(hierarchyResult)) {
      hierarchyGroups.value = hierarchyResult
    }
  } catch (error) {
    console.error('Ошибка загрузки групп:', error)
  }
}

// Debounced search
let searchTimeout: NodeJS.Timeout
const debouncedSearch = () => {
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(() => {
    currentPage.value = 0
    // Обновляем URL и перезагружаем данные
    urlSync.updateFilter('search', searchQuery.value || '')
    loadTemplates()
  }, 500)
}

const refreshData = async () => {
  await loadGroups() // Обновляем группы (это также обновит groupSuggestions)
  await loadTemplates()
}

// Обработчики событий
const onPageChange = (event: any) => {
  currentPage.value = event.page
  pageSize.value = event.rows
  loadTemplates()
}

const editTemplate = (template: any) => {
  editingTemplate.value = template
  templateForm.value = { ...template }
  showCreateDialog.value = true
}

const openSynonyms = (template: any) => {
  selectedTemplateForSynonyms.value = template
  showSynonymsDialog.value = true
}

const createNewTemplate = () => {
  editingTemplate.value = null
  templateForm.value = {
    dataType: 'STRING',
    isRequired: false,
    allowedValues: []
  }
  showCreateDialog.value = true
}

const deleteTemplate = async (template: any) => {
  const confirmed = window.confirm(`Вы уверены, что хотите удалить шаблон "${template.title}"?`)

  if (confirmed) {
    try {
      await attributeTemplates.delete({ id: template.id })
      console.log('Шаблон успешно удален')
      loadTemplates()
    } catch (error: any) {
      console.error('Ошибка удаления шаблона:', error)
      alert(error.message || 'Не удалось удалить шаблон')
    }
  }
}

const saveTemplate = async (formData: any) => {
  try {
    saving.value = true

    if (editingTemplate.value) {
      await attributeTemplates.update({ id: editingTemplate.value.id, ...formData })
      console.log('Шаблон успешно обновлен')
    } else {
      await attributeTemplates.create(formData)
      console.log('Шаблон успешно создан')
    }

    showCreateDialog.value = false
    editingTemplate.value = null
    templateForm.value = {}
    await loadGroups() // Обновляем группы на случай создания новой (это также обновит groupSuggestions)
    await loadTemplates()
  } catch (error: any) {
    console.error('Ошибка сохранения шаблона:', error)
    alert(error.message || 'Не удалось сохранить шаблон')
  } finally {
    saving.value = false
  }
}

// Инициализация
onMounted(async () => {
  // Сначала инициализируем статичные данные автокомплита
  dataTypeSuggestions.value = [...dataTypeOptions]
  viewModeSuggestions.value = [...viewModeOptions]

  // Применяем состояние из URL к локальным фильтрам (до загрузки)
  const f = urlSync.filters.value as any
  searchQuery.value = f.search || ''
  selectedGroup.value = f.groupId ?? null
  selectedDataType.value = f.dataType ?? null

  // Загружаем динамические данные
  await loadGroups() // Загружаем группы (это также обновит groupSuggestions)
  await loadTemplates()

  // При начальной загрузке выделим первую корневую группу
  const first = groups.value.find(g => !g.parentId)
  if (first) {
    selectedTreeGroup.value = first
    updateExpandedForSelection()
  }
})

// Методы для работы с группами
const selectTreeGroup = (group: any) => {
  selectedTreeGroup.value = group
  updateExpandedForSelection()
  showGroupTemplates.value = false
  groupTemplates.value = []
}

const isGroupExpanded = (group: any) => {
  return expandedKeys.value[String(group.id)] === true
}

const toggleGroupExpansion = (group: any) => {
  const key = String(group.id)
  const newKeys = { ...expandedKeys.value }
  if (newKeys[key]) {
    delete newKeys[key]
  } else {
    newKeys[key] = true
  }
  expandedKeys.value = newKeys
}

const updateExpandedForSelection = () => {
  const keys: Record<string, boolean> = {}
  let current = selectedTreeGroup.value ? idMap.value.get(selectedTreeGroup.value.id) : null
  while (current && current.parentId) {
    keys[String(current.parentId)] = true
    current = idMap.value.get(current.parentId)
  }
  expandedKeys.value = keys
}

const refreshGroupData = async () => {
  await loadGroups()
}

const editGroup = (group: any) => {
  editingGroup.value = group
  groupForm.value = {
    name: group.name,
    description: group.description || '',
    parentId: group.parentId ?? null,
  }
  showGroupDialog.value = true
}

const deleteGroup = async (group: any) => {
  if (!confirm(`Удалить группу "${group.name}"?`)) return

  try {
    await attributeTemplates.deleteGroup({ id: group.id })
    await loadGroups()
    if (selectedTreeGroup.value?.id === group.id) {
      selectedTreeGroup.value = null
    }
  } catch (error) {
    console.error('Ошибка удаления группы:', error)
  }
}

const saveGroup = async () => {
  if (!groupForm.value.name.trim()) {
    groupErrors.value.name = 'Название обязательно'
    return
  }

  savingGroup.value = true
  groupErrors.value = {}

  try {
    if (editingGroup.value) {
      await attributeTemplates.updateGroup({
        id: editingGroup.value.id,
        name: groupForm.value.name.trim(),
        description: groupForm.value.description || undefined,
        parentId: groupForm.value.parentId,
      })
    } else {
      await attributeTemplates.createGroup({
        name: groupForm.value.name.trim(),
        description: groupForm.value.description || undefined,
        parentId: groupForm.value.parentId,
      })
    }

    closeGroupDialog()
    await loadGroups()
  } catch (error: any) {
    if (error.message?.includes('уже существует')) {
      groupErrors.value.name = 'Группа с таким именем уже существует'
    } else {
      console.error('Ошибка сохранения группы:', error)
    }
  } finally {
    savingGroup.value = false
  }
}

const onGroupCreated = async () => {
  // Перезагружаем группы после создания новой группы в TemplateForm
  await loadGroups()
}

const closeGroupDialog = () => {
  showGroupDialog.value = false
  editingGroup.value = null
  groupForm.value = { name: '', description: '', parentId: null }
  groupErrors.value = {}
}

const toggleGroupTemplates = async () => {
  if (!selectedTreeGroup.value) return

  showGroupTemplates.value = !showGroupTemplates.value

  if (showGroupTemplates.value && groupTemplates.value.length === 0) {
    loadingGroupTemplates.value = true
    try {
      const result = await attributeTemplates.findMany({ groupId: selectedTreeGroup.value.id, limit: 100 })
      groupTemplates.value = (result as any)?.templates || []
    } catch (error) {
      console.error('Ошибка загрузки шаблонов группы:', error)
    } finally {
      loadingGroupTemplates.value = false
    }
  }
}
</script>
