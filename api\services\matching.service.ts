import { TRPCError } from '@trpc/server'
import { getSystemDB } from '../db'
import { getNumericValue } from '../lib/attribute-value-sync'
import type { MatchKind } from '../schemas/matching'
import {
  FindMatchingPartsInput,
  FindMatchingCatalogItemsInput,
  ProposeLinkInput,
  ListProposalsInput,
  RejectProposalInput,
  ApproveProposalInput,
  CreatePartFromItemsInput,
  GenerateProposalsInput,
} from '../schemas/matching'

export class MatchingService {
  static async findMatchingParts(input: typeof FindMatchingPartsInput._type) {
    const db = getSystemDB()

    const item = await db.catalogItem.findUnique({
      where: { id: input.catalogItemId },
      include: { attributes: { include: { template: true } } },
    })
    if (!item) throw new TRPCError({ code: 'NOT_FOUND', message: 'CatalogItem не найден' })

    const requiredSet = input.requiredTemplateIds && input.requiredTemplateIds.length > 0 ? new Set(input.requiredTemplateIds) : null

    const itemAttrByTemplateId = new Map<number, { value: string; numericValue?: number | null; template: any }>()
    const templateIds = new Set<number>()
    const itemStringValuesByTemplate = new Map<number, Set<string>>()
    for (const a of item.attributes) {
      itemAttrByTemplateId.set(a.templateId, { value: a.value, numericValue: (a as any).numericValue ?? null, template: a.template })
      templateIds.add(a.templateId)
      if (a.template.dataType === 'STRING') {
        const set = itemStringValuesByTemplate.get(a.templateId) || new Set<string>()
        set.add(a.value)
        itemStringValuesByTemplate.set(a.templateId, set)
      }
    }
    if (templateIds.size === 0) return { candidates: [] as any[] }

    // Базовый фильтр: искать только те Part, у которых есть пересечение по атрибутам текущего товара
    const where: any = { attributes: { some: { templateId: { in: Array.from(templateIds) } } } }
    // Если заданы обязательные шаблоны — требуем наличие КАЖДОГО из них у Part
    if (requiredSet && requiredSet.size > 0) {
      where.AND = Array.from(requiredSet).map((id) => ({ attributes: { some: { templateId: id } } }))
    }
    const parts = await db.part.findMany({
      where,
      include: { attributes: { include: { template: true } } },
    })
    if (parts.length === 0) return { candidates: [] as any[] }

    const allStringValues: Array<{ templateId: number; value: string }> = []
    for (const [templateId, set] of itemStringValuesByTemplate) {
      for (const v of set) allStringValues.push({ templateId, value: v })
    }
    for (const p of parts) {
      for (const pa of p.attributes) {
        if (pa.template.dataType === 'STRING') {
          allStringValues.push({ templateId: pa.templateId, value: pa.value })
        }
      }
    }

    const uniqKey = (t: number, v: string) => `${t}__${v.toLowerCase()}`
    const seen = new Set<string>()
    const dedupedValues = allStringValues.filter(({ templateId, value }) => {
      const key = uniqKey(templateId, value)
      if (seen.has(key)) return false
      seen.add(key)
      return true
    })

    const synonyms = await db.attributeSynonym.findMany({
      where: { OR: dedupedValues.map(({ templateId, value }) => ({ value: { equals: value, mode: 'insensitive' }, group: { templateId } })) },
      include: { group: true },
    })

    const valueToGroup = new Map<string, { templateId: number; level: 'EXACT'|'NEAR'|'LEGACY'; notes: string | null; groupId: number; canonical: string | null }>()
    for (const s of synonyms) {
      const level = (s as any).compatibilityLevel ?? s.group.compatibilityLevel
      const notes = [s.group.notes, (s as any).notes].filter(Boolean).join('; ') || null
      const key = uniqKey(s.group.templateId, s.value)
      valueToGroup.set(key, { templateId: s.group.templateId, level: level as any, notes, groupId: s.groupId, canonical: (s.group as any).canonicalValue ?? null })
    }

    const cmpNumber = (
      itemAttr: { value: string; numericValue?: number | null },
      partAttr: { value: string; numericValue?: number | null },
      tolerance?: number | null,
    ) => {
      const itemVal = getNumericValue(itemAttr)
      const partVal = getNumericValue(partAttr)
      if (itemVal === null || partVal === null) {
        return { ok: false as const, delta: undefined }
      }
      const tol = tolerance ?? 0
      const delta = Math.abs(itemVal - partVal)
      if (delta === 0) return { ok: true as const, kind: 'NUMBER_EXACT' as MatchKind, delta }
      if (delta <= tol) return { ok: true as const, kind: 'NUMBER_WITHIN_TOLERANCE' as MatchKind, delta }
      return { ok: false as const, delta }
    }

    const cmpString = (templateId: number, itemVal: string, partVal: string) => {
      if (itemVal.toLowerCase() === partVal.toLowerCase()) return { ok: true as const, kind: 'STRING_EXACT' as MatchKind }
      const ig = valueToGroup.get(uniqKey(templateId, itemVal))
      const pg = valueToGroup.get(uniqKey(templateId, partVal))
      if (!ig || !pg || ig.groupId !== pg.groupId) return { ok: false as const }
      if (ig.level === 'EXACT') return { ok: true as const, kind: 'STRING_SYNONYM_EXACT' as MatchKind, level: 'EXACT' as const, notes: ig.notes, canonical: ig.canonical }
      if (ig.level === 'NEAR') return { ok: true as const, kind: 'STRING_SYNONYM_NEAR' as MatchKind, level: 'NEAR' as const, notes: ig.notes, canonical: ig.canonical }
      return { ok: true as const, kind: 'STRING_SYNONYM_LEGACY' as MatchKind, level: 'LEGACY' as const, notes: ig.notes, canonical: ig.canonical }
    }

    const candidates: Array<any> = []
    for (const part of parts) {
      const details: Array<any> = []
      let allOk = true
      let hasNear = false
      for (const pa of part.attributes) {
        const itemAttr = itemAttrByTemplateId.get(pa.templateId)
        if (!itemAttr) {
          allOk = false
          details.push({ templateId: pa.templateId, templateName: pa.template.name, templateTitle: pa.template.title, reason: 'MISSING_IN_ITEM', partValue: pa.value })
          continue
        }
        const template = pa.template
        if (template.dataType === 'NUMBER') {
          const res = cmpNumber(itemAttr, pa, template.tolerance as any)
          if (!('ok' in res) || !res.ok) {
            allOk = false
            details.push({ templateId: pa.templateId, templateName: template.name, templateTitle: template.title, kind: 'NUMBER', ok: false, itemValue: itemAttr.value, partValue: pa.value, toleranceUsed: template.tolerance ?? 0, delta: res.delta })
            continue
          }
          if (res.kind === 'NUMBER_WITHIN_TOLERANCE') hasNear = true
          details.push({ templateId: pa.templateId, templateName: template.name, templateTitle: template.title, kind: res.kind, ok: true, itemValue: itemAttr.value, partValue: pa.value, toleranceUsed: template.tolerance ?? 0, delta: res.delta })
        } else if (template.dataType === 'STRING') {
          const res = cmpString(pa.templateId, itemAttr.value, pa.value)
          if (!res.ok) {
            allOk = false
            details.push({ templateId: pa.templateId, templateName: template.name, templateTitle: template.title, kind: 'STRING', ok: false, itemValue: itemAttr.value, partValue: pa.value })
            continue
          }
          if (res.kind === 'STRING_SYNONYM_NEAR' || res.kind === 'STRING_SYNONYM_LEGACY') hasNear = true
          details.push({ templateId: pa.templateId, templateName: template.name, templateTitle: template.title, kind: res.kind, ok: true, itemValue: itemAttr.value, partValue: pa.value, notes: (res as any).notes, synonymLevel: (res as any).level, canonical: (res as any).canonical })
        } else {
          const ok = String(itemAttr.value) === String(pa.value)
          if (!ok) {
            allOk = false
            details.push({ templateId: pa.templateId, templateName: template.name, templateTitle: template.title, kind: 'EXACT_STRING', ok: false, itemValue: itemAttr.value, partValue: pa.value })
            continue
          }
          details.push({ templateId: pa.templateId, templateName: template.name, templateTitle: template.title, kind: 'EXACT_STRING', ok: true, itemValue: itemAttr.value, partValue: pa.value })
        }
      }
      if (!allOk) continue
      const accuracy = hasNear ? 'MATCH_WITH_NOTES' : 'EXACT_MATCH'
      candidates.push({ part: { id: part.id, name: part.name }, accuracySuggestion: accuracy, details })
    }

    candidates.sort((a, b) => {
      if (a.accuracySuggestion === b.accuracySuggestion) return 0
      return a.accuracySuggestion === 'EXACT_MATCH' ? -1 : 1
    })

    return { candidates }
  }

  static async findMatchingCatalogItems(input: typeof FindMatchingCatalogItemsInput._type) {
    const db = getSystemDB()
    const part = await db.part.findUnique({ where: { id: input.partId }, include: { attributes: { include: { template: true } } } })
    if (!part) throw new TRPCError({ code: 'NOT_FOUND', message: 'Part не найден' })

    const partAttrByTemplateId = new Map<number, { value: string; numericValue?: number | null; template: any }>()
    const templateIds = new Set<number>()
    const partStringValuesByTemplate = new Map<number, Set<string>>()
    for (const a of part.attributes) {
      partAttrByTemplateId.set(a.templateId, { value: a.value, template: a.template })
      templateIds.add(a.templateId)
      if (a.template.dataType === 'STRING') {
        const set = partStringValuesByTemplate.get(a.templateId) || new Set<string>()
        set.add(a.value)
        partStringValuesByTemplate.set(a.templateId, set)
      }
    }
    if (templateIds.size === 0) return { candidates: [] as any[] }

    const items = await db.catalogItem.findMany({
      where: { attributes: { some: { templateId: { in: Array.from(templateIds) } } } },
      include: { brand: true, attributes: { include: { template: true } } },
    })
    if (items.length === 0) return { candidates: [] as any[] }

    const allStringValues: Array<{ templateId: number; value: string }> = []
    for (const [templateId, set] of partStringValuesByTemplate) for (const v of set) allStringValues.push({ templateId, value: v })
    for (const it of items) for (const ia of it.attributes) if (ia.template.dataType === 'STRING') allStringValues.push({ templateId: ia.templateId, value: ia.value })
    const uniqKey = (t: number, v: string) => `${t}__${v.toLowerCase()}`
    const seen = new Set<string>()
    const dedupedValues = allStringValues.filter(({ templateId, value }) => { const key = uniqKey(templateId, value); if (seen.has(key)) return false; seen.add(key); return true })
    const synonyms = await db.attributeSynonym.findMany({ where: { OR: dedupedValues.map(({ templateId, value }) => ({ value: { equals: value, mode: 'insensitive' }, group: { templateId } })) }, include: { group: true } })
    const valueToGroup = new Map<string, { templateId: number; level: 'EXACT'|'NEAR'|'LEGACY'; notes: string | null; groupId: number; canonical: string | null }>()
    for (const s of synonyms) {
      const level = (s as any).compatibilityLevel ?? s.group.compatibilityLevel
      const notes = [s.group.notes, (s as any).notes].filter(Boolean).join('; ') || null
      valueToGroup.set(uniqKey(s.group.templateId, s.value), { templateId: s.group.templateId, level: level as any, notes, groupId: s.groupId, canonical: (s.group as any).canonicalValue ?? null })
    }

    const cmpNumber = (
      itemAttr: { value: string; numericValue?: number | null },
      partAttr: { value: string; numericValue?: number | null },
      tolerance?: number | null,
    ) => {
      const itemVal = getNumericValue(itemAttr)
      const partVal = getNumericValue(partAttr)
      if (itemVal === null || partVal === null) return { ok: false as const, delta: undefined }
      const tol = tolerance ?? 0
      const delta = Math.abs(itemVal - partVal)
      if (delta === 0) return { ok: true as const, kind: 'NUMBER_EXACT' as MatchKind, delta }
      if (delta <= tol) return { ok: true as const, kind: 'NUMBER_WITHIN_TOLERANCE' as MatchKind, delta }
      return { ok: false as const, delta }
    }
    const cmpString = (templateId: number, v1: string, v2: string) => {
      if (v1.toLowerCase() === v2.toLowerCase()) return { ok: true as const, kind: 'STRING_EXACT' as MatchKind }
      const g1 = valueToGroup.get(uniqKey(templateId, v1))
      const g2 = valueToGroup.get(uniqKey(templateId, v2))
      if (!g1 || !g2 || g1.groupId !== g2.groupId) return { ok: false as const }
      if (g1.level === 'EXACT') return { ok: true as const, kind: 'STRING_SYNONYM_EXACT' as MatchKind, level: 'EXACT' as const, notes: g1.notes, canonical: g1.canonical }
      if (g1.level === 'NEAR') return { ok: true as const, kind: 'STRING_SYNONYM_NEAR' as MatchKind, level: 'NEAR' as const, notes: g1.notes, canonical: g1.canonical }
      return { ok: true as const, kind: 'STRING_SYNONYM_LEGACY' as MatchKind, level: 'LEGACY' as const, notes: g1.notes, canonical: g1.canonical }
    }

    const candidates: Array<any> = []
    for (const it of items) {
      const itemAttrByTemplateId = new Map<number, { value: string; numericValue?: number | null; template: any }>()
      for (const ia of it.attributes) itemAttrByTemplateId.set(ia.templateId, { value: ia.value, numericValue: (ia as any).numericValue ?? null, template: ia.template })
      const details: Array<any> = []
      let allOk = true
      let hasNear = false
      for (const pa of part.attributes) {
        const itemAttr = itemAttrByTemplateId.get(pa.templateId)
        if (!itemAttr) { allOk = false; details.push({ templateId: pa.templateId, templateName: pa.template.name, templateTitle: pa.template.title, reason: 'MISSING_IN_ITEM', partValue: pa.value }); continue }
        const template = pa.template
        if (template.dataType === 'NUMBER') {
          const res = cmpNumber(itemAttr, pa, template.tolerance as any)
          if (!('ok' in res) || !res.ok) { allOk = false; details.push({ templateId: pa.templateId, templateName: template.name, templateTitle: template.title, kind: 'NUMBER', ok: false, itemValue: itemAttr.value, partValue: pa.value, toleranceUsed: template.tolerance ?? 0, delta: (res as any).delta }); continue }
          if (res.kind === 'NUMBER_WITHIN_TOLERANCE') hasNear = true
          details.push({ templateId: pa.templateId, templateName: template.name, templateTitle: template.title, kind: res.kind, ok: true, itemValue: itemAttr.value, partValue: pa.value, toleranceUsed: template.tolerance ?? 0, delta: (res as any).delta })
        } else if (template.dataType === 'STRING') {
          const res = cmpString(pa.templateId, itemAttr.value, pa.value)
          if (!res.ok) { allOk = false; details.push({ templateId: pa.templateId, templateName: template.name, templateTitle: template.title, kind: 'STRING', ok: false, itemValue: itemAttr.value, partValue: pa.value }); continue }
          if (res.kind === 'STRING_SYNONYM_NEAR' || res.kind === 'STRING_SYNONYM_LEGACY') hasNear = true
          details.push({ templateId: pa.templateId, templateName: template.name, templateTitle: template.title, kind: res.kind, ok: true, itemValue: itemAttr.value, partValue: pa.value, notes: (res as any).notes, synonymLevel: (res as any).level, canonical: (res as any).canonical })
        } else {
          const ok = String(itemAttr.value) === String(pa.value)
          if (!ok) { allOk = false; details.push({ templateId: pa.templateId, templateName: template.name, templateTitle: template.title, kind: 'EXACT_STRING', ok: false, itemValue: itemAttr.value, partValue: pa.value }); continue }
          details.push({ templateId: pa.templateId, templateName: template.name, templateTitle: template.title, kind: 'EXACT_STRING', ok: true, itemValue: itemAttr.value, partValue: pa.value })
        }
      }
      if (!allOk) continue
      const accuracy = hasNear ? 'MATCH_WITH_NOTES' : 'EXACT_MATCH'
      candidates.push({ catalogItem: { id: it.id, sku: it.sku, brand: it.brand ? { id: it.brand.id, name: it.brand.name } : null }, accuracySuggestion: accuracy, details })
    }
    candidates.sort((a, b) => { if (a.accuracySuggestion === b.accuracySuggestion) return 0; return a.accuracySuggestion === 'EXACT_MATCH' ? -1 : 1 })
    return { candidates }
  }

  static async proposeLink(input: typeof ProposeLinkInput._type) {
    const db = getSystemDB()
    const existing = await db.matchingProposal.findFirst({ where: { catalogItemId: input.catalogItemId, partId: input.partId, status: 'PENDING' } })
    if (existing) return { id: existing.id }
    const created = await db.matchingProposal.create({ data: { catalogItemId: input.catalogItemId, partId: input.partId, accuracySuggestion: input.accuracySuggestion as any, notesSuggestion: input.notesSuggestion, details: input.details as any } })
    return { id: created.id }
  }

  static async listProposals(input: typeof ListProposalsInput._type) {
    const db = getSystemDB()
    const where: any = { status: input.status }
    if (input.catalogItemId) where.catalogItemId = input.catalogItemId
    if (input.partId) where.partId = input.partId
    const items = await db.matchingProposal.findMany({ where, take: input.take, skip: input.skip, orderBy: { createdAt: 'desc' }, include: { catalogItem: { include: { brand: true } }, part: true } })
    const total = await db.matchingProposal.count({ where })
    return { items, total }
  }

  static async rejectProposal(input: typeof RejectProposalInput._type) {
    const db = getSystemDB()
    const updated = await db.matchingProposal.update({ where: { id: input.id }, data: { status: 'REJECTED' } })
    return { success: true, proposal: updated }
  }

  static async approveProposal(input: typeof ApproveProposalInput._type) {
    const db = getSystemDB()
    const p = await db.matchingProposal.findUnique({ where: { id: input.id } })
    if (!p || p.status !== 'PENDING') throw new TRPCError({ code: 'BAD_REQUEST', message: 'Предложение не найдено или уже обработано' })
    const accuracy = input.override?.accuracy ?? (p.accuracySuggestion as any)
    const notes = input.override?.notes ?? p.notesSuggestion ?? undefined
    await db.partApplicability.upsert({ where: { partId_catalogItemId: { partId: p.partId, catalogItemId: p.catalogItemId } }, create: { partId: p.partId, catalogItemId: p.catalogItemId, accuracy, notes }, update: { accuracy, notes } })
    const updated = await db.matchingProposal.update({ where: { id: p.id }, data: { status: 'APPROVED' } })
    return { success: true, proposal: updated }
  }

  static async createPartFromItems(input: typeof CreatePartFromItemsInput._type) {
    const db = getSystemDB()
    const part = await db.part.create({ data: { name: input.name ?? null, partCategoryId: input.partCategoryId, level: 0, path: `/${Date.now()}` } })
    for (const id of input.itemIds) {
      await db.partApplicability.create({ data: { partId: part.id, catalogItemId: id, accuracy: input.accuracy as any, notes: input.notes } })
    }
    await db.matchingProposal.updateMany({ where: { catalogItemId: { in: input.itemIds }, status: 'PENDING' }, data: { status: 'INVALIDATED' } })
    return { success: true, part }
  }

  static async generateProposals(input: typeof GenerateProposalsInput._type) {
    const db = getSystemDB()
    const where: any = {}
    if (input.catalogItemIds?.length) where.id = { in: input.catalogItemIds }
    if (input.brandId) where.brandId = input.brandId
    const items = await db.catalogItem.findMany({ where, take: input.take, skip: input.skip, include: { attributes: { include: { template: true } } } })
    let createdCount = 0
    const createdIds: number[] = []
    for (const it of items) {
      const itemAttrByTemplateId = new Map<number, { value: string; template: any }>()
      const templateIds = new Set<number>()
      const itemStringValuesByTemplate = new Map<number, Set<string>>()
      for (const a of it.attributes) {
        itemAttrByTemplateId.set(a.templateId, { value: a.value, template: a.template })
        templateIds.add(a.templateId)
        if (a.template.dataType === 'STRING') {
          const set = itemStringValuesByTemplate.get(a.templateId) || new Set<string>()
          set.add(a.value)
          itemStringValuesByTemplate.set(a.templateId, set)
        }
      }
      if (templateIds.size === 0) continue

      const parts = await db.part.findMany({ where: { attributes: { some: { templateId: { in: Array.from(templateIds) } } } }, include: { attributes: { include: { template: true } } } })
      if (parts.length === 0) continue

      const allStringValues: Array<{ templateId: number; value: string }> = []
      for (const [templateId, set] of itemStringValuesByTemplate) for (const v of set) allStringValues.push({ templateId, value: v })
      for (const p of parts) for (const pa of p.attributes) if (pa.template.dataType === 'STRING') allStringValues.push({ templateId: pa.templateId, value: pa.value })
      const uniqKey = (t: number, v: string) => `${t}__${v.toLowerCase()}`
      const seen = new Set<string>()
      const dedupedValues = allStringValues.filter(({ templateId, value }) => { const key = uniqKey(templateId, value); if (seen.has(key)) return false; seen.add(key); return true })
      const synonyms = await db.attributeSynonym.findMany({ where: { OR: dedupedValues.map(({ templateId, value }) => ({ value: { equals: value, mode: 'insensitive' }, group: { templateId } })) }, include: { group: true } })
      const valueToGroup = new Map<string, { templateId: number; level: 'EXACT'|'NEAR'|'LEGACY'; notes: string | null; groupId: number; canonical: string | null }>()
      for (const s of synonyms) {
        const level = (s as any).compatibilityLevel ?? s.group.compatibilityLevel
        const notes = [s.group.notes, (s as any).notes].filter(Boolean).join('; ') || null
        valueToGroup.set(uniqKey(s.group.templateId, s.value), { templateId: s.group.templateId, level: level as any, notes, groupId: s.groupId, canonical: (s.group as any).canonicalValue ?? null })
      }

      const cmpNumber = (
        itemAttr: { value: string; numericValue?: number | null },
        partAttr: { value: string; numericValue?: number | null },
        tolerance?: number | null,
      ) => {
        const itemVal = getNumericValue(itemAttr)
        const partVal = getNumericValue(partAttr)
        if (itemVal === null || partVal === null) return { ok: false as const, delta: undefined }
        const tol = tolerance ?? 0
        const delta = Math.abs(itemVal - partVal)
        if (delta === 0) return { ok: true as const, kind: 'NUMBER_EXACT' as MatchKind, delta }
        if (delta <= tol) return { ok: true as const, kind: 'NUMBER_WITHIN_TOLERANCE' as MatchKind, delta }
        return { ok: false as const, delta }
      }
      const cmpString = (templateId: number, itemVal: string, partVal: string) => {
        if (itemVal.toLowerCase() === partVal.toLowerCase()) return { ok: true as const, kind: 'STRING_EXACT' as MatchKind }
        const ig = valueToGroup.get(uniqKey(templateId, itemVal)); const pg = valueToGroup.get(uniqKey(templateId, partVal))
        if (!ig || !pg || ig.groupId !== pg.groupId) return { ok: false as const }
        if (ig.level === 'EXACT') return { ok: true as const, kind: 'STRING_SYNONYM_EXACT' as MatchKind, level: 'EXACT' as const, notes: ig.notes, canonical: ig.canonical }
        if (ig.level === 'NEAR') return { ok: true as const, kind: 'STRING_SYNONYM_NEAR' as MatchKind, level: 'NEAR' as const, notes: ig.notes, canonical: ig.canonical }
        return { ok: true as const, kind: 'STRING_SYNONYM_LEGACY' as MatchKind, level: 'LEGACY' as const, notes: ig.notes, canonical: ig.canonical }
      }

      for (const part of parts) {
        const details: any[] = []
        let allOk = true
        let hasNear = false
        for (const pa of part.attributes) {
          const itemAttr = itemAttrByTemplateId.get(pa.templateId)
          if (!itemAttr) { allOk = false; break }
          const template = pa.template
          if (template.dataType === 'NUMBER') {
            const res = cmpNumber(itemAttr as any, pa as any, template.tolerance as any)
            if (!('ok' in res) || !res.ok) { allOk = false; break }
            if (res.kind === 'NUMBER_WITHIN_TOLERANCE') hasNear = true
            details.push({ templateId: pa.templateId, templateName: template.name, templateTitle: template.title, kind: res.kind, ok: true, itemValue: (itemAttr as any).value, partValue: pa.value, toleranceUsed: template.tolerance ?? 0, delta: (res as any).delta })
          } else if (template.dataType === 'STRING') {
            const res = cmpString(pa.templateId, (itemAttr as any).value, pa.value)
            if (!res.ok) { allOk = false; break }
            if (res.kind === 'STRING_SYNONYM_NEAR' || res.kind === 'STRING_SYNONYM_LEGACY') hasNear = true
            details.push({ templateId: pa.templateId, templateName: template.name, templateTitle: template.title, kind: res.kind, ok: true, itemValue: (itemAttr as any).value, partValue: pa.value, notes: (res as any).notes, synonymLevel: (res as any).level, canonical: (res as any).canonical })
          } else {
            const ok = String((itemAttr as any).value) === String(pa.value)
            if (!ok) { allOk = false; break }
            details.push({ templateId: pa.templateId, templateName: template.name, templateTitle: template.title, kind: 'EXACT_STRING', ok: true, itemValue: (itemAttr as any).value, partValue: pa.value })
          }
        }
        if (!allOk) continue
        const accuracy = hasNear ? 'MATCH_WITH_NOTES' : 'EXACT_MATCH'
        const exists = await db.matchingProposal.findFirst({ where: { catalogItemId: it.id, partId: part.id, status: 'PENDING' } })
        if (exists) continue
        const withNotes = details.find((d: any) => String(d.kind).includes('NEAR') || String(d.kind).includes('LEGACY'))
        const tol = details.find((d: any) => d.kind === 'NUMBER_WITHIN_TOLERANCE')
        const notesSuggestion = withNotes?.notes || (tol ? 'Совпадение по допуску' : undefined)
        const created = await db.matchingProposal.create({ data: { catalogItemId: it.id, partId: part.id, accuracySuggestion: accuracy as any, notesSuggestion, details: details as any } })
        createdCount += 1
        createdIds.push(created.id)
      }
    }
    return { success: true, createdCount, createdIds }
  }
}



