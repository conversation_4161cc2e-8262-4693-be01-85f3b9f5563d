import{E as N4}from"./ErrorBoundary.Def_Csyr.js";import{u as y4}from"./useTrpc.CAkGIEe7.js";import{u as j4}from"./useUrlParams.DMgQWPCH.js";import{u as q4,C as H4}from"./ConfirmDialog.B2pd8YuJ.js";import{u as x4}from"./useToast.DbdIHNOo.js";import o4 from"./Card.CAr8QcdG.js";import{I as k4}from"./InputText.COaPodMV.js";import{V as F4}from"./AutoComplete.XhB-0aUS.js";import l4 from"./Button.oEwD-lSq.js";import{D as b4}from"./Dialog.Dcz8Sg9i.js";import{M as G4}from"./MultiSelect.C2H5fiK5.js";import W4 from"./Toast.SYHAzbvX.js";import{D as Q4,s as K4}from"./index.xFbNStuK.js";import{T as C4}from"./Tag.DvN1X7lb.js";import{s as X4,_ as $}from"./_plugin-vue_export-helper.BX07OBiL.js";import{U as J4,c as i,o as l,m as v4,a as t,d as u4,p as i4,g as E,j as o,e as s,b as p,F as Y,r as Z,q as r4,f as U,h as g4,w as B4,i as I4}from"./index.BglzLLgy.js";import{I as E4}from"./Icon.Ci-mb2Ee.js";import{L as p4,M as Y4,a as Z4,R as w4}from"./MatchingLoadingState.0NBtvQFg.js";import{T as $4,P as A4,a as uu}from"./trash.BzwTNQJC.js";import{c as V4}from"./createLucideIcon.3yDVQAYz.js";import{t as f,n as h4,r as g,a as eu}from"./reactivity.esm-bundler.D5IypM4U.js";import{w as tu}from"./runtime-dom.esm-bundler.C-dfRCGi.js";import{V as S4}from"./Textarea.Bgj6qPNm.js";import{C as au}from"./Checkbox.B9YJ1Jrl.js";import{Q as su,A as ru}from"./QuickCreateBrand.BfvKWKIq.js";import{f as D4,r as lu}from"./utils.NP7rd5-k.js";import{S as nu}from"./Select.CKfyRLxl.js";/* empty css                       */import{M as ou,u as iu}from"./MatchingDetailsGrid.DabYCGDw.js";import{S as du}from"./search.SsUYlPPO.js";import"./trpc.BpyaUO08.js";import"./router.DKcY2uv6.js";import"./index.CKgBWlpe.js";import"./index.DQmIdHOH.js";import"./index.B_yc9D3m.js";import"./index.CRcBj2l1.js";import"./SecondaryButton.DjJyItVx.js";import"./DangerButton.PrtrqpTW.js";import"./index.CzaMXvxd.js";import"./index.CLkTvMlq.js";import"./index.CJuyVe3p.js";import"./index.CLs7nh7g.js";import"./index.BHZvt6Rq.js";import"./index.Tc5ZRw49.js";import"./index.D7_1DwEX.js";import"./index.irlSZ_18.js";import"./index.B0GjtQuk.js";import"./index.CBatl9QV.js";import"./index.BsVcoVHU.js";import"./index.CXDqTVvt.js";import"./index.BksvaNwr.js";import"./index.D6LCJW96.js";import"./AttributeValueInput.CSCilM_R.js";import"./InputNumber.BtccV88f.js";import"./Message.BOpJRjRi.js";import"./ToggleSwitch.7R1jWLlg.js";import"./info.NGiRJ8VE.js";/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cu=V4("scan-eye",[["path",{d:"M3 7V5a2 2 0 0 1 2-2h2",key:"aa7l1z"}],["path",{d:"M17 3h2a2 2 0 0 1 2 2v2",key:"4qcy5o"}],["path",{d:"M21 17v2a2 2 0 0 1-2 2h-2",key:"6vwrx8"}],["path",{d:"M7 21H5a2 2 0 0 1-2-2v-2",key:"ioqczr"}],["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["path",{d:"M18.944 12.33a1 1 0 0 0 0-.66 7.5 7.5 0 0 0-13.888 0 1 1 0 0 0 0 .66 7.5 7.5 0 0 0 13.888 0",key:"11ak4c"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mu=V4("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]);var fu=`
    .p-progressspinner {
        position: relative;
        margin: 0 auto;
        width: 100px;
        height: 100px;
        display: inline-block;
    }

    .p-progressspinner::before {
        content: '';
        display: block;
        padding-top: 100%;
    }

    .p-progressspinner-spin {
        height: 100%;
        transform-origin: center center;
        width: 100%;
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        margin: auto;
        animation: p-progressspinner-rotate 2s linear infinite;
    }

    .p-progressspinner-circle {
        stroke-dasharray: 89, 200;
        stroke-dashoffset: 0;
        stroke: dt('progressspinner.colorOne');
        animation:
            p-progressspinner-dash 1.5s ease-in-out infinite,
            p-progressspinner-color 6s ease-in-out infinite;
        stroke-linecap: round;
    }

    @keyframes p-progressspinner-rotate {
        100% {
            transform: rotate(360deg);
        }
    }
    @keyframes p-progressspinner-dash {
        0% {
            stroke-dasharray: 1, 200;
            stroke-dashoffset: 0;
        }
        50% {
            stroke-dasharray: 89, 200;
            stroke-dashoffset: -35px;
        }
        100% {
            stroke-dasharray: 89, 200;
            stroke-dashoffset: -124px;
        }
    }
    @keyframes p-progressspinner-color {
        100%,
        0% {
            stroke: dt('progressspinner.color.one');
        }
        40% {
            stroke: dt('progressspinner.color.two');
        }
        66% {
            stroke: dt('progressspinner.color.three');
        }
        80%,
        90% {
            stroke: dt('progressspinner.color.four');
        }
    }
`,vu={root:"p-progressspinner",spin:"p-progressspinner-spin",circle:"p-progressspinner-circle"},gu=J4.extend({name:"progressspinner",style:fu,classes:vu}),yu={name:"BaseProgressSpinner",extends:X4,props:{strokeWidth:{type:String,default:"2"},fill:{type:String,default:"none"},animationDuration:{type:String,default:"2s"}},style:gu,provide:function(){return{$pcProgressSpinner:this,$parentInstance:this}}},T4={name:"ProgressSpinner",extends:yu,inheritAttrs:!1,computed:{svgStyle:function(){return{"animation-duration":this.animationDuration}}}},bu=["fill","stroke-width"];function Cu(m,e,r,u,_,v){return l(),i("div",v4({class:m.cx("root"),role:"progressbar"},m.ptmi("root")),[(l(),i("svg",v4({class:m.cx("spin"),viewBox:"25 25 50 50",style:v.svgStyle},m.ptm("spin")),[t("circle",v4({class:m.cx("circle"),cx:"50",cy:"50",r:"20",fill:m.fill,"stroke-width":m.strokeWidth,strokeMiterlimit:"10"},m.ptm("circle")),null,16,bu)],16))],16)}T4.render=Cu;const Eu=u4({__name:"CatalogItemsTable",props:{items:{},loading:{type:Boolean},totalRecords:{},rows:{},first:{}},emits:["page","sort","edit","delete","view-details","match"],setup(m,{expose:e}){e();const u={formatDate:_=>{if(!_)return"";const v=new Date(_);return new Intl.DateTimeFormat("ru-RU",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"}).format(v)},VCard:o4,VDataTable:Q4,get VColumn(){return K4},VButton:l4,VTag:C4,get VProgressSpinner(){return T4},get PencilIcon(){return A4},get ScanEyeIcon(){return cu},get TrashIcon(){return $4},Icon:E4,get LinkIcon(){return p4}};return Object.defineProperty(u,"__isScriptSetup",{enumerable:!1,value:!0}),u}}),pu={class:"flex items-center gap-2"},_u={class:"font-mono font-medium"},hu={class:"flex items-center gap-2"},Du={class:"font-medium"},xu={class:"max-w-xs"},ku=["title"],Fu={key:1,class:"text-surface-400 italic"},Bu={class:"flex flex-wrap gap-1"},Iu={class:"text-center"},wu={key:1,class:"text-surface-400 text-sm"},Au={key:0,class:"text-surface-600 dark:text-surface-400 text-sm"},Vu={key:1,class:"text-surface-400 italic"},Su={class:"text-surface-600 dark:text-surface-400 font-mono text-sm"},Tu={class:"flex gap-1"},Mu={class:"py-8 text-center"},Pu={class:"py-8 text-center"};function Lu(m,e,r,u,_,v){const d=i4("tooltip");return l(),E(u.VCard,null,{content:o(()=>[s(u.VDataTable,{value:r.items,loading:r.loading,paginator:!0,rows:r.rows,"total-records":r.totalRecords,first:r.first,lazy:!0,sortable:!0,"paginator-template":"FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown","current-page-report-template":"Показано {first} - {last} из {totalRecords} записей","rows-per-page-options":[10,20,50],onPage:e[0]||(e[0]=a=>m.$emit("page",a)),onSort:e[1]||(e[1]=a=>m.$emit("sort",a))},{empty:o(()=>[t("div",Mu,[s(u.Icon,{name:"pi pi-inbox",class:"text-surface-300 dark:text-surface-600 mb-4 inline-block text-4xl"}),e[2]||(e[2]=t("p",{class:"text-surface-500 dark:text-surface-400 mb-2 text-lg"},"Каталожные позиции не найдены",-1)),e[3]||(e[3]=t("p",{class:"text-surface-400 dark:text-surface-500 text-sm"},"Попробуйте изменить параметры поиска или создайте новую позицию",-1))])]),loading:o(()=>[t("div",Pu,[s(u.VProgressSpinner,{size:"50"}),e[4]||(e[4]=t("p",{class:"text-surface-500 dark:text-surface-400 mt-4"},"Загрузка каталожных позиций...",-1))])]),default:o(()=>[s(u.VColumn,{field:"sku",header:"Артикул",sortable:!0,class:"min-w-32"},{body:o(({data:a})=>[t("div",pu,[t("span",_u,f(a.sku),1),a.isPublic?p("",!0):(l(),E(u.VTag,{key:0,value:"Приватная",severity:"warning",size:"small"}))])]),_:1}),s(u.VColumn,{field:"brand.name",header:"Бренд",sortable:!0,class:"min-w-32"},{body:o(({data:a})=>[t("div",hu,[t("span",Du,f(a.brand?.name||"Не указан"),1),a.brand?.isOem?(l(),E(u.VTag,{key:0,value:"OEM",severity:"info",size:"small"})):p("",!0)])]),_:1}),s(u.VColumn,{field:"description",header:"Описание",class:"min-w-48"},{body:o(({data:a})=>[t("div",xu,[a.description?(l(),i("p",{key:0,class:"text-surface-600 dark:text-surface-400 truncate text-sm",title:a.description},f(a.description),9,ku)):(l(),i("span",Fu,"Нет описания"))])]),_:1}),s(u.VColumn,{header:"Атрибуты",class:"min-w-32"},{body:o(({data:a})=>[t("div",Bu,[(l(!0),i(Y,null,Z(a.attributes?.slice(0,3),y=>(l(),E(u.VTag,{key:y.id,value:`${y.template?.title}: ${y.value}`,severity:"secondary",size:"small",class:"text-xs"},null,8,["value"]))),128)),a.attributes?.length>3?(l(),E(u.VTag,{key:0,value:`+${a.attributes.length-3}`,severity:"secondary",size:"small",class:"text-xs"},null,8,["value"])):p("",!0)])]),_:1}),s(u.VColumn,{header:"Применимость",class:"min-w-24"},{body:o(({data:a})=>[t("div",Iu,[a.applicabilities?.length>0?(l(),E(u.VTag,{key:0,value:`${a.applicabilities.length} групп`,severity:"success",size:"small"},null,8,["value"])):(l(),i("span",wu,"Не назначена"))])]),_:1}),s(u.VColumn,{field:"source",header:"Источник",class:"min-w-32"},{body:o(({data:a})=>[a.source?(l(),i("span",Au,f(a.source),1)):(l(),i("span",Vu,"Не указан"))]),_:1}),s(u.VColumn,{field:"id",header:"ID",sortable:!0,class:"min-w-20"},{body:o(({data:a})=>[t("span",Su," #"+f(a.id),1)]),_:1}),s(u.VColumn,{header:"Действия",class:"min-w-32"},{body:o(({data:a})=>[t("div",Tu,[r4((l(),E(u.VButton,{onClick:y=>m.$emit("match",a),severity:"secondary",size:"small",text:""},{default:o(()=>[s(u.LinkIcon,{class:"w-5 h-5"})]),_:2},1032,["onClick"])),[[d,"Найти группу взаимозаменяемости"]]),s(u.VButton,{onClick:y=>m.$emit("view-details",a),severity:"secondary",size:"small",text:""},{default:o(()=>[s(u.ScanEyeIcon,{class:"w-5 h-5"})]),_:2},1032,["onClick"]),s(u.VButton,{onClick:y=>m.$emit("edit",a),severity:"secondary",size:"small",text:""},{default:o(()=>[s(u.PencilIcon,{class:"w-5 h-5"})]),_:2},1032,["onClick"]),s(u.VButton,{onClick:y=>m.$emit("delete",a),severity:"danger",size:"small",text:""},{default:o(()=>[s(u.TrashIcon,{class:"w-5 h-5"})]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["value","loading","rows","total-records","first"])]),_:1})}const Ru=$(Eu,[["render",Lu]]),Uu=u4({__name:"CatalogItemForm",props:{item:{}},emits:["save","cancel"],setup(m,{expose:e,emit:r}){e();const u=m,_=r,v=g({sku:"",selectedBrand:null,description:"",source:"",isPublic:!0,attributes:[]}),d=g({sku:"",brandId:""}),a=g(!1),y=g(!1),T=g([]),k=()=>(d.value.sku="",v.value.sku.trim()?v.value.sku.length<2?(d.value.sku="Артикул должен содержать минимум 2 символа",!1):v.value.sku.length>64?(d.value.sku="Артикул не может быть длиннее 64 символов",!1):!0:(d.value.sku="Артикул обязателен",!1)),L=()=>(d.value.brandId="",v.value.selectedBrand?!0:(d.value.brandId="Бренд обязателен",!1)),W=g4(()=>v.value.sku.trim()&&v.value.selectedBrand&&!d.value.sku&&!d.value.brandId),h=g(u.item||null),M=g4(()=>h.value?.image?.url||null),D=g(null),B=g(!1),I=g(null),j=g(!1),{media:C,brands:x,loading:q,client:F}=y4(),w=b=>{const A=b.target;D.value=A.files&&A.files[0]||null},z=async()=>{if(!(!h.value?.id||!D.value)){B.value=!0;try{const b=await D4(D.value);await C.uploadCatalogItemImage({catalogItemId:h.value.id,fileName:D.value.name,fileData:b,mimeType:D.value.type||"image/png"}),await Q(),D.value=null}finally{B.value=!1}}},K=async()=>{h.value?.id&&(await C.deleteCatalogItemImage({catalogItemId:h.value.id}),await Q())},X=b=>{const A=b.target;I.value=A.files||null},e4=async()=>{if(!(!h.value?.id||!I.value||I.value.length===0)){j.value=!0;try{for(const b of Array.from(I.value)){const A=await D4(b);await C.uploadCatalogItemMedia({catalogItemId:h.value.id,fileName:b.name,fileData:A,mimeType:b.type||"application/octet-stream"})}await Q(),I.value=null}finally{j.value=!1}}},t4=async b=>{h.value?.id&&(await C.removeCatalogItemMedia({catalogItemId:h.value.id,mediaId:b}),await Q())};async function Q(){if(!h.value?.id)return;const b=await F.crud.catalogItem.findUnique.query({where:{id:h.value.id},include:{image:!0,mediaAssets:!0,brand:!0}});h.value=b}const O=async b=>{try{const A=b.query.toLowerCase(),G=await x.findMany({where:{name:{contains:A,mode:"insensitive"}},take:10});G&&(T.value=G)}catch(A){console.error("Ошибка поиска брендов:",A)}},H=b=>{v.value.selectedBrand=b,T.value=[b,...T.value]},V=b=>{v.value.attributes.splice(b,1)},d4=b=>({MM:"мм",INCH:"дюймы",FT:"футы",G:"г",KG:"кг",T:"т",LB:"фунты",ML:"мл",L:"л",GAL:"галлоны",PCS:"шт",SET:"комплект",PAIR:"пара",BAR:"бар",PSI:"PSI",KW:"кВт",HP:"л.с.",NM:"Н⋅м",RPM:"об/мин",C:"°C",F:"°F",PERCENT:"%"})[b]||b,J=()=>{const b=k(),A=L();if(!b||!A)return;const G={sku:v.value.sku.toUpperCase().trim(),brandId:v.value.selectedBrand.id,description:v.value.description.trim()||void 0,source:v.value.source.trim()||void 0,isPublic:v.value.isPublic},s4=v.value.attributes?v.value.attributes.filter(S=>{const c4=S.value&&String(S.value).trim()!=="",m4=S.templateId||S.template?.id;return c4&&m4}):[];s4.length>0?u.item?G.attributes={deleteMany:{},create:s4.map(S=>({templateId:S.templateId||S.template?.id,value:S.value}))}:G.attributes={create:s4.map(S=>({templateId:S.templateId||S.template?.id,value:S.value}))}:u.item&&(G.attributes={deleteMany:{}}),_("save",G)},a4=()=>{u.item&&(v.value={sku:u.item.sku||"",selectedBrand:u.item.brand||null,description:u.item.description||"",source:u.item.source||"",isPublic:u.item.isPublic??!0,attributes:u.item.attributes||[]})};B4(()=>u.item,a4,{immediate:!0}),I4(()=>{a4()});const n4={props:u,emit:_,formData:v,errors:d,showAttributeManager:a,showCreateBrand:y,brandSuggestions:T,validateSku:k,validateBrand:L,isFormValid:W,currentItem:h,itemImageUrl:M,selectedImage:D,uploadingImage:B,selectedMedia:I,uploadingMedia:j,media:C,brands:x,loading:q,client:F,onSelectItemImage:w,uploadItemImage:z,removeItemImage:K,onSelectItemMedia:X,uploadItemMedia:e4,removeItemMedia:t4,refreshCurrentItem:Q,searchBrands:O,onBrandCreated:H,removeAttribute:V,getUnitLabel:d4,onSubmit:J,loadItemData:a4,VInputText:k4,VTextarea:S4,VAutoComplete:F4,VCheckbox:au,VButton:l4,VDialog:b4,AttributeManager:ru,QuickCreateBrand:su,Icon:E4,get resolveMediaUrl(){return lu}};return Object.defineProperty(n4,"__isScriptSetup",{enumerable:!1,value:!0}),n4}}),zu={class:"catalog-item-form"},Ou={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Nu={key:0,class:"p-error"},ju={class:"flex gap-2"},qu={key:0,class:"p-error"},Hu={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Gu={class:"flex items-center gap-4"},Wu={class:"w-32 h-32 border rounded bg-surface-50 dark:bg-surface-900 flex items-center justify-center overflow-hidden"},Qu=["src"],Ku={key:1,class:"text-surface-500 text-sm"},Xu={class:"flex flex-col gap-2"},Ju={class:"flex gap-2"},Yu={key:0,class:"text-surface-500 text-xs"},Zu={class:"flex items-start gap-4"},$u={class:"flex-1 grid grid-cols-2 gap-3"},u0=["src","alt"],e0=["href"],t0={class:"text-sm truncate"},a0={class:"p-2 border-t flex justify-end"},s0={class:"flex flex-col gap-2 w-64"},r0={key:0,class:"text-surface-500 text-xs"},l0={class:"flex items-center gap-3"},n0={class:"flex items-center justify-between mb-4"},o0={key:0,class:"space-y-3"},i0={class:"flex-1"},d0={class:"font-medium text-surface-900 dark:text-surface-0"},c0={key:0,class:"text-red-500 ml-1"},m0={class:"text-sm text-surface-600 dark:text-surface-400"},f0={key:0,class:"ml-2 px-2 py-1 bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 rounded text-xs"},v0={key:1,class:"text-center py-6 text-surface-500"},g0={class:"flex justify-end gap-3 pt-4 border-t border-surface-200 dark:border-surface-700"};function y0(m,e,r,u,_,v){const d=i4("tooltip");return l(),i("div",zu,[t("form",{onSubmit:tu(u.onSubmit,["prevent"]),class:"space-y-6"},[t("div",Ou,[t("div",null,[e[12]||(e[12]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Артикул (SKU) * ",-1)),s(u.VInputText,{modelValue:u.formData.sku,"onUpdate:modelValue":e[0]||(e[0]=a=>u.formData.sku=a),placeholder:"Например: 12345-ABC",class:h4(["w-full",{"p-invalid":u.errors.sku}]),onBlur:u.validateSku},null,8,["modelValue","class"]),u.errors.sku?(l(),i("small",Nu,f(u.errors.sku),1)):p("",!0)]),t("div",null,[e[13]||(e[13]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Бренд * ",-1)),t("div",ju,[s(u.VAutoComplete,{modelValue:u.formData.selectedBrand,"onUpdate:modelValue":e[1]||(e[1]=a=>u.formData.selectedBrand=a),suggestions:u.brandSuggestions,onComplete:u.searchBrands,"option-label":"name",placeholder:"Поиск бренда...",class:h4(["flex-1",{"p-invalid":u.errors.brandId}]),dropdown:""},null,8,["modelValue","suggestions","class"]),r4((l(),E(u.VButton,{onClick:e[2]||(e[2]=a=>u.showCreateBrand=!0),severity:"secondary",outlined:"",size:"small"},{default:o(()=>[s(u.Icon,{name:"pi pi-plus",class:"w-5 h-5"})]),_:1})),[[d,"Создать новый бренд"]])]),u.errors.brandId?(l(),i("small",qu,f(u.errors.brandId),1)):p("",!0)])]),t("div",null,[e[14]||(e[14]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Описание ",-1)),s(u.VTextarea,{modelValue:u.formData.description,"onUpdate:modelValue":e[3]||(e[3]=a=>u.formData.description=a),placeholder:"Описание каталожной позиции...",rows:"3",class:"w-full"},null,8,["modelValue"])]),t("div",null,[e[15]||(e[15]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Источник информации ",-1)),s(u.VInputText,{modelValue:u.formData.source,"onUpdate:modelValue":e[4]||(e[4]=a=>u.formData.source=a),placeholder:"Например: Официальный каталог, Данные клиента",class:"w-full"},null,8,["modelValue"])]),t("div",Hu,[t("div",null,[e[18]||(e[18]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"},"Основное изображение",-1)),t("div",Gu,[t("div",Wu,[u.itemImageUrl?(l(),i("img",{key:0,src:u.resolveMediaUrl(u.itemImageUrl),alt:"Изображение позиции",class:"object-cover w-full h-full"},null,8,Qu)):(l(),i("span",Ku,"Нет изображения"))]),t("div",Xu,[t("input",{type:"file",accept:"image/*",onChange:u.onSelectItemImage},null,32),t("div",Ju,[s(u.VButton,{size:"small",disabled:!u.selectedImage||u.uploadingImage,onClick:u.uploadItemImage},{default:o(()=>e[16]||(e[16]=[U("Загрузить")])),_:1,__:[16]},8,["disabled"]),s(u.VButton,{size:"small",severity:"danger",outlined:"",disabled:!u.itemImageUrl||u.uploadingImage,onClick:u.removeItemImage},{default:o(()=>e[17]||(e[17]=[U("Удалить")])),_:1,__:[17]},8,["disabled"])]),u.uploadingImage?(l(),i("div",Yu,"Загрузка...")):p("",!0)])])]),t("div",null,[e[21]||(e[21]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"},"Дополнительные медиа (изображения, PDF)",-1)),t("div",Zu,[t("div",$u,[(l(!0),i(Y,null,Z(u.currentItem?.mediaAssets||[],a=>(l(),i("div",{key:a.id,class:"border rounded overflow-hidden"},[a.mimeType?.startsWith("image/")?(l(),i("img",{key:0,src:u.resolveMediaUrl(a.url),alt:a.fileName,class:"w-full h-32 object-cover"},null,8,u0)):(l(),i("a",{key:1,href:u.resolveMediaUrl(a.url),target:"_blank",class:"flex items-center gap-2 p-2 hover:bg-surface-50 dark:hover:bg-surface-900"},[s(u.Icon,{name:"pi pi-file-pdf",class:"text-red-600 w-4 h-4"}),t("span",t0,f(a.fileName),1)],8,e0)),t("div",a0,[s(u.VButton,{size:"small",severity:"danger",text:"",onClick:y=>u.removeItemMedia(a.id)},{default:o(()=>e[19]||(e[19]=[U("Удалить")])),_:2,__:[19]},1032,["onClick"])])]))),128))]),t("div",s0,[t("input",{type:"file",multiple:"",accept:"image/*,application/pdf",onChange:u.onSelectItemMedia},null,32),s(u.VButton,{size:"small",disabled:!u.selectedMedia||u.uploadingMedia,onClick:u.uploadItemMedia},{default:o(()=>e[20]||(e[20]=[U("Загрузить выбранные")])),_:1,__:[20]},8,["disabled"]),u.uploadingMedia?(l(),i("div",r0,"Загрузка...")):p("",!0)])])])]),t("div",l0,[s(u.VCheckbox,{modelValue:u.formData.isPublic,"onUpdate:modelValue":e[5]||(e[5]=a=>u.formData.isPublic=a),"input-id":"isPublic",binary:""},null,8,["modelValue"]),e[22]||(e[22]=t("label",{for:"isPublic",class:"text-sm font-medium text-surface-700 dark:text-surface-300"}," Публичная позиция (видна всем пользователям) ",-1))]),t("div",null,[t("div",n0,[e[23]||(e[23]=t("h4",{class:"text-lg font-medium text-surface-900 dark:text-surface-0"}," Атрибуты ",-1)),s(u.VButton,{onClick:e[6]||(e[6]=a=>u.showAttributeManager=!0),severity:"secondary",outlined:"",size:"small",label:"Добавить атрибут"},{icon:o(()=>[s(u.Icon,{name:"pi pi-plus",class:"w-5 h-5"})]),_:1})]),u.formData.attributes.length>0?(l(),i("div",o0,[(l(!0),i(Y,null,Z(u.formData.attributes,(a,y)=>(l(),i("div",{key:y,class:"flex items-center justify-between p-3 bg-surface-50 dark:bg-surface-900 rounded border"},[t("div",i0,[t("div",d0,[U(f(a.template?.title||a.templateTitle)+" ",1),a.template?.isRequired?(l(),i("span",c0,"*")):p("",!0)]),t("div",m0,[U(f(a.value)+" "+f(a.template?.unit?u.getUnitLabel(a.template.unit):"")+" ",1),a.template?.group?.name?(l(),i("span",f0,f(a.template.group.name),1)):p("",!0)])]),s(u.VButton,{onClick:T=>u.removeAttribute(y),severity:"danger",size:"small",text:""},{default:o(()=>[s(u.Icon,{name:"pi pi-trash",class:"w-5 h-5"})]),_:2},1032,["onClick"])]))),128))])):(l(),i("div",v0," Атрибуты не добавлены "))]),t("div",g0,[s(u.VButton,{onClick:e[7]||(e[7]=a=>m.$emit("cancel")),severity:"secondary",outlined:"",label:"Отмена"}),s(u.VButton,{type:"submit",loading:u.loading,disabled:!u.isFormValid,label:r.item?"Сохранить изменения":"Создать позицию"},null,8,["loading","disabled","label"])])],32),s(u.VDialog,{visible:u.showAttributeManager,"onUpdate:visible":e[10]||(e[10]=a=>u.showAttributeManager=a),modal:"",header:"Управление атрибутами",class:"w-full max-w-2xl"},{default:o(()=>[s(u.AttributeManager,{modelValue:u.formData.attributes,"onUpdate:modelValue":e[8]||(e[8]=a=>u.formData.attributes=a),onClose:e[9]||(e[9]=a=>u.showAttributeManager=!1)},null,8,["modelValue"])]),_:1},8,["visible"]),s(u.QuickCreateBrand,{visible:u.showCreateBrand,"onUpdate:visible":e[11]||(e[11]=a=>u.showCreateBrand=a),onCreated:u.onBrandCreated},null,8,["visible"])])}const b0=$(Uu,[["render",y0]]),C0=u4({__name:"CatalogItemCard",props:{item:{}},emits:["edit","close","match","unlink"],setup(m,{expose:e}){e();const d={getUnitLabel:a=>({MM:"мм",INCH:"дюймы",FT:"футы",G:"г",KG:"кг",T:"т",LB:"фунты",ML:"мл",L:"л",GAL:"галлоны",PCS:"шт",SET:"комплект",PAIR:"пара",BAR:"бар",PSI:"PSI",KW:"кВт",HP:"л.с.",NM:"Н⋅м",RPM:"об/мин",C:"°C",F:"°F",PERCENT:"%"})[a]||a,getDataTypeLabel:a=>a?{STRING:"Строка",NUMBER:"Число",BOOLEAN:"Логическое",DATE:"Дата",JSON:"JSON"}[a]||a:"",getAccuracyLabel:a=>({EXACT_MATCH:"Точное совпадение",MATCH_WITH_NOTES:"С примечаниями",REQUIRES_MODIFICATION:"Требует доработки",PARTIAL_MATCH:"Частичное совпадение"})[a]||a,getAccuracySeverity:a=>({EXACT_MATCH:"success",MATCH_WITH_NOTES:"info",REQUIRES_MODIFICATION:"warning",PARTIAL_MATCH:"secondary"})[a]||"secondary",VCard:o4,VButton:l4,VTag:C4,Icon:E4,get PencilIcon(){return A4},get LinkIcon(){return p4}};return Object.defineProperty(d,"__isScriptSetup",{enumerable:!1,value:!0}),d}}),E0={class:"catalog-item-card"},p0={class:"flex items-start justify-between mb-6"},_0={class:"flex items-center gap-4"},h0={class:"text-2xl font-bold text-surface-900 dark:text-surface-0 font-mono"},D0={class:"flex flex-wrap items-center gap-2 mt-1"},x0={class:"flex gap-2"},k0={class:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6"},F0={class:"space-y-4"},B0={class:"p-4"},I0={key:0,class:"text-surface-700 dark:text-surface-300"},w0={key:1,class:"text-surface-400 italic"},A0={class:"p-4 space-y-3"},V0={class:"flex justify-between"},S0={class:"font-medium"},T0={class:"flex justify-between"},M0={class:"flex justify-between"},P0={class:"font-medium font-mono"},L0={class:"space-y-4"},R0={class:"p-4 space-y-3"},U0={class:"flex justify-between"},z0={class:"font-medium"},O0={class:"flex justify-between"},N0={key:0,class:"flex justify-between"},j0={class:"font-medium"},q0={class:"p-4 space-y-3"},H0={class:"flex justify-between"},G0={class:"flex justify-between"},W0={class:"p-4 border-b border-surface-200 dark:border-surface-700"},Q0={class:"font-semibold text-surface-900 dark:text-surface-0"},K0={class:"p-4"},X0={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"},J0={class:"font-medium text-surface-900 dark:text-surface-0 mb-1"},Y0={key:0,class:"text-red-500 ml-1"},Z0={class:"text-lg font-semibold text-primary-600 dark:text-primary-400 mb-1"},$0={class:"flex items-center gap-2"},ue={class:"text-xs text-surface-500"},ee={class:"p-4 border-b border-surface-200 dark:border-surface-700"},te={class:"font-semibold text-surface-900 dark:text-surface-0"},ae={class:"p-4"},se={class:"space-y-3"},re={class:"flex-1 min-w-0"},le={class:"font-medium text-surface-900 dark:text-surface-0 truncate"},ne=["href"],oe={key:1},ie={class:"flex flex-wrap items-center gap-2 mt-1"},de={key:0,class:"text-sm text-surface-600 dark:text-surface-400"},ce={class:"ml-3 shrink-0"};function me(m,e,r,u,_,v){const d=i4("tooltip");return l(),i("div",E0,[t("div",p0,[t("div",_0,[t("div",null,[t("h2",h0,f(r.item.sku),1),t("div",D0,[s(u.VTag,{value:r.item.brand?.name||"Бренд не указан",severity:r.item.brand?.isOem?"info":"secondary"},null,8,["value","severity"]),r.item.brand?.isOem?(l(),E(u.VTag,{key:0,value:"OEM",severity:"info",size:"small"})):p("",!0),r.item.isPublic?p("",!0):(l(),E(u.VTag,{key:1,value:"Приватная",severity:"warning",size:"small"})),s(u.VTag,{value:"#"+r.item.id,severity:"secondary",size:"small"},null,8,["value"]),r.item.source?(l(),E(u.VTag,{key:2,value:"Источник: "+r.item.source,severity:"secondary",size:"small"},null,8,["value"])):p("",!0),s(u.VTag,{value:"Атрибутов: "+(r.item.attributes?.length||0),severity:"secondary",size:"small"},null,8,["value"]),s(u.VTag,{value:"Применимостей: "+(r.item.applicabilities?.length||0),severity:(r.item.applicabilities?.length||0)>0?"success":"secondary",size:"small"},null,8,["value","severity"])])])]),t("div",x0,[s(u.VButton,{onClick:e[0]||(e[0]=a=>m.$emit("edit")),severity:"secondary",outlined:"",size:"small"},{default:o(()=>[s(u.PencilIcon,{class:"w-5 h-5"})]),_:1}),s(u.VButton,{onClick:e[1]||(e[1]=a=>m.$emit("match")),severity:"secondary",outlined:"",size:"small",label:"Подобрать"},{default:o(()=>[s(u.LinkIcon,{class:"w-5 h-5"})]),_:1})])]),t("div",k0,[t("div",F0,[s(u.VCard,null,{header:o(()=>e[2]||(e[2]=[t("div",{class:"p-4 border-b border-surface-200 dark:border-surface-700"},[t("h3",{class:"font-semibold text-surface-900 dark:text-surface-0"}," Описание ")],-1)])),content:o(()=>[t("div",B0,[r.item.description?(l(),i("p",I0,f(r.item.description),1)):(l(),i("span",w0," Описание не указано "))])]),_:1}),s(u.VCard,null,{header:o(()=>e[3]||(e[3]=[t("div",{class:"p-4 border-b border-surface-200 dark:border-surface-700"},[t("h3",{class:"font-semibold text-surface-900 dark:text-surface-0"}," Метаданные ")],-1)])),content:o(()=>[t("div",A0,[t("div",V0,[e[4]||(e[4]=t("span",{class:"text-surface-600 dark:text-surface-400"},"Источник:",-1)),t("span",S0,f(r.item.source||"Не указан"),1)]),t("div",T0,[e[5]||(e[5]=t("span",{class:"text-surface-600 dark:text-surface-400"},"Видимость:",-1)),s(u.VTag,{value:r.item.isPublic?"Публичная":"Приватная",severity:r.item.isPublic?"success":"warning",size:"small"},null,8,["value","severity"])]),t("div",M0,[e[6]||(e[6]=t("span",{class:"text-surface-600 dark:text-surface-400"},"ID:",-1)),t("span",P0," #"+f(r.item.id),1)])])]),_:1})]),t("div",L0,[r.item.brand?(l(),E(u.VCard,{key:0},{header:o(()=>e[7]||(e[7]=[t("div",{class:"p-4 border-b border-surface-200 dark:border-surface-700"},[t("h3",{class:"font-semibold text-surface-900 dark:text-surface-0"}," Информация о бренде ")],-1)])),content:o(()=>[t("div",R0,[t("div",U0,[e[8]||(e[8]=t("span",{class:"text-surface-600 dark:text-surface-400"},"Название:",-1)),t("span",z0,f(r.item.brand.name),1)]),t("div",O0,[e[9]||(e[9]=t("span",{class:"text-surface-600 dark:text-surface-400"},"Тип:",-1)),s(u.VTag,{value:r.item.brand.isOem?"OEM производитель":"Aftermarket",severity:r.item.brand.isOem?"info":"secondary",size:"small"},null,8,["value","severity"])]),r.item.brand.country?(l(),i("div",N0,[e[10]||(e[10]=t("span",{class:"text-surface-600 dark:text-surface-400"},"Страна:",-1)),t("span",j0,f(r.item.brand.country),1)])):p("",!0)])]),_:1})):p("",!0),s(u.VCard,null,{header:o(()=>e[11]||(e[11]=[t("div",{class:"p-4 border-b border-surface-200 dark:border-surface-700"},[t("h3",{class:"font-semibold text-surface-900 dark:text-surface-0"}," Статистика ")],-1)])),content:o(()=>[t("div",q0,[t("div",H0,[e[12]||(e[12]=t("span",{class:"text-surface-600 dark:text-surface-400"},"Атрибутов:",-1)),s(u.VTag,{value:r.item.attributes?.length||0,severity:"secondary",size:"small"},null,8,["value"])]),t("div",G0,[e[13]||(e[13]=t("span",{class:"text-surface-600 dark:text-surface-400"},"Групп применимости:",-1)),s(u.VTag,{value:r.item.applicabilities?.length||0,severity:(r.item.applicabilities?.length||0)>0?"success":"secondary",size:"small"},null,8,["value","severity"])])])]),_:1})])]),r.item.attributes?.length>0?(l(),E(u.VCard,{key:0,class:"mb-6"},{header:o(()=>[t("div",W0,[t("h3",Q0," Атрибуты ("+f(r.item.attributes.length)+") ",1)])]),content:o(()=>[t("div",K0,[t("div",X0,[(l(!0),i(Y,null,Z(r.item.attributes,a=>(l(),i("div",{key:a.id,class:"p-3 bg-surface-50 dark:bg-surface-900 rounded border"},[t("div",J0,[U(f(a.template?.title||"Неизвестный атрибут")+" ",1),a.template?.isRequired?(l(),i("span",Y0,"*")):p("",!0)]),t("div",Z0,f(a.value)+" "+f(a.template?.unit?u.getUnitLabel(a.template.unit):""),1),t("div",$0,[a.template?.group?.name?(l(),E(u.VTag,{key:0,value:a.template.group.name,severity:"secondary",size:"small"},null,8,["value"])):p("",!0),t("span",ue,f(u.getDataTypeLabel(a.template?.dataType)),1)])]))),128))])])]),_:1})):p("",!0),r.item.applicabilities?.length>0?(l(),E(u.VCard,{key:1},{header:o(()=>[t("div",ee,[t("h3",te," Применимость ("+f(r.item.applicabilities.length)+") ",1)])]),content:o(()=>[t("div",ae,[t("div",se,[(l(!0),i(Y,null,Z(r.item.applicabilities,a=>(l(),i("div",{key:a.id,class:"flex items-center justify-between p-3 bg-surface-50 dark:bg-surface-900 rounded border"},[t("div",re,[t("div",le,[a.part?.id?(l(),i("a",{key:0,class:"hover:text-primary-600 dark:hover:text-primary-400 underline-offset-2 hover:underline",href:`/admin/parts/${a.part.id}`,target:"_blank",rel:"noopener"},f(a.part?.name||`Группа #${a.part?.id}`),9,ne)):(l(),i("span",oe,f(a.part?.name||`Группа #${a.part?.id}`),1))]),t("div",ie,[s(u.VTag,{value:u.getAccuracyLabel(a.accuracy),severity:u.getAccuracySeverity(a.accuracy),size:"small"},null,8,["value","severity"]),a.notes?(l(),i("span",de,f(a.notes),1)):p("",!0)])]),t("div",ce,[r4((l(),E(u.VButton,{severity:"danger",text:"",size:"small",onClick:y=>m.$emit("unlink",a)},{icon:o(()=>[s(u.Icon,{name:"pi pi-trash",class:"w-4 h-4"})]),_:2},1032,["onClick"])),[[d,"Отвязать"]])])]))),128))])])]),_:1})):p("",!0)])}const fe=$(C0,[["render",me]]),ve=u4({__name:"MatchingResults",props:{item:{},results:{},loading:{type:Boolean}},emits:["refresh","link"],setup(m,{expose:e,emit:r}){e();const u=m,_=r,{getAccuracyLabel:v,getAccuracySeverity:d}=iu(),a=g(!1),y=g(null),T=g(!1),k=eu({accuracy:"EXACT_MATCH",notes:""}),L=[{label:"Точное совпадение",value:"EXACT_MATCH"},{label:"Совпадение с примечаниями",value:"MATCH_WITH_NOTES"},{label:"Требуется модификация",value:"REQUIRES_MODIFICATION"},{label:"Частичное совпадение",value:"PARTIAL_MATCH"}],W=C=>{y.value=C,k.accuracy=C.accuracySuggestion,k.notes="";const x=(C.details||[]).find(F=>String(F.kind).includes("NEAR")||String(F.kind).includes("LEGACY"));x?.notes&&(k.notes=x.notes),(C.details||[]).find(F=>F.kind==="NUMBER_WITHIN_TOLERANCE")&&!k.notes&&(k.notes="Совпадение по допуску"),a.value=!0},h=()=>{a.value=!1,y.value=null,k.accuracy="EXACT_MATCH",k.notes=""},M=()=>{y.value&&(T.value=!0,_("link",{partId:y.value.part.id,accuracy:k.accuracy,notes:k.notes||void 0}),T.value=!1,h())},{matching:D}=y4(),B=x4(),j={props:u,emit:_,getAccuracyLabel:v,getAccuracySeverity:d,showConfirmDialog:a,selectedCandidate:y,linking:T,confirmForm:k,accuracyOptions:L,openConfirmDialog:W,closeConfirmDialog:h,confirmLink:M,matching:D,toast:B,queueProposal:async C=>{try{const x={partId:C.part.id,accuracy:C.accuracySuggestion,notes:void 0},q=(C.details||[]).find(w=>String(w.kind).includes("NEAR")||String(w.kind).includes("LEGACY"));q?.notes&&(x.notes=q.notes),(C.details||[]).find(w=>w.kind==="NUMBER_WITHIN_TOLERANCE")&&!x.notes&&(x.notes="Совпадение по допуску"),await D.proposeLink({catalogItemId:u.item.id,partId:x.partId,accuracySuggestion:x.accuracy,notesSuggestion:x.notes,details:C.details})}catch{B.error("Ошибка","Не удалось добавить предложение")}},VButton:l4,VCard:o4,VTag:C4,VDialog:b4,VSelect:nu,VTextarea:S4,MatchingDetailsGrid:ou,MatchingEmptyState:Z4,get RefreshCcwIcon(){return w4},get LinkIcon(){return p4},get SendIcon(){return mu},MatchingLoadingState:Y4};return Object.defineProperty(j,"__isScriptSetup",{enumerable:!1,value:!0}),j}}),ge={class:"p-4 space-y-4"},ye={class:"flex items-center justify-between"},be={class:"text-lg font-mono font-semibold"},Ce={class:"flex gap-2"},Ee={key:2,class:"space-y-3"},pe={class:"p-4 grid grid-cols-1 md:grid-cols-3 gap-3 items-start"},_e={class:"md:col-span-1"},he={class:"font-semibold text-surface-900 dark:text-surface-0"},De={class:"mt-2"},xe={class:"mt-3"},ke={class:"md:col-span-2"},Fe={key:0,class:"space-y-4"},Be={class:"grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-surface-50 dark:bg-surface-900 rounded"},Ie={class:"font-semibold"},we={class:"text-sm"},Ae={class:"font-semibold"},Ve={class:"space-y-3"},Se={class:"flex justify-between"};function Te(m,e,r,u,_,v){const d=i4("tooltip");return l(),i("div",ge,[t("div",ye,[t("div",null,[e[5]||(e[5]=t("div",{class:"text-sm text-surface-500"},"Каталожная позиция",-1)),t("div",be,[U(f(r.item.sku)+" ",1),e[4]||(e[4]=t("span",{class:"text-surface-500"},"—",-1)),U(" "+f(r.item.brand?.name),1)])]),t("div",Ce,[s(u.VButton,{severity:"secondary",outlined:"",size:"small",loading:r.loading,onClick:e[0]||(e[0]=a=>m.$emit("refresh"))},{default:o(()=>[s(u.RefreshCcwIcon)]),_:1},8,["loading"])])]),r.loading?(l(),E(u.MatchingLoadingState,{key:0})):!r.results||r.results.length===0?(l(),E(u.MatchingEmptyState,{key:1})):(l(),i("div",Ee,[(l(!0),i(Y,null,Z(r.results,a=>(l(),E(u.VCard,{key:a.part.id,class:"border"},{content:o(()=>[t("div",pe,[t("div",_e,[t("div",he,f(a.part.name||"Группа #"+a.part.id),1),t("div",De,[s(u.VTag,{value:u.getAccuracyLabel(a.accuracySuggestion),severity:u.getAccuracySeverity(a.accuracySuggestion)},null,8,["value","severity"])]),t("div",xe,[r4((l(),E(u.VButton,{size:"small",severity:"secondary",outlined:"",onClick:y=>u.openConfirmDialog(a)},{default:o(()=>[e[6]||(e[6]=U(" Привязать ")),s(u.LinkIcon,{class:"w-5 h-5"})]),_:2,__:[6]},1032,["onClick"])),[[d,"Создать связь"]]),r4((l(),E(u.VButton,{size:"small",severity:"secondary",outlined:"",onClick:y=>u.queueProposal(a),class:"ml-2"},{default:o(()=>[e[7]||(e[7]=U(" В очередь ")),s(u.SendIcon,{class:"w-5 h-5"})]),_:2,__:[7]},1032,["onClick"])),[[d,"В очередь предложений"]])])]),t("div",ke,[e[8]||(e[8]=t("div",{class:"text-sm text-surface-500 mb-2"},"Детали сопоставления",-1)),s(u.MatchingDetailsGrid,{details:a.details,controls:!1},null,8,["details"])])])]),_:2},1024))),128))])),s(u.VDialog,{visible:u.showConfirmDialog,"onUpdate:visible":e[3]||(e[3]=a=>u.showConfirmDialog=a),modal:"",header:"Подтверждение связи",class:"w-full max-w-3xl"},{footer:o(()=>[t("div",Se,[s(u.VButton,{label:"Отмена",severity:"secondary",onClick:u.closeConfirmDialog}),s(u.VButton,{label:"Создать связь",severity:"success",onClick:u.confirmLink,loading:u.linking},null,8,["loading"])])]),default:o(()=>[u.selectedCandidate?(l(),i("div",Fe,[t("div",Be,[t("div",null,[e[9]||(e[9]=t("div",{class:"text-sm text-surface-500"},"Каталожная позиция",-1)),t("div",Ie,f(r.item.sku),1),t("div",we,f(r.item.brand?.name),1)]),t("div",null,[e[10]||(e[10]=t("div",{class:"text-sm text-surface-500"},"Группа взаимозаменяемости",-1)),t("div",Ae,f(u.selectedCandidate.part.name||`Группа #${u.selectedCandidate.part.id}`),1)])]),t("div",null,[e[11]||(e[11]=t("h3",{class:"text-lg font-semibold mb-3"},"Детали сопоставления",-1)),s(u.MatchingDetailsGrid,{details:u.selectedCandidate.details,controls:!1},null,8,["details"])]),t("div",Ve,[t("div",null,[e[12]||(e[12]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Точность совпадения ",-1)),s(u.VSelect,{modelValue:u.confirmForm.accuracy,"onUpdate:modelValue":e[1]||(e[1]=a=>u.confirmForm.accuracy=a),options:u.accuracyOptions,"option-label":"label","option-value":"value",placeholder:"Выберите точность",class:"w-full"},null,8,["modelValue"])]),t("div",null,[e[13]||(e[13]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Примечания ",-1)),s(u.VTextarea,{modelValue:u.confirmForm.notes,"onUpdate:modelValue":e[2]||(e[2]=a=>u.confirmForm.notes=a),rows:"3",placeholder:"Дополнительная информация о совместимости...",class:"w-full"},null,8,["modelValue"]),e[14]||(e[14]=t("small",{class:"text-surface-500"}," Укажите особенности применения, ограничения или условия замены ",-1))])])])):p("",!0)]),_:1},8,["visible"])])}const Me=$(ve,[["render",Te]]),Pe=u4({__name:"CatalogItemsManager",setup(m,{expose:e}){e();const{catalogItems:r,brands:u,loading:_,error:v,matching:d,partApplicability:a,client:y}=y4(),T=y.crud.catalogItemAttribute,k=q4(),L=x4(),W=g([]),h=g(0),M=g(1),D=g(20),B=g(""),I=g(null),j=g([]),C=j4({q:"",brandId:void 0,page:1,rows:20,sortField:"",sortOrder:1},{prefix:"ci_",numberParams:["brandId","page","rows"],debounceMs:300}),x=g(!1),q=g(!1),F=g(null),w=g(null),z=g(null),K=g(!1),X=g(null),e4=g(!1),t4=g([]),Q=g4(()=>{const n=z.value;if(!n||!Array.isArray(n.attributes))return[];const c=new Set,P=[];for(const N of n.attributes){const R=N.template;R&&(c.has(R.id)||(c.add(R.id),P.push({id:R.id,title:R.title||R.name||`#${R.id}`})))}return P.sort((N,R)=>N.title.localeCompare(R.title))}),O=g(""),H=g(1),V=async()=>{try{const n={skip:(M.value-1)*D.value,take:D.value,include:{brand:!0,image:!0,mediaAssets:!0,attributes:{include:{template:!0}},applicabilities:{include:{part:!0}}}};if(B.value.trim()&&(n.where={OR:[{sku:{contains:B.value.trim(),mode:"insensitive"}},{description:{contains:B.value.trim(),mode:"insensitive"}},{brand:{name:{contains:B.value.trim(),mode:"insensitive"}}}]}),I.value){const N={brandId:I.value.id};n.where?n.where={AND:[n.where,N]}:n.where=N}O.value?n.orderBy={[O.value]:H.value===1?"asc":"desc"}:n.orderBy={id:"desc"};const c=await r.findMany(n);Array.isArray(c)?W.value=c:W.value=[];const P=await r.findMany({where:n.where,select:{id:!0}});h.value=Array.isArray(P)?P.length:0}catch(n){console.error("Ошибка загрузки каталожных позиций:",n),L.error("Ошибка","Не удалось загрузить каталожные позиции")}},d4=async n=>{try{const c=n.query.toLowerCase(),P=await u.findMany({where:{name:{contains:c,mode:"insensitive"}},take:10});j.value=Array.isArray(P)?P:[]}catch(c){console.error("Ошибка поиска брендов:",c)}};let J;const a4=()=>{clearTimeout(J),J=setTimeout(()=>{M.value=1,C.updateFilters({q:B.value||void 0,page:1}),V()},300)},n4=()=>{M.value=1,C.updateFilters({brandId:I.value?.id,page:1}),V()},b=n=>{M.value=Math.floor(n.first/n.rows)+1,D.value=n.rows,C.updateFilters({page:M.value,rows:D.value}),V()},A=n=>{O.value=n.sortField,H.value=n.sortOrder,C.updateFilters({sortField:O.value,sortOrder:H.value}),V()},G=()=>{V()},s4=n=>{F.value={...n},x.value=!0},S=n=>{k.confirmDelete(`позицию "${n.sku}" (${n.brand?.name})`,async()=>{try{await r.delete({where:{id:n.id}}),L.success("Успешно","Позиция удалена"),V()}catch(c){console.error("Ошибка удаления:",c),L.error("Ошибка","Не удалось удалить позицию")}})},c4=n=>{w.value=n,q.value=!0},m4=n=>{z.value=n,K.value=!0,f4()},f4=async()=>{if(z.value){e4.value=!0,X.value=null;try{const n=await d.findMatchingParts({catalogItemId:z.value.id,requiredTemplateIds:t4.value.length?t4.value:void 0});X.value=n?n.candidates||[]:[]}catch(n){console.error("Ошибка подбора:",n),X.value=[]}finally{e4.value=!1}}},M4=async n=>{if(z.value)try{await a.upsert({where:{partId_catalogItemId:{partId:n.partId,catalogItemId:z.value.id}},create:{partId:n.partId,catalogItemId:z.value.id,accuracy:n.accuracy,notes:n.notes},update:{accuracy:n.accuracy,notes:n.notes}}),K.value=!1,V()}catch(c){console.error("Ошибка привязки:",c),L.error("Ошибка","Не удалось привязать позицию")}},P4=async n=>{try{const{attributes:c,...P}=n;let N=null;if(F.value?(N=await r.update({where:{id:F.value.id},data:P}),c?.deleteMany!==void 0&&await T.deleteMany.mutate({where:{catalogItemId:F.value.id}})):N=await r.create({data:P}),c?.create&&c.create.length>0)for(const R of c.create)try{await T.create.mutate({data:{value:String(R.value),catalogItem:{connect:{id:N.id}},template:{connect:{id:R.templateId}}}})}catch(O4){console.error("Ошибка создания атрибута:",O4)}x.value=!1,F.value=null,V()}catch(c){console.error("❌ Ошибка сохранения:",c),L.error("Ошибка","Не удалось сохранить позицию")}},L4=()=>{x.value=!1,F.value=null},R4=()=>{F.value={...w.value},q.value=!1,x.value=!0},U4=()=>{z.value=w.value,K.value=!0,f4()},z4=async n=>{n?.id&&k.show({header:"Удалить связь?",message:`Отвязать позицию от группы #${n.part?.id||""}?`,icon:"pi pi-trash",acceptLabel:"Отвязать",rejectLabel:"Отмена",acceptClass:"bg-red-500 hover:bg-red-600",accept:async()=>{try{await a.delete({where:{id:n.id}}),w.value&&(w.value={...w.value,applicabilities:(w.value.applicabilities||[]).filter(c=>c.id!==n.id)}),V()}catch(c){console.error("Ошибка отвязки:",c),L.error("Ошибка","Не удалось отвязать позицию")}}})};I4(()=>{const n=C.filters.value;B.value=n.q||"",I.value=n.brandId?{id:n.brandId}:null,M.value=n.page||1,D.value=n.rows||20,O.value=n.sortField||"",H.value=n.sortOrder===-1?-1:1,V()}),B4(C.filters,n=>{const c=n,P=c.brandId?{id:c.brandId}:null;B.value!==(c.q||"")&&(B.value=c.q||""),(I.value?.id||null)!==(c.brandId??null)&&(I.value=P),M.value!==(c.page||1)&&(M.value=c.page||1),D.value!==(c.rows||20)&&(D.value=c.rows||20),O.value!==(c.sortField||"")&&(O.value=c.sortField||""),H.value!==(c.sortOrder===-1?-1:1)&&(H.value=c.sortOrder===-1?-1:1),V()});const _4={catalogItemsApi:r,brands:u,loading:_,error:v,matching:d,partApplicability:a,client:y,catalogItemAttributesApi:T,confirm:k,toast:L,catalogItems:W,totalRecords:h,currentPage:M,pageSize:D,searchQuery:B,selectedBrand:I,brandSuggestions:j,urlSync:C,showCreateDialog:x,showDetailsDialog:q,editingItem:F,selectedItem:w,matchingItem:z,showMatchingDialog:K,matchingResults:X,matchingLoading:e4,requiredTemplateIds:t4,attributeOptions:Q,sortField:O,sortOrder:H,loadCatalogItems:V,searchBrands:d4,get searchTimeout(){return J},set searchTimeout(n){J=n},debouncedSearch:a4,onBrandFilterChange:n4,onPageChange:b,onSort:A,refreshData:G,onEdit:s4,onDelete:S,onViewDetails:c4,onMatch:m4,runMatching:f4,linkToPart:M4,onSave:P4,onCancel:L4,onEditFromDetails:R4,onMatchFromCard:U4,onUnlink:z4,VCard:o4,VInputText:k4,VAutoComplete:F4,VButton:l4,VDialog:b4,VConfirmDialog:H4,VMultiSelect:G4,Toast:W4,CatalogItemsTable:Ru,CatalogItemForm:b0,CatalogItemCard:fe,MatchingResults:Me,get SearchIcon(){return du},get PlusIcon(){return uu},get RefreshCcwIcon(){return w4}};return Object.defineProperty(_4,"__isScriptSetup",{enumerable:!1,value:!0}),_4}}),Le={class:"catalog-items-manager"},Re={class:"p-6"},Ue={class:"flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between"},ze={class:"flex flex-col sm:flex-row gap-4 flex-1"},Oe={class:"flex-1"},Ne={class:"flex gap-2"},je={key:0,class:"p-4 space-y-4"},qe={class:"flex flex-col md:flex-row md:items-end gap-3"},He={class:"flex-1"},Ge={class:"flex gap-2"};function We(m,e,r,u,_,v){return l(),i("div",Le,[s(u.VCard,{class:"mb-6"},{content:o(()=>[t("div",Re,[t("div",Ue,[t("div",ze,[t("div",Oe,[s(u.VInputText,{modelValue:u.searchQuery,"onUpdate:modelValue":e[0]||(e[0]=d=>u.searchQuery=d),placeholder:"Поиск по артикулу, описанию или бренду...",class:"w-full",onInput:u.debouncedSearch},{prefix:o(()=>[s(u.SearchIcon)]),_:1},8,["modelValue"])]),s(u.VAutoComplete,{modelValue:u.selectedBrand,"onUpdate:modelValue":e[1]||(e[1]=d=>u.selectedBrand=d),suggestions:u.brandSuggestions,onComplete:u.searchBrands,"option-label":"name",placeholder:"Фильтр по бренду",class:"w-full sm:w-64",onChange:u.onBrandFilterChange,dropdown:""},null,8,["modelValue","suggestions"])]),t("div",Ne,[s(u.VButton,{onClick:e[2]||(e[2]=d=>u.showCreateDialog=!0),severity:"secondary",outlined:"",label:"Добавить позицию"},{default:o(()=>[s(u.PlusIcon)]),_:1}),s(u.VButton,{onClick:u.refreshData,severity:"secondary",outlined:"",loading:u.loading},{default:o(()=>[s(u.RefreshCcwIcon)]),_:1},8,["loading"])])])])]),_:1}),s(u.CatalogItemsTable,{items:u.catalogItems,loading:u.loading,"total-records":u.totalRecords,rows:u.pageSize,first:(u.currentPage-1)*u.pageSize,onPage:u.onPageChange,onSort:u.onSort,onEdit:u.onEdit,onDelete:u.onDelete,onViewDetails:u.onViewDetails,onMatch:u.onMatch},null,8,["items","loading","total-records","rows","first"]),s(u.VDialog,{visible:u.showCreateDialog,"onUpdate:visible":e[3]||(e[3]=d=>u.showCreateDialog=d),modal:"",header:u.editingItem?"Редактировать позицию":"Создать позицию",class:"w-full max-w-2xl"},{default:o(()=>[s(u.CatalogItemForm,{item:u.editingItem,onSave:u.onSave,onCancel:u.onCancel},null,8,["item"])]),_:1},8,["visible","header"]),s(u.VDialog,{visible:u.showDetailsDialog,"onUpdate:visible":e[5]||(e[5]=d=>u.showDetailsDialog=d),modal:"",header:"Детали позиции",class:"w-auto"},{default:o(()=>[u.selectedItem?(l(),E(u.CatalogItemCard,{key:0,item:u.selectedItem,onEdit:u.onEditFromDetails,onMatch:u.onMatchFromCard,onClose:e[4]||(e[4]=d=>u.showDetailsDialog=!1),onUnlink:u.onUnlink},null,8,["item"])):p("",!0)]),_:1},8,["visible"]),s(u.VDialog,{visible:u.showMatchingDialog,"onUpdate:visible":e[7]||(e[7]=d=>u.showMatchingDialog=d),modal:"",header:"Подбор взаимозаменяемых групп",class:"w-auto"},{default:o(()=>[u.matchingItem?(l(),i("div",je,[t("div",qe,[t("div",He,[e[8]||(e[8]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Обязательные атрибуты для совпадения ",-1)),s(u.VMultiSelect,{modelValue:u.requiredTemplateIds,"onUpdate:modelValue":e[6]||(e[6]=d=>u.requiredTemplateIds=d),options:u.attributeOptions,"option-label":"title","option-value":"id",placeholder:"Выберите атрибуты (рекомендуется 2+)",class:"w-full"},null,8,["modelValue","options"]),e[9]||(e[9]=t("small",{class:"text-surface-500"},"Чтобы исключить ложные совпадения по одному признаку — выберите ключевые атрибуты.",-1))]),t("div",Ge,[s(u.VButton,{severity:"secondary",outlined:"",loading:u.matchingLoading,onClick:u.runMatching},{default:o(()=>e[10]||(e[10]=[U("Найти")])),_:1,__:[10]},8,["loading"])])]),s(u.MatchingResults,{item:u.matchingItem,results:u.matchingResults,loading:u.matchingLoading,onRefresh:u.runMatching,onLink:u.linkToPart},null,8,["item","results","loading"])])):p("",!0)]),_:1},8,["visible"]),s(u.VConfirmDialog),s(u.Toast)])}const Qe=$(Pe,[["render",We]]),Ke=u4({__name:"CatalogItemsManagerBoundary",setup(m,{expose:e}){e();const r=g(0),_={key:r,onRetry:()=>{r.value++},ErrorBoundary:N4,CatalogItemsManager:Qe};return Object.defineProperty(_,"__isScriptSetup",{enumerable:!1,value:!0}),_}});function Xe(m,e,r,u,_,v){return l(),E(u.ErrorBoundary,{variant:"detailed",title:"Ошибка каталожных позиций",message:"Не удалось загрузить или отрисовать таблицу. Попробуйте повторить.",onRetry:u.onRetry},{default:o(()=>[(l(),E(u.CatalogItemsManager,{key:u.key}))]),_:1})}const $t=$(Ke,[["render",Xe]]);export{$t as default};
