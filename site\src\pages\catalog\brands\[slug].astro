---
import MainLayout from "../../../layouts/MainLayout.astro";
import { trpcClient } from "@/lib/trpc";
import { TrpcProvider } from "@/components/providers/TrpcProvider";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

const { slug } = Astro.params;

if (!slug) {
  return Astro.redirect('/brands');
}

// Загружаем бренд и связанные данные
let brand: any = null;
let catalogItems: any[] = [];
let equipmentModels: any[] = [];

try {
  // Получаем бренд по slug
  const brandResponse = await trpcClient.crud.brand.findUnique.query({
    where: { slug },
    include: {
      _count: { 
        select: { 
          catalogItems: true,
          equipmentModel: true 
        } 
      }
    }
  });
  
  if (!brandResponse) {
    return Astro.redirect('/brands');
  }
  
  brand = brandResponse;

  // Получаем каталожные позиции бренда
  const catalogItemsResponse = await trpcClient.crud.catalogItem.findMany.query({
    where: { brandId: brand.id },
    include: {
      brand: true,
      attributes: {
        include: { template: true },
        take: 3
      },
      applicabilities: {
        include: {
          part: {
            include: { partCategory: true }
          }
        },
        take: 1
      }
    },
    take: 20,
    orderBy: { sku: 'asc' }
  });
  catalogItems = catalogItemsResponse || [];

  // Получаем модели техники бренда (если это OEM)
  if (brand.isOem) {
    const equipmentResponse = await trpcClient.crud.equipmentModel.findMany.query({
      where: { brandId: brand.id },
      include: {
        brand: true,
        attributes: {
          include: { template: true },
          take: 3
        }
      },
      take: 20,
      orderBy: { name: 'asc' }
    });
    equipmentModels = equipmentResponse || [];
  }

} catch (error) {
  console.error('Error loading brand data:', error);
  return Astro.redirect('/brands');
}
---

<MainLayout title={brand.name} description={`Каталог запчастей и техники бренда ${brand.name}`}>
  <TrpcProvider client:load>
    <div class="container mx-auto px-4 py-8">
      <!-- Хлебные крошки -->
      <nav class="flex mb-8" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
          <li class="inline-flex items-center">
            <a href="/" class="inline-flex items-center text-sm font-medium text-muted-foreground hover:text-foreground">
              Главная
            </a>
          </li>
          <li>
            <div class="flex items-center">
              <span class="mx-2 text-muted-foreground">/</span>
              <a href="/brands" class="text-sm font-medium text-muted-foreground hover:text-foreground">Бренды</a>
            </div>
          </li>
          <li aria-current="page">
            <div class="flex items-center">
              <span class="mx-2 text-muted-foreground">/</span>
              <span class="text-sm font-medium text-foreground">{brand.name}</span>
            </div>
          </li>
        </ol>
      </nav>

      <!-- Заголовок бренда -->
      <div class="mb-8">
        <div class="flex items-start justify-between">
          <div>
            <h1 class="text-3xl font-bold tracking-tight mb-2">{brand.name}</h1>
            <div class="flex items-center gap-4 mb-4">
              <Badge variant={brand.isOem ? "default" : "secondary"}>
                {brand.isOem ? "OEM производитель" : "Aftermarket производитель"}
              </Badge>
              {brand.country && (
                <span class="text-muted-foreground">{brand.country}</span>
              )}
            </div>
          </div>
        </div>
        
        <!-- Статистика -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div class="text-center p-4 bg-muted/20 rounded-lg">
            <div class="text-2xl font-bold">{brand._count.catalogItems}</div>
            <div class="text-sm text-muted-foreground">Запчастей</div>
          </div>
          <div class="text-center p-4 bg-muted/20 rounded-lg">
            <div class="text-2xl font-bold">{brand._count.equipmentModel}</div>
            <div class="text-sm text-muted-foreground">Моделей техники</div>
          </div>
        </div>
      </div>

      <div class="space-y-12">
        <!-- Модели техники (для OEM) -->
        {brand.isOem && equipmentModels.length > 0 && (
          <div>
            <div class="flex items-center justify-between mb-6">
              <h2 class="text-2xl font-semibold">Модели техники</h2>
              {equipmentModels.length === 20 && (
                <a 
                  href={`/equipment?brand=${brand.id}`}
                  class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2"
                >
                  Показать все
                </a>
              )}
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {equipmentModels.map((model) => (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">{model.name}</CardTitle>
                    <CardDescription>{brand.name}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    {model.attributes.length > 0 && (
                      <div class="space-y-1">
                        {model.attributes.slice(0, 3).map((attr: any) => (
                          <div class="flex justify-between text-sm">
                            <span class="text-muted-foreground">{attr.template.title}:</span>
                            <span class="font-medium">{attr.value}</span>
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        <!-- Каталожные позиции -->
        <div>
          <div class="flex items-center justify-between mb-6">
            <h2 class="text-2xl font-semibold">Каталог запчастей</h2>
            {catalogItems.length === 20 && (
              <a 
                href={`/catalog?brand=${brand.id}`}
                class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2"
              >
                Показать все
              </a>
            )}
          </div>

          {catalogItems.length > 0 ? (
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {catalogItems.map((item) => (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">{item.sku}</CardTitle>
                    <CardDescription>{brand.name}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    {item.description && (
                      <p class="text-sm text-muted-foreground mb-3 line-clamp-2">{item.description}</p>
                    )}
                    
                    {/* Группа взаимозаменяемости */}
                    {item.applicabilities.length > 0 && (
                      <div class="mb-3">
                        <div class="text-xs text-muted-foreground mb-1">Группа взаимозаменяемости:</div>
                        <a 
                          href={`/catalog/parts/${item.applicabilities[0].part.id}`}
                          class="text-sm text-primary hover:underline"
                        >
                          {item.applicabilities[0].part.name || `Запчасть #${item.applicabilities[0].part.id}`}
                        </a>
                        <div class="text-xs text-muted-foreground">
                          {item.applicabilities[0].part.partCategory.name}
                        </div>
                      </div>
                    )}
                    
                    {/* Атрибуты */}
                    {item.attributes.length > 0 && (
                      <div class="space-y-1">
                        {item.attributes.slice(0, 3).map((attr: any) => (
                          <div class="flex justify-between text-sm">
                            <span class="text-muted-foreground">{attr.template.title}:</span>
                            <span class="font-medium">{attr.value}</span>
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div class="text-center py-12">
              <h3 class="text-lg font-semibold mb-2">Запчасти не найдены</h3>
              <p class="text-muted-foreground">
                У этого бренда пока нет запчастей в каталоге
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  </TrpcProvider>
</MainLayout>
