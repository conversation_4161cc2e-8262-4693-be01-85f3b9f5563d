/**
 * Composable для управления аутентификацией
 * Использует сгенерированные типы из API
 */

import { ref, computed, reactive, readonly } from 'vue'
import {
  authClient,
  useSession,
  hasRole,
  isAdmin,
  isShopOwner,
  canAccessAdmin,
  getErrorMessage,
  type User,
  type Role
} from '@/lib/auth-client'

import { z } from "zod"

// Локальные схемы для валидации форм
export const UserCreateSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6),
  name: z.string().min(1),
  role: z.enum(['USER', 'ADMIN', 'SHOP']).optional().default('USER'),
})

// Типы для форм на основе Zod схем
export const LoginFormSchema = z.object({
  email: z.string().email('Введите корректный email'),
  password: z.string().min(6, 'Пароль должен содержать минимум 6 символов'),
  rememberMe: z.boolean().optional().default(false)
})

export const RegisterFormSchema = UserCreateSchema.extend({
  password: z.string().min(6, 'Пароль должен содержать минимум 6 символов'),
  confirmPassword: z.string(),
  acceptTerms: z.boolean().refine(val => val === true, 'Необходимо принять условия использования')
}).refine(data => data.password === data.confirmPassword, {
  message: 'Пароли не совпадают',
  path: ['confirmPassword']
})

export type LoginFormData = z.infer<typeof LoginFormSchema>
export type RegisterFormData = z.infer<typeof RegisterFormSchema>

// Состояние загрузки
const loadingState = reactive({
  signIn: false,
  signUp: false,
  signOut: false,
  session: false
})

/**
 * Основной composable для аутентификации
 */
export function useAuth() {
  // Используем встроенный хук better-auth для сессии
  const sessionData = useSession()

  // Вычисляемые свойства
  const session = computed(() => sessionData.value?.data || null)
  const user = computed(() => session.value?.user as User | null)
  const isAuthenticated = computed(() => !!session.value?.user)
  const isLoading = computed(() =>
    Object.values(loadingState).some(loading => loading)
  )

  // Функция для обновления сессии
  const refetchSession = async () => {
    try {
      const result = await authClient.getSession()
      if (result.data) {
        // Сессия обновится автоматически через reactive store
        return result.data
      }
    } catch (error) {
      console.error('Error refetching session:', error)
    }
  }

  // Проверки ролей
  const userRole = computed(() => user.value?.role || null)
  const isUserAdmin = computed(() => user.value ? isAdmin(user.value) : false)
  const isUserShopOwner = computed(() => user.value ? isShopOwner(user.value) : false)
  const isUserRegular = computed(() => user.value?.role === 'USER')
  const userCanAccessAdmin = computed(() => user.value ? canAccessAdmin(user.value) : false)

  /**
   * Вход в систему
   */
  const signIn = async (data: LoginFormData) => {
    // Валидируем данные
    const validationResult = LoginFormSchema.safeParse(data)
    if (!validationResult.success) {
      return { 
        error: { 
          message: 'Ошибка валидации', 
          details: validationResult.error.issues 
        } 
      }
    }

    loadingState.signIn = true
    
    try {
      const result = await authClient.signIn.email({
        email: data.email,
        password: data.password,
        rememberMe: data.rememberMe
      })

      if (result.error) {
        return { error: { message: getErrorMessage(result.error) } }
      }

      // Обновляем сессию после успешного входа
      await refetchSession()
      
      return { data: result.data }
    } catch (error) {
      return { error: { message: getErrorMessage(error, 'Произошла ошибка при входе') } }
    } finally {
      loadingState.signIn = false
    }
  }

  /**
   * Регистрация
   */
  const signUp = async (data: RegisterFormData) => {
    // Валидируем данные
    const validationResult = RegisterFormSchema.safeParse(data)
    if (!validationResult.success) {
      return {
        error: {
          message: 'Ошибка валидации',
          details: validationResult.error.issues
        }
      }
    }

    loadingState.signUp = true

    try {
      const result = await authClient.signUp.email({
        email: data.email,
        password: data.password,
        name: data.name || ''
        // Роль будет установлена через дополнительные поля на сервере
      })

      if (result.error) {
        return { error: { message: getErrorMessage(result.error) } }
      }

      // После успешной регистрации можно установить роль через admin API
      if (data.role && data.role !== 'USER') {
        try {
          // Преобразуем роль в формат, понятный better-auth admin plugin
          const adminRole = data.role === 'ADMIN' ? 'admin' : 'user'
          await authClient.admin.setRole({
            userId: result.data?.user?.id,
            role: adminRole
          })
        } catch (roleError) {
          console.warn('Failed to set user role:', roleError)
          // Не прерываем процесс регистрации из-за ошибки установки роли
        }
      }

      return { data: result.data }
    } catch (error) {
      return { error: { message: getErrorMessage(error, 'Произошла ошибка при регистрации') } }
    } finally {
      loadingState.signUp = false
    }
  }

  /**
   * Выход из системы
   */
  const signOut = async () => {
    loadingState.signOut = true

    try {
      const result = await authClient.signOut()

      if (result.error) {
        return { error: { message: getErrorMessage(result.error) } }
      }

      // Обновляем сессию после выхода
      await refetchSession()
      
      return { data: result.data }
    } catch (error) {
      return { error: { message: getErrorMessage(error, 'Произошла ошибка при выходе') } }
    } finally {
      loadingState.signOut = false
    }
  }

  /**
   * Проверка роли пользователя
   */
  const checkRole = (role: Role): boolean => {
    return user.value ? hasRole(user.value, role) : false
  }

  /**
   * Получение отображаемого имени пользователя
   */
  const displayName = computed(() => {
    if (!user.value) return null
    return user.value.name || user.value.email
  })

  /**
   * Получение аватара пользователя
   */
  const userAvatar = computed(() => {
    return user.value?.image || null
  })

  return {
    // Состояние
    user,
    session,
    isAuthenticated,
    isLoading,

    // Роли
    userRole,
    isUserAdmin,
    isUserShopOwner,
    isUserRegular,
    userCanAccessAdmin,

    // Пользовательские данные
    displayName,
    userAvatar,

    // Методы
    signIn,
    signUp,
    signOut,
    checkRole,
    refetchSession,

    // Состояния загрузки
    loadingState: readonly(loadingState),

    // Схемы валидации
    LoginFormSchema,
    RegisterFormSchema
  }
}
