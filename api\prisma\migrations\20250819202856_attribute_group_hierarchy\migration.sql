-- CreateEnum
CREATE TYPE "public"."AttributeDataType" AS ENUM ('STRING', 'NUMBER', 'BOOLEAN', 'DATE', 'JSON');

-- CreateEnum
CREATE TYPE "public"."AttributeUnit" AS ENUM ('MM', 'INCH', 'FT', 'G', 'KG', 'T', 'LB', 'ML', 'L', 'GAL', 'SEC', 'MIN', 'H', 'PCS', 'SET', 'PAIR', 'BAR', 'PSI', 'KW', 'HP', 'NM', 'RPM', 'C', 'F', 'PERCENT');

-- CreateEnum
CREATE TYPE "public"."ApplicabilityAccuracy" AS ENUM ('EXACT_MATCH', 'MATCH_WITH_NOTES', 'REQUIRES_MODIFICATION', 'PARTIAL_MATCH');

-- CreateEnum
CREATE TYPE "public"."SynonymCompatibilityLevel" AS ENUM ('EXACT', 'NEAR', 'LEGACY');

-- CreateEnum
CREATE TYPE "public"."ProposalStatus" AS ENUM ('PENDING', 'APPROVED', 'REJECTED', 'INVALIDATED');

-- CreateEnum
CREATE TYPE "public"."Role" AS ENUM ('GUEST', 'USER', 'SHOP', 'ADMIN');

-- CreateTable
CREATE TABLE "public"."MediaAsset" (
    "id" SERIAL NOT NULL,
    "fileName" TEXT NOT NULL,
    "mimeType" TEXT NOT NULL,
    "fileSize" INTEGER,
    "url" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "MediaAsset_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."AttributeGroup" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "parentId" INTEGER,

    CONSTRAINT "AttributeGroup_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."AttributeTemplate" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "dataType" "public"."AttributeDataType" NOT NULL DEFAULT 'STRING',
    "unit" "public"."AttributeUnit",
    "isRequired" BOOLEAN NOT NULL DEFAULT false,
    "minValue" DOUBLE PRECISION,
    "maxValue" DOUBLE PRECISION,
    "allowedValues" TEXT[],
    "tolerance" DOUBLE PRECISION DEFAULT 0,
    "groupId" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "AttributeTemplate_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."AttributeSynonymGroup" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "templateId" INTEGER NOT NULL,
    "parentId" INTEGER,
    "canonicalValue" TEXT,
    "compatibilityLevel" "public"."SynonymCompatibilityLevel" NOT NULL DEFAULT 'EXACT',
    "notes" TEXT,

    CONSTRAINT "AttributeSynonymGroup_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."AttributeSynonym" (
    "id" SERIAL NOT NULL,
    "value" TEXT NOT NULL,
    "groupId" INTEGER NOT NULL,
    "notes" TEXT,
    "brandId" INTEGER,
    "compatibilityLevel" "public"."SynonymCompatibilityLevel",

    CONSTRAINT "AttributeSynonym_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."PartAttribute" (
    "id" SERIAL NOT NULL,
    "value" TEXT NOT NULL,
    "numericValue" DOUBLE PRECISION,
    "partId" INTEGER NOT NULL,
    "templateId" INTEGER NOT NULL,

    CONSTRAINT "PartAttribute_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."CatalogItemAttribute" (
    "id" SERIAL NOT NULL,
    "value" TEXT NOT NULL,
    "numericValue" DOUBLE PRECISION,
    "catalogItemId" INTEGER NOT NULL,
    "templateId" INTEGER NOT NULL,

    CONSTRAINT "CatalogItemAttribute_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."EquipmentModelAttribute" (
    "id" SERIAL NOT NULL,
    "value" TEXT NOT NULL,
    "numericValue" DOUBLE PRECISION,
    "equipmentModelId" TEXT NOT NULL,
    "templateId" INTEGER NOT NULL,

    CONSTRAINT "EquipmentModelAttribute_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."EquipmentApplicability" (
    "id" SERIAL NOT NULL,
    "partId" INTEGER NOT NULL,
    "equipmentModelId" TEXT NOT NULL,
    "notes" TEXT,

    CONSTRAINT "EquipmentApplicability_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."Part" (
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "id" SERIAL NOT NULL,
    "name" TEXT,
    "parentId" INTEGER,
    "level" INTEGER NOT NULL DEFAULT 0,
    "path" TEXT NOT NULL,
    "imageId" INTEGER,
    "partCategoryId" INTEGER NOT NULL,

    CONSTRAINT "Part_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."PartApplicability" (
    "id" SERIAL NOT NULL,
    "partId" INTEGER NOT NULL,
    "catalogItemId" INTEGER NOT NULL,
    "accuracy" "public"."ApplicabilityAccuracy" NOT NULL DEFAULT 'EXACT_MATCH',
    "notes" TEXT,

    CONSTRAINT "PartApplicability_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."CatalogItem" (
    "id" SERIAL NOT NULL,
    "sku" TEXT NOT NULL,
    "source" TEXT,
    "description" TEXT,
    "brandId" INTEGER NOT NULL,
    "isPublic" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "CatalogItem_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."MatchingProposal" (
    "id" SERIAL NOT NULL,
    "catalogItemId" INTEGER NOT NULL,
    "partId" INTEGER NOT NULL,
    "accuracySuggestion" "public"."ApplicabilityAccuracy" NOT NULL DEFAULT 'EXACT_MATCH',
    "notesSuggestion" TEXT,
    "details" JSONB,
    "status" "public"."ProposalStatus" NOT NULL DEFAULT 'PENDING',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "MatchingProposal_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."PartCategory" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "slug" TEXT NOT NULL,
    "description" TEXT,
    "parentId" INTEGER,
    "level" INTEGER NOT NULL DEFAULT 0,
    "path" TEXT NOT NULL,
    "icon" TEXT,
    "imageId" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PartCategory_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."Brand" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "slug" TEXT NOT NULL,
    "country" TEXT,
    "isOem" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "Brand_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."equipment_model" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "name" TEXT NOT NULL,
    "brandId" INTEGER,

    CONSTRAINT "equipment_model_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."aggregate_schema" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "partId" INTEGER,
    "imageUrl" TEXT,
    "imageWidth" INTEGER,
    "imageHeight" INTEGER,
    "svgContent" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "sortOrder" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "aggregate_schema_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."schema_position" (
    "id" TEXT NOT NULL,
    "schemaId" TEXT NOT NULL,
    "partId" INTEGER NOT NULL,
    "positionNumber" TEXT NOT NULL,
    "x" DOUBLE PRECISION NOT NULL,
    "y" DOUBLE PRECISION NOT NULL,
    "width" DOUBLE PRECISION,
    "height" DOUBLE PRECISION,
    "shape" TEXT NOT NULL DEFAULT 'circle',
    "color" TEXT,
    "label" TEXT,
    "quantity" INTEGER NOT NULL DEFAULT 1,
    "isRequired" BOOLEAN NOT NULL DEFAULT true,
    "isHighlighted" BOOLEAN NOT NULL DEFAULT false,
    "installationOrder" INTEGER,
    "notes" TEXT,
    "isVisible" BOOLEAN NOT NULL DEFAULT true,
    "sortOrder" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "schema_position_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."schema_annotation" (
    "id" TEXT NOT NULL,
    "schemaId" TEXT NOT NULL,
    "x" DOUBLE PRECISION NOT NULL,
    "y" DOUBLE PRECISION NOT NULL,
    "width" DOUBLE PRECISION,
    "height" DOUBLE PRECISION,
    "text" TEXT NOT NULL,
    "annotationType" TEXT NOT NULL DEFAULT 'note',
    "color" TEXT,
    "fontSize" INTEGER DEFAULT 12,
    "strokeWidth" INTEGER DEFAULT 1,
    "opacity" DOUBLE PRECISION DEFAULT 1.0,
    "isVisible" BOOLEAN NOT NULL DEFAULT true,
    "sortOrder" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "schema_annotation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."user" (
    "id" TEXT NOT NULL,
    "name" TEXT,
    "email" TEXT NOT NULL,
    "emailVerified" BOOLEAN NOT NULL DEFAULT false,
    "image" TEXT,
    "role" "public"."Role" NOT NULL DEFAULT 'USER',
    "banned" BOOLEAN NOT NULL DEFAULT false,
    "banReason" TEXT,
    "banExpires" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "user_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."account" (
    "id" TEXT NOT NULL,
    "accountId" TEXT NOT NULL,
    "providerId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "accessToken" TEXT,
    "refreshToken" TEXT,
    "idToken" TEXT,
    "accessTokenExpiresAt" TIMESTAMP(3),
    "refreshTokenExpiresAt" TIMESTAMP(3),
    "scope" TEXT,
    "password" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "account_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."session" (
    "id" TEXT NOT NULL,
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "token" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "userId" TEXT NOT NULL,
    "impersonatedBy" TEXT,

    CONSTRAINT "session_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "MediaAsset_url_key" ON "public"."MediaAsset"("url");

-- CreateIndex
CREATE INDEX "MediaAsset_mimeType_idx" ON "public"."MediaAsset"("mimeType");

-- CreateIndex
CREATE INDEX "MediaAsset_fileName_idx" ON "public"."MediaAsset"("fileName");

-- CreateIndex
CREATE UNIQUE INDEX "AttributeGroup_name_key" ON "public"."AttributeGroup"("name");

-- CreateIndex
CREATE INDEX "AttributeGroup_parentId_idx" ON "public"."AttributeGroup"("parentId");

-- CreateIndex
CREATE UNIQUE INDEX "AttributeTemplate_name_key" ON "public"."AttributeTemplate"("name");

-- CreateIndex
CREATE INDEX "AttributeTemplate_groupId_idx" ON "public"."AttributeTemplate"("groupId");

-- CreateIndex
CREATE UNIQUE INDEX "AttributeSynonymGroup_name_key" ON "public"."AttributeSynonymGroup"("name");

-- CreateIndex
CREATE INDEX "AttributeSynonymGroup_parentId_idx" ON "public"."AttributeSynonymGroup"("parentId");

-- CreateIndex
CREATE INDEX "AttributeSynonymGroup_templateId_idx" ON "public"."AttributeSynonymGroup"("templateId");

-- CreateIndex
CREATE INDEX "AttributeSynonym_value_idx" ON "public"."AttributeSynonym"("value");

-- CreateIndex
CREATE UNIQUE INDEX "AttributeSynonym_groupId_value_key" ON "public"."AttributeSynonym"("groupId", "value");

-- CreateIndex
CREATE INDEX "PartAttribute_partId_idx" ON "public"."PartAttribute"("partId");

-- CreateIndex
CREATE INDEX "PartAttribute_templateId_idx" ON "public"."PartAttribute"("templateId");

-- CreateIndex
CREATE INDEX "PartAttribute_value_idx" ON "public"."PartAttribute"("value");

-- CreateIndex
CREATE INDEX "PartAttribute_numericValue_idx" ON "public"."PartAttribute"("numericValue");

-- CreateIndex
CREATE UNIQUE INDEX "PartAttribute_partId_templateId_key" ON "public"."PartAttribute"("partId", "templateId");

-- CreateIndex
CREATE INDEX "CatalogItemAttribute_catalogItemId_idx" ON "public"."CatalogItemAttribute"("catalogItemId");

-- CreateIndex
CREATE INDEX "CatalogItemAttribute_templateId_idx" ON "public"."CatalogItemAttribute"("templateId");

-- CreateIndex
CREATE INDEX "CatalogItemAttribute_value_idx" ON "public"."CatalogItemAttribute"("value");

-- CreateIndex
CREATE INDEX "CatalogItemAttribute_numericValue_idx" ON "public"."CatalogItemAttribute"("numericValue");

-- CreateIndex
CREATE UNIQUE INDEX "CatalogItemAttribute_catalogItemId_templateId_key" ON "public"."CatalogItemAttribute"("catalogItemId", "templateId");

-- CreateIndex
CREATE INDEX "EquipmentModelAttribute_equipmentModelId_idx" ON "public"."EquipmentModelAttribute"("equipmentModelId");

-- CreateIndex
CREATE INDEX "EquipmentModelAttribute_templateId_idx" ON "public"."EquipmentModelAttribute"("templateId");

-- CreateIndex
CREATE INDEX "EquipmentModelAttribute_value_idx" ON "public"."EquipmentModelAttribute"("value");

-- CreateIndex
CREATE INDEX "EquipmentModelAttribute_numericValue_idx" ON "public"."EquipmentModelAttribute"("numericValue");

-- CreateIndex
CREATE UNIQUE INDEX "EquipmentModelAttribute_equipmentModelId_templateId_key" ON "public"."EquipmentModelAttribute"("equipmentModelId", "templateId");

-- CreateIndex
CREATE INDEX "EquipmentApplicability_equipmentModelId_idx" ON "public"."EquipmentApplicability"("equipmentModelId");

-- CreateIndex
CREATE UNIQUE INDEX "EquipmentApplicability_partId_equipmentModelId_key" ON "public"."EquipmentApplicability"("partId", "equipmentModelId");

-- CreateIndex
CREATE UNIQUE INDEX "Part_imageId_key" ON "public"."Part"("imageId");

-- CreateIndex
CREATE INDEX "Part_parentId_idx" ON "public"."Part"("parentId");

-- CreateIndex
CREATE INDEX "Part_path_idx" ON "public"."Part"("path");

-- CreateIndex
CREATE INDEX "PartApplicability_catalogItemId_idx" ON "public"."PartApplicability"("catalogItemId");

-- CreateIndex
CREATE UNIQUE INDEX "PartApplicability_partId_catalogItemId_key" ON "public"."PartApplicability"("partId", "catalogItemId");

-- CreateIndex
CREATE INDEX "CatalogItem_brandId_idx" ON "public"."CatalogItem"("brandId");

-- CreateIndex
CREATE UNIQUE INDEX "CatalogItem_sku_brandId_key" ON "public"."CatalogItem"("sku", "brandId");

-- CreateIndex
CREATE INDEX "MatchingProposal_status_idx" ON "public"."MatchingProposal"("status");

-- CreateIndex
CREATE UNIQUE INDEX "MatchingProposal_catalogItemId_partId_status_key" ON "public"."MatchingProposal"("catalogItemId", "partId", "status");

-- CreateIndex
CREATE UNIQUE INDEX "PartCategory_slug_key" ON "public"."PartCategory"("slug");

-- CreateIndex
CREATE UNIQUE INDEX "PartCategory_imageId_key" ON "public"."PartCategory"("imageId");

-- CreateIndex
CREATE INDEX "PartCategory_parentId_idx" ON "public"."PartCategory"("parentId");

-- CreateIndex
CREATE INDEX "PartCategory_slug_idx" ON "public"."PartCategory"("slug");

-- CreateIndex
CREATE UNIQUE INDEX "Brand_slug_key" ON "public"."Brand"("slug");

-- CreateIndex
CREATE INDEX "Brand_isOem_idx" ON "public"."Brand"("isOem");

-- CreateIndex
CREATE INDEX "equipment_model_brandId_idx" ON "public"."equipment_model"("brandId");

-- CreateIndex
CREATE INDEX "aggregate_schema_isActive_idx" ON "public"."aggregate_schema"("isActive");

-- CreateIndex
CREATE INDEX "schema_position_schemaId_idx" ON "public"."schema_position"("schemaId");

-- CreateIndex
CREATE INDEX "schema_position_partId_idx" ON "public"."schema_position"("partId");

-- CreateIndex
CREATE INDEX "schema_position_positionNumber_idx" ON "public"."schema_position"("positionNumber");

-- CreateIndex
CREATE UNIQUE INDEX "schema_position_schemaId_partId_key" ON "public"."schema_position"("schemaId", "partId");

-- CreateIndex
CREATE UNIQUE INDEX "schema_position_schemaId_positionNumber_key" ON "public"."schema_position"("schemaId", "positionNumber");

-- CreateIndex
CREATE INDEX "schema_annotation_schemaId_idx" ON "public"."schema_annotation"("schemaId");

-- CreateIndex
CREATE UNIQUE INDEX "user_email_key" ON "public"."user"("email");

-- CreateIndex
CREATE UNIQUE INDEX "session_token_key" ON "public"."session"("token");

-- AddForeignKey
ALTER TABLE "public"."AttributeGroup" ADD CONSTRAINT "AttributeGroup_parentId_fkey" FOREIGN KEY ("parentId") REFERENCES "public"."AttributeGroup"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."AttributeTemplate" ADD CONSTRAINT "AttributeTemplate_groupId_fkey" FOREIGN KEY ("groupId") REFERENCES "public"."AttributeGroup"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."AttributeSynonymGroup" ADD CONSTRAINT "AttributeSynonymGroup_templateId_fkey" FOREIGN KEY ("templateId") REFERENCES "public"."AttributeTemplate"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."AttributeSynonymGroup" ADD CONSTRAINT "AttributeSynonymGroup_parentId_fkey" FOREIGN KEY ("parentId") REFERENCES "public"."AttributeSynonymGroup"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."AttributeSynonym" ADD CONSTRAINT "AttributeSynonym_groupId_fkey" FOREIGN KEY ("groupId") REFERENCES "public"."AttributeSynonymGroup"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."AttributeSynonym" ADD CONSTRAINT "AttributeSynonym_brandId_fkey" FOREIGN KEY ("brandId") REFERENCES "public"."Brand"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."PartAttribute" ADD CONSTRAINT "PartAttribute_partId_fkey" FOREIGN KEY ("partId") REFERENCES "public"."Part"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."PartAttribute" ADD CONSTRAINT "PartAttribute_templateId_fkey" FOREIGN KEY ("templateId") REFERENCES "public"."AttributeTemplate"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."CatalogItemAttribute" ADD CONSTRAINT "CatalogItemAttribute_catalogItemId_fkey" FOREIGN KEY ("catalogItemId") REFERENCES "public"."CatalogItem"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."CatalogItemAttribute" ADD CONSTRAINT "CatalogItemAttribute_templateId_fkey" FOREIGN KEY ("templateId") REFERENCES "public"."AttributeTemplate"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."EquipmentModelAttribute" ADD CONSTRAINT "EquipmentModelAttribute_equipmentModelId_fkey" FOREIGN KEY ("equipmentModelId") REFERENCES "public"."equipment_model"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."EquipmentModelAttribute" ADD CONSTRAINT "EquipmentModelAttribute_templateId_fkey" FOREIGN KEY ("templateId") REFERENCES "public"."AttributeTemplate"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."EquipmentApplicability" ADD CONSTRAINT "EquipmentApplicability_partId_fkey" FOREIGN KEY ("partId") REFERENCES "public"."Part"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."EquipmentApplicability" ADD CONSTRAINT "EquipmentApplicability_equipmentModelId_fkey" FOREIGN KEY ("equipmentModelId") REFERENCES "public"."equipment_model"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Part" ADD CONSTRAINT "Part_parentId_fkey" FOREIGN KEY ("parentId") REFERENCES "public"."Part"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Part" ADD CONSTRAINT "Part_imageId_fkey" FOREIGN KEY ("imageId") REFERENCES "public"."MediaAsset"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Part" ADD CONSTRAINT "Part_partCategoryId_fkey" FOREIGN KEY ("partCategoryId") REFERENCES "public"."PartCategory"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."PartApplicability" ADD CONSTRAINT "PartApplicability_partId_fkey" FOREIGN KEY ("partId") REFERENCES "public"."Part"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."PartApplicability" ADD CONSTRAINT "PartApplicability_catalogItemId_fkey" FOREIGN KEY ("catalogItemId") REFERENCES "public"."CatalogItem"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."CatalogItem" ADD CONSTRAINT "CatalogItem_brandId_fkey" FOREIGN KEY ("brandId") REFERENCES "public"."Brand"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."MatchingProposal" ADD CONSTRAINT "MatchingProposal_catalogItemId_fkey" FOREIGN KEY ("catalogItemId") REFERENCES "public"."CatalogItem"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."MatchingProposal" ADD CONSTRAINT "MatchingProposal_partId_fkey" FOREIGN KEY ("partId") REFERENCES "public"."Part"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."PartCategory" ADD CONSTRAINT "PartCategory_parentId_fkey" FOREIGN KEY ("parentId") REFERENCES "public"."PartCategory"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."PartCategory" ADD CONSTRAINT "PartCategory_imageId_fkey" FOREIGN KEY ("imageId") REFERENCES "public"."MediaAsset"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."equipment_model" ADD CONSTRAINT "equipment_model_brandId_fkey" FOREIGN KEY ("brandId") REFERENCES "public"."Brand"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."aggregate_schema" ADD CONSTRAINT "aggregate_schema_partId_fkey" FOREIGN KEY ("partId") REFERENCES "public"."Part"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."schema_position" ADD CONSTRAINT "schema_position_schemaId_fkey" FOREIGN KEY ("schemaId") REFERENCES "public"."aggregate_schema"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."schema_position" ADD CONSTRAINT "schema_position_partId_fkey" FOREIGN KEY ("partId") REFERENCES "public"."Part"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."schema_annotation" ADD CONSTRAINT "schema_annotation_schemaId_fkey" FOREIGN KEY ("schemaId") REFERENCES "public"."aggregate_schema"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."account" ADD CONSTRAINT "account_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."user"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."session" ADD CONSTRAINT "session_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."user"("id") ON DELETE CASCADE ON UPDATE CASCADE;
