export { r as renderers } from '../../../chunks/_@astro-renderers_CicWY1rm.mjs';

const API_SERVER_URL = "http://localhost:3000";
async function proxyToApiServer(request) {
  const url = new URL(request.url);
  const apiUrl = `${API_SERVER_URL}${url.pathname}${url.search}`;
  try {
    const response = await fetch(apiUrl, {
      method: request.method,
      headers: request.headers,
      body: request.body
    });
    return response;
  } catch (error) {
    console.error("Proxy error:", error);
    return new Response(
      JSON.stringify({ error: "API server unavailable" }),
      {
        status: 503,
        headers: { "Content-Type": "application/json" }
      }
    );
  }
}
const GET = async (ctx) => {
  return proxyToApiServer(ctx.request);
};
const POST = async (ctx) => {
  return proxyToApiServer(ctx.request);
};
const PUT = async (ctx) => {
  return proxyToApiServer(ctx.request);
};
const PATCH = async (ctx) => {
  return proxyToApiServer(ctx.request);
};
const DELETE = async (ctx) => {
  return proxyToApiServer(ctx.request);
};

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  DELETE,
  GET,
  PATCH,
  POST,
  PUT
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
