import{u as h0}from"./useTrpc.CAkGIEe7.js";import{u as k0,C as E0}from"./ConfirmDialog.B2pd8YuJ.js";import{u as D0}from"./useToast.DbdIHNOo.js";import{M as F0,u as p0}from"./MatchingDetailsGrid.DabYCGDw.js";import{t as w0}from"./trpc.BpyaUO08.js";import A0 from"./Card.CAr8QcdG.js";import I0 from"./Button.oEwD-lSq.js";import{I as V0}from"./InputText.COaPodMV.js";import{D as B0,s as T0}from"./index.xFbNStuK.js";import{T as M0}from"./Tag.DvN1X7lb.js";import{M as P0}from"./Message.BOpJRjRi.js";import{D as L0}from"./Dialog.Dcz8Sg9i.js";import S0 from"./PartWizard.Ca1THCee.js";import{V as R0}from"./AutoComplete.XhB-0aUS.js";import{S as N0}from"./Select.CKfyRLxl.js";import{V as U0}from"./Textarea.Bgj6qPNm.js";import{I as z0}from"./Icon.Ci-mb2Ee.js";import{M as j0,a as H0,L as O0,R as G0}from"./MatchingLoadingState.0NBtvQFg.js";import{S as q0}from"./SecondaryButton.DjJyItVx.js";import{D as W0}from"./DangerButton.PrtrqpTW.js";import Q0 from"./Toast.SYHAzbvX.js";import{_ as X0}from"./_plugin-vue_export-helper.BX07OBiL.js";import{c as K0}from"./createLucideIcon.3yDVQAYz.js";import{P as Y0,T as J0,a as Z0}from"./trash.BzwTNQJC.js";import{d as $0,c,a as e,e as o,g as b,b as x,j as l,i as u4,o as i,f,F as T,r as M}from"./index.BglzLLgy.js";import{r as d,t as n}from"./reactivity.esm-bundler.D5IypM4U.js";import"./index.CKgBWlpe.js";import"./index.DQmIdHOH.js";import"./index.B_yc9D3m.js";import"./index.CRcBj2l1.js";import"./index.CzaMXvxd.js";import"./ToggleSwitch.7R1jWLlg.js";import"./index.CLkTvMlq.js";import"./info.NGiRJ8VE.js";import"./index.BHZvt6Rq.js";import"./index.Tc5ZRw49.js";import"./index.CJuyVe3p.js";import"./index.CLs7nh7g.js";import"./index.D7_1DwEX.js";import"./index.irlSZ_18.js";import"./index.B0GjtQuk.js";import"./runtime-dom.esm-bundler.C-dfRCGi.js";import"./index.D6LCJW96.js";import"./index.CXDqTVvt.js";import"./AttributeValueInput.CSCilM_R.js";import"./InputNumber.BtccV88f.js";import"./Checkbox.B9YJ1Jrl.js";import"./QuickCreateBrand.BfvKWKIq.js";import"./utils.NP7rd5-k.js";import"./index.CBatl9QV.js";import"./index.BsVcoVHU.js";/* empty css                       */import"./index.BksvaNwr.js";/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const e4=K0("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),t4=$0({__name:"PartsTable",setup(e0,{expose:t}){t();const{loading:G,error:u,clearError:P,parts:h,partCategories:a,matching:r,partApplicability:q}=h0(),W=k0(),p=D0(),{getAccuracyLabel:t0,getAccuracySeverity:a0}=p0(),Q=d([]),k=d([]),X=d(0),w=d(25),E=d(0),s0=d([]),L=d({}),S=d(""),D=d(null),A=d([]),R=d(!1),N=d(null),U=d(!1),C=d(null),z=d(!1),I=d(null),V=d(!1),y=d(null),j=d(!1),g=d({accuracy:"EXACT_MATCH",notes:""}),o0=[{label:"Точное совпадение",value:"EXACT_MATCH"},{label:"Совпадение с примечаниями",value:"MATCH_WITH_NOTES"},{label:"Требуется модификация",value:"REQUIRES_MODIFICATION"},{label:"Частичное совпадение",value:"PARTIAL_MATCH"}],H=d("createdAt"),O=d(-1),v=async()=>{try{P();const s={skip:E.value*w.value,take:w.value,orderBy:{[H.value]:O.value===1?"asc":"desc"},include:{image:!0,partCategory:!0,attributes:!0,applicabilities:{include:{catalogItem:{include:{brand:!0}}}},equipmentApplicabilities:{include:{equipmentModel:{include:{brand:!0}}}}}};if(S.value.trim()&&(s.where={...s.where,name:{contains:S.value.trim(),mode:"insensitive"}}),D.value){const _=typeof D.value=="object"?D.value.id:D.value;s.where={...s.where,partCategoryId:_}}const m=await h.findMany(s);m&&(Q.value=m);const F=await h.findMany({where:s.where,select:{id:!0}});F&&(X.value=F.length)}catch(s){console.error("Ошибка загрузки запчастей:",s)}},K=async()=>{try{const s=await a.findMany({orderBy:{name:"asc"}});s&&(k.value=s)}catch(s){console.error("Ошибка загрузки категорий:",s)}},Y=async s=>{try{const m=await w0.partAttributes.findByPartId.query({partId:s});m&&(L.value[s]=m)}catch(m){console.error("Ошибка загрузки атрибутов запчасти:",m)}},r0=s=>{E.value=s.page,w.value=s.rows,v()},l0=s=>{H.value=s.sortField,O.value=s.sortOrder,v()},i0=s=>{const m=s.data.id;L.value[m]||Y(m)},n0=()=>{v()},c0=()=>{E.value=0,v()};let B;const d0=()=>{clearTimeout(B),B=setTimeout(()=>{E.value=0,v()},500)},m0=s=>{N.value=s,R.value=!0},f0=s=>{W.confirmDelete("запчасть",async()=>{try{await h.delete({where:{id:s.id}}),v()}catch{p.error("Ошибка","Не удалось удалить запчасть",5e3)}})},g0=s=>{v(),N.value=null,R.value=!1},v0=s=>{C.value=s,U.value=!0,J()},J=async()=>{if(C.value){z.value=!0,I.value=null;try{const s=await r.findMatchingCatalogItems({partId:C.value.id});I.value=s?s.candidates||[]:[]}catch(s){console.error("Ошибка подбора для Part:",s),I.value=[]}finally{z.value=!1}}},x0=s=>{y.value=s,g.value.accuracy=s.accuracySuggestion,g.value.notes="";const m=(s.details||[]).find(_=>String(_.kind).includes("NEAR")||String(_.kind).includes("LEGACY"));m?.notes&&(g.value.notes=m.notes),(s.details||[]).find(_=>_.kind==="NUMBER_WITHIN_TOLERANCE")&&!g.value.notes&&(g.value.notes="Совпадение по допуску"),V.value=!0},Z=()=>{V.value=!1,y.value=null,g.value.accuracy="EXACT_MATCH",g.value.notes=""},C0=async()=>{if(!(!C.value||!y.value)){j.value=!0;try{await q.upsert({where:{partId_catalogItemId:{partId:C.value.id,catalogItemId:y.value.catalogItem.id}},create:{partId:C.value.id,catalogItemId:y.value.catalogItem.id,accuracy:g.value.accuracy,notes:g.value.notes||void 0},update:{accuracy:g.value.accuracy,notes:g.value.notes||void 0}}),U.value=!1,V.value=!1,p.success("Успешно","Позиция привязана к группе"),v(),Z()}catch(s){console.error("Ошибка привязки:",s),p.error("Ошибка","Не удалось привязать позицию")}finally{j.value=!1}}},y0=s=>new Date(s).toLocaleDateString("ru-RU",{day:"2-digit",month:"2-digit",year:"numeric"}),_0=s=>({MM:"мм",INCH:"дюймы",FT:"футы",G:"г",KG:"кг",T:"т",LB:"фунты",ML:"мл",L:"л",GAL:"галлоны",PCS:"шт",SET:"комплект",PAIR:"пара",BAR:"бар",PSI:"PSI",KW:"кВт",HP:"л.с.",NM:"Н⋅м",RPM:"об/мин",C:"°C",F:"°F",PERCENT:"%"})[s]||s,b0=s=>{const m=s.query.toLowerCase();m?A.value=k.value.filter(F=>F.name.toLowerCase().includes(m)):A.value=k.value},$=()=>{A.value=k.value};u4(()=>{K(),v(),$()});const u0={loading:G,error:u,clearError:P,partsApi:h,partCategories:a,matching:r,partApplicability:q,confirm:W,toast:p,getAccuracyLabel:t0,getAccuracySeverity:a0,parts:Q,categories:k,totalCount:X,pageSize:w,currentPage:E,expandedRows:s0,partAttributes:L,searchQuery:S,selectedCategory:D,categorySuggestions:A,editDialogVisible:R,selectedPartForEdit:N,matchingDialogVisible:U,selectedPartForMatching:C,matchingLoading:z,matchingResults:I,showLinkConfirmDialog:V,selectedLinkCandidate:y,linking:j,linkConfirmForm:g,accuracyOptions:o0,sortField:H,sortOrder:O,loadParts:v,loadCategories:K,loadPartAttributes:Y,onPageChange:r0,onSort:l0,onRowExpand:i0,refreshData:n0,applyFilters:c0,get searchTimeout(){return B},set searchTimeout(s){B=s},debouncedSearch:d0,editPart:m0,deletePart:f0,onPartSaved:g0,openMatching:v0,runPartMatching:J,openLinkConfirmDialog:x0,closeLinkConfirmDialog:Z,confirmLinkItem:C0,formatDate:y0,getUnitLabel:_0,filterCategories:b0,initializeCategorySuggestions:$,VCard:A0,VButton:I0,VInputText:V0,VDataTable:B0,get VColumn(){return T0},VTag:M0,VMessage:P0,VDialog:L0,PartWizard:S0,VConfirmDialog:E0,VAutoComplete:R0,VSelect:N0,VTextarea:U0,Icon:z0,MatchingDetailsGrid:F0,MatchingEmptyState:H0,MatchingLoadingState:j0,get RefreshCcwIcon(){return G0},get PlusIcon(){return Z0},SecondaryButton:q0,get LinkIcon(){return O0},get TrashIcon(){return J0},DangerButton:W0,get PencilIcon(){return Y0},Toast:Q0,get EyeIcon(){return e4}};return Object.defineProperty(u0,"__isScriptSetup",{enumerable:!1,value:!0}),u0}}),a4={class:"space-y-6"},s4={class:"flex items-center justify-between"},o4={class:"flex gap-3"},r4={href:"/admin/parts/create"},l4={class:"p-6"},i4={class:"grid grid-cols-1 gap-4 md:grid-cols-3"},n4={class:"flex-1"},c4={class:"flex items-end"},d4={class:"text-surface-600 dark:text-surface-400 text-sm"},m4={class:"text-surface-900 dark:text-surface-100 font-medium"},f4={class:"text-surface-900 dark:text-surface-100 font-medium"},g4={class:"text-surface-700 dark:text-surface-300 font-mono text-sm"},v4={class:"text-surface-900 dark:text-surface-100 font-medium"},x4=["href"],C4={class:"text-surface-600 dark:text-surface-400 mt-1 text-sm"},y4={key:1,class:"text-surface-500 dark:text-surface-400 text-sm"},_4={class:"flex items-center gap-2"},b4={class:"text-surface-600 dark:text-surface-400 text-sm"},h4={class:"flex gap-2"},k4=["href"],E4=["href"],D4={class:"bg-surface-50 dark:bg-surface-800 border-surface-200 dark:border-surface-700 border-t p-4"},F4={class:"grid grid-cols-1 gap-6 lg:grid-cols-2"},p4={class:"text-surface-900 dark:text-surface-100 mb-3 flex items-center gap-2 text-lg font-medium"},w4={key:0,class:"space-y-2"},A4={class:"flex-1"},I4={class:"text-surface-900 dark:text-surface-100 text-sm font-medium"},V4={key:0,class:"text-surface-500 dark:text-surface-400 mt-1 text-sm"},B4={class:"ml-3 text-right"},T4={class:"text-surface-700 dark:text-surface-300 font-medium"},M4={key:0,class:"text-surface-500 dark:text-surface-400 text-sm"},P4={key:1,class:"text-surface-500 dark:text-surface-400 py-6 text-center"},L4={class:"text-surface-900 dark:text-surface-100 mb-3 flex items-center gap-2 text-lg font-medium"},S4={key:0,class:"space-y-2"},R4={class:"flex items-start justify-between"},N4={class:"flex-1"},U4={class:"text-surface-900 dark:text-surface-100 font-medium"},z4={class:"text-surface-600 dark:text-surface-400 mt-1 text-sm"},j4={key:0,class:"text-surface-500 dark:text-surface-400 mt-1 text-sm"},H4={class:"ml-3"},O4={key:0,class:"bg-surface-100 dark:bg-surface-800 text-surface-600 dark:text-surface-400 mt-2 rounded p-2 text-sm"},G4={key:1,class:"text-surface-500 dark:text-surface-400 py-6 text-center"},q4={class:"lg:col-span-2"},W4={key:0,class:"grid grid-cols-1 gap-3 md:grid-cols-2"},Q4={class:"flex items-start justify-between"},X4={class:"flex-1"},K4={class:"text-surface-900 dark:text-surface-100 font-medium"},Y4={key:0,class:"text-surface-600 dark:text-surface-400 mt-1 text-sm"},J4={class:"ml-3"},Z4={key:0,class:"bg-surface-100 dark:bg-surface-800 text-surface-600 dark:text-surface-400 mt-2 rounded p-2 text-sm"},$4={key:1,class:"text-surface-500 dark:text-surface-400 py-6 text-center"},uu={class:"p-4"},eu={class:"mb-4 flex items-center justify-between"},tu={class:"font-semibold"},au={key:2,class:"space-y-3"},su={class:"grid grid-cols-1 items-start gap-3 p-4 md:grid-cols-3"},ou={class:"md:col-span-1"},ru={class:"font-mono font-semibold"},lu={class:"text-surface-500"},iu={class:"mt-2"},nu={class:"mt-3"},cu={class:"md:col-span-2"},du={key:0,class:"space-y-4"},mu={class:"bg-surface-50 dark:bg-surface-900 grid grid-cols-1 gap-4 rounded p-4 md:grid-cols-2"},fu={class:"font-semibold"},gu={class:"font-semibold"},vu={class:"text-sm"},xu={class:"space-y-3"},Cu={class:"flex justify-between"};function yu(e0,t,G,u,P,h){return i(),c("div",a4,[e("div",s4,[t[10]||(t[10]=e("div",null,[e("h2",{class:"text-surface-900 dark:text-surface-0 text-xl font-semibold"},"Запчасти"),e("p",{class:"text-surface-600 dark:text-surface-400 mt-1 text-sm"},"Управление группами взаимозаменяемости")],-1)),e("div",o4,[o(u.SecondaryButton,{onClick:u.refreshData,disabled:u.loading,outlined:""},{default:l(()=>[t[8]||(t[8]=f(" Обновить ")),o(u.RefreshCcwIcon,{class:"h-4 w-4"})]),_:1,__:[8]},8,["disabled"]),e("a",r4,[o(u.VButton,{outlined:""},{default:l(()=>[t[9]||(t[9]=f(" Создать ")),o(u.PlusIcon)]),_:1,__:[9]})])])]),o(u.VCard,null,{content:l(()=>[e("div",l4,[e("div",i4,[e("div",null,[t[11]||(t[11]=e("label",{class:"text-surface-700 dark:text-surface-300 mb-2 block text-sm font-medium"}," Поиск по названию ",-1)),o(u.VInputText,{modelValue:u.searchQuery,"onUpdate:modelValue":t[0]||(t[0]=a=>u.searchQuery=a),placeholder:"Введите название запчасти...",class:"w-full",onInput:u.debouncedSearch},null,8,["modelValue"])]),e("div",n4,[t[12]||(t[12]=e("label",{class:"text-surface-700 dark:text-surface-300 mb-2 block text-sm font-medium"}," Фильтр по категории ",-1)),o(u.VAutoComplete,{modelValue:u.selectedCategory,"onUpdate:modelValue":t[1]||(t[1]=a=>u.selectedCategory=a),suggestions:u.categorySuggestions,onComplete:u.filterCategories,"option-label":"name","option-value":"id",placeholder:"Поиск категории...",class:"w-full",dropdown:"","show-clear":"",onChange:u.applyFilters},null,8,["modelValue","suggestions"])]),e("div",c4,[e("div",d4,[e("div",null,[t[13]||(t[13]=f(" Всего запчастей: ")),e("span",m4,n(u.totalCount),1)]),e("div",null,[t[14]||(t[14]=f(" Показано: ")),e("span",f4,n(u.parts.length),1)])])])])])]),_:1}),o(u.VCard,null,{content:l(()=>[o(u.VDataTable,{value:u.parts,loading:u.loading,paginator:"",rows:u.pageSize,"total-records":u.totalCount,"rows-per-page-options":[10,25,50],lazy:"",onPage:u.onPageChange,onSort:u.onSort,"table-style":"min-width: 50rem",class:"p-datatable-sm","striped-rows":"",expandedRows:u.expandedRows,"onUpdate:expandedRows":t[2]||(t[2]=a=>u.expandedRows=a),onRowExpand:u.onRowExpand},{expansion:l(({data:a})=>[e("div",D4,[e("div",F4,[e("div",null,[e("h4",p4,[o(u.Icon,{name:"pi pi-list",class:"h-4 w-4 text-blue-600"}),t[16]||(t[16]=f(" Атрибуты запчасти "))]),u.partAttributes[a.id]?.length>0?(i(),c("div",w4,[(i(!0),c(T,null,M(u.partAttributes[a.id],r=>(i(),c("div",{key:r.id,class:"bg-surface-0 dark:bg-surface-900 border-surface-200 dark:border-surface-700 flex items-start justify-between rounded border p-3"},[e("div",A4,[e("div",I4,n(r.template?.title||"Без названия"),1),r.description?(i(),c("div",V4,n(r.description),1)):x("",!0)]),e("div",B4,[e("div",T4,n(r.value||"Не указано"),1),r.unit?(i(),c("div",M4,n(u.getUnitLabel(r.unit)),1)):x("",!0)])]))),128))])):(i(),c("div",P4,[o(u.Icon,{name:"pi pi-info-circle",class:"mb-2 inline-block text-2xl"}),t[17]||(t[17]=f(" Атрибуты не заданы "))]))]),e("div",null,[e("h4",L4,[o(u.Icon,{name:"pi pi-box",class:"h-4 w-4 text-green-600"}),t[18]||(t[18]=f(" Каталожные позиции "))]),a.applicabilities?.length>0?(i(),c("div",S4,[(i(!0),c(T,null,M(a.applicabilities,r=>(i(),c("div",{key:r.id,class:"bg-surface-0 dark:bg-surface-900 border-surface-200 dark:border-surface-700 rounded border p-3"},[e("div",R4,[e("div",N4,[e("div",U4,n(r.catalogItem?.sku||"N/A"),1),e("div",z4,n(r.catalogItem?.brand?.name||"Неизвестный бренд"),1),r.catalogItem?.description?(i(),c("div",j4,n(r.catalogItem.description),1)):x("",!0)]),e("div",H4,[o(u.VTag,{severity:u.getAccuracySeverity(r.accuracy),class:"text-sm"},{default:l(()=>[f(n(u.getAccuracyLabel(r.accuracy)),1)]),_:2},1032,["severity"])])]),r.notes?(i(),c("div",O4,[o(u.Icon,{name:"pi pi-info-circle",class:"mr-1 inline-block h-4 w-4"}),f(" "+n(r.notes),1)])):x("",!0)]))),128))])):(i(),c("div",G4,[o(u.Icon,{name:"pi pi-info-circle",class:"mb-2 inline-block text-2xl"}),t[19]||(t[19]=f(" Каталожные позиции не добавлены "))]))]),e("div",q4,[t[22]||(t[22]=e("h4",{class:"text-surface-900 dark:text-surface-100 mb-3 flex items-center gap-2 text-lg font-medium"},"Применимость к технике",-1)),a.equipmentApplicabilities?.length>0?(i(),c("div",W4,[(i(!0),c(T,null,M(a.equipmentApplicabilities,r=>(i(),c("div",{key:r.id,class:"bg-surface-0 dark:bg-surface-900 border-surface-200 dark:border-surface-700 rounded border p-3"},[e("div",Q4,[e("div",X4,[e("div",K4,n(r.equipmentModel?.name||"N/A"),1),r.equipmentModel?.brand?(i(),c("div",Y4,n(r.equipmentModel.brand.name),1)):x("",!0)]),e("div",J4,[o(u.VTag,{severity:"info",class:"text-sm"},{default:l(()=>t[20]||(t[20]=[f(" Техника ")])),_:1,__:[20]})])]),r.notes?(i(),c("div",Z4,[o(u.Icon,{name:"pi pi-info-circle",class:"mr-1 inline-block h-4 w-4"}),f(" "+n(r.notes),1)])):x("",!0)]))),128))])):(i(),c("div",$4,[o(u.Icon,{name:"pi pi-info-circle",class:"mb-2 inline-block text-2xl"}),t[21]||(t[21]=f(" Применимость к технике не указана "))]))])])])]),default:l(()=>[o(u.VColumn,{expander:"",style:{width:"50px"}}),o(u.VColumn,{field:"id",header:"ID",sortable:"",style:{width:"80px"}},{body:l(({data:a})=>[e("span",g4,"#"+n(a.id),1)]),_:1}),o(u.VColumn,{field:"name",header:"Название",sortable:"",style:{width:"30%"}},{body:l(({data:a})=>[e("div",null,[e("div",v4,[e("a",{href:`/admin/parts/${a.id}`,class:"hover:underline text-primary"},n(a.name||"Без названия"),9,x4)]),e("div",C4,"Уровень: "+n(a.level)+" | Путь: "+n(a.path),1)])]),_:1}),o(u.VColumn,{field:"partCategory.name",header:"Категория",style:{width:"20%"}},{body:l(({data:a})=>[a.partCategory?(i(),b(u.VTag,{key:0,severity:"info",class:"text-sm"},{default:l(()=>[f(n(a.partCategory.name),1)]),_:2},1024)):(i(),c("span",y4,"Не указана"))]),_:1}),o(u.VColumn,{header:"Детали",style:{width:"20%"}},{body:l(({data:a})=>[e("div",_4,[o(u.VTag,{severity:"secondary",class:"text-sm"},{default:l(()=>[f(n(u.partAttributes[a.id]?.length||a.attributes?.length||0)+" атр. ",1)]),_:2},1024),o(u.VTag,{severity:"success",class:"text-sm"},{default:l(()=>[f(n(a.applicabilities?.length||0)+" поз. ",1)]),_:2},1024)])]),_:1}),o(u.VColumn,{field:"createdAt",header:"Создано",sortable:"",style:{width:"120px"}},{body:l(({data:a})=>[e("span",b4,n(u.formatDate(a.createdAt)),1)]),_:1}),o(u.VColumn,{header:"Действия",style:{width:"140px"}},{body:l(({data:a})=>[e("div",h4,[e("a",{href:`/admin/parts/${a.id}`},[o(u.VButton,{size:"small",outlined:""},{default:l(()=>[o(u.EyeIcon,{class:"h-4 w-4"})]),_:1})],8,k4),e("a",{href:`/admin/parts/${a.id}/edit`},[o(u.VButton,{outlined:""},{default:l(()=>[o(u.PencilIcon,{class:"h-4 w-4"})]),_:1})],8,E4),o(u.VButton,{size:"small",severity:"secondary",outlined:"",onClick:r=>u.openMatching(a)},{default:l(()=>[t[15]||(t[15]=f(" Подобрать ")),o(u.LinkIcon,{class:"h-4 w-4"})]),_:2,__:[15]},1032,["onClick"]),o(u.DangerButton,{size:"small",outlined:"",onClick:r=>u.deletePart(a)},{default:l(()=>[o(u.TrashIcon,{class:"h-4 w-4"})]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["value","loading","rows","total-records","expandedRows"])]),_:1}),u.error?(i(),b(u.VMessage,{key:0,severity:"error",class:"mt-4"},{default:l(()=>[f(n(u.error),1)]),_:1})):x("",!0),o(u.VDialog,{visible:u.editDialogVisible,"onUpdate:visible":t[3]||(t[3]=a=>u.editDialogVisible=a),modal:"",header:"Редактировать запчасть",class:""},{default:l(()=>[u.selectedPartForEdit?(i(),b(u.PartWizard,{key:0,part:u.selectedPartForEdit,mode:"edit",onUpdated:u.onPartSaved},null,8,["part"])):x("",!0)]),_:1},8,["visible"]),o(u.VDialog,{visible:u.matchingDialogVisible,"onUpdate:visible":t[4]||(t[4]=a=>u.matchingDialogVisible=a),modal:"",header:"Подбор каталожных позиций",class:"w-full max-w-4xl"},{default:l(()=>[e("div",uu,[e("div",eu,[t[23]||(t[23]=e("div",{class:"text-surface-500 text-sm"},"Группа",-1)),e("div",tu,n(u.selectedPartForMatching?.name||"#"+u.selectedPartForMatching?.id),1),o(u.VButton,{severity:"secondary",outlined:"",size:"small",loading:u.matchingLoading,onClick:u.runPartMatching},{default:l(()=>[o(u.RefreshCcwIcon)]),_:1},8,["loading"])]),u.matchingLoading?(i(),b(u.MatchingLoadingState,{key:0,paddingClass:"py-8"})):!u.matchingResults||u.matchingResults.length===0?(i(),b(u.MatchingEmptyState,{key:1})):(i(),c("div",au,[(i(!0),c(T,null,M(u.matchingResults,a=>(i(),b(u.VCard,{key:a.catalogItem.id,class:"border"},{content:l(()=>[e("div",su,[e("div",ou,[e("div",ru,n(a.catalogItem.sku),1),e("div",lu,n(a.catalogItem.brand?.name),1),e("div",iu,[o(u.VTag,{value:u.getAccuracyLabel(a.accuracySuggestion),severity:u.getAccuracySeverity(a.accuracySuggestion)},null,8,["value","severity"])]),e("div",nu,[o(u.VButton,{size:"small",label:"Привязать",onClick:r=>u.openLinkConfirmDialog(a)},{icon:l(()=>[o(u.Icon,{name:"pi pi-link",class:"h-5 w-5"})]),_:2},1032,["onClick"])])]),e("div",cu,[t[24]||(t[24]=e("div",{class:"text-surface-500 mb-2 text-sm"},"Детали сопоставления",-1)),o(u.MatchingDetailsGrid,{details:a.details},null,8,["details"])])])]),_:2},1024))),128))]))])]),_:1},8,["visible"]),o(u.VDialog,{visible:u.showLinkConfirmDialog,"onUpdate:visible":t[7]||(t[7]=a=>u.showLinkConfirmDialog=a),modal:"",header:"Подтверждение привязки",class:"w-full max-w-3xl"},{footer:l(()=>[e("div",Cu,[o(u.VButton,{label:"Отмена",severity:"secondary",onClick:u.closeLinkConfirmDialog}),o(u.VButton,{label:"Создать связь",severity:"success",onClick:u.confirmLinkItem,loading:u.linking},null,8,["loading"])])]),default:l(()=>[u.selectedLinkCandidate?(i(),c("div",du,[e("div",mu,[e("div",null,[t[25]||(t[25]=e("div",{class:"text-surface-500 text-sm"},"Группа взаимозаменяемости",-1)),e("div",fu,n(u.selectedPartForMatching?.name||`Группа #${u.selectedPartForMatching?.id}`),1)]),e("div",null,[t[26]||(t[26]=e("div",{class:"text-surface-500 text-sm"},"Каталожная позиция",-1)),e("div",gu,n(u.selectedLinkCandidate.catalogItem.sku),1),e("div",vu,n(u.selectedLinkCandidate.catalogItem.brand?.name),1)])]),e("div",null,[t[27]||(t[27]=e("h3",{class:"mb-3 text-lg font-semibold"},"Детали сопоставления",-1)),o(u.MatchingDetailsGrid,{details:u.selectedLinkCandidate.details},null,8,["details"])]),e("div",xu,[e("div",null,[t[28]||(t[28]=e("label",{class:"text-surface-700 dark:text-surface-300 mb-2 block text-sm font-medium"}," Точность совпадения ",-1)),o(u.VSelect,{modelValue:u.linkConfirmForm.accuracy,"onUpdate:modelValue":t[5]||(t[5]=a=>u.linkConfirmForm.accuracy=a),options:u.accuracyOptions,"option-label":"label","option-value":"value",placeholder:"Выберите точность",class:"w-full"},null,8,["modelValue"])]),e("div",null,[t[29]||(t[29]=e("label",{class:"text-surface-700 dark:text-surface-300 mb-2 block text-sm font-medium"}," Примечания ",-1)),o(u.VTextarea,{modelValue:u.linkConfirmForm.notes,"onUpdate:modelValue":t[6]||(t[6]=a=>u.linkConfirmForm.notes=a),rows:"3",placeholder:"Дополнительная информация о совместимости...",class:"w-full"},null,8,["modelValue"]),t[30]||(t[30]=e("small",{class:"text-surface-500"}," Укажите особенности применения, ограничения или условия замены ",-1))])])])):x("",!0)]),_:1},8,["visible"]),o(u.VConfirmDialog),o(u.Toast)])}const ye=X0(t4,[["render",yu]]);export{ye as default};
