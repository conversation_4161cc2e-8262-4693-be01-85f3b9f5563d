import { renderers } from './renderers.mjs';
import { c as createExports, s as serverEntrypointModule } from './chunks/_@astrojs-ssr-adapter_C9YiZH-u.mjs';
import { manifest } from './manifest_B37eRlrD.mjs';

const serverIslandMap = new Map();;

const _page0 = () => import('./pages/_image.astro.mjs');
const _page1 = () => import('./pages/account.astro.mjs');
const _page2 = () => import('./pages/brands.astro.mjs');
const _page3 = () => import('./pages/catalog/brands/_slug_.astro.mjs');
const _page4 = () => import('./pages/catalog/categories/_slug_.astro.mjs');
const _page5 = () => import('./pages/catalog/parts/_id_.astro.mjs');
const _page6 = () => import('./pages/catalog.astro.mjs');
const _page7 = () => import('./pages/categories.astro.mjs');
const _page8 = () => import('./pages/login.astro.mjs');
const _page9 = () => import('./pages/register.astro.mjs');
const _page10 = () => import('./pages/search.astro.mjs');
const _page11 = () => import('./pages/index.astro.mjs');
const pageMap = new Map([
    ["node_modules/astro/dist/assets/endpoint/node.js", _page0],
    ["src/pages/account.astro", _page1],
    ["src/pages/brands.astro", _page2],
    ["src/pages/catalog/brands/[slug].astro", _page3],
    ["src/pages/catalog/categories/[slug].astro", _page4],
    ["src/pages/catalog/parts/[id].astro", _page5],
    ["src/pages/catalog.astro", _page6],
    ["src/pages/categories.astro", _page7],
    ["src/pages/login.astro", _page8],
    ["src/pages/register.astro", _page9],
    ["src/pages/search.astro", _page10],
    ["src/pages/index.astro", _page11]
]);

const _manifest = Object.assign(manifest, {
    pageMap,
    serverIslandMap,
    renderers,
    actions: () => import('./_noop-actions.mjs'),
    middleware: () => import('./_astro-internal_middleware.mjs')
});
const _args = {
    "mode": "standalone",
    "client": "file:///D:/Dev/parttec/site/dist/client/",
    "server": "file:///D:/Dev/parttec/site/dist/server/",
    "host": false,
    "port": 4323,
    "assets": "_astro",
    "experimentalStaticHeaders": false
};
const _exports = createExports(_manifest, _args);
const handler = _exports['handler'];
const startServer = _exports['startServer'];
const options = _exports['options'];
const _start = 'start';
if (Object.prototype.hasOwnProperty.call(serverEntrypointModule, _start)) {
	serverEntrypointModule[_start](_manifest, _args);
}

export { handler, options, pageMap, startServer };
