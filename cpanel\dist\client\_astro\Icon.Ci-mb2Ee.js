import{u as i}from"./runtime-dom.esm-bundler.C-dfRCGi.js";/* empty css                       */import{_ as p}from"./_plugin-vue_export-helper.BX07OBiL.js";import{d as l,c as F,o as m,m as _,h as r}from"./index.BglzLLgy.js";const f=l({__name:"Icon",props:{name:{},size:{default:16},color:{}},setup(t,{expose:n}){n(),i(C=>({b098c906:c.value}));const e=t,o={plus:"➕",edit:"✏️",trash:"🗑️",search:"🔍",filter:"🔽",check:"✅",x:"❌","chevron-up":"⬆️","chevron-down":"⬇️",folder:"📁",ruler:"📏",tag:"🏷️",template:"📋",info:"ℹ️",target:"🎯",grid:"⊞",list:"☰"},s=r(()=>"inline-block"),u=r(()=>({fontSize:typeof e.size=="number"?`${e.size}px`:e.size,color:e.color})),c=r(()=>o[e.name]||"?"),a={props:e,iconMap:o,iconClass:s,iconStyle:u,iconContent:c};return Object.defineProperty(a,"__isScriptSetup",{enumerable:!1,value:!0}),a}});function d(t,n,e,o,s,u){return m(),F("i",_({class:o.iconClass,style:o.iconStyle},t.$attrs),null,16)}const v=p(f,[["render",d],["__scopeId","data-v-ae567aec"]]);export{v as I};
