# Project Structure

## Root Level Organization

```
parttec3/
├── api/                    # Backend API server
├── cpanel/                 # Control panel frontend (Vue + Astro)
├── site/                   # Public website (React + Astro)
├── docs/                   # Project documentation
├── .kiro/                  # Kiro AI assistant configuration
├── .git/                   # Git repository
└── package.json           # Root project scripts
```

## Backend Structure (api/)

```
api/
├── routers/               # tRPC route handlers
├── services/              # Business logic layer
├── lib/                   # Utility functions and helpers
├── types/                 # TypeScript type definitions
├── prisma/                # Database schema and migrations
├── generated/             # Auto-generated files (ZenStack, Zod, tRPC)
├── schemas/               # ZModel schema files
├── scripts/               # Database seeding and utility scripts
├── uploads/               # File upload storage
├── schema.zmodel          # Main ZenStack schema
├── *.zmodel               # Individual schema modules
├── index.ts               # Server entry point
├── trpc.ts                # tRPC configuration
├── auth.ts                # Authentication setup
├── db.ts                  # Database connection
└── router.ts              # Main router configuration
```

## Control Panel Structure (cpanel/)

```
cpanel/
├── src/
│   ├── components/        # Vue components
│   ├── layouts/           # Astro layout templates
│   ├── pages/             # Astro pages (file-based routing)
│   ├── plugins/           # Vue plugins (PrimeVue setup)
│   ├── volt/              # Volt UI component library
│   ├── lib/               # Utility functions
│   ├── composables/       # Vue composables
│   ├── types/             # TypeScript definitions
│   ├── styles/            # Global styles and Tailwind
│   └── utils/             # Helper utilities
├── public/                # Static assets
├── astro.config.mjs       # Astro configuration
└── package.json           # Dependencies and scripts
```

## Public Site Structure (site/)

```
site/
├── src/
│   ├── components/        # React components
│   ├── pages/             # Astro pages (file-based routing)
│   ├── layouts/           # Page layouts
│   └── lib/               # Utilities and helpers
├── public/                # Static assets
├── astro.config.mjs       # Astro configuration
└── package.json           # Dependencies and scripts
```

## Key Configuration Files

- **Root package.json** - Orchestrates development commands across all services
- **api/schema.zmodel** - Main database schema definition
- ***/astro.config.mjs** - Astro framework configuration with integrations
- **.env files** - Environment variables (not tracked in git)
- **tsconfig.json** - TypeScript configuration per project

## Development Workflow

1. **Schema Changes** - Modify `.zmodel` files in `api/schemas/`
2. **Code Generation** - ZenStack generates Prisma, Zod, and tRPC types
3. **Database Migration** - Prisma handles schema migrations
4. **API Development** - Add routes in `api/routers/`
5. **Frontend Integration** - Use generated tRPC client in frontends

## File Naming Conventions

- **Components** - PascalCase (e.g., `UserProfile.vue`, `PartCard.tsx`)
- **Pages** - kebab-case (e.g., `part-details.astro`, `user-settings.vue`)
- **Utilities** - camelCase (e.g., `formatDate.ts`, `apiClient.ts`)
- **Schema files** - snake_case (e.g., `user_schema.zmodel`, `catalog_schema.zmodel`)

## Import Aliases

Both frontend applications use path aliases:
- `@/` - src directory root
- `@/components` - component directory
- `@/lib` - utility functions
- `@/types` - type definitions