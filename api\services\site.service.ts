import { z } from 'zod'
import type { Context } from '../trpc'
import { SearchService } from './search.service'

// ===== Вспомогательные утилиты: IP, rate-limit, логирование (на уровне сервиса) =====
const RATE_LIMIT_PER_MIN = 60
const rateMap = new Map<string, { count: number; resetAt: number }>()

function getClientIp(req: Request) {
  const xf = req.headers.get('x-forwarded-for') || ''
  const xr = req.headers.get('x-real-ip') || ''
  const ip = xf.split(',')[0].trim() || xr || 'local'
  return ip
}

function assertRateLimit(ctx: Context, key: string) {
  const ip = getClientIp(ctx.req as unknown as Request)
  const now = Date.now()
  const bucketKey = `${ip}:${key}`
  const rec = rateMap.get(bucketKey)
  if (!rec || now > rec.resetAt) {
    rateMap.set(bucketKey, { count: 1, resetAt: now + 60_000 })
    return
  }
  rec.count += 1
  if (rec.count > RATE_LIMIT_PER_MIN) {
    throw new Error('Rate limit exceeded')
  }
}

function logSiteAccess(route: string, ctx: Context, info?: any) {
  const ip = getClientIp(ctx.req as unknown as Request)
  const userId = (ctx.user as any)?.id || 'anon'
  console.log(`[site:${route}] ip=${ip} user=${userId} info=${JSON.stringify(info ?? {})}`)
}

// ===== Схемы ввода (можно вынести в отдельный файл при расширении) =====
export const AttributeFilterInput = z.object({
  templateId: z.number(),
  values: z.array(z.string()).optional(),
  minValue: z.number().optional(),
  maxValue: z.number().optional(),
})

export const SearchCatalogItemsSiteInput = z.object({
  search: z.string().optional(),
  sku: z.string().optional(),
  brandIds: z.array(z.number()).optional(),
  categoryIds: z.array(z.number()).optional(),
  isOemOnly: z.boolean().optional(),
  accuracy: z.array(z.enum(['EXACT_MATCH','MATCH_WITH_NOTES','REQUIRES_MODIFICATION','PARTIAL_MATCH'])).optional(),
  attributeFilters: z.array(AttributeFilterInput).optional(),
  limit: z.number().min(1).max(50).default(20),
  offset: z.number().min(0).default(0),
  sortBy: z.enum(['sku','description','createdAt','updatedAt']).default('updatedAt'),
  sortDir: z.enum(['asc','desc']).default('desc'),
})

export type SearchCatalogItemsSiteInputType = z.infer<typeof SearchCatalogItemsSiteInput>

export class SiteService {
  static async searchCatalogItems(ctx: Context, input: SearchCatalogItemsSiteInputType) {
    assertRateLimit(ctx, 'search.catalogItems')
    logSiteAccess('search.catalogItems', ctx, { hasSearch: !!input.search, brandIds: input.brandIds?.length ?? 0, categoryIds: input.categoryIds?.length ?? 0 })

    const serviceInput: any = {
      search: input.search,
      sku: input.sku,
      brandId: undefined,
      attributeFilters: input.attributeFilters?.map((f) => ({
        templateId: f.templateId,
        minValue: f.minValue,
        maxValue: f.maxValue,
        values: f.values,
        matchType: f.values && f.values.length ? 'in' : (f.minValue !== undefined || f.maxValue !== undefined) ? 'range' : 'exact',
      })),
      limit: input.limit,
      offset: input.offset,
      orderBy: input.sortBy,
      orderDir: input.sortDir,
    }

    const result = await SearchService.searchCatalogItems(serviceInput)

    // Фильтруем под правила клиентов
    const filtered = (result.items || []).filter((ci: any) => {
      if (ci.isPublic !== true) return false
      if (input.brandIds && input.brandIds.length && !input.brandIds.includes(ci.brandId)) return false
      if (input.isOemOnly && !ci.brand?.isOem) return false
      if (input.categoryIds && input.categoryIds.length) {
        const matchCategory = (ci.applicabilities || []).some((ap: any) => input.categoryIds!.includes(ap.part?.partCategoryId))
        if (!matchCategory) return false
      }
      if (input.accuracy && input.accuracy.length) {
        const matchAcc = (ci.applicabilities || []).some((ap: any) => input.accuracy!.includes(ap.accuracy))
        if (!matchAcc) return false
      }
      return true
    })

    // Маппинг к безопасной форме
    const safeItems = filtered.map((ci: any) => ({
      id: ci.id,
      sku: ci.sku,
      description: ci.description,
      brandId: ci.brandId,
      brand: ci.brand ? { id: ci.brand.id, name: ci.brand.name, slug: ci.brand.slug, country: ci.brand.country, isOem: ci.brand.isOem } : null,
      isPublic: true,
      attributes: (ci.attributes || []).map((a: any) => ({ id: a.id, value: a.value, numericValue: a.numericValue, templateId: a.templateId, template: a.template && { id: a.template.id, name: a.template.name, title: a.template.title, dataType: a.template.dataType, unit: a.template.unit } })),
      image: ci.image ? { id: ci.image.id, fileName: ci.image.fileName, url: ci.image.url, mimeType: ci.image.mimeType, fileSize: ci.image.fileSize, createdAt: ci.image.createdAt, updatedAt: ci.image.updatedAt } : null,
      mediaAssets: (ci.mediaAssets || []).map((m: any) => ({ id: m.id, fileName: m.fileName, url: m.url, mimeType: m.mimeType, fileSize: m.fileSize, createdAt: m.createdAt, updatedAt: m.updatedAt })),
      applicabilities: (ci.applicabilities || []).map((ap: any) => ({
        id: ap.id,
        partId: ap.partId,
        catalogItemId: ap.catalogItemId,
        accuracy: ap.accuracy,
        notes: null,
        part: ap.part && {
          id: ap.part.id,
          name: ap.part.name,
          level: ap.part.level,
          path: ap.part.path,
          partCategoryId: ap.part.partCategoryId,
          partCategory: ap.part.partCategory && { id: ap.part.partCategory.id, name: ap.part.partCategory.name, slug: ap.part.partCategory.slug, level: ap.part.partCategory.level, path: ap.part.partCategory.path },
          image: ap.part.image ? { id: ap.part.image.id, fileName: ap.part.image.fileName, url: ap.part.image.url, mimeType: ap.part.image.mimeType, fileSize: ap.part.image.fileSize, createdAt: ap.part.image.createdAt, updatedAt: ap.part.image.updatedAt } : null,
          mediaAssets: (ap.part.mediaAssets || []).map((m: any) => ({ id: m.id, fileName: m.fileName, url: m.url, mimeType: m.mimeType, fileSize: m.fileSize, createdAt: m.createdAt, updatedAt: m.updatedAt })),
          attributes: (ap.part.attributes || []).map((a: any) => ({ id: a.id, value: a.value, numericValue: a.numericValue, templateId: a.templateId, template: a.template && { id: a.template.id, name: a.template.name, title: a.template.title, dataType: a.template.dataType, unit: a.template.unit } })),
          createdAt: ap.part.createdAt,
          updatedAt: ap.part.updatedAt,
        },
      })),
    }))

    return { items: safeItems, total: filtered.length, limit: input.limit, offset: input.offset }
  }

  static async brands(ctx: Context, input?: { take?: number }) {
    assertRateLimit(ctx, 'catalog.brands')
    const db = ctx.db
    const items = await db.brand.findMany({ orderBy: { name: 'asc' }, take: input?.take ?? 100 })
    return items.map((b) => ({ id: b.id, name: b.name, slug: b.slug, country: b.country, isOem: b.isOem }))
  }

  static async categories(ctx: Context, input?: { rootOnly?: boolean; take?: number }) {
    assertRateLimit(ctx, 'catalog.categories')
    const db = ctx.db
    const where = input?.rootOnly ? { level: 0 } : {}
    const cats = await db.partCategory.findMany({ where, orderBy: { name: 'asc' }, take: input?.take ?? 200 })
    return cats.map((c) => ({ id: c.id, name: c.name, slug: c.slug, level: c.level, path: c.path }))
  }

  static async attributeTemplates(ctx: Context, input?: { take?: number }) {
    assertRateLimit(ctx, 'attributes.templates')
    const db = ctx.db
    const items = await db.attributeTemplate.findMany({ orderBy: { name: 'asc' }, take: input?.take ?? 100 })
    return items.map((t) => ({ id: t.id, name: t.name, title: t.title, dataType: t.dataType, unit: t.unit, isRequired: t.isRequired, allowedValues: t.allowedValues, tolerance: t.tolerance }))
  }

  static async attributeStats(ctx: Context, input: { templateId: number }) {
    assertRateLimit(ctx, 'attributes.stats')
    const stats = await SearchService.getAttributeStats({ templateId: input.templateId, entityType: 'catalogItem' } as any)
    if (!stats) return null
    return { templateId: stats.templateId as number, min: stats.min as number, max: stats.max as number, avg: stats.avg as number }
  }
}

