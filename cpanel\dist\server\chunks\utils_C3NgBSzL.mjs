import 'clsx';

function fileToBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(String(reader.result));
    reader.onerror = (e) => reject(e);
    reader.readAsDataURL(file);
  });
}
function slugify(source) {
  if (!source) return "";
  const map = {
    а: "a",
    б: "b",
    в: "v",
    г: "g",
    д: "d",
    е: "e",
    ё: "e",
    ж: "zh",
    з: "z",
    и: "i",
    й: "y",
    к: "k",
    л: "l",
    м: "m",
    н: "n",
    о: "o",
    п: "p",
    р: "r",
    с: "s",
    т: "t",
    у: "u",
    ф: "f",
    х: "h",
    ц: "ts",
    ч: "ch",
    ш: "sh",
    щ: "sch",
    ъ: "",
    ы: "y",
    ь: "",
    э: "e",
    ю: "yu",
    я: "ya"
  };
  const lower = source.trim().toLowerCase();
  let transliterated = "";
  for (const char of lower) {
    const code = char.charCodeAt(0);
    if (code >= 97 && code <= 122 || code >= 48 && code <= 57) {
      transliterated += char;
      continue;
    }
    if (map[char] !== void 0) {
      transliterated += map[char];
      continue;
    }
    if (/\s|[_]+/.test(char)) {
      transliterated += "-";
      continue;
    }
  }
  return transliterated.replace(/[^a-z0-9-]/g, "-").replace(/-{2,}/g, "-").replace(/^-+|-+$/g, "");
}
function resolveMediaUrl(url) {
  if (!url) return void 0;
  if (/^https?:\/\//i.test(url)) return url;
  const apiBase = typeof window !== "undefined" ? "http://localhost:3000" : process.env.API_URL ? String(process.env.API_URL).replace(/\/$/, "") : "http://localhost:3000";
  if (url.startsWith("/")) return `${apiBase}${url}`;
  return `${apiBase}/${url}`;
}

export { fileToBase64 as f, resolveMediaUrl as r, slugify as s };
