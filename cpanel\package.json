{"name": "frontend", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro"}, "dependencies": {"@astrojs/node": "^9.3.0", "@astrojs/vue": "^5.1.0", "@stagewise-plugins/vue": "^0.6.2", "@stagewise/toolbar-vue": "^0.6.2", "@tailwindcss/vite": "^4.1.11", "@tanstack/vue-query": "^5.85.3", "@trpc/client": "^11.4.4", "@trpc/server": "^11.4.4", "astro": "^5.12.1", "better-auth": "^1.3.2", "clsx": "^2.1.1", "lucide-vue-next": "^0.525.0", "motion-v": "^1.5.0", "motion-vue": "^0.0.1", "primevue": "^4.3.6", "superjson": "^2.2.2", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "tailwindcss-primeui": "^0.6.1", "vue": "^3.5.17"}, "devDependencies": {"@biomejs/biome": "2.1.2", "eslint": "^9.31.0", "eslint-plugin-vue": "^10.3.0", "prettier": "3.6.2", "prettier-plugin-astro": "0.14.1", "prettier-plugin-tailwindcss": "0.6.14"}}