import { e as createComponent, k as renderComponent, r as renderTemplate } from '../../chunks/astro/server_DbndhTWv.mjs';
import 'kleur/colors';
import { defineComponent, useSSRContext, ref, mergeProps, withCtx, createTextVNode, toDisplayString, createVNode, createBlock, createCommentVNode, openBlock, Fragment, renderList } from 'vue';
import { u as useTrpc } from '../../chunks/useTrpc_B-zNiBto.mjs';
import { V as VCard } from '../../chunks/Card_BWxK5e93.mjs';
import { C as Checkbox } from '../../chunks/Checkbox_aVHeO0Hn.mjs';
import { I as InputText } from '../../chunks/InputText_CtReD0EA.mjs';
import { V as VButton } from '../../chunks/Button_CuwpNmer.mjs';
import { S as Select } from '../../chunks/Select_Bk34xTKM.mjs';
import { D as DangerButton } from '../../chunks/DangerButton_CkG9WtM3.mjs';
import { ssrRenderAttrs, ssrRenderComponent, ssrInterpolate, ssrRenderList } from 'vue/server-renderer';
import { _ as _export_sfc } from '../../chunks/ClientRouter_B8Zzhk9G.mjs';
import { $ as $$AdminLayout } from '../../chunks/AdminLayout_b20tykPC.mjs';
export { r as renderers } from '../../chunks/_@astro-renderers_CicWY1rm.mjs';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "ImportExportPanel",
  setup(__props, { expose: __expose }) {
    __expose();
    const { importExport, excelImport } = useTrpc();
    const include = ref({
      Brand: true,
      PartCategory: true,
      AttributeGroup: false,
      AttributeTemplate: true,
      AttributeSynonymGroup: false,
      AttributeSynonym: false,
      Part: true,
      CatalogItem: true,
      EquipmentModel: true,
      PartAttribute: false,
      CatalogItemAttribute: false,
      EquipmentModelAttribute: false,
      PartApplicability: false,
      EquipmentApplicability: false
    });
    const brandSlugsInput = ref("");
    const onConflict = ref("upsert");
    const createMissingRefs = ref(false);
    const file = ref(null);
    const loading = ref(false);
    const loadingTemplate = ref(false);
    const currentAction = ref(null);
    const dryRunResult = ref(null);
    const conflictOptions = [
      { label: "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u0438\u043B\u0438 \u043E\u0431\u043D\u043E\u0432\u0438\u0442\u044C (upsert)", value: "upsert" },
      { label: "\u0422\u043E\u043B\u044C\u043A\u043E \u043E\u0431\u043D\u043E\u0432\u0438\u0442\u044C (update_only)", value: "update_only" },
      { label: "\u041F\u0440\u043E\u043F\u0443\u0441\u0442\u0438\u0442\u044C (skip)", value: "skip" },
      { label: "\u041E\u0448\u0438\u0431\u043A\u0430 (error)", value: "error" }
    ];
    const formatFileSize = (bytes) => {
      if (bytes === 0) return "0 Bytes";
      const k = 1024;
      const sizes = ["Bytes", "KB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    };
    const clearFile = () => {
      file.value = null;
      dryRunResult.value = null;
    };
    const onExport = async () => {
      loading.value = true;
      try {
        const brandSlugs = brandSlugsInput.value.split(",").map((s) => s.trim()).filter(Boolean);
        const res = await importExport.exportXlsx({
          include: include.value,
          filters: { brandSlugs },
          meta: {
            createMissingRefs: createMissingRefs.value,
            onConflict: onConflict.value
          }
        });
        if (!res) return;
        const blob = b64toBlob(res.base64, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = res.fileName;
        a.click();
        URL.revokeObjectURL(url);
      } catch (error) {
        console.error("Export failed:", error);
      } finally {
        loading.value = false;
      }
    };
    const onExportTemplate = async () => {
      loadingTemplate.value = true;
      try {
        const res = await importExport.exportTemplate({
          include: include.value
        });
        if (!res) return;
        const blob = b64toBlob(res.base64, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = res.fileName;
        a.click();
        URL.revokeObjectURL(url);
      } catch (error) {
        console.error("Export template failed:", error);
      } finally {
        loadingTemplate.value = false;
      }
    };
    const onFileChange = (e) => {
      const target = e.target;
      file.value = target.files?.[0] ?? null;
      dryRunResult.value = null;
    };
    function fileToBase64(file2) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(String(reader.result).split(",")[1] || "");
        reader.onerror = reject;
        reader.readAsDataURL(file2);
      });
    }
    const onDryRun = async () => {
      if (!file.value) return;
      currentAction.value = "dryRun";
      loading.value = true;
      try {
        const base64 = await fileToBase64(file.value);
        const res = await useTrpc().excelImport.dryRun({
          base64,
          overrides: {
            createMissingRefs: createMissingRefs.value,
            onConflict: onConflict.value
          }
        });
        dryRunResult.value = res;
      } catch (error) {
        console.error("Dry run failed:", error);
      } finally {
        loading.value = false;
        currentAction.value = null;
      }
    };
    const onExecute = async () => {
      if (!file.value) return;
      currentAction.value = "execute";
      loading.value = true;
      try {
        const base64 = await fileToBase64(file.value);
        const res = await useTrpc().excelImport.execute({
          base64,
          overrides: {
            createMissingRefs: createMissingRefs.value,
            onConflict: onConflict.value
          }
        });
        if (!res) return;
        dryRunResult.value = res;
        if (res.reportBase64) downloadReport(res.reportBase64);
      } catch (error) {
        console.error("Execute failed:", error);
      } finally {
        loading.value = false;
        currentAction.value = null;
      }
    };
    function downloadReport(base64) {
      const blob = b64toBlob(base64, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `import-report-${Date.now()}.xlsx`;
      a.click();
      URL.revokeObjectURL(url);
    }
    function b64toBlob(base64, contentType = "", sliceSize = 512) {
      const byteCharacters = atob(base64);
      const byteArrays = [];
      for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
        const slice = byteCharacters.slice(offset, offset + sliceSize);
        const byteNumbers = new Array(slice.length);
        for (let i = 0; i < slice.length; i++) {
          byteNumbers[i] = slice.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        byteArrays.push(byteArray);
      }
      const blob = new Blob(byteArrays, { type: contentType });
      return blob;
    }
    const __returned__ = { importExport, excelImport, include, brandSlugsInput, onConflict, createMissingRefs, file, loading, loadingTemplate, currentAction, dryRunResult, conflictOptions, formatFileSize, clearFile, onExport, onExportTemplate, onFileChange, fileToBase64, onDryRun, onExecute, downloadReport, b64toBlob, VCard, VCheckbox: Checkbox, VInputText: InputText, VButton, VSelect: Select, DangerButton };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "w-full max-w-7xl" }, _attrs))}><div class="grid grid-cols-1 xl:grid-cols-3 gap-6">`);
  _push(ssrRenderComponent($setup["VCard"], { class: "p-6" }, {
    title: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<h2 class="text-lg font-semibold mb-4"${_scopeId}>\u042D\u043A\u0441\u043F\u043E\u0440\u0442 \u043A\u0430\u0442\u0430\u043B\u043E\u0433\u0430</h2>`);
      } else {
        return [
          createVNode("h2", { class: "text-lg font-semibold mb-4" }, "\u042D\u043A\u0441\u043F\u043E\u0440\u0442 \u043A\u0430\u0442\u0430\u043B\u043E\u0433\u0430")
        ];
      }
    }),
    content: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="space-y-4"${_scopeId}><div${_scopeId}><h3 class="text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}>\u041E\u0441\u043D\u043E\u0432\u043D\u044B\u0435 \u0441\u0443\u0449\u043D\u043E\u0441\u0442\u0438</h3><div class="grid grid-cols-1 gap-2"${_scopeId}><div class="flex items-center space-x-2"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["VCheckbox"], {
          modelValue: $setup.include.Brand,
          "onUpdate:modelValue": ($event) => $setup.include.Brand = $event,
          inputId: "brand",
          binary: true
        }, null, _parent2, _scopeId));
        _push2(`<label for="brand" class="text-sm"${_scopeId}>\u0411\u0440\u0435\u043D\u0434\u044B</label></div><div class="flex items-center space-x-2"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["VCheckbox"], {
          modelValue: $setup.include.PartCategory,
          "onUpdate:modelValue": ($event) => $setup.include.PartCategory = $event,
          inputId: "partCategory",
          binary: true
        }, null, _parent2, _scopeId));
        _push2(`<label for="partCategory" class="text-sm"${_scopeId}>\u041A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438 \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439</label></div><div class="flex items-center space-x-2"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["VCheckbox"], {
          modelValue: $setup.include.AttributeGroup,
          "onUpdate:modelValue": ($event) => $setup.include.AttributeGroup = $event,
          inputId: "attributeGroup",
          binary: true
        }, null, _parent2, _scopeId));
        _push2(`<label for="attributeGroup" class="text-sm"${_scopeId}>\u0413\u0440\u0443\u043F\u043F\u044B \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432</label></div><div class="flex items-center space-x-2"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["VCheckbox"], {
          modelValue: $setup.include.AttributeTemplate,
          "onUpdate:modelValue": ($event) => $setup.include.AttributeTemplate = $event,
          inputId: "attributeTemplate",
          binary: true
        }, null, _parent2, _scopeId));
        _push2(`<label for="attributeTemplate" class="text-sm"${_scopeId}>\u0428\u0430\u0431\u043B\u043E\u043D\u044B \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432</label></div><div class="flex items-center space-x-2"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["VCheckbox"], {
          modelValue: $setup.include.AttributeSynonymGroup,
          "onUpdate:modelValue": ($event) => $setup.include.AttributeSynonymGroup = $event,
          inputId: "synonymGroup",
          binary: true
        }, null, _parent2, _scopeId));
        _push2(`<label for="synonymGroup" class="text-sm"${_scopeId}>\u0413\u0440\u0443\u043F\u043F\u044B \u0441\u0438\u043D\u043E\u043D\u0438\u043C\u043E\u0432</label></div><div class="flex items-center space-x-2"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["VCheckbox"], {
          modelValue: $setup.include.AttributeSynonym,
          "onUpdate:modelValue": ($event) => $setup.include.AttributeSynonym = $event,
          inputId: "synonym",
          binary: true
        }, null, _parent2, _scopeId));
        _push2(`<label for="synonym" class="text-sm"${_scopeId}>\u0421\u0438\u043D\u043E\u043D\u0438\u043C\u044B \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432</label></div><div class="flex items-center space-x-2"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["VCheckbox"], {
          modelValue: $setup.include.Part,
          "onUpdate:modelValue": ($event) => $setup.include.Part = $event,
          inputId: "part",
          binary: true
        }, null, _parent2, _scopeId));
        _push2(`<label for="part" class="text-sm"${_scopeId}>\u0413\u0440\u0443\u043F\u043F\u044B \u0432\u0437\u0430\u0438\u043C\u043E\u0437\u0430\u043C\u0435\u043D\u044F\u0435\u043C\u043E\u0441\u0442\u0438</label></div><div class="flex items-center space-x-2"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["VCheckbox"], {
          modelValue: $setup.include.CatalogItem,
          "onUpdate:modelValue": ($event) => $setup.include.CatalogItem = $event,
          inputId: "catalogItem",
          binary: true
        }, null, _parent2, _scopeId));
        _push2(`<label for="catalogItem" class="text-sm"${_scopeId}>\u041A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u044B\u0435 \u043F\u043E\u0437\u0438\u0446\u0438\u0438</label></div><div class="flex items-center space-x-2"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["VCheckbox"], {
          modelValue: $setup.include.EquipmentModel,
          "onUpdate:modelValue": ($event) => $setup.include.EquipmentModel = $event,
          inputId: "equipmentModel",
          binary: true
        }, null, _parent2, _scopeId));
        _push2(`<label for="equipmentModel" class="text-sm"${_scopeId}>\u041C\u043E\u0434\u0435\u043B\u0438 \u0442\u0435\u0445\u043D\u0438\u043A\u0438</label></div></div></div><div${_scopeId}><h3 class="text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}>\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u0438 \u0441\u0432\u044F\u0437\u0438</h3><div class="grid grid-cols-1 gap-2"${_scopeId}><div class="flex items-center space-x-2"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["VCheckbox"], {
          modelValue: $setup.include.PartAttribute,
          "onUpdate:modelValue": ($event) => $setup.include.PartAttribute = $event,
          inputId: "partAttribute",
          binary: true
        }, null, _parent2, _scopeId));
        _push2(`<label for="partAttribute" class="text-sm"${_scopeId}>\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439</label></div><div class="flex items-center space-x-2"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["VCheckbox"], {
          modelValue: $setup.include.CatalogItemAttribute,
          "onUpdate:modelValue": ($event) => $setup.include.CatalogItemAttribute = $event,
          inputId: "catalogItemAttribute",
          binary: true
        }, null, _parent2, _scopeId));
        _push2(`<label for="catalogItemAttribute" class="text-sm"${_scopeId}>\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u043F\u043E\u0437\u0438\u0446\u0438\u0439</label></div><div class="flex items-center space-x-2"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["VCheckbox"], {
          modelValue: $setup.include.EquipmentModelAttribute,
          "onUpdate:modelValue": ($event) => $setup.include.EquipmentModelAttribute = $event,
          inputId: "equipmentModelAttribute",
          binary: true
        }, null, _parent2, _scopeId));
        _push2(`<label for="equipmentModelAttribute" class="text-sm"${_scopeId}>\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u0442\u0435\u0445\u043D\u0438\u043A\u0438</label></div><div class="flex items-center space-x-2"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["VCheckbox"], {
          modelValue: $setup.include.PartApplicability,
          "onUpdate:modelValue": ($event) => $setup.include.PartApplicability = $event,
          inputId: "partApplicability",
          binary: true
        }, null, _parent2, _scopeId));
        _push2(`<label for="partApplicability" class="text-sm"${_scopeId}>\u041F\u0440\u0438\u043C\u0435\u043D\u0438\u043C\u043E\u0441\u0442\u044C \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439</label></div><div class="flex items-center space-x-2"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["VCheckbox"], {
          modelValue: $setup.include.EquipmentApplicability,
          "onUpdate:modelValue": ($event) => $setup.include.EquipmentApplicability = $event,
          inputId: "equipmentApplicability",
          binary: true
        }, null, _parent2, _scopeId));
        _push2(`<label for="equipmentApplicability" class="text-sm"${_scopeId}>\u041F\u0440\u0438\u043C\u0435\u043D\u0438\u043C\u043E\u0441\u0442\u044C \u043A \u0442\u0435\u0445\u043D\u0438\u043A\u0435</label></div></div></div><div${_scopeId}><h3 class="text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}>\u0424\u0438\u043B\u044C\u0442\u0440\u044B</h3>`);
        _push2(ssrRenderComponent($setup["VInputText"], {
          modelValue: $setup.brandSlugsInput,
          "onUpdate:modelValue": ($event) => $setup.brandSlugsInput = $event,
          placeholder: "\u0411\u0440\u0435\u043D\u0434\u044B \u0447\u0435\u0440\u0435\u0437 \u0437\u0430\u043F\u044F\u0442\u0443\u044E (\u043D\u0430\u043F\u0440\u0438\u043C\u0435\u0440: cat,komatsu)",
          class: "w-full"
        }, null, _parent2, _scopeId));
        _push2(`</div><div class="flex flex-col gap-2"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["VButton"], {
          onClick: $setup.onExport,
          disabled: $setup.loading,
          loading: $setup.loading,
          class: "w-full"
        }, {
          default: withCtx((_2, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(`${ssrInterpolate($setup.loading ? "\u042D\u043A\u0441\u043F\u043E\u0440\u0442..." : "\u0421\u043A\u0430\u0447\u0430\u0442\u044C \u0434\u0430\u043D\u043D\u044B\u0435")}`);
            } else {
              return [
                createTextVNode(toDisplayString($setup.loading ? "\u042D\u043A\u0441\u043F\u043E\u0440\u0442..." : "\u0421\u043A\u0430\u0447\u0430\u0442\u044C \u0434\u0430\u043D\u043D\u044B\u0435"), 1)
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["VButton"], {
          onClick: $setup.onExportTemplate,
          disabled: $setup.loading,
          loading: $setup.loadingTemplate,
          severity: "secondary",
          outlined: "",
          class: "w-full"
        }, {
          default: withCtx((_2, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(`${ssrInterpolate($setup.loadingTemplate ? "\u0421\u043E\u0437\u0434\u0430\u043D\u0438\u0435..." : "\u0421\u043A\u0430\u0447\u0430\u0442\u044C \u0448\u0430\u0431\u043B\u043E\u043D")}`);
            } else {
              return [
                createTextVNode(toDisplayString($setup.loadingTemplate ? "\u0421\u043E\u0437\u0434\u0430\u043D\u0438\u0435..." : "\u0421\u043A\u0430\u0447\u0430\u0442\u044C \u0448\u0430\u0431\u043B\u043E\u043D"), 1)
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(`</div></div>`);
      } else {
        return [
          createVNode("div", { class: "space-y-4" }, [
            createVNode("div", null, [
              createVNode("h3", { class: "text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, "\u041E\u0441\u043D\u043E\u0432\u043D\u044B\u0435 \u0441\u0443\u0449\u043D\u043E\u0441\u0442\u0438"),
              createVNode("div", { class: "grid grid-cols-1 gap-2" }, [
                createVNode("div", { class: "flex items-center space-x-2" }, [
                  createVNode($setup["VCheckbox"], {
                    modelValue: $setup.include.Brand,
                    "onUpdate:modelValue": ($event) => $setup.include.Brand = $event,
                    inputId: "brand",
                    binary: true
                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                  createVNode("label", {
                    for: "brand",
                    class: "text-sm"
                  }, "\u0411\u0440\u0435\u043D\u0434\u044B")
                ]),
                createVNode("div", { class: "flex items-center space-x-2" }, [
                  createVNode($setup["VCheckbox"], {
                    modelValue: $setup.include.PartCategory,
                    "onUpdate:modelValue": ($event) => $setup.include.PartCategory = $event,
                    inputId: "partCategory",
                    binary: true
                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                  createVNode("label", {
                    for: "partCategory",
                    class: "text-sm"
                  }, "\u041A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438 \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439")
                ]),
                createVNode("div", { class: "flex items-center space-x-2" }, [
                  createVNode($setup["VCheckbox"], {
                    modelValue: $setup.include.AttributeGroup,
                    "onUpdate:modelValue": ($event) => $setup.include.AttributeGroup = $event,
                    inputId: "attributeGroup",
                    binary: true
                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                  createVNode("label", {
                    for: "attributeGroup",
                    class: "text-sm"
                  }, "\u0413\u0440\u0443\u043F\u043F\u044B \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432")
                ]),
                createVNode("div", { class: "flex items-center space-x-2" }, [
                  createVNode($setup["VCheckbox"], {
                    modelValue: $setup.include.AttributeTemplate,
                    "onUpdate:modelValue": ($event) => $setup.include.AttributeTemplate = $event,
                    inputId: "attributeTemplate",
                    binary: true
                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                  createVNode("label", {
                    for: "attributeTemplate",
                    class: "text-sm"
                  }, "\u0428\u0430\u0431\u043B\u043E\u043D\u044B \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432")
                ]),
                createVNode("div", { class: "flex items-center space-x-2" }, [
                  createVNode($setup["VCheckbox"], {
                    modelValue: $setup.include.AttributeSynonymGroup,
                    "onUpdate:modelValue": ($event) => $setup.include.AttributeSynonymGroup = $event,
                    inputId: "synonymGroup",
                    binary: true
                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                  createVNode("label", {
                    for: "synonymGroup",
                    class: "text-sm"
                  }, "\u0413\u0440\u0443\u043F\u043F\u044B \u0441\u0438\u043D\u043E\u043D\u0438\u043C\u043E\u0432")
                ]),
                createVNode("div", { class: "flex items-center space-x-2" }, [
                  createVNode($setup["VCheckbox"], {
                    modelValue: $setup.include.AttributeSynonym,
                    "onUpdate:modelValue": ($event) => $setup.include.AttributeSynonym = $event,
                    inputId: "synonym",
                    binary: true
                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                  createVNode("label", {
                    for: "synonym",
                    class: "text-sm"
                  }, "\u0421\u0438\u043D\u043E\u043D\u0438\u043C\u044B \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432")
                ]),
                createVNode("div", { class: "flex items-center space-x-2" }, [
                  createVNode($setup["VCheckbox"], {
                    modelValue: $setup.include.Part,
                    "onUpdate:modelValue": ($event) => $setup.include.Part = $event,
                    inputId: "part",
                    binary: true
                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                  createVNode("label", {
                    for: "part",
                    class: "text-sm"
                  }, "\u0413\u0440\u0443\u043F\u043F\u044B \u0432\u0437\u0430\u0438\u043C\u043E\u0437\u0430\u043C\u0435\u043D\u044F\u0435\u043C\u043E\u0441\u0442\u0438")
                ]),
                createVNode("div", { class: "flex items-center space-x-2" }, [
                  createVNode($setup["VCheckbox"], {
                    modelValue: $setup.include.CatalogItem,
                    "onUpdate:modelValue": ($event) => $setup.include.CatalogItem = $event,
                    inputId: "catalogItem",
                    binary: true
                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                  createVNode("label", {
                    for: "catalogItem",
                    class: "text-sm"
                  }, "\u041A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u044B\u0435 \u043F\u043E\u0437\u0438\u0446\u0438\u0438")
                ]),
                createVNode("div", { class: "flex items-center space-x-2" }, [
                  createVNode($setup["VCheckbox"], {
                    modelValue: $setup.include.EquipmentModel,
                    "onUpdate:modelValue": ($event) => $setup.include.EquipmentModel = $event,
                    inputId: "equipmentModel",
                    binary: true
                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                  createVNode("label", {
                    for: "equipmentModel",
                    class: "text-sm"
                  }, "\u041C\u043E\u0434\u0435\u043B\u0438 \u0442\u0435\u0445\u043D\u0438\u043A\u0438")
                ])
              ])
            ]),
            createVNode("div", null, [
              createVNode("h3", { class: "text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, "\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u0438 \u0441\u0432\u044F\u0437\u0438"),
              createVNode("div", { class: "grid grid-cols-1 gap-2" }, [
                createVNode("div", { class: "flex items-center space-x-2" }, [
                  createVNode($setup["VCheckbox"], {
                    modelValue: $setup.include.PartAttribute,
                    "onUpdate:modelValue": ($event) => $setup.include.PartAttribute = $event,
                    inputId: "partAttribute",
                    binary: true
                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                  createVNode("label", {
                    for: "partAttribute",
                    class: "text-sm"
                  }, "\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439")
                ]),
                createVNode("div", { class: "flex items-center space-x-2" }, [
                  createVNode($setup["VCheckbox"], {
                    modelValue: $setup.include.CatalogItemAttribute,
                    "onUpdate:modelValue": ($event) => $setup.include.CatalogItemAttribute = $event,
                    inputId: "catalogItemAttribute",
                    binary: true
                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                  createVNode("label", {
                    for: "catalogItemAttribute",
                    class: "text-sm"
                  }, "\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u043F\u043E\u0437\u0438\u0446\u0438\u0439")
                ]),
                createVNode("div", { class: "flex items-center space-x-2" }, [
                  createVNode($setup["VCheckbox"], {
                    modelValue: $setup.include.EquipmentModelAttribute,
                    "onUpdate:modelValue": ($event) => $setup.include.EquipmentModelAttribute = $event,
                    inputId: "equipmentModelAttribute",
                    binary: true
                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                  createVNode("label", {
                    for: "equipmentModelAttribute",
                    class: "text-sm"
                  }, "\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u0442\u0435\u0445\u043D\u0438\u043A\u0438")
                ]),
                createVNode("div", { class: "flex items-center space-x-2" }, [
                  createVNode($setup["VCheckbox"], {
                    modelValue: $setup.include.PartApplicability,
                    "onUpdate:modelValue": ($event) => $setup.include.PartApplicability = $event,
                    inputId: "partApplicability",
                    binary: true
                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                  createVNode("label", {
                    for: "partApplicability",
                    class: "text-sm"
                  }, "\u041F\u0440\u0438\u043C\u0435\u043D\u0438\u043C\u043E\u0441\u0442\u044C \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439")
                ]),
                createVNode("div", { class: "flex items-center space-x-2" }, [
                  createVNode($setup["VCheckbox"], {
                    modelValue: $setup.include.EquipmentApplicability,
                    "onUpdate:modelValue": ($event) => $setup.include.EquipmentApplicability = $event,
                    inputId: "equipmentApplicability",
                    binary: true
                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                  createVNode("label", {
                    for: "equipmentApplicability",
                    class: "text-sm"
                  }, "\u041F\u0440\u0438\u043C\u0435\u043D\u0438\u043C\u043E\u0441\u0442\u044C \u043A \u0442\u0435\u0445\u043D\u0438\u043A\u0435")
                ])
              ])
            ]),
            createVNode("div", null, [
              createVNode("h3", { class: "text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, "\u0424\u0438\u043B\u044C\u0442\u0440\u044B"),
              createVNode($setup["VInputText"], {
                modelValue: $setup.brandSlugsInput,
                "onUpdate:modelValue": ($event) => $setup.brandSlugsInput = $event,
                placeholder: "\u0411\u0440\u0435\u043D\u0434\u044B \u0447\u0435\u0440\u0435\u0437 \u0437\u0430\u043F\u044F\u0442\u0443\u044E (\u043D\u0430\u043F\u0440\u0438\u043C\u0435\u0440: cat,komatsu)",
                class: "w-full"
              }, null, 8, ["modelValue", "onUpdate:modelValue"])
            ]),
            createVNode("div", { class: "flex flex-col gap-2" }, [
              createVNode($setup["VButton"], {
                onClick: $setup.onExport,
                disabled: $setup.loading,
                loading: $setup.loading,
                class: "w-full"
              }, {
                default: withCtx(() => [
                  createTextVNode(toDisplayString($setup.loading ? "\u042D\u043A\u0441\u043F\u043E\u0440\u0442..." : "\u0421\u043A\u0430\u0447\u0430\u0442\u044C \u0434\u0430\u043D\u043D\u044B\u0435"), 1)
                ]),
                _: 1
              }, 8, ["disabled", "loading"]),
              createVNode($setup["VButton"], {
                onClick: $setup.onExportTemplate,
                disabled: $setup.loading,
                loading: $setup.loadingTemplate,
                severity: "secondary",
                outlined: "",
                class: "w-full"
              }, {
                default: withCtx(() => [
                  createTextVNode(toDisplayString($setup.loadingTemplate ? "\u0421\u043E\u0437\u0434\u0430\u043D\u0438\u0435..." : "\u0421\u043A\u0430\u0447\u0430\u0442\u044C \u0448\u0430\u0431\u043B\u043E\u043D"), 1)
                ]),
                _: 1
              }, 8, ["disabled", "loading"])
            ])
          ])
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(ssrRenderComponent($setup["VCard"], { class: "p-6" }, {
    title: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<h2 class="text-lg font-semibold mb-4"${_scopeId}>\u0418\u043C\u043F\u043E\u0440\u0442 \u043A\u0430\u0442\u0430\u043B\u043E\u0433\u0430</h2>`);
      } else {
        return [
          createVNode("h2", { class: "text-lg font-semibold mb-4" }, "\u0418\u043C\u043F\u043E\u0440\u0442 \u043A\u0430\u0442\u0430\u043B\u043E\u0433\u0430")
        ];
      }
    }),
    content: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="space-y-4"${_scopeId}><div${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}> \u0424\u0430\u0439\u043B Excel (.xlsx) </label><div class="flex items-center gap-2"${_scopeId}><input type="file" accept=".xlsx" class="flex-1 text-sm text-surface-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-primary-50 file:text-primary-700 hover:file:bg-primary-100"${_scopeId}>`);
        if ($setup.file) {
          _push2(ssrRenderComponent($setup["DangerButton"], {
            onClick: $setup.clearFile,
            severity: "secondary",
            outlined: "",
            size: "small"
          }, {
            default: withCtx((_2, _push3, _parent3, _scopeId2) => {
              if (_push3) {
                _push3(` \u041E\u0447\u0438\u0441\u0442\u0438\u0442\u044C `);
              } else {
                return [
                  createTextVNode(" \u041E\u0447\u0438\u0441\u0442\u0438\u0442\u044C ")
                ];
              }
            }),
            _: 1
          }, _parent2, _scopeId));
        } else {
          _push2(`<!---->`);
        }
        _push2(`</div>`);
        if ($setup.file) {
          _push2(`<div class="mt-2 text-sm text-surface-600"${_scopeId}> \u0412\u044B\u0431\u0440\u0430\u043D \u0444\u0430\u0439\u043B: ${ssrInterpolate($setup.file.name)} (${ssrInterpolate($setup.formatFileSize($setup.file.size))}) </div>`);
        } else {
          _push2(`<!---->`);
        }
        _push2(`</div><div class="grid grid-cols-1 md:grid-cols-2 gap-4"${_scopeId}><div${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}> \u0420\u0435\u0436\u0438\u043C \u043A\u043E\u043D\u0444\u043B\u0438\u043A\u0442\u043E\u0432 </label>`);
        _push2(ssrRenderComponent($setup["VSelect"], {
          modelValue: $setup.onConflict,
          "onUpdate:modelValue": ($event) => $setup.onConflict = $event,
          options: $setup.conflictOptions,
          optionLabel: "label",
          optionValue: "value",
          placeholder: "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0440\u0435\u0436\u0438\u043C",
          class: "w-full"
        }, null, _parent2, _scopeId));
        _push2(`</div><div class="flex items-center space-x-2 pt-6"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["VCheckbox"], {
          modelValue: $setup.createMissingRefs,
          "onUpdate:modelValue": ($event) => $setup.createMissingRefs = $event,
          inputId: "createMissingRefs",
          binary: true
        }, null, _parent2, _scopeId));
        _push2(`<label for="createMissingRefs" class="text-sm"${_scopeId}>\u0421\u043E\u0437\u0434\u0430\u0432\u0430\u0442\u044C \u043E\u0442\u0441\u0443\u0442\u0441\u0442\u0432\u0443\u044E\u0449\u0438\u0435 \u0441\u0441\u044B\u043B\u043A\u0438</label></div></div><div class="flex flex-col gap-2"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["VButton"], {
          onClick: $setup.onDryRun,
          disabled: $setup.loading || !$setup.file,
          loading: $setup.loading && $setup.currentAction === "dryRun",
          severity: "info",
          class: "w-full"
        }, {
          default: withCtx((_2, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(`${ssrInterpolate($setup.loading && $setup.currentAction === "dryRun" ? "\u041F\u0440\u043E\u0432\u0435\u0440\u043A\u0430..." : "\u041F\u0440\u043E\u0432\u0435\u0440\u0438\u0442\u044C \u0444\u0430\u0439\u043B (Dry Run)")}`);
            } else {
              return [
                createTextVNode(toDisplayString($setup.loading && $setup.currentAction === "dryRun" ? "\u041F\u0440\u043E\u0432\u0435\u0440\u043A\u0430..." : "\u041F\u0440\u043E\u0432\u0435\u0440\u0438\u0442\u044C \u0444\u0430\u0439\u043B (Dry Run)"), 1)
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["VButton"], {
          onClick: $setup.onExecute,
          disabled: $setup.loading || !$setup.file,
          loading: $setup.loading && $setup.currentAction === "execute",
          severity: "success",
          class: "w-full"
        }, {
          default: withCtx((_2, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(`${ssrInterpolate($setup.loading && $setup.currentAction === "execute" ? "\u0412\u044B\u043F\u043E\u043B\u043D\u044F\u044E..." : "\u0412\u044B\u043F\u043E\u043B\u043D\u0438\u0442\u044C \u0438\u043C\u043F\u043E\u0440\u0442")}`);
            } else {
              return [
                createTextVNode(toDisplayString($setup.loading && $setup.currentAction === "execute" ? "\u0412\u044B\u043F\u043E\u043B\u043D\u044F\u044E..." : "\u0412\u044B\u043F\u043E\u043B\u043D\u0438\u0442\u044C \u0438\u043C\u043F\u043E\u0440\u0442"), 1)
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(`</div>`);
        if ($setup.dryRunResult) {
          _push2(`<div class="space-y-3"${_scopeId}><h3 class="text-sm font-medium text-surface-700 dark:text-surface-300"${_scopeId}>\u0420\u0435\u0437\u0443\u043B\u044C\u0442\u0430\u0442:</h3>`);
          if ($setup.dryRunResult.perSheet) {
            _push2(`<div class="grid grid-cols-2 gap-2"${_scopeId}><!--[-->`);
            ssrRenderList($setup.dryRunResult.perSheet, (counters, sheetName) => {
              _push2(`<div class="p-3 bg-surface-50 dark:bg-surface-900 rounded border"${_scopeId}><div class="text-xs font-medium text-surface-700 dark:text-surface-300"${_scopeId}>${ssrInterpolate(sheetName)}</div><div class="text-xs text-surface-600 dark:text-surface-400"${_scopeId}> \u0421\u0442\u0440\u043E\u043A: ${ssrInterpolate(counters.rowsSeen)} | \u0412\u0430\u043B\u0438\u0434\u043D\u044B\u0445: ${ssrInterpolate(counters.rowsValid)} `);
              if (counters.created) {
                _push2(`<span${_scopeId}>| \u0421\u043E\u0437\u0434\u0430\u043D\u043E: ${ssrInterpolate(counters.created)}</span>`);
              } else {
                _push2(`<!---->`);
              }
              if (counters.updated) {
                _push2(`<span${_scopeId}>| \u041E\u0431\u043D\u043E\u0432\u043B\u0435\u043D\u043E: ${ssrInterpolate(counters.updated)}</span>`);
              } else {
                _push2(`<!---->`);
              }
              if (counters.errorsCount) {
                _push2(`<span${_scopeId}>| \u041E\u0448\u0438\u0431\u043E\u043A: ${ssrInterpolate(counters.errorsCount)}</span>`);
              } else {
                _push2(`<!---->`);
              }
              _push2(`</div></div>`);
            });
            _push2(`<!--]--></div>`);
          } else {
            _push2(`<!---->`);
          }
          if ($setup.dryRunResult.errors?.length) {
            _push2(`<div class="space-y-2"${_scopeId}><h4 class="text-sm font-medium text-red-700 dark:text-red-400"${_scopeId}>\u041E\u0448\u0438\u0431\u043A\u0438:</h4><div class="max-h-32 overflow-y-auto space-y-1"${_scopeId}><!--[-->`);
            ssrRenderList($setup.dryRunResult.errors.slice(0, 10), (error, idx) => {
              _push2(`<div class="text-xs p-2 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 rounded"${_scopeId}>${ssrInterpolate(error.sheet)}:${ssrInterpolate(error.rowIndex)} - ${ssrInterpolate(error.message)}</div>`);
            });
            _push2(`<!--]-->`);
            if ($setup.dryRunResult.errors.length > 10) {
              _push2(`<div class="text-xs text-surface-500"${_scopeId}> ... \u0438 \u0435\u0449\u0435 ${ssrInterpolate($setup.dryRunResult.errors.length - 10)} \u043E\u0448\u0438\u0431\u043E\u043A </div>`);
            } else {
              _push2(`<!---->`);
            }
            _push2(`</div></div>`);
          } else {
            _push2(`<!---->`);
          }
          if ($setup.dryRunResult.reportBase64) {
            _push2(`<div${_scopeId}>`);
            _push2(ssrRenderComponent($setup["VButton"], {
              onClick: ($event) => $setup.downloadReport($setup.dryRunResult.reportBase64),
              severity: "secondary",
              outlined: "",
              size: "small"
            }, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(` \u0421\u043A\u0430\u0447\u0430\u0442\u044C \u043E\u0442\u0447\u0451\u0442 `);
                } else {
                  return [
                    createTextVNode(" \u0421\u043A\u0430\u0447\u0430\u0442\u044C \u043E\u0442\u0447\u0451\u0442 ")
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
            _push2(`</div>`);
          } else {
            _push2(`<!---->`);
          }
          _push2(`</div>`);
        } else {
          _push2(`<!---->`);
        }
        _push2(`</div>`);
      } else {
        return [
          createVNode("div", { class: "space-y-4" }, [
            createVNode("div", null, [
              createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u0424\u0430\u0439\u043B Excel (.xlsx) "),
              createVNode("div", { class: "flex items-center gap-2" }, [
                createVNode("input", {
                  type: "file",
                  onChange: $setup.onFileChange,
                  accept: ".xlsx",
                  class: "flex-1 text-sm text-surface-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-primary-50 file:text-primary-700 hover:file:bg-primary-100"
                }, null, 32),
                $setup.file ? (openBlock(), createBlock($setup["DangerButton"], {
                  key: 0,
                  onClick: $setup.clearFile,
                  severity: "secondary",
                  outlined: "",
                  size: "small"
                }, {
                  default: withCtx(() => [
                    createTextVNode(" \u041E\u0447\u0438\u0441\u0442\u0438\u0442\u044C ")
                  ]),
                  _: 1
                })) : createCommentVNode("", true)
              ]),
              $setup.file ? (openBlock(), createBlock("div", {
                key: 0,
                class: "mt-2 text-sm text-surface-600"
              }, " \u0412\u044B\u0431\u0440\u0430\u043D \u0444\u0430\u0439\u043B: " + toDisplayString($setup.file.name) + " (" + toDisplayString($setup.formatFileSize($setup.file.size)) + ") ", 1)) : createCommentVNode("", true)
            ]),
            createVNode("div", { class: "grid grid-cols-1 md:grid-cols-2 gap-4" }, [
              createVNode("div", null, [
                createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u0420\u0435\u0436\u0438\u043C \u043A\u043E\u043D\u0444\u043B\u0438\u043A\u0442\u043E\u0432 "),
                createVNode($setup["VSelect"], {
                  modelValue: $setup.onConflict,
                  "onUpdate:modelValue": ($event) => $setup.onConflict = $event,
                  options: $setup.conflictOptions,
                  optionLabel: "label",
                  optionValue: "value",
                  placeholder: "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0440\u0435\u0436\u0438\u043C",
                  class: "w-full"
                }, null, 8, ["modelValue", "onUpdate:modelValue"])
              ]),
              createVNode("div", { class: "flex items-center space-x-2 pt-6" }, [
                createVNode($setup["VCheckbox"], {
                  modelValue: $setup.createMissingRefs,
                  "onUpdate:modelValue": ($event) => $setup.createMissingRefs = $event,
                  inputId: "createMissingRefs",
                  binary: true
                }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                createVNode("label", {
                  for: "createMissingRefs",
                  class: "text-sm"
                }, "\u0421\u043E\u0437\u0434\u0430\u0432\u0430\u0442\u044C \u043E\u0442\u0441\u0443\u0442\u0441\u0442\u0432\u0443\u044E\u0449\u0438\u0435 \u0441\u0441\u044B\u043B\u043A\u0438")
              ])
            ]),
            createVNode("div", { class: "flex flex-col gap-2" }, [
              createVNode($setup["VButton"], {
                onClick: $setup.onDryRun,
                disabled: $setup.loading || !$setup.file,
                loading: $setup.loading && $setup.currentAction === "dryRun",
                severity: "info",
                class: "w-full"
              }, {
                default: withCtx(() => [
                  createTextVNode(toDisplayString($setup.loading && $setup.currentAction === "dryRun" ? "\u041F\u0440\u043E\u0432\u0435\u0440\u043A\u0430..." : "\u041F\u0440\u043E\u0432\u0435\u0440\u0438\u0442\u044C \u0444\u0430\u0439\u043B (Dry Run)"), 1)
                ]),
                _: 1
              }, 8, ["disabled", "loading"]),
              createVNode($setup["VButton"], {
                onClick: $setup.onExecute,
                disabled: $setup.loading || !$setup.file,
                loading: $setup.loading && $setup.currentAction === "execute",
                severity: "success",
                class: "w-full"
              }, {
                default: withCtx(() => [
                  createTextVNode(toDisplayString($setup.loading && $setup.currentAction === "execute" ? "\u0412\u044B\u043F\u043E\u043B\u043D\u044F\u044E..." : "\u0412\u044B\u043F\u043E\u043B\u043D\u0438\u0442\u044C \u0438\u043C\u043F\u043E\u0440\u0442"), 1)
                ]),
                _: 1
              }, 8, ["disabled", "loading"])
            ]),
            $setup.dryRunResult ? (openBlock(), createBlock("div", {
              key: 0,
              class: "space-y-3"
            }, [
              createVNode("h3", { class: "text-sm font-medium text-surface-700 dark:text-surface-300" }, "\u0420\u0435\u0437\u0443\u043B\u044C\u0442\u0430\u0442:"),
              $setup.dryRunResult.perSheet ? (openBlock(), createBlock("div", {
                key: 0,
                class: "grid grid-cols-2 gap-2"
              }, [
                (openBlock(true), createBlock(Fragment, null, renderList($setup.dryRunResult.perSheet, (counters, sheetName) => {
                  return openBlock(), createBlock("div", {
                    key: sheetName,
                    class: "p-3 bg-surface-50 dark:bg-surface-900 rounded border"
                  }, [
                    createVNode("div", { class: "text-xs font-medium text-surface-700 dark:text-surface-300" }, toDisplayString(sheetName), 1),
                    createVNode("div", { class: "text-xs text-surface-600 dark:text-surface-400" }, [
                      createTextVNode(" \u0421\u0442\u0440\u043E\u043A: " + toDisplayString(counters.rowsSeen) + " | \u0412\u0430\u043B\u0438\u0434\u043D\u044B\u0445: " + toDisplayString(counters.rowsValid) + " ", 1),
                      counters.created ? (openBlock(), createBlock("span", { key: 0 }, "| \u0421\u043E\u0437\u0434\u0430\u043D\u043E: " + toDisplayString(counters.created), 1)) : createCommentVNode("", true),
                      counters.updated ? (openBlock(), createBlock("span", { key: 1 }, "| \u041E\u0431\u043D\u043E\u0432\u043B\u0435\u043D\u043E: " + toDisplayString(counters.updated), 1)) : createCommentVNode("", true),
                      counters.errorsCount ? (openBlock(), createBlock("span", { key: 2 }, "| \u041E\u0448\u0438\u0431\u043E\u043A: " + toDisplayString(counters.errorsCount), 1)) : createCommentVNode("", true)
                    ])
                  ]);
                }), 128))
              ])) : createCommentVNode("", true),
              $setup.dryRunResult.errors?.length ? (openBlock(), createBlock("div", {
                key: 1,
                class: "space-y-2"
              }, [
                createVNode("h4", { class: "text-sm font-medium text-red-700 dark:text-red-400" }, "\u041E\u0448\u0438\u0431\u043A\u0438:"),
                createVNode("div", { class: "max-h-32 overflow-y-auto space-y-1" }, [
                  (openBlock(true), createBlock(Fragment, null, renderList($setup.dryRunResult.errors.slice(0, 10), (error, idx) => {
                    return openBlock(), createBlock("div", {
                      key: idx,
                      class: "text-xs p-2 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 rounded"
                    }, toDisplayString(error.sheet) + ":" + toDisplayString(error.rowIndex) + " - " + toDisplayString(error.message), 1);
                  }), 128)),
                  $setup.dryRunResult.errors.length > 10 ? (openBlock(), createBlock("div", {
                    key: 0,
                    class: "text-xs text-surface-500"
                  }, " ... \u0438 \u0435\u0449\u0435 " + toDisplayString($setup.dryRunResult.errors.length - 10) + " \u043E\u0448\u0438\u0431\u043E\u043A ", 1)) : createCommentVNode("", true)
                ])
              ])) : createCommentVNode("", true),
              $setup.dryRunResult.reportBase64 ? (openBlock(), createBlock("div", { key: 2 }, [
                createVNode($setup["VButton"], {
                  onClick: ($event) => $setup.downloadReport($setup.dryRunResult.reportBase64),
                  severity: "secondary",
                  outlined: "",
                  size: "small"
                }, {
                  default: withCtx(() => [
                    createTextVNode(" \u0421\u043A\u0430\u0447\u0430\u0442\u044C \u043E\u0442\u0447\u0451\u0442 ")
                  ]),
                  _: 1
                }, 8, ["onClick"])
              ])) : createCommentVNode("", true)
            ])) : createCommentVNode("", true)
          ])
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(ssrRenderComponent($setup["VCard"], { class: "p-6" }, {
    title: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<h2 class="text-lg font-semibold mb-4"${_scopeId}>\u0418\u0441\u0442\u043E\u0440\u0438\u044F \u0438\u043C\u043F\u043E\u0440\u0442\u043E\u0432</h2>`);
      } else {
        return [
          createVNode("h2", { class: "text-lg font-semibold mb-4" }, "\u0418\u0441\u0442\u043E\u0440\u0438\u044F \u0438\u043C\u043F\u043E\u0440\u0442\u043E\u0432")
        ];
      }
    }),
    content: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="text-sm text-surface-600 dark:text-surface-400"${_scopeId}> \u0424\u0443\u043D\u043A\u0446\u0438\u043E\u043D\u0430\u043B \u0438\u0441\u0442\u043E\u0440\u0438\u0438 \u0438\u043C\u043F\u043E\u0440\u0442\u043E\u0432 \u0431\u0443\u0434\u0435\u0442 \u0434\u043E\u0431\u0430\u0432\u043B\u0435\u043D \u0432 \u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0435\u0439 \u0438\u0442\u0435\u0440\u0430\u0446\u0438\u0438 </div>`);
      } else {
        return [
          createVNode("div", { class: "text-sm text-surface-600 dark:text-surface-400" }, " \u0424\u0443\u043D\u043A\u0446\u0438\u043E\u043D\u0430\u043B \u0438\u0441\u0442\u043E\u0440\u0438\u0438 \u0438\u043C\u043F\u043E\u0440\u0442\u043E\u0432 \u0431\u0443\u0434\u0435\u0442 \u0434\u043E\u0431\u0430\u0432\u043B\u0435\u043D \u0432 \u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0435\u0439 \u0438\u0442\u0435\u0440\u0430\u0446\u0438\u0438 ")
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</div></div>`);
}
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/widgets/catalog/ImportExportPanel.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const ImportExportPanel = /* @__PURE__ */ _export_sfc(_sfc_main, [["ssrRender", _sfc_ssrRender]]);

const $$ImportExport = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${renderComponent($$result, "AdminLayout", $$AdminLayout, { "title": "\u0418\u043C\u043F\u043E\u0440\u0442 / \u042D\u043A\u0441\u043F\u043E\u0440\u0442 - PartTec Admin", "showSidebar": true }, { "default": ($$result2) => renderTemplate` ${renderComponent($$result2, "ImportExportPanel", ImportExportPanel, { "client:load": true, "client:component-hydration": "load", "client:component-path": "@/widgets/catalog/ImportExportPanel.vue", "client:component-export": "default" })} ` })}`;
}, "D:/Dev/parttec/cpanel/src/pages/admin/import-export.astro", void 0);

const $$file = "D:/Dev/parttec/cpanel/src/pages/admin/import-export.astro";
const $$url = "/admin/import-export";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$ImportExport,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
