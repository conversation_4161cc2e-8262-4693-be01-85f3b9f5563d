import{j as t}from"./jsx-runtime.D_zvdyIk.js";import{Q as o,t as r,l,h as u,S as f,a as p}from"./trpc.9HS4D061.js";import{r as n}from"./index.0yr9KlQE.js";const h="http://localhost:3000";function y({children:s}){const[e]=n.useState(()=>new o({defaultOptions:{queries:{staleTime:3e5,gcTime:18e5,retry:2,refetchOnWindowFocus:!1,refetchOnReconnect:!0,refetchOnMount:!1,refetchInterval:!1},mutations:{retry:1}}})),[a]=n.useState(()=>r.createClient({transformer:f,links:[l({enabled:()=>!1}),u({url:`${h}/trpc`,fetch:(c,i)=>fetch(c,{...i,credentials:"include"})})]}));return t.jsx(r.Provider,{client:a,queryClient:e,children:t.jsx(p,{client:e,children:s})})}export{y as TrpcProvider};
