import { e as createComponent, k as renderComponent, r as renderTemplate, m as maybeRenderHead } from '../chunks/astro/server_D7mwM5eH.mjs';
import 'kleur/colors';
import { $ as $$MainLayout } from '../chunks/MainLayout_M77LEN4m.mjs';
import { t as trpcClient } from '../chunks/badge_BlauDB1K.mjs';
import { T as TrpcProvider } from '../chunks/TrpcProvider_CX1isq_a.mjs';
import { B as BrandCard } from '../chunks/BrandCard_B_BCCN3m.mjs';
export { renderers } from '../renderers.mjs';

const $$Brands = createComponent(async ($$result, $$props, $$slots) => {
  let brands = [];
  try {
    const brandsResponse = await trpcClient.crud.brand.findMany.query({
      include: {
        _count: {
          select: {
            catalogItems: true,
            equipmentModel: true
          }
        }
      },
      orderBy: { name: "asc" }
    });
    brands = brandsResponse || [];
  } catch (error) {
    console.error("Error loading brands:", error);
  }
  const oemBrands = brands.filter((brand) => brand.isOem);
  const aftermarketBrands = brands.filter((brand) => !brand.isOem);
  return renderTemplate`${renderComponent($$result, "MainLayout", $$MainLayout, { "title": "\u0411\u0440\u0435\u043D\u0434\u044B", "description": "\u041A\u0430\u0442\u0430\u043B\u043E\u0433 \u043F\u0440\u043E\u0438\u0437\u0432\u043E\u0434\u0438\u0442\u0435\u043B\u0435\u0439 \u0442\u0435\u0445\u043D\u0438\u043A\u0438 \u0438 \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439" }, { "default": async ($$result2) => renderTemplate` ${renderComponent($$result2, "TrpcProvider", TrpcProvider, { "client:load": true, "client:component-hydration": "load", "client:component-path": "@/components/providers/TrpcProvider", "client:component-export": "TrpcProvider" }, { "default": async ($$result3) => renderTemplate` ${maybeRenderHead()}<div class="container mx-auto px-4 py-8"> <!-- Заголовок --> <div class="mb-8"> <h1 class="text-3xl font-bold tracking-tight mb-2">Бренды</h1> <p class="text-muted-foreground">
Производители техники и запчастей в нашем каталоге
</p> </div> <!-- OEM бренды --> ${oemBrands.length > 0 && renderTemplate`<div class="mb-12"> <h2 class="text-2xl font-semibold mb-6">OEM производители</h2> <p class="text-muted-foreground mb-6">
Оригинальные производители техники
</p> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"> ${oemBrands.map((brand) => renderTemplate`${renderComponent($$result3, "BrandCard", BrandCard, { "brand": brand, "client:load": true, "client:component-hydration": "load", "client:component-path": "@/components/catalog/BrandCard", "client:component-export": "BrandCard" })}`)} </div> </div>`} <!-- Aftermarket бренды --> ${aftermarketBrands.length > 0 && renderTemplate`<div class="mb-12"> <h2 class="text-2xl font-semibold mb-6">Aftermarket производители</h2> <p class="text-muted-foreground mb-6">
Производители запчастей и комплектующих
</p> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"> ${aftermarketBrands.map((brand) => renderTemplate`${renderComponent($$result3, "BrandCard", BrandCard, { "brand": brand, "client:load": true, "client:component-hydration": "load", "client:component-path": "@/components/catalog/BrandCard", "client:component-export": "BrandCard" })}`)} </div> </div>`} <!-- Если нет брендов --> ${brands.length === 0 && renderTemplate`<div class="text-center py-12"> <h3 class="text-lg font-semibold mb-2">Бренды не найдены</h3> <p class="text-muted-foreground">
Каталог брендов пуст или недоступен
</p> </div>`} </div> ` })} ` })}`;
}, "D:/Dev/parttec/site/src/pages/brands.astro", void 0);

const $$file = "D:/Dev/parttec/site/src/pages/brands.astro";
const $$url = "/brands";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Brands,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
