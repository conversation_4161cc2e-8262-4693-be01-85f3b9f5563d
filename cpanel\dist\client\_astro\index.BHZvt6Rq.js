import{u as h,Y as s,v as L}from"./index.BglzLLgy.js";var _={STARTS_WITH:"startsWith",CONTAINS:"contains",NOT_CONTAINS:"notContains",ENDS_WITH:"endsWith",EQUALS:"equals",NOT_EQUALS:"notEquals",LESS_THAN:"lt",LESS_THAN_OR_EQUAL_TO:"lte",GREATER_THAN:"gt",GREATER_THAN_OR_EQUAL_TO:"gte",DATE_IS:"dateIs",DATE_IS_NOT:"dateIsNot",DATE_BEFORE:"dateBefore",DATE_AFTER:"dateAfter"},w={AND:"and",OR:"or"};function l(t,e){var n=typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=y(t))||e){n&&(t=n);var r=0,u=function(){};return{s:u,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(o){throw o},f:u}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var i,d=!0,a=!1;return{s:function(){n=n.call(t)},n:function(){var o=n.next();return d=o.done,o},e:function(o){a=!0,i=o},f:function(){try{d||n.return==null||n.return()}finally{if(a)throw i}}}}function y(t,e){if(t){if(typeof t=="string")return m(t,e);var n={}.toString.call(t).slice(8,-1);return n==="Object"&&t.constructor&&(n=t.constructor.name),n==="Map"||n==="Set"?Array.from(t):n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?m(t,e):void 0}}function m(t,e){(e==null||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}var C={filter:function(e,n,r,u,i){var d=[];if(!e)return d;var a=l(e),f;try{for(a.s();!(f=a.n()).done;){var o=f.value;if(typeof o=="string"){if(this.filters[u](o,r,i)){d.push(o);continue}}else{var g=l(n),c;try{for(g.s();!(c=g.n()).done;){var S=c.value,A=L(o,S);if(this.filters[u](A,r,i)){d.push(o);break}}}catch(T){g.e(T)}finally{g.f()}}}}catch(T){a.e(T)}finally{a.f()}return d},filters:{startsWith:function(e,n,r){if(n==null||n==="")return!0;if(e==null)return!1;var u=s(n.toString()).toLocaleLowerCase(r),i=s(e.toString()).toLocaleLowerCase(r);return i.slice(0,u.length)===u},contains:function(e,n,r){if(n==null||n==="")return!0;if(e==null)return!1;var u=s(n.toString()).toLocaleLowerCase(r),i=s(e.toString()).toLocaleLowerCase(r);return i.indexOf(u)!==-1},notContains:function(e,n,r){if(n==null||n==="")return!0;if(e==null)return!1;var u=s(n.toString()).toLocaleLowerCase(r),i=s(e.toString()).toLocaleLowerCase(r);return i.indexOf(u)===-1},endsWith:function(e,n,r){if(n==null||n==="")return!0;if(e==null)return!1;var u=s(n.toString()).toLocaleLowerCase(r),i=s(e.toString()).toLocaleLowerCase(r);return i.indexOf(u,i.length-u.length)!==-1},equals:function(e,n,r){return n==null||n===""?!0:e==null?!1:e.getTime&&n.getTime?e.getTime()===n.getTime():s(e.toString()).toLocaleLowerCase(r)==s(n.toString()).toLocaleLowerCase(r)},notEquals:function(e,n,r){return n==null||n===""?!1:e==null?!0:e.getTime&&n.getTime?e.getTime()!==n.getTime():s(e.toString()).toLocaleLowerCase(r)!=s(n.toString()).toLocaleLowerCase(r)},in:function(e,n){if(n==null||n.length===0)return!0;for(var r=0;r<n.length;r++)if(h(e,n[r]))return!0;return!1},between:function(e,n){return n==null||n[0]==null||n[1]==null?!0:e==null?!1:e.getTime?n[0].getTime()<=e.getTime()&&e.getTime()<=n[1].getTime():n[0]<=e&&e<=n[1]},lt:function(e,n){return n==null?!0:e==null?!1:e.getTime&&n.getTime?e.getTime()<n.getTime():e<n},lte:function(e,n){return n==null?!0:e==null?!1:e.getTime&&n.getTime?e.getTime()<=n.getTime():e<=n},gt:function(e,n){return n==null?!0:e==null?!1:e.getTime&&n.getTime?e.getTime()>n.getTime():e>n},gte:function(e,n){return n==null?!0:e==null?!1:e.getTime&&n.getTime?e.getTime()>=n.getTime():e>=n},dateIs:function(e,n){return n==null?!0:e==null?!1:e.toDateString()===n.toDateString()},dateIsNot:function(e,n){return n==null?!0:e==null?!1:e.toDateString()!==n.toDateString()},dateBefore:function(e,n){return n==null?!0:e==null?!1:e.getTime()<n.getTime()},dateAfter:function(e,n){return n==null?!0:e==null?!1:e.getTime()>n.getTime()}},register:function(e,n){this.filters[e]=n}};export{C as F,_ as a,w as b};
