import { TRPCError } from '@trpc/server'
import { getSystemDB } from '../db'

export class PartAttributesService {
  static async create(input: { partId: number; templateId: number; value: string }) {
    try {
      const db = getSystemDB()
      const part = await db.part.findUnique({ where: { id: input.partId } })
      if (!part) throw new TRPCError({ code: 'NOT_FOUND', message: 'Запчасть не найдена' })
      const template = await db.attributeTemplate.findUnique({ where: { id: input.templateId } })
      if (!template) throw new TRPCError({ code: 'NOT_FOUND', message: 'Шаблон атрибута не найден' })
      const existingAttribute = await db.partAttribute.findUnique({ where: { partId_templateId: { partId: input.partId, templateId: input.templateId } } })
      if (existingAttribute) throw new TRPCError({ code: 'CONFLICT', message: 'Атрибут с таким шаблоном уже существует для данной запчасти' })
      const attribute = await db.partAttribute.create({
        data: { partId: input.partId, templateId: input.templateId, value: input.value },
        include: { template: true },
      })
      return attribute
    } catch (error: any) {
      if (error instanceof TRPCError) throw error
      throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: error.message || 'Не удалось создать атрибут запчасти' })
    }
  }

  static async findByPartId(input: { partId: number }) {
    try {
      const db = getSystemDB()
      const attributes = await db.partAttribute.findMany({
        where: { partId: input.partId },
        include: { template: { include: { group: true } } },
        orderBy: [{ template: { group: { name: 'asc' } } }, { template: { name: 'asc' } }],
      })
      return attributes
    } catch (error: any) {
      throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: error.message || 'Не удалось получить атрибуты запчасти' })
    }
  }

  static async update(input: { id: number; value: string }) {
    try {
      const db = getSystemDB()
      const existingAttribute = await db.partAttribute.findUnique({ where: { id: input.id } })
      if (!existingAttribute) throw new TRPCError({ code: 'NOT_FOUND', message: 'Атрибут не найден' })
      const updatedAttribute = await db.partAttribute.update({ where: { id: input.id }, data: { value: input.value }, include: { template: true } })
      return updatedAttribute
    } catch (error: any) {
      if (error instanceof TRPCError) throw error
      throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: error.message || 'Не удалось обновить атрибут запчасти' })
    }
  }

  static async delete(input: { id: number }) {
    try {
      const db = getSystemDB()
      const existingAttribute = await db.partAttribute.findUnique({ where: { id: input.id } })
      if (!existingAttribute) throw new TRPCError({ code: 'NOT_FOUND', message: 'Атрибут не найден' })
      await db.partAttribute.delete({ where: { id: input.id } })
      return { success: true }
    } catch (error: any) {
      if (error instanceof TRPCError) throw error
      throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: error.message || 'Не удалось удалить атрибут запчасти' })
    }
  }

  static async bulkCreate(input: { partId: number; attributes: Array<{ templateId: number; value: string }> }) {
    try {
      const db = getSystemDB()
      const part = await db.part.findUnique({ where: { id: input.partId } })
      if (!part) throw new TRPCError({ code: 'NOT_FOUND', message: 'Запчасть не найдена' })
      const templateIds = input.attributes.map(a => a.templateId)
      const templates = await db.attributeTemplate.findMany({ where: { id: { in: templateIds } } })
      if (templates.length !== templateIds.length) throw new TRPCError({ code: 'NOT_FOUND', message: 'Один или несколько шаблонов атрибутов не найдены' })
      const createdAttributes = await db.$transaction(async (tx) => {
        const results: any[] = []
        for (const attr of input.attributes) {
          const existing = await tx.partAttribute.findUnique({ where: { partId_templateId: { partId: input.partId, templateId: attr.templateId } } })
          if (!existing) {
            const created = await tx.partAttribute.create({ data: { partId: input.partId, templateId: attr.templateId, value: attr.value }, include: { template: true } })
            results.push(created)
          }
        }
        return results
      })
      return createdAttributes
    } catch (error: any) {
      if (error instanceof TRPCError) throw error
      throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: error.message || 'Не удалось создать атрибуты запчасти' })
    }
  }
}


