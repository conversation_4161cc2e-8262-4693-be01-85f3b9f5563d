import{R as qu,E as Hu}from"./ErrorBoundary.Def_Csyr.js";import{w as Q,c as <PERSON>,b as De}from"./runtime-dom.esm-bundler.C-dfRCGi.js";import{u as ce}from"./useTrpc.CAkGIEe7.js";import{u as Wu}from"./useUrlParams.DMgQWPCH.js";import Qe from"./Card.CAr8QcdG.js";import he from"./Button.oEwD-lSq.js";import{I as Ee}from"./InputText.COaPodMV.js";import{V as Ge}from"./Textarea.Bgj6qPNm.js";import{S as Me}from"./Select.CKfyRLxl.js";import{D as Ze,s as $e,a as Yu}from"./index.xFbNStuK.js";import{T as eu}from"./Tag.DvN1X7lb.js";import{D as we}from"./Dialog.Dcz8Sg9i.js";import{a as uu,e as Ju,d as Qu,b as tu}from"./index.CJuyVe3p.js";import{c as lu}from"./index.D6LCJW96.js";import{U as Zu,X as $u,v as et,as as Ie,a9 as Ue,am as Re,ag as $,p as ut,c as h,o as d,a as n,b as F,q as ou,g as E,m as z,F as ae,V as re,j as g,f as de,r as fe,l as ge,e as r,d as ee,k as tt,n as lt,h as j,aK as ot,w as H,i as xe,I as nt}from"./index.BglzLLgy.js";import{a as nu}from"./index.DQmIdHOH.js";import{s as at}from"./index.CLkTvMlq.js";import{s as au,_ as ue,p as rt}from"./_plugin-vue_export-helper.BX07OBiL.js";import{s as it,a as st}from"./index.D7_1DwEX.js";import{a as dt,s as ct}from"./index.CXDqTVvt.js";import{R as ft,f as ze}from"./index.B_yc9D3m.js";import{n as L,t as D,b as pt,r as v,c as mt}from"./reactivity.esm-bundler.D5IypM4U.js";import{u as Te}from"./useToast.DbdIHNOo.js";import{u as gt}from"./useAuth.DETRY1gV.js";/* empty css                       */import{V as vt}from"./InputNumber.BtccV88f.js";import{C as yt}from"./Checkbox.B9YJ1Jrl.js";import{V as Ke}from"./AutoComplete.XhB-0aUS.js";import{D as Ne}from"./DangerButton.PrtrqpTW.js";import{T as Pe,P as Oe,a as ru}from"./trash.BzwTNQJC.js";import{c as se}from"./createLucideIcon.3yDVQAYz.js";import{C as iu,T as su}from"./tags.C8rWdFOL.js";/* empty css                            */import{I as bt}from"./Icon.Ci-mb2Ee.js";import"./trpc.BpyaUO08.js";import"./router.DKcY2uv6.js";import"./index.BHZvt6Rq.js";import"./index.Tc5ZRw49.js";import"./index.irlSZ_18.js";import"./index.CLs7nh7g.js";import"./index.B0GjtQuk.js";import"./SecondaryButton.DjJyItVx.js";import"./index.CRcBj2l1.js";import"./index.CzaMXvxd.js";import"./auth-client.D7sWEz1h.js";import"./types.FgRm47Sn.js";import"./index.CBatl9QV.js";import"./index.BsVcoVHU.js";/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const du=se("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ht=se("circle-plus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xt=se("file",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cu=se("folder",[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ct=se("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kt=se("pencil-line",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}],["path",{d:"m15 5 3 3",key:"1w25hb"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dt=se("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]]);var Et=`
    .p-tree {
        display: block;
        background: dt('tree.background');
        color: dt('tree.color');
        padding: dt('tree.padding');
    }

    .p-tree-root-children,
    .p-tree-node-children {
        display: flex;
        list-style-type: none;
        flex-direction: column;
        margin: 0;
        gap: dt('tree.gap');
    }

    .p-tree-root-children {
        padding: 0;
        padding-block-start: dt('tree.gap');
    }

    .p-tree-node-children {
        padding: 0;
        padding-block-start: dt('tree.gap');
        padding-inline-start: dt('tree.indent');
    }

    .p-tree-node {
        padding: 0;
        outline: 0 none;
    }

    .p-tree-node-content {
        border-radius: dt('tree.node.border.radius');
        padding: dt('tree.node.padding');
        display: flex;
        align-items: center;
        outline-color: transparent;
        color: dt('tree.node.color');
        gap: dt('tree.node.gap');
        transition:
            background dt('tree.transition.duration'),
            color dt('tree.transition.duration'),
            outline-color dt('tree.transition.duration'),
            box-shadow dt('tree.transition.duration');
    }

    .p-tree-node:focus-visible > .p-tree-node-content {
        box-shadow: dt('tree.node.focus.ring.shadow');
        outline: dt('tree.node.focus.ring.width') dt('tree.node.focus.ring.style') dt('tree.node.focus.ring.color');
        outline-offset: dt('tree.node.focus.ring.offset');
    }

    .p-tree-node-content.p-tree-node-selectable:not(.p-tree-node-selected):hover {
        background: dt('tree.node.hover.background');
        color: dt('tree.node.hover.color');
    }

    .p-tree-node-content.p-tree-node-selectable:not(.p-tree-node-selected):hover .p-tree-node-icon {
        color: dt('tree.node.icon.hover.color');
    }

    .p-tree-node-content.p-tree-node-selected {
        background: dt('tree.node.selected.background');
        color: dt('tree.node.selected.color');
    }

    .p-tree-node-content.p-tree-node-selected .p-tree-node-toggle-button {
        color: inherit;
    }

    .p-tree-node-toggle-button {
        cursor: pointer;
        user-select: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        position: relative;
        flex-shrink: 0;
        width: dt('tree.node.toggle.button.size');
        height: dt('tree.node.toggle.button.size');
        color: dt('tree.node.toggle.button.color');
        border: 0 none;
        background: transparent;
        border-radius: dt('tree.node.toggle.button.border.radius');
        transition:
            background dt('tree.transition.duration'),
            color dt('tree.transition.duration'),
            border-color dt('tree.transition.duration'),
            outline-color dt('tree.transition.duration'),
            box-shadow dt('tree.transition.duration');
        outline-color: transparent;
        padding: 0;
    }

    .p-tree-node-toggle-button:enabled:hover {
        background: dt('tree.node.toggle.button.hover.background');
        color: dt('tree.node.toggle.button.hover.color');
    }

    .p-tree-node-content.p-tree-node-selected .p-tree-node-toggle-button:hover {
        background: dt('tree.node.toggle.button.selected.hover.background');
        color: dt('tree.node.toggle.button.selected.hover.color');
    }

    .p-tree-root {
        overflow: auto;
    }

    .p-tree-node-selectable {
        cursor: pointer;
        user-select: none;
    }

    .p-tree-node-leaf > .p-tree-node-content .p-tree-node-toggle-button {
        visibility: hidden;
    }

    .p-tree-node-icon {
        color: dt('tree.node.icon.color');
        transition: color dt('tree.transition.duration');
    }

    .p-tree-node-content.p-tree-node-selected .p-tree-node-icon {
        color: dt('tree.node.icon.selected.color');
    }

    .p-tree-filter {
        margin: dt('tree.filter.margin');
    }

    .p-tree-filter-input {
        width: 100%;
    }

    .p-tree-loading {
        position: relative;
        height: 100%;
    }

    .p-tree-loading-icon {
        font-size: dt('tree.loading.icon.size');
        width: dt('tree.loading.icon.size');
        height: dt('tree.loading.icon.size');
    }

    .p-tree .p-tree-mask {
        position: absolute;
        z-index: 1;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .p-tree-flex-scrollable {
        display: flex;
        flex: 1;
        height: 100%;
        flex-direction: column;
    }

    .p-tree-flex-scrollable .p-tree-root {
        flex: 1;
    }
`,wt={root:function(u){var o=u.props;return["p-tree p-component",{"p-tree-selectable":o.selectionMode!=null,"p-tree-loading":o.loading,"p-tree-flex-scrollable":o.scrollHeight==="flex"}]},mask:"p-tree-mask p-overlay-mask",loadingIcon:"p-tree-loading-icon",pcFilterContainer:"p-tree-filter",pcFilterInput:"p-tree-filter-input",wrapper:"p-tree-root",rootChildren:"p-tree-root-children",node:function(u){var o=u.instance;return["p-tree-node",{"p-tree-node-leaf":o.leaf}]},nodeContent:function(u){var o=u.instance;return["p-tree-node-content",o.node.styleClass,{"p-tree-node-selectable":o.selectable,"p-tree-node-selected":o.checkboxMode&&o.$parentInstance.highlightOnSelect?o.checked:o.selected}]},nodeToggleButton:"p-tree-node-toggle-button",nodeToggleIcon:"p-tree-node-toggle-icon",nodeCheckbox:"p-tree-node-checkbox",nodeIcon:"p-tree-node-icon",nodeLabel:"p-tree-node-label",nodeChildren:"p-tree-node-children"},Tt=Zu.extend({name:"tree",style:Et,classes:wt}),Ft={name:"BaseTree",extends:au,props:{value:{type:null,default:null},expandedKeys:{type:null,default:null},selectionKeys:{type:null,default:null},selectionMode:{type:String,default:null},metaKeySelection:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},loadingIcon:{type:String,default:void 0},loadingMode:{type:String,default:"mask"},filter:{type:Boolean,default:!1},filterBy:{type:[String,Function],default:"label"},filterMode:{type:String,default:"lenient"},filterPlaceholder:{type:String,default:null},filterLocale:{type:String,default:void 0},highlightOnSelect:{type:Boolean,default:!1},scrollHeight:{type:String,default:null},level:{type:Number,default:0},ariaLabelledby:{type:String,default:null},ariaLabel:{type:String,default:null}},style:Tt,provide:function(){return{$pcTree:this,$parentInstance:this}}};function ve(t){"@babel/helpers - typeof";return ve=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(u){return typeof u}:function(u){return u&&typeof Symbol=="function"&&u.constructor===Symbol&&u!==Symbol.prototype?"symbol":typeof u},ve(t)}function qe(t,u){var o=typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(!o){if(Array.isArray(t)||(o=fu(t))||u){o&&(t=o);var e=0,s=function(){};return{s,n:function(){return e>=t.length?{done:!0}:{done:!1,value:t[e++]}},e:function(f){throw f},f:s}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var a,l=!0,m=!1;return{s:function(){o=o.call(t)},n:function(){var f=o.next();return l=f.done,f},e:function(f){m=!0,a=f},f:function(){try{l||o.return==null||o.return()}finally{if(m)throw a}}}}function He(t,u){var o=Object.keys(t);if(Object.getOwnPropertySymbols){var e=Object.getOwnPropertySymbols(t);u&&(e=e.filter(function(s){return Object.getOwnPropertyDescriptor(t,s).enumerable})),o.push.apply(o,e)}return o}function Xe(t){for(var u=1;u<arguments.length;u++){var o=arguments[u]!=null?arguments[u]:{};u%2?He(Object(o),!0).forEach(function(e){Vt(t,e,o[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(o)):He(Object(o)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(o,e))})}return t}function Vt(t,u,o){return(u=Bt(u))in t?Object.defineProperty(t,u,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[u]=o,t}function Bt(t){var u=It(t,"string");return ve(u)=="symbol"?u:u+""}function It(t,u){if(ve(t)!="object"||!t)return t;var o=t[Symbol.toPrimitive];if(o!==void 0){var e=o.call(t,u);if(ve(e)!="object")return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return(u==="string"?String:Number)(t)}function me(t){return At(t)||_t(t)||fu(t)||St()}function St(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function fu(t,u){if(t){if(typeof t=="string")return _e(t,u);var o={}.toString.call(t).slice(8,-1);return o==="Object"&&t.constructor&&(o=t.constructor.name),o==="Map"||o==="Set"?Array.from(t):o==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)?_e(t,u):void 0}}function _t(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function At(t){if(Array.isArray(t))return _e(t)}function _e(t,u){(u==null||u>t.length)&&(u=t.length);for(var o=0,e=Array(u);o<u;o++)e[o]=t[o];return e}var pu={name:"TreeNode",hostName:"Tree",extends:au,emits:["node-toggle","node-click","checkbox-change"],props:{node:{type:null,default:null},expandedKeys:{type:null,default:null},loadingMode:{type:String,default:"mask"},selectionKeys:{type:null,default:null},selectionMode:{type:String,default:null},templates:{type:null,default:null},level:{type:Number,default:null},index:null},nodeTouched:!1,toggleClicked:!1,mounted:function(){this.setAllNodesTabIndexes()},methods:{toggle:function(){this.$emit("node-toggle",this.node),this.toggleClicked=!0},label:function(u){return typeof u.label=="function"?u.label():u.label},onChildNodeToggle:function(u){this.$emit("node-toggle",u)},getPTOptions:function(u){return this.ptm(u,{context:{node:this.node,index:this.index,expanded:this.expanded,selected:this.selected,checked:this.checked,partialChecked:this.partialChecked,leaf:this.leaf}})},onClick:function(u){if(this.toggleClicked||Ie(u.target,'[data-pc-section="nodetogglebutton"]')||Ie(u.target.parentElement,'[data-pc-section="nodetogglebutton"]')){this.toggleClicked=!1;return}this.isCheckboxSelectionMode()?this.node.selectable!=!1&&this.toggleCheckbox():this.$emit("node-click",{originalEvent:u,nodeTouched:this.nodeTouched,node:this.node}),this.nodeTouched=!1},onChildNodeClick:function(u){this.$emit("node-click",u)},onTouchEnd:function(){this.nodeTouched=!0},onKeyDown:function(u){if(this.isSameNode(u))switch(u.code){case"Tab":this.onTabKey(u);break;case"ArrowDown":this.onArrowDown(u);break;case"ArrowUp":this.onArrowUp(u);break;case"ArrowRight":this.onArrowRight(u);break;case"ArrowLeft":this.onArrowLeft(u);break;case"Enter":case"NumpadEnter":case"Space":this.onEnterKey(u);break}},onArrowDown:function(u){var o=u.target.getAttribute("data-pc-section")==="nodetogglebutton"?u.target.closest('[role="treeitem"]'):u.target,e=o.children[1];if(e)this.focusRowChange(o,e.children[0]);else if(o.nextElementSibling)this.focusRowChange(o,o.nextElementSibling);else{var s=this.findNextSiblingOfAncestor(o);s&&this.focusRowChange(o,s)}u.preventDefault()},onArrowUp:function(u){var o=u.target;if(o.previousElementSibling)this.focusRowChange(o,o.previousElementSibling,this.findLastVisibleDescendant(o.previousElementSibling));else{var e=this.getParentNodeElement(o);e&&this.focusRowChange(o,e)}u.preventDefault()},onArrowRight:function(u){var o=this;this.leaf||this.expanded||(u.currentTarget.tabIndex=-1,this.$emit("node-toggle",this.node),this.$nextTick(function(){o.onArrowDown(u)}))},onArrowLeft:function(u){var o=Ue(u.currentTarget,'[data-pc-section="nodetogglebutton"]');if(this.level===0&&!this.expanded)return!1;if(this.expanded&&!this.leaf)return o.click(),!1;var e=this.findBeforeClickableNode(u.currentTarget);e&&this.focusRowChange(u.currentTarget,e)},onEnterKey:function(u){this.setTabIndexForSelectionMode(u,this.nodeTouched),this.onClick(u),u.preventDefault()},onTabKey:function(){this.setAllNodesTabIndexes()},setAllNodesTabIndexes:function(){var u=Re(this.$refs.currentNode.closest('[data-pc-section="rootchildren"]'),'[role="treeitem"]'),o=me(u).some(function(s){return s.getAttribute("aria-selected")==="true"||s.getAttribute("aria-checked")==="true"});if(me(u).forEach(function(s){s.tabIndex=-1}),o){var e=me(u).filter(function(s){return s.getAttribute("aria-selected")==="true"||s.getAttribute("aria-checked")==="true"});e[0].tabIndex=0;return}me(u)[0].tabIndex=0},setTabIndexForSelectionMode:function(u,o){if(this.selectionMode!==null){var e=me(Re(this.$refs.currentNode.parentElement,'[role="treeitem"]'));u.currentTarget.tabIndex=o===!1?-1:0,e.every(function(s){return s.tabIndex===-1})&&(e[0].tabIndex=0)}},focusRowChange:function(u,o,e){u.tabIndex="-1",o.tabIndex="0",this.focusNode(e||o)},findBeforeClickableNode:function(u){var o=u.closest("ul").closest("li");if(o){var e=Ue(o,"button");return e&&e.style.visibility!=="hidden"?o:this.findBeforeClickableNode(u.previousElementSibling)}return null},toggleCheckbox:function(){var u=this.selectionKeys?Xe({},this.selectionKeys):{},o=!this.checked;this.propagateDown(this.node,o,u),this.$emit("checkbox-change",{node:this.node,check:o,selectionKeys:u})},propagateDown:function(u,o,e){if(o&&u.selectable!=!1?e[u.key]={checked:!0,partialChecked:!1}:delete e[u.key],u.children&&u.children.length){var s=qe(u.children),a;try{for(s.s();!(a=s.n()).done;){var l=a.value;this.propagateDown(l,o,e)}}catch(m){s.e(m)}finally{s.f()}}},propagateUp:function(u){var o=u.check,e=Xe({},u.selectionKeys),s=0,a=!1,l=qe(this.node.children),m;try{for(l.s();!(m=l.n()).done;){var c=m.value;e[c.key]&&e[c.key].checked?s++:e[c.key]&&e[c.key].partialChecked&&(a=!0)}}catch(f){l.e(f)}finally{l.f()}o&&s===this.node.children.length?e[this.node.key]={checked:!0,partialChecked:!1}:(o||delete e[this.node.key],a||s>0&&s!==this.node.children.length?e[this.node.key]={checked:!1,partialChecked:!0}:delete e[this.node.key]),this.$emit("checkbox-change",{node:u.node,check:u.check,selectionKeys:e})},onChildCheckboxChange:function(u){this.$emit("checkbox-change",u)},findNextSiblingOfAncestor:function(u){var o=this.getParentNodeElement(u);return o?o.nextElementSibling?o.nextElementSibling:this.findNextSiblingOfAncestor(o):null},findLastVisibleDescendant:function(u){var o=u.children[1];if(o){var e=o.children[o.children.length-1];return this.findLastVisibleDescendant(e)}else return u},getParentNodeElement:function(u){var o=u.parentElement.parentElement;return Ie(o,"role")==="treeitem"?o:null},focusNode:function(u){u.focus()},isCheckboxSelectionMode:function(){return this.selectionMode==="checkbox"},isSameNode:function(u){return u.currentTarget&&(u.currentTarget.isSameNode(u.target)||u.currentTarget.isSameNode(u.target.closest('[role="treeitem"]')))}},computed:{hasChildren:function(){return this.node.children&&this.node.children.length>0},expanded:function(){return this.expandedKeys&&this.expandedKeys[this.node.key]===!0},leaf:function(){return this.node.leaf===!1?!1:!(this.node.children&&this.node.children.length)},selectable:function(){return this.node.selectable===!1?!1:this.selectionMode!=null},selected:function(){return this.selectionMode&&this.selectionKeys?this.selectionKeys[this.node.key]===!0:!1},checkboxMode:function(){return this.selectionMode==="checkbox"&&this.node.selectable!==!1},checked:function(){return this.selectionKeys?this.selectionKeys[this.node.key]&&this.selectionKeys[this.node.key].checked:!1},partialChecked:function(){return this.selectionKeys?this.selectionKeys[this.node.key]&&this.selectionKeys[this.node.key].partialChecked:!1},ariaChecked:function(){return this.selectionMode==="single"||this.selectionMode==="multiple"?this.selected:void 0},ariaSelected:function(){return this.checkboxMode?this.checked:void 0}},components:{Checkbox:ct,ChevronDownIcon:tu,ChevronRightIcon:lu,CheckIcon:it,MinusIcon:dt,SpinnerIcon:nu},directives:{ripple:ft}},Gt=["aria-label","aria-selected","aria-expanded","aria-setsize","aria-posinset","aria-level","aria-checked","tabindex"],Mt=["data-p-selected","data-p-selectable"],Kt=["data-p-leaf"];function Nt(t,u,o,e,s,a){var l=$("SpinnerIcon"),m=$("Checkbox"),c=$("TreeNode",!0),f=ut("ripple");return d(),h("li",z({ref:"currentNode",class:t.cx("node"),role:"treeitem","aria-label":a.label(o.node),"aria-selected":a.ariaSelected,"aria-expanded":a.expanded,"aria-setsize":o.node.children?o.node.children.length:0,"aria-posinset":o.index+1,"aria-level":o.level,"aria-checked":a.ariaChecked,tabindex:o.index===0?0:-1,onKeydown:u[4]||(u[4]=function(){return a.onKeyDown&&a.onKeyDown.apply(a,arguments)})},a.getPTOptions("node")),[n("div",z({class:t.cx("nodeContent"),onClick:u[2]||(u[2]=function(){return a.onClick&&a.onClick.apply(a,arguments)}),onTouchend:u[3]||(u[3]=function(){return a.onTouchEnd&&a.onTouchEnd.apply(a,arguments)}),style:o.node.style},a.getPTOptions("nodeContent"),{"data-p-selected":a.checkboxMode?a.checked:a.selected,"data-p-selectable":a.selectable}),[ou((d(),h("button",z({type:"button",class:t.cx("nodeToggleButton"),onClick:u[0]||(u[0]=function(){return a.toggle&&a.toggle.apply(a,arguments)}),tabindex:"-1","data-p-leaf":a.leaf},a.getPTOptions("nodeToggleButton")),[o.node.loading&&o.loadingMode==="icon"?(d(),h(ae,{key:0},[o.templates.nodetoggleicon||o.templates.nodetogglericon?(d(),E(re(o.templates.nodetoggleicon||o.templates.nodetogglericon),{key:0,node:o.node,expanded:a.expanded,class:L(t.cx("nodeToggleIcon"))},null,8,["node","expanded","class"])):(d(),E(l,z({key:1,spin:"",class:t.cx("nodeToggleIcon")},a.getPTOptions("nodeToggleIcon")),null,16,["class"]))],64)):(d(),h(ae,{key:1},[o.templates.nodetoggleicon||o.templates.togglericon?(d(),E(re(o.templates.nodetoggleicon||o.templates.togglericon),{key:0,node:o.node,expanded:a.expanded,class:L(t.cx("nodeToggleIcon"))},null,8,["node","expanded","class"])):a.expanded?(d(),E(re(o.node.expandedIcon?"span":"ChevronDownIcon"),z({key:1,class:t.cx("nodeToggleIcon")},a.getPTOptions("nodeToggleIcon")),null,16,["class"])):(d(),E(re(o.node.collapsedIcon?"span":"ChevronRightIcon"),z({key:2,class:t.cx("nodeToggleIcon")},a.getPTOptions("nodeToggleIcon")),null,16,["class"]))],64))],16,Kt)),[[f]]),a.checkboxMode?(d(),E(m,{key:0,defaultValue:a.checked,binary:!0,indeterminate:a.partialChecked,class:L(t.cx("nodeCheckbox")),tabindex:-1,unstyled:t.unstyled,pt:a.getPTOptions("pcNodeCheckbox"),"data-p-partialchecked":a.partialChecked},{icon:g(function(I){return[o.templates.checkboxicon?(d(),E(re(o.templates.checkboxicon),{key:0,checked:I.checked,partialChecked:a.partialChecked,class:L(I.class)},null,8,["checked","partialChecked","class"])):F("",!0)]}),_:1},8,["defaultValue","indeterminate","class","unstyled","pt","data-p-partialchecked"])):F("",!0),o.templates.nodeicon?(d(),E(re(o.templates.nodeicon),z({key:1,node:o.node,class:[t.cx("nodeIcon")]},a.getPTOptions("nodeIcon")),null,16,["node","class"])):(d(),h("span",z({key:2,class:[t.cx("nodeIcon"),o.node.icon]},a.getPTOptions("nodeIcon")),null,16)),n("span",z({class:t.cx("nodeLabel")},a.getPTOptions("nodeLabel"),{onKeydown:u[1]||(u[1]=Q(function(){},["stop"]))}),[o.templates[o.node.type]||o.templates.default?(d(),E(re(o.templates[o.node.type]||o.templates.default),{key:0,node:o.node,expanded:a.expanded,selected:a.checkboxMode?a.checked:a.selected},null,8,["node","expanded","selected"])):(d(),h(ae,{key:1},[de(D(a.label(o.node)),1)],64))],16)],16,Mt),a.hasChildren&&a.expanded?(d(),h("ul",z({key:0,class:t.cx("nodeChildren"),role:"group"},t.ptm("nodeChildren")),[(d(!0),h(ae,null,fe(o.node.children,function(I){return d(),E(c,{key:I.key,node:I,templates:o.templates,level:o.level+1,loadingMode:o.loadingMode,expandedKeys:o.expandedKeys,onNodeToggle:a.onChildNodeToggle,onNodeClick:a.onChildNodeClick,selectionMode:o.selectionMode,selectionKeys:o.selectionKeys,onCheckboxChange:a.propagateUp,unstyled:t.unstyled,pt:t.pt},null,8,["node","templates","level","loadingMode","expandedKeys","onNodeToggle","onNodeClick","selectionMode","selectionKeys","onCheckboxChange","unstyled","pt"])}),128))],16)):F("",!0)],16,Gt)}pu.render=Nt;function ye(t){"@babel/helpers - typeof";return ye=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(u){return typeof u}:function(u){return u&&typeof Symbol=="function"&&u.constructor===Symbol&&u!==Symbol.prototype?"symbol":typeof u},ye(t)}function Se(t,u){var o=typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(!o){if(Array.isArray(t)||(o=mu(t))||u){o&&(t=o);var e=0,s=function(){};return{s,n:function(){return e>=t.length?{done:!0}:{done:!1,value:t[e++]}},e:function(f){throw f},f:s}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var a,l=!0,m=!1;return{s:function(){o=o.call(t)},n:function(){var f=o.next();return l=f.done,f},e:function(f){m=!0,a=f},f:function(){try{l||o.return==null||o.return()}finally{if(m)throw a}}}}function Pt(t){return jt(t)||Lt(t)||mu(t)||Ot()}function Ot(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function mu(t,u){if(t){if(typeof t=="string")return Ae(t,u);var o={}.toString.call(t).slice(8,-1);return o==="Object"&&t.constructor&&(o=t.constructor.name),o==="Map"||o==="Set"?Array.from(t):o==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)?Ae(t,u):void 0}}function Lt(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function jt(t){if(Array.isArray(t))return Ae(t)}function Ae(t,u){(u==null||u>t.length)&&(u=t.length);for(var o=0,e=Array(u);o<u;o++)e[o]=t[o];return e}function We(t,u){var o=Object.keys(t);if(Object.getOwnPropertySymbols){var e=Object.getOwnPropertySymbols(t);u&&(e=e.filter(function(s){return Object.getOwnPropertyDescriptor(t,s).enumerable})),o.push.apply(o,e)}return o}function ie(t){for(var u=1;u<arguments.length;u++){var o=arguments[u]!=null?arguments[u]:{};u%2?We(Object(o),!0).forEach(function(e){Ut(t,e,o[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(o)):We(Object(o)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(o,e))})}return t}function Ut(t,u,o){return(u=Rt(u))in t?Object.defineProperty(t,u,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[u]=o,t}function Rt(t){var u=zt(t,"string");return ye(u)=="symbol"?u:u+""}function zt(t,u){if(ye(t)!="object"||!t)return t;var o=t[Symbol.toPrimitive];if(o!==void 0){var e=o.call(t,u);if(ye(e)!="object")return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return(u==="string"?String:Number)(t)}var gu={name:"Tree",extends:Ft,inheritAttrs:!1,emits:["node-expand","node-collapse","update:expandedKeys","update:selectionKeys","node-select","node-unselect","filter"],data:function(){return{d_expandedKeys:this.expandedKeys||{},filterValue:null}},watch:{expandedKeys:function(u){this.d_expandedKeys=u}},methods:{onNodeToggle:function(u){var o=u.key;this.d_expandedKeys[o]?(delete this.d_expandedKeys[o],this.$emit("node-collapse",u)):(this.d_expandedKeys[o]=!0,this.$emit("node-expand",u)),this.d_expandedKeys=ie({},this.d_expandedKeys),this.$emit("update:expandedKeys",this.d_expandedKeys)},onNodeClick:function(u){if(this.selectionMode!=null&&u.node.selectable!==!1){var o=u.nodeTouched?!1:this.metaKeySelection,e=o?this.handleSelectionWithMetaKey(u):this.handleSelectionWithoutMetaKey(u);this.$emit("update:selectionKeys",e)}},onCheckboxChange:function(u){this.$emit("update:selectionKeys",u.selectionKeys),u.check?this.$emit("node-select",u.node):this.$emit("node-unselect",u.node)},handleSelectionWithMetaKey:function(u){var o=u.originalEvent,e=u.node,s=o.metaKey||o.ctrlKey,a=this.isNodeSelected(e),l;return a&&s?(this.isSingleSelectionMode()?l={}:(l=ie({},this.selectionKeys),delete l[e.key]),this.$emit("node-unselect",e)):(this.isSingleSelectionMode()?l={}:this.isMultipleSelectionMode()&&(l=s?this.selectionKeys?ie({},this.selectionKeys):{}:{}),l[e.key]=!0,this.$emit("node-select",e)),l},handleSelectionWithoutMetaKey:function(u){var o=u.node,e=this.isNodeSelected(o),s;return this.isSingleSelectionMode()?e?(s={},this.$emit("node-unselect",o)):(s={},s[o.key]=!0,this.$emit("node-select",o)):e?(s=ie({},this.selectionKeys),delete s[o.key],this.$emit("node-unselect",o)):(s=this.selectionKeys?ie({},this.selectionKeys):{},s[o.key]=!0,this.$emit("node-select",o)),s},isSingleSelectionMode:function(){return this.selectionMode==="single"},isMultipleSelectionMode:function(){return this.selectionMode==="multiple"},isNodeSelected:function(u){return this.selectionMode&&this.selectionKeys?this.selectionKeys[u.key]===!0:!1},isChecked:function(u){return this.selectionKeys?this.selectionKeys[u.key]&&this.selectionKeys[u.key].checked:!1},isNodeLeaf:function(u){return u.leaf===!1?!1:!(u.children&&u.children.length)},onFilterKeyup:function(u){(u.code==="Enter"||u.code==="NumpadEnter")&&u.preventDefault(),this.$emit("filter",{originalEvent:u,value:u.target.value})},findFilteredNodes:function(u,o){if(u){var e=!1;if(u.children){var s=Pt(u.children);u.children=[];var a=Se(s),l;try{for(a.s();!(l=a.n()).done;){var m=l.value,c=ie({},m);this.isFilterMatched(c,o)&&(e=!0,u.children.push(c))}}catch(f){a.e(f)}finally{a.f()}}if(e)return!0}},isFilterMatched:function(u,o){var e=o.searchFields,s=o.filterText,a=o.strict,l=!1,m=Se(e),c;try{for(m.s();!(c=m.n()).done;){var f=c.value,I=String(et(u,f)).toLocaleLowerCase(this.filterLocale);I.indexOf(s)>-1&&(l=!0)}}catch(C){m.e(C)}finally{m.f()}return(!l||a&&!this.isNodeLeaf(u))&&(l=this.findFilteredNodes(u,{searchFields:e,filterText:s,strict:a})||l),l}},computed:{filteredValue:function(){var u=[],o=$u(this.filterBy)?[this.filterBy]:this.filterBy.split(","),e=this.filterValue.trim().toLocaleLowerCase(this.filterLocale),s=this.filterMode==="strict",a=Se(this.value),l;try{for(a.s();!(l=a.n()).done;){var m=l.value,c=ie({},m),f={searchFields:o,filterText:e,strict:s};(s&&(this.findFilteredNodes(c,f)||this.isFilterMatched(c,f))||!s&&(this.isFilterMatched(c,f)||this.findFilteredNodes(c,f)))&&u.push(c)}}catch(I){a.e(I)}finally{a.f()}return u},valueToRender:function(){return this.filterValue&&this.filterValue.trim().length>0?this.filteredValue:this.value},containerDataP:function(){return ze({loading:this.loading,scrollable:this.scrollHeight==="flex"})},wrapperDataP:function(){return ze({scrollable:this.scrollHeight==="flex"})}},components:{TreeNode:pu,InputText:at,InputIcon:Qu,IconField:Ju,SearchIcon:uu,SpinnerIcon:nu}};function be(t){"@babel/helpers - typeof";return be=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(u){return typeof u}:function(u){return u&&typeof Symbol=="function"&&u.constructor===Symbol&&u!==Symbol.prototype?"symbol":typeof u},be(t)}function Ye(t,u){var o=Object.keys(t);if(Object.getOwnPropertySymbols){var e=Object.getOwnPropertySymbols(t);u&&(e=e.filter(function(s){return Object.getOwnPropertyDescriptor(t,s).enumerable})),o.push.apply(o,e)}return o}function Je(t){for(var u=1;u<arguments.length;u++){var o=arguments[u]!=null?arguments[u]:{};u%2?Ye(Object(o),!0).forEach(function(e){qt(t,e,o[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(o)):Ye(Object(o)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(o,e))})}return t}function qt(t,u,o){return(u=Ht(u))in t?Object.defineProperty(t,u,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[u]=o,t}function Ht(t){var u=Xt(t,"string");return be(u)=="symbol"?u:u+""}function Xt(t,u){if(be(t)!="object"||!t)return t;var o=t[Symbol.toPrimitive];if(o!==void 0){var e=o.call(t,u);if(be(e)!="object")return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return(u==="string"?String:Number)(t)}var Wt=["data-p"],Yt=["data-p"],Jt=["aria-labelledby","aria-label"];function Qt(t,u,o,e,s,a){var l=$("SpinnerIcon"),m=$("InputText"),c=$("SearchIcon"),f=$("InputIcon"),I=$("IconField"),C=$("TreeNode");return d(),h("div",z({class:t.cx("root"),"data-p":a.containerDataP},t.ptmi("root")),[t.loading&&t.loadingMode==="mask"?(d(),h("div",z({key:0,class:t.cx("mask")},t.ptm("mask")),[ge(t.$slots,"loadingicon",{class:L(t.cx("loadingIcon"))},function(){return[t.loadingIcon?(d(),h("i",z({key:0,class:[t.cx("loadingIcon"),"pi-spin",t.loadingIcon]},t.ptm("loadingIcon")),null,16)):(d(),E(l,z({key:1,spin:"",class:t.cx("loadingIcon")},t.ptm("loadingIcon")),null,16,["class"]))]})],16)):F("",!0),t.filter?(d(),E(I,{key:1,unstyled:t.unstyled,pt:Je(Je({},t.ptm("pcFilter")),t.ptm("pcFilterContainer")),class:L(t.cx("pcFilterContainer"))},{default:g(function(){return[r(m,{modelValue:s.filterValue,"onUpdate:modelValue":u[0]||(u[0]=function(V){return s.filterValue=V}),autocomplete:"off",class:L(t.cx("pcFilterInput")),placeholder:t.filterPlaceholder,unstyled:t.unstyled,onKeyup:a.onFilterKeyup,pt:t.ptm("pcFilterInput")},null,8,["modelValue","class","placeholder","unstyled","onKeyup","pt"]),r(f,{unstyled:t.unstyled,pt:t.ptm("pcFilterIconContainer")},{default:g(function(){return[ge(t.$slots,t.$slots.filtericon?"filtericon":"searchicon",{class:L(t.cx("filterIcon"))},function(){return[r(c,z({class:t.cx("filterIcon")},t.ptm("filterIcon")),null,16,["class"])]})]}),_:3},8,["unstyled","pt"])]}),_:3},8,["unstyled","pt","class"])):F("",!0),n("div",z({class:t.cx("wrapper"),style:{maxHeight:t.scrollHeight},"data-p":a.wrapperDataP},t.ptm("wrapper")),[ge(t.$slots,"header",{value:t.value,expandedKeys:t.expandedKeys,selectionKeys:t.selectionKeys}),n("ul",z({class:t.cx("rootChildren"),role:"tree","aria-labelledby":t.ariaLabelledby,"aria-label":t.ariaLabel},t.ptm("rootChildren")),[(d(!0),h(ae,null,fe(a.valueToRender,function(V,_){return d(),E(C,{key:V.key,node:V,templates:t.$slots,level:t.level+1,index:_,expandedKeys:s.d_expandedKeys,onNodeToggle:a.onNodeToggle,onNodeClick:a.onNodeClick,selectionMode:t.selectionMode,selectionKeys:t.selectionKeys,onCheckboxChange:a.onCheckboxChange,loadingMode:t.loadingMode,unstyled:t.unstyled,pt:t.pt},null,8,["node","templates","level","index","expandedKeys","onNodeToggle","onNodeClick","selectionMode","selectionKeys","onCheckboxChange","loadingMode","unstyled","pt"])}),128))],16,Jt),ge(t.$slots,"footer",{value:t.value,expandedKeys:t.expandedKeys,selectionKeys:t.selectionKeys})],16,Yt)],16,Wt)}gu.render=Qt;const Zt=ee({__name:"Tree",setup(t,{expose:u}){u();const e={theme:v({root:`bg-surface-0 dark:bg-surface-900 text-surface-700 dark:text-surface-0 p-4
        p-scrollable:flex p-scrollable:flex-1 p-scrollable:h-full p-scrollable:flex-col`,pcFilterContainer:{root:"relative mb-2"},pcFilterInput:{root:`w-full appearance-none rounded-md outline-hidden
            bg-surface-0 dark:bg-surface-950
            text-surface-700 dark:text-surface-0
            placeholder:text-surface-500 dark:placeholder:text-surface-400
            border border-surface-300 dark:border-surface-700
            hover:border-surface-400 dark:hover:border-surface-600
            focus:border-primary
            disabled:bg-surface-200 disabled:text-surface-500
            dark:disabled:bg-surface-700 dark:disabled:text-surface-400
            ps-3 pe-10 py-2 p-fluid:w-full
            transition-colors duration-200 shadow-[0_1px_2px_0_rgba(18,18,23,0.05)]`},pcFilterIconContainer:{root:"absolute top-1/2 -mt-2 leading-none end-3 z-1"},wrapper:"overflow-auto p-scrollable:flex-1",rootChildren:"flex flex-col list-none m-0 gap-[4px] pt-[2px] pb-0 px-0",node:"p-0 outline-none focus-visible:*:first:outline focus-visible:*:first:-outline-offset-1 focus-visible:*:first:outline-primary",nodeContent:`group rounded-md px-2 py-1 flex items-center text-surface-700 dark:text-surface-0 gap-1 transition-colors duration-200
        hover:p-selectable:not-p-selected:bg-surface-100 hover:p-selectable:not-p-selected:text-surface-700 
        dark:hover:p-selectable:not-p-selected:bg-surface-800 dark:hover:p-selectable:not-p-selected:text-surface-0
        p-selected:bg-highlight 
        p-selectable:cursor-pointer p-selectable:select-none`,nodeToggleButton:`cursor-pointer select-none inline-flex justify-center rounded-full items-center overflow-hidden relative flex-shrink-0
        w-7 h-7 p-0 p-leaf:invisible transition-colors duration-200 border-none
        bg-transparent hover:bg-surface-100 dark:hover:bg-surface-800
        group-p-selected:hover:bg-surface-0 dark:group-p-selected:hover:bg-surface-900 group-p-selected:hover:text-primary
        text-surface-500 dark:text-surface-400 hover:text-surface-600 dark:hover:text-surface-300
        group-p-selected:text-inherit`,nodeToggleIcon:"",nodeIcon:`text-surface-500 dark:text-surface-400 group-p-selected:text-primary
        group-hover:group-p-selectable:not-group-p-selected:text-surface-600 
        dark:group-hover:group-p-selectable:not-group-p-selected:text-surface-300
        transition-colors duration-200`,nodeLabel:"",pcNodeCheckbox:{root:"relative inline-flex select-none w-5 h-5 align-bottom",input:`peer cursor-pointer disabled:cursor-default appearance-none 
            absolute start-0 top-0 w-full h-full m-0 p-0 opacity-0 z-10
            border border-transparent rounded-xs`,box:`flex justify-center items-center rounded-sm w-5 h-5
            border border-surface-300 dark:border-surface-700
            bg-surface-0 dark:bg-surface-950
            text-surface-700 dark:text-surface-0
            peer-enabled:peer-hover:border-surface-400 dark:peer-enabled:peer-hover:border-surface-600
            p-checked:border-primary p-checked:bg-primary p-checked:text-primary-contrast
            peer-enabled:peer-hover:p-checked:bg-primary-emphasis peer-enabled:peer-hover:p-checked:border-primary-emphasis
            shadow-[0_1px_2px_0_rgba(18,18,23,0.05)] transition-colors duration-200`,icon:"text-sm w-[0.875rem] h-[0.875rem] transition-none"},nodeChildren:"flex flex-col list-none m-0 gap-[2px] pt-[2px] pe-0 pb-0 ps-4",mask:"bg-black/50 text-surface-200 absolute z-10 flex items-center justify-center",loadingIcon:"text-[2rem] h-8 w-8"}),get ChevronDownIcon(){return tu},get ChevronRightIcon(){return lu},get SearchIcon(){return uu},get Tree(){return gu},get ptViewMerge(){return rt}};return Object.defineProperty(e,"__isScriptSetup",{enumerable:!1,value:!0}),e}});function $t(t,u,o,e,s,a){return d(),E(e.Tree,{unstyled:"",pt:e.theme,ptOptions:{mergeProps:e.ptViewMerge}},tt({togglericon:g(({expanded:l})=>[l?(d(),E(e.ChevronDownIcon,{key:0})):(d(),E(e.ChevronRightIcon,{key:1}))]),filtericon:g(()=>[r(e.SearchIcon,{class:"text-surface-400"})]),_:2},[fe(t.$slots,(l,m)=>({name:m,fn:g(c=>[ge(t.$slots,m,pt(lt(c??{})))])}))]),1032,["pt","ptOptions"])}const vu=ue(Zt,[["render",$t]]),e4=`m-0 py-1.5 px-3 list-none cursor-text overflow-hidden flex items-center flex-wrap
        w-full text-surface-900 dark:text-surface-0 bg-surface-0 dark:bg-surface-950 
        border border-surface-300 dark:border-surface-600 rounded-md 
        transition-colors duration-200 appearance-none
        hover:border-surface-400 dark:hover:border-surface-500
        focus-within:outline-none focus-within:outline-offset-0 focus-within:ring-1 focus-within:ring-primary-500 focus-within:border-primary-500
        p-invalid:border-red-500 p-invalid:focus-within:ring-red-500 p-invalid:focus-within:border-red-500`,u4=`py-1 px-2 mr-2 bg-surface-200 dark:bg-surface-700 text-surface-700 dark:text-surface-300 rounded-md 
        inline-flex items-center`,t4="leading-none",l4="ml-2 w-4 h-4 cursor-pointer",o4=`border-0 outline-none bg-transparent m-0 p-0 shadow-none rounded-none w-full
        text-surface-700 dark:text-surface-200 placeholder:text-surface-400 dark:placeholder:text-surface-500 flex-1 inline-flex`,n4=ee({__name:"InputChips",props:{modelValue:{},separator:{},addOnBlur:{type:Boolean},allowDuplicate:{type:Boolean},max:{}},emits:["update:modelValue"],setup(t,{expose:u,emit:o}){u();const e=t,s=o,a=j({get:()=>e.modelValue,set:_=>s("update:modelValue",_)}),l=v(""),m=v(null),c=()=>{if(l.value.trim()!==""){if(e.max&&a.value.length>=e.max)return;if(!e.allowDuplicate&&a.value.includes(l.value.trim())){l.value="";return}a.value=[...a.value,l.value.trim()],l.value=""}},f=_=>{a.value=a.value.filter((N,B)=>B!==_)},V={props:e,emit:s,model:a,inputValue:l,inputRef:m,addValue:c,removeValue:f,handleBackspace:()=>{l.value===""&&a.value.length>0&&f(a.value.length-1)},focusInput:async()=>{await ot(),m.value?.focus()},containerClass:e4,tokenClass:u4,labelClass:t4,removeIconClass:l4,inputClass:o4,get TimesIcon(){return st}};return Object.defineProperty(V,"__isScriptSetup",{enumerable:!1,value:!0}),V}}),a4=["onClick"],r4=["onKeydown"];function i4(t,u,o,e,s,a){return d(),h("div",{class:L(e.containerClass),onClick:e.focusInput},[(d(!0),h(ae,null,fe(e.model,(l,m)=>(d(),h("div",{key:m,class:L(e.tokenClass)},[n("span",{class:L(e.labelClass)},D(l),1),n("span",{class:L(e.removeIconClass),onClick:Q(c=>e.removeValue(m),["stop"])},[r(e.TimesIcon)],8,a4)]))),128)),ou(n("input",{ref:"inputRef",type:"text","onUpdate:modelValue":u[0]||(u[0]=l=>e.inputValue=l),class:L(e.inputClass),onKeydown:[De(Q(e.addValue,["prevent"]),["enter"]),De(e.handleBackspace,["backspace"])]},null,40,r4),[[Xu,e.inputValue]])])}const s4=ue(n4,[["render",i4]]),d4=ee({__name:"EditSynonymGroupDialog",props:{visible:{type:Boolean},templateId:{},group:{}},emits:["update:visible","saved"],setup(t,{expose:u,emit:o}){u();const e=t,s=o,{attributeSynonyms:a}=ce(),l=Te(),m=j({get:()=>e.visible,set:k=>s("update:visible",k)}),c=j(()=>!!e.group?.id),f=j(()=>c.value?"Редактировать группу":"Создать группу"),I=[{label:"EXACT",value:"EXACT"},{label:"NEAR",value:"NEAR"},{label:"LEGACY",value:"LEGACY"}],C=v({name:"",canonicalValue:"",parentId:null,description:"",compatibilityLevel:"EXACT",notes:""}),V=v([]),_=j(()=>{const k=Array.isArray(V.value)?V.value:[];if(!e.group?.id)return[{id:null,name:"Нет родителя"},...k];const X=new Map;for(const y of k)if(typeof y.parentId=="number"){const p=X.get(y.parentId)||[];p.push(y.id),X.set(y.parentId,p)}const U=new Set,R=[e.group.id];for(;R.length;){const y=R.pop(),p=X.get(y)||[];for(const T of p)U.has(T)||(U.add(T),R.push(T))}const P=k.filter(y=>y&&typeof y.id=="number"?y.id!==e.group.id&&!U.has(y.id):!0);return[{id:null,name:"Нет родителя"},...P]}),N=v({}),B=v(!1);H(()=>e.group,k=>{k?C.value={name:k.name||"",canonicalValue:k.canonicalValue||k.name||"",parentId:k.parentId??null,description:k.description||null,compatibilityLevel:k.compatibilityLevel||"EXACT",notes:k.notes||null}:C.value={name:"",canonicalValue:"",parentId:null,description:null,compatibilityLevel:"EXACT",notes:null}},{immediate:!0}),H(()=>e.templateId,async k=>{if(!k)return;const{attributeSynonyms:X}=ce(),U=100;let R=0,P=[],y=0;for(;;){const p=await X.groups.findMany({templateId:k,limit:U,offset:R}),T=p?.groups??[];if(y=p?.total??y,P=P.concat(T),T.length<U||(R+=U,y&&R>=y))break}V.value=P},{immediate:!0});const w=()=>(N.value={},C.value.name.trim()||(N.value.name="Введите название"),!C.value.parentId&&!C.value.canonicalValue.trim()&&(N.value.canonicalValue="Введите каноническое значение"),Object.keys(N.value).length===0),K=()=>{m.value=!1},A={props:e,emit:s,attributeSynonyms:a,toast:l,visible:m,isEdit:c,dialogTitle:f,compatibilityOptions:I,form:C,parentOptions:V,parentSelectOptions:_,errors:N,saving:B,validate:w,close:K,save:async()=>{if(w()){B.value=!0;try{c.value?(await a.groups.update({id:e.group.id,...C.value}),l.success("Группа обновлена")):(await a.groups.create({templateId:e.templateId,name:C.value.name,canonicalValue:C.value.canonicalValue||void 0,parentId:C.value.parentId,description:C.value.description,compatibilityLevel:C.value.compatibilityLevel,notes:C.value.notes}),l.success("Группа создана")),s("saved"),K()}catch(k){l.error(k?.message||"Не удалось сохранить группу")}finally{B.value=!1}}},VDialog:we,VInputText:Ee,VTextarea:Ge,VButton:he,VSelect:Me};return Object.defineProperty(A,"__isScriptSetup",{enumerable:!1,value:!0}),A}}),c4={class:"space-y-4"},f4={key:0,class:"p-error"},p4={key:0,class:"p-error"},m4={class:"grid grid-cols-1 md:grid-cols-2 gap-4"};function g4(t,u,o,e,s,a){return d(),E(e.VDialog,{visible:e.visible,"onUpdate:visible":u[6]||(u[6]=l=>e.visible=l),modal:"",header:e.dialogTitle,style:{width:"34rem"}},{footer:g(()=>[r(e.VButton,{label:"Отмена",severity:"secondary",onClick:e.close}),r(e.VButton,{label:e.isEdit?"Сохранить":"Создать",loading:e.saving,onClick:e.save},null,8,["label","loading"])]),default:g(()=>[n("div",c4,[n("div",null,[u[7]||(u[7]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"},"Название *",-1)),r(e.VInputText,{modelValue:e.form.name,"onUpdate:modelValue":u[0]||(u[0]=l=>e.form.name=l),placeholder:"Стандартные типы уплотнений",class:L(["w-full",{"p-invalid":!!e.errors.name}])},null,8,["modelValue","class"]),e.errors.name?(d(),h("small",f4,D(e.errors.name),1)):F("",!0)]),n("div",null,[u[8]||(u[8]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"},"Описание",-1)),r(e.VTextarea,{modelValue:e.form.description,"onUpdate:modelValue":u[1]||(u[1]=l=>e.form.description=l),rows:"2",class:"w-full"},null,8,["modelValue"])]),n("div",null,[u[9]||(u[9]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"},"Каноническое значение",-1)),r(e.VInputText,{modelValue:e.form.canonicalValue,"onUpdate:modelValue":u[2]||(u[2]=l=>e.form.canonicalValue=l),placeholder:"Например: TC (стандарт)",class:L(["w-full",{"p-invalid":!!e.errors.canonicalValue}])},null,8,["modelValue","class"]),e.errors.canonicalValue?(d(),h("small",p4,D(e.errors.canonicalValue),1)):F("",!0)]),n("div",m4,[n("div",null,[u[10]||(u[10]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"},"Родительская группа",-1)),r(e.VSelect,{modelValue:e.form.parentId,"onUpdate:modelValue":u[3]||(u[3]=l=>e.form.parentId=l),options:e.parentSelectOptions,"option-label":"name","option-value":"id",class:"w-full",placeholder:"Не выбрано"},null,8,["modelValue","options"])]),n("div",null,[u[11]||(u[11]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"},"Уровень совместимости",-1)),r(e.VSelect,{modelValue:e.form.compatibilityLevel,"onUpdate:modelValue":u[4]||(u[4]=l=>e.form.compatibilityLevel=l),options:e.compatibilityOptions,"option-label":"label","option-value":"value",class:"w-full"},null,8,["modelValue"])]),n("div",null,[u[12]||(u[12]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"},"Заметки",-1)),r(e.VInputText,{modelValue:e.form.notes,"onUpdate:modelValue":u[5]||(u[5]=l=>e.form.notes=l),placeholder:"Например: для старых спецификаций",class:"w-full"},null,8,["modelValue"])])])])]),_:1},8,["visible","header"])}const v4=ue(d4,[["render",g4]]),y4=ee({__name:"SynonymValueEditor",props:{groupId:{}},setup(t,{expose:u}){u();const o=t,{attributeSynonyms:e,brands:s}=ce(),a=Te(),l=v([]),m=v(!1),c=v(""),f=v(null),I=v(null),C=v(null),V=[{label:"EXACT",value:"EXACT"},{label:"NEAR",value:"NEAR"},{label:"LEGACY",value:"LEGACY"}],_=v([]),N=async()=>{const y=await s.findMany({});_.value=Array.isArray(y)?y.map(p=>({id:p.id,name:p.name})):[]},B=y=>_.value.find(p=>p.id===y)?.name||"",w=v(!1);H([c,f,I,C],()=>{const y=(c.value||"").trim();w.value=y.length>0&&!l.value.some(p=>p.value.toLowerCase()===y.toLowerCase())});const K=async()=>{m.value=!0;try{await N();const y=await e.synonyms.findMany({groupId:o.groupId});Array.isArray(y)&&(l.value=y)}catch(y){a.error(y?.message||"Не удалось загрузить значения")}finally{m.value=!1}},G=async()=>{const y=c.value.trim();if(y){if(l.value.some(p=>p.value.toLowerCase()===y.toLowerCase())){a.error("Дубликаты не допускаются");return}try{const p=await e.synonyms.create({groupId:o.groupId,value:y,notes:f.value,brandId:C.value??void 0,compatibilityLevel:I.value??void 0});p&&typeof p=="object"&&(l.value.push(p),c.value="",f.value=null,I.value=null,C.value=null)}catch(p){a.error(p?.message||"Не удалось добавить значение")}}},A=v(!1),k=v({id:0,notes:null,brandId:null,compatibilityLevel:null}),X=y=>{k.value={id:y.id,notes:y.notes??null,brandId:y.brandId??null,compatibilityLevel:y.compatibilityLevel??null},A.value=!0},U=async()=>{try{const y=await e.synonyms.update({id:k.value.id,notes:k.value.notes,brandId:k.value.brandId??void 0,compatibilityLevel:k.value.compatibilityLevel??void 0});if(y){const p=l.value.findIndex(T=>T.id===k.value.id);p!==-1&&(l.value[p]={...l.value[p],...y}),A.value=!1}}catch(y){a.error(y?.message||"Не удалось сохранить изменения")}},R=async y=>{if(confirm("Удалить значение?"))try{await e.synonyms.delete({id:y.id}),l.value=l.value.filter(p=>p.id!==y.id)}catch(p){a.error(p?.message||"Не удалось удалить значение")}};xe(K),H(()=>o.groupId,K);const P={props:o,attributeSynonyms:e,brands:s,toast:a,synonyms:l,loading:m,newValue:c,newNotes:f,newLevel:I,newBrandId:C,compatibilityOptions:V,brandOptions:_,loadBrands:N,brandName:B,canAdd:w,load:K,addValue:G,showEdit:A,editForm:k,editRow:X,saveEdit:U,removeValue:R,VInputText:Ee,VButton:he,VDataTable:Ze,VDialog:we,VSelect:Me,get Column(){return $e},get PlusIcon(){return ru},get PencilIcon(){return Oe},get TrashIcon(){return Pe},DangerButton:Ne};return Object.defineProperty(P,"__isScriptSetup",{enumerable:!1,value:!0}),P}}),b4={class:"space-y-4"},h4={class:"grid grid-cols-1 md:grid-cols-5 gap-2 items-end"},x4={class:"flex gap-2"},C4={class:"space-y-3"};function k4(t,u,o,e,s,a){return d(),h("div",b4,[n("div",h4,[r(e.VInputText,{modelValue:e.newValue,"onUpdate:modelValue":u[0]||(u[0]=l=>e.newValue=l),placeholder:"Значение",class:"w-full md:col-span-1",onKeyup:De(e.addValue,["enter"])},null,8,["modelValue"]),r(e.VInputText,{modelValue:e.newNotes,"onUpdate:modelValue":u[1]||(u[1]=l=>e.newNotes=l),placeholder:"Заметки (опц.)",class:"w-full md:col-span-1",onKeyup:De(e.addValue,["enter"])},null,8,["modelValue"]),r(e.VSelect,{modelValue:e.newBrandId,"onUpdate:modelValue":u[2]||(u[2]=l=>e.newBrandId=l),options:e.brandOptions,"option-label":"name","option-value":"id",class:"w-full md:col-span-1",placeholder:"Бренд (опц.)"},null,8,["modelValue","options"]),r(e.VSelect,{modelValue:e.newLevel,"onUpdate:modelValue":u[3]||(u[3]=l=>e.newLevel=l),options:e.compatibilityOptions,"option-label":"label","option-value":"value",class:"w-full md:col-span-1"},null,8,["modelValue"]),r(e.VButton,{onClick:e.addValue,disabled:!e.canAdd},{default:g(()=>[r(e.PlusIcon,{class:"w-4 h-4"})]),_:1},8,["disabled"])]),r(e.VDataTable,{value:e.synonyms,loading:e.loading,class:"p-datatable-sm","table-style":"min-width: 44rem","striped-rows":""},{default:g(()=>[r(e.Column,{field:"value",header:"Значение"}),r(e.Column,{field:"brandId",header:"Бренд"},{body:g(({data:l})=>[n("span",null,D(e.brandName(l.brandId)),1)]),_:1}),r(e.Column,{field:"compatibilityLevel",header:"Уровень"}),r(e.Column,{field:"notes",header:"Заметки"}),r(e.Column,{header:"",style:{width:"120px"}},{body:g(({data:l})=>[n("div",x4,[r(e.VButton,{size:"small",severity:"secondary",outlined:"",onClick:m=>e.editRow(l)},{default:g(()=>[r(e.PencilIcon,{class:"w-4 h-4"})]),_:2},1032,["onClick"]),r(e.DangerButton,{size:"small",outlined:"",onClick:m=>e.removeValue(l)},{default:g(()=>[r(e.TrashIcon,{class:"w-4 h-4"})]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["value","loading"]),r(e.VDialog,{visible:e.showEdit,"onUpdate:visible":u[8]||(u[8]=l=>e.showEdit=l),modal:"",header:"Редактировать синоним",style:{width:"28rem"}},{footer:g(()=>[r(e.VButton,{label:"Отмена",severity:"secondary",onClick:u[7]||(u[7]=l=>e.showEdit=!1)}),r(e.VButton,{label:"Сохранить",onClick:e.saveEdit})]),default:g(()=>[n("div",C4,[r(e.VSelect,{modelValue:e.editForm.brandId,"onUpdate:modelValue":u[4]||(u[4]=l=>e.editForm.brandId=l),options:e.brandOptions,"option-label":"name","option-value":"id",class:"w-full",placeholder:"Бренд (опц.)"},null,8,["modelValue","options"]),r(e.VInputText,{modelValue:e.editForm.notes,"onUpdate:modelValue":u[5]||(u[5]=l=>e.editForm.notes=l),placeholder:"Заметки",class:"w-full"},null,8,["modelValue"]),r(e.VSelect,{modelValue:e.editForm.compatibilityLevel,"onUpdate:modelValue":u[6]||(u[6]=l=>e.editForm.compatibilityLevel=l),options:e.compatibilityOptions,"option-label":"label","option-value":"value",class:"w-full"},null,8,["modelValue"])])]),_:1},8,["visible"])])}const D4=ue(y4,[["render",k4]]),E4=ee({__name:"AttributeSynonymManager",props:{template:{}},setup(t,{expose:u}){u();const o=t,e=Te(),{attributeSynonyms:s}=ce(),a=v([]),l=v(0),m=v(!1),c=v(null),f=v(!1),I=v(null),C=v({}),V=async()=>{if(o.template?.id){m.value=!0;try{let T=0,S=[],q=0;for(;;){const W=await s.groups.findMany({templateId:o.template.id,limit:100,offset:T}),Y=W?.groups??[];if(q=W?.total??q,S=S.concat(Y),Y.length<100||(T+=100,q&&T>=q))break}if(a.value=S,l.value=q||S.length,c.value){const W=a.value.find(Y=>Y.id===c.value.id);W&&(c.value=W)}U()}catch(p){e.error(p?.message||"Не удалось загрузить группы")}finally{m.value=!1}}},_=p=>{switch(p){case"EXACT":return"EXACT";case"NEAR":return"NEAR";case"LEGACY":return"LEGACY";default:return String(p)}},N=p=>p==="EXACT"?"success":p==="NEAR"?"warn":"secondary",B=p=>{c.value=p,U()},w=()=>{I.value=null,f.value=!0},K=p=>{I.value=p,f.value=!0},G=async p=>{if(confirm(`Удалить группу "${p.name}"?`))try{await s.groups.delete({id:p.id}),c.value?.id===p.id&&(c.value=null),V()}catch(T){e.error(T?.message||"Не удалось удалить группу")}},A=()=>{f.value=!1,V()},k=j(()=>{const p=new Map;for(const T of a.value)p.set(T.id,T);return p}),X=j(()=>{const p=new Map,T=[];for(const S of a.value)p.set(S.id,{key:String(S.id),label:S.name,data:S,children:[]});for(const S of a.value){const q=p.get(S.id);S.parentId&&p.has(S.parentId)?p.get(S.parentId).children.push(q):T.push(q)}return T});function U(){const p={};let T=c.value?k.value.get(c.value.id):null;for(;T&&T.parentId;)p[String(T.parentId)]=!0,T=k.value.get(T.parentId);C.value=p}const R=p=>C.value[String(p.id)]===!0,P=p=>{const T=String(p.id),S={...C.value};S[T]?delete S[T]:S[T]=!0,C.value=S};xe(()=>{V()});const y={props:o,toast:e,attributeSynonyms:s,groups:a,total:l,loadingGroups:m,selectedGroup:c,showGroupDialog:f,editingGroup:I,expandedKeys:C,loadGroups:V,compatibilityLabel:_,compatibilitySeverity:N,selectGroup:B,openCreateGroup:w,openEditGroup:K,deleteGroup:G,onGroupSaved:A,idMap:k,treeNodes:X,updateExpandedForSelection:U,isGroupExpanded:R,toggleGroupExpansion:P,VCard:Qe,VButton:he,VTag:eu,VTree:vu,EditSynonymGroupDialog:v4,SynonymValueEditor:D4,get PlusCircleIcon(){return ht},get PencilIcon(){return Oe},get TrashIcon(){return Pe},get ChevronDownIcon(){return iu},get ChevronRightIcon(){return du},DangerButton:Ne};return Object.defineProperty(y,"__isScriptSetup",{enumerable:!1,value:!0}),y}}),w4={class:"flex justify-center gap-4"},T4={class:"p-4 space-y-3"},F4={class:"flex items-center justify-between"},V4=["onClick"],B4={class:"font-medium"},I4={key:0,class:"text-surface-500"},S4={class:"flex gap-2"},_4={class:"p-4 space-y-4"},A4={class:"flex items-center justify-between"},G4={class:"text-lg font-semibold text-surface-900 dark:text-surface-0"},M4={key:0,class:"flex items-center gap-2 mt-1"},K4={key:0,class:"text-surface-500"},N4={key:0,class:"flex gap-2"},P4={key:0,class:"text-surface-500"},O4={key:1};function L4(t,u,o,e,s,a){return d(),h("div",w4,[r(e.VCard,null,{content:g(()=>[n("div",T4,[n("div",F4,[u[6]||(u[6]=n("h3",{class:"text-lg font-semibold text-surface-900 dark:text-surface-0"},"Группы синонимов",-1)),r(e.VButton,{size:"small",onClick:e.openCreateGroup},{default:g(()=>[r(e.PlusCircleIcon,{class:"w-4 h-4"})]),_:1})]),r(e.VTree,{value:e.treeNodes,expandedKeys:e.expandedKeys,"onUpdate:expandedKeys":u[1]||(u[1]=l=>e.expandedKeys=l),filter:"",filterPlaceholder:"Поиск по названию / описанию..."},{default:g(({node:l})=>[n("div",{class:"flex items-center justify-between w-full",onClick:u[0]||(u[0]=Q(()=>{},["stop"]))},[n("div",{class:L(["flex items-center gap-2 flex-1 cursor-pointer rounded px-2 py-1 transition-colors",{"bg-primary text-primary-contrast":e.selectedGroup?.id===l.data.id,"hover:bg-surface-100 dark:hover:bg-surface-800":e.selectedGroup?.id!==l.data.id}]),onClick:m=>e.selectGroup(l.data)},[n("span",B4,D(l.data.name),1),l.data.description?(d(),h("small",I4,D(l.data.description),1)):F("",!0),r(e.VTag,{value:e.compatibilityLabel(l.data.compatibilityLevel),severity:e.compatibilitySeverity(l.data.compatibilityLevel)},null,8,["value","severity"]),r(e.VTag,{value:l.data._count?.synonyms||0,severity:"secondary"},null,8,["value"])],10,V4),n("div",S4,[r(e.VButton,{size:"small",severity:"secondary",outlined:"",onClick:Q(m=>e.openEditGroup(l.data),["stop"])},{default:g(()=>[r(e.PencilIcon,{class:"w-4 h-4"})]),_:2},1032,["onClick"]),r(e.VButton,{size:"small",severity:"danger",outlined:"",onClick:Q(m=>e.deleteGroup(l.data),["stop"])},{default:g(()=>[r(e.TrashIcon,{class:"w-4 h-4"})]),_:2},1032,["onClick"])])])]),_:1},8,["value","expandedKeys"])])]),_:1}),r(e.VCard,null,{content:g(()=>[n("div",_4,[n("div",A4,[n("div",null,[n("h3",G4,D(e.selectedGroup?e.selectedGroup.name:"Выберите группу"),1),e.selectedGroup?(d(),h("div",M4,[r(e.VTag,{value:e.compatibilityLabel(e.selectedGroup.compatibilityLevel),severity:e.compatibilitySeverity(e.selectedGroup.compatibilityLevel)},null,8,["value","severity"]),e.selectedGroup.notes?(d(),h("small",K4,D(e.selectedGroup.notes),1)):F("",!0)])):F("",!0)]),e.selectedGroup?(d(),h("div",N4,[r(e.VButton,{size:"small",severity:"secondary",outlined:"",onClick:u[2]||(u[2]=l=>e.toggleGroupExpansion(e.selectedGroup)),title:e.isGroupExpanded(e.selectedGroup)?"Свернуть группу":"Развернуть группу"},{default:g(()=>[e.isGroupExpanded(e.selectedGroup)?(d(),E(e.ChevronDownIcon,{key:0,class:"w-4 h-4"})):(d(),E(e.ChevronRightIcon,{key:1,class:"w-4 h-4"}))]),_:1},8,["title"]),r(e.VButton,{size:"small",severity:"secondary",outlined:"",onClick:u[3]||(u[3]=l=>e.openEditGroup(e.selectedGroup))},{default:g(()=>[r(e.PencilIcon,{class:"w-4 h-4"})]),_:1}),r(e.DangerButton,{size:"small",outlined:"",onClick:u[4]||(u[4]=l=>e.deleteGroup(e.selectedGroup))},{default:g(()=>[r(e.TrashIcon,{class:"w-4 h-4"})]),_:1})])):F("",!0)]),e.selectedGroup?(d(),h("div",O4,[r(e.SynonymValueEditor,{"group-id":e.selectedGroup.id},null,8,["group-id"])])):(d(),h("div",P4,"Слева выберите группу, чтобы редактировать значения."))])]),_:1}),r(e.EditSynonymGroupDialog,{visible:e.showGroupDialog,"onUpdate:visible":u[5]||(u[5]=l=>e.showGroupDialog=l),"template-id":o.template.id,group:e.editingGroup,onSaved:e.onGroupSaved},null,8,["visible","template-id","group"])])}const yu=ue(E4,[["render",L4]]),j4=ee({__name:"GroupTreeAutoComplete",props:{modelValue:{},groups:{},placeholder:{default:"Поиск группы..."},class:{default:""},invalid:{type:Boolean,default:!1}},emits:["update:modelValue","group-select","group-clear"],setup(t,{expose:u,emit:o}){u();const e=t,s=o,a=v(null),l=v([]),m=v(null),c=j(()=>{let B="w-full";return e.class&&(B+=` ${e.class}`),e.invalid&&(B+=" p-invalid"),B}),f=(B,w=0,K="")=>{const G=[];for(const A of B){const k=K?`${K} / ${A.name}`:A.name;G.push({...A,level:w,displayName:k,hasChildren:A.children&&A.children.length>0,templatesCount:A._count?.templates||0}),A.children&&A.children.length>0&&G.push(...f(A.children,w+1,k))}return G},I=async B=>{console.log("searchGroups вызван в GroupTreeAutoComplete",{event:B,groupsLength:e.groups.length});const w=B.query||"";m.value&&clearTimeout(m.value);const K=f(e.groups);if(!w.trim()){l.value=K,console.log("Показываем все группы:",l.value.length);return}m.value=setTimeout(()=>{try{l.value=K.filter(G=>G.name.toLowerCase().includes(w.toLowerCase())||G.description&&G.description.toLowerCase().includes(w.toLowerCase())||G.displayName.toLowerCase().includes(w.toLowerCase())),console.log("Фильтруем группы:",l.value.length)}catch(G){console.error("Ошибка поиска групп:",G),l.value=K}},300)},C=B=>{const w=B.value;s("update:modelValue",w.id),s("group-select",w),a.value=w},V=()=>{s("update:modelValue",null),s("group-clear"),a.value=null},_=()=>{if(e.modelValue){const B=f(e.groups);a.value=B.find(w=>w.id===e.modelValue)||null}else a.value=null};H(()=>e.modelValue,()=>{_()},{immediate:!0}),H(()=>e.groups,()=>{const B=f(e.groups);l.value=B,_()},{immediate:!0}),xe(()=>{console.log("GroupTreeAutoComplete монтируется");const B=f(e.groups);l.value=B,_(),console.log("Инициализированы группы в GroupTreeAutoComplete:",{hierarchyGroups:e.groups.length,flatGroups:l.value.length})}),nt(()=>{m.value&&clearTimeout(m.value)});const N={props:e,emit:s,selectedGroup:a,filteredGroups:l,searchTimeout:m,inputClass:c,flattenGroupsHierarchy:f,searchGroups:I,onGroupSelect:C,onGroupClear:V,findSelectedGroup:_,VAutoComplete:Ke,get FolderIcon(){return cu},get FileIcon(){return xt}};return Object.defineProperty(N,"__isScriptSetup",{enumerable:!1,value:!0}),N}}),U4={class:"group-tree-autocomplete"},R4={class:"flex items-center gap-2 py-1"},z4={class:"flex-1"},q4={key:2,class:"text-xs text-surface-500 bg-surface-100 dark:bg-surface-800 px-2 py-1 rounded"};function H4(t,u,o,e,s,a){return d(),h("div",U4,[r(e.VAutoComplete,{modelValue:e.selectedGroup,"onUpdate:modelValue":u[0]||(u[0]=l=>e.selectedGroup=l),suggestions:e.filteredGroups,onComplete:e.searchGroups,onDropdownClick:u[1]||(u[1]=()=>e.searchGroups({query:""})),"option-label":"displayName",placeholder:o.placeholder,class:L(e.inputClass),dropdown:"","dropdown-mode":"current","show-clear":"",onItemSelect:e.onGroupSelect,onClear:e.onGroupClear},{option:g(l=>[n("div",R4,[n("div",{style:mt({paddingLeft:`${l.option.level*16}px`}),class:"flex items-center gap-2 w-full"},[l.option.hasChildren?(d(),E(e.FolderIcon,{key:0,class:"w-4 h-4 text-surface-500"})):(d(),E(e.FileIcon,{key:1,class:"w-4 h-4 text-surface-400"})),n("span",z4,D(l.option.name),1),l.option.templatesCount>0?(d(),h("span",q4,D(l.option.templatesCount),1)):F("",!0)],4)])]),empty:g(()=>u[2]||(u[2]=[n("div",{class:"p-3 text-surface-500 text-center"}," Группы не найдены ",-1)])),_:1},8,["modelValue","suggestions","placeholder","class"])])}const X4=ue(j4,[["render",H4],["__scopeId","data-v-be3fab62"]]),W4=ee({__name:"TemplateForm",props:{modelValue:{},groups:{},hierarchyGroups:{default:()=>[]},loading:{type:Boolean,default:!1}},emits:["update:modelValue","save","cancel","group-created"],setup(t,{expose:u,emit:o}){u();const e=t,s=o,{attributeTemplates:a}=ce(),l=Te(),{userRole:m}=gt(),c=j({get:()=>e.modelValue,set:x=>s("update:modelValue",x)}),f=v({}),I=v(!1),C=v(!1),V=v({name:"",description:""}),_=v(!1),N=()=>{if(!c.value.id){l.info("Сначала сохраните шаблон");return}_.value=!0},B=v(null),w=[{label:"Строка",value:"STRING"},{label:"Число",value:"NUMBER"},{label:"Логическое",value:"BOOLEAN"},{label:"Дата",value:"DATE"},{label:"JSON",value:"JSON"}],K=[{label:"мм",value:"MM"},{label:"дюймы",value:"INCH"},{label:"футы",value:"FT"},{label:"г",value:"G"},{label:"кг",value:"KG"},{label:"т",value:"T"},{label:"фунты",value:"LB"},{label:"мл",value:"ML"},{label:"л",value:"L"},{label:"галлоны",value:"GAL"},{label:"шт",value:"PCS"},{label:"комплект",value:"SET"},{label:"пара",value:"PAIR"},{label:"бар",value:"BAR"},{label:"PSI",value:"PSI"},{label:"кВт",value:"KW"},{label:"л.с.",value:"HP"},{label:"Н⋅м",value:"NM"},{label:"об/мин",value:"RPM"},{label:"°C",value:"C"},{label:"°F",value:"F"},{label:"%",value:"PERCENT"}],G=v(w),A=v(K),k=j({get:()=>w.find(x=>x.value===c.value.dataType)||null,set:x=>{c.value.dataType=x?x.value:null}}),X=j({get:()=>K.find(x=>x.value===c.value.unit)||null,set:x=>{c.value.unit=x?x.value:null}}),U=x=>{console.log("filterDataTypes вызван в TemplateForm",{event:x});const J=x.query?.toLowerCase()||"";J.trim()?(G.value=w.filter(le=>le.label.toLowerCase().includes(J)),console.log("Фильтруем типы данных:",G.value.length)):(G.value=[...w],console.log("Показываем все типы данных:",G.value.length))},R=x=>{console.log("filterUnits вызван в TemplateForm",{event:x});const J=x.query?.toLowerCase()||"";J.trim()?(A.value=K.filter(le=>le.label.toLowerCase().includes(J)),console.log("Фильтруем единицы:",A.value.length)):(A.value=[...K],console.log("Показываем все единицы:",A.value.length))},P=()=>{c.value.groupId?B.value=e.groups.find(x=>x.id===c.value.groupId)||null:B.value=null},y=j(()=>!!c.value.id),p=j(()=>c.value.name&&c.value.title&&c.value.dataType&&!Object.keys(f.value).length),T=j(()=>`w-full ${f.value.dataType?"p-invalid":""}`),S=()=>{f.value={},c.value.name?/^[a-z0-9_]+$/.test(c.value.name)||(f.value.name="Только строчные буквы, цифры и подчеркивания"):f.value.name="Системное имя обязательно",c.value.title||(f.value.title="Отображаемое название обязательно"),c.value.dataType||(f.value.dataType="Тип данных обязателен")},q=()=>{S(),p.value?s("save",c.value):l.error("Пожалуйста, исправьте ошибки в форме")},W=x=>{l.info(`Выбрана группа: ${x.name}`)},Y=()=>{l.info("Группа сброшена")},pe=async()=>{if(!V.value.name){l.error("Введите название группы");return}try{C.value=!0,l.info("Создание группы...");const x=await a.createGroup(V.value);x&&typeof x=="object"&&"id"in x&&(c.value.groupId=x.id,B.value=x,I.value=!1,V.value={name:"",description:""},s("group-created",x))}catch(x){console.error("Ошибка создания группы:",x),x.message?.includes("уже существует")?l.error("Группа с таким названием уже существует"):l.error(x.message||"Не удалось создать группу")}finally{C.value=!1}};H(()=>e.modelValue,x=>{if(!x){c.value={dataType:"STRING",isRequired:!1,allowedValues:[]};return}x.dataType||(c.value.dataType="STRING"),x.isRequired||(c.value.isRequired=!1),x.allowedValues||(c.value.allowedValues=[]),P()},{immediate:!0}),H(()=>e.groups,()=>{P()},{immediate:!0}),xe(()=>{console.log("TemplateForm монтируется"),G.value=[...w],A.value=[...K],console.log("Инициализированы автокомплиты в TemplateForm:",{dataTypes:G.value.length,units:A.value.length,groups:e.groups.length,hierarchyGroups:e.hierarchyGroups.length}),P()}),H(()=>c.value.name,()=>{f.value.name&&delete f.value.name}),H(()=>c.value.title,()=>{f.value.title&&delete f.value.title}),H(()=>c.value.dataType,()=>{f.value.dataType&&delete f.value.dataType});const te={props:e,emit:s,attributeTemplates:a,toast:l,userRole:m,form:c,errors:f,showCreateGroupDialog:I,creatingGroup:C,newGroupForm:V,showSynonymsDialog:_,openSynonyms:N,selectedGroup:B,dataTypeOptions:w,unitOptions:K,filteredDataTypeOptions:G,filteredUnitOptions:A,selectedDataType:k,selectedUnit:X,filterDataTypes:U,filterUnits:R,findSelectedGroup:P,isEditing:y,isValid:p,dataTypeClass:T,validateForm:S,save:q,onGroupSelect:W,onGroupClear:Y,createGroup:pe,VInputText:Ee,VTextarea:Ge,VInputNumber:vt,InputChips:s4,VCheckbox:yt,VButton:he,VDialog:we,VAutoComplete:Ke,AttributeSynonymManager:yu,GroupTreeAutoComplete:X4,get PlusIcon(){return ru},get TagsIcon(){return su}};return Object.defineProperty(te,"__isScriptSetup",{enumerable:!1,value:!0}),te}}),Y4={class:"template-form"},J4={class:"space-y-4"},Q4={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Z4={key:0,class:"p-error"},$4={key:0,class:"p-error"},e0={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},u0={key:0,class:"p-error"},t0={class:"flex gap-2"},l0={key:0,class:"p-error"},o0={key:0,class:"grid grid-cols-1 md:grid-cols-2 gap-4"},n0={key:1},a0={key:2},r0={class:"mt-3"},i0={class:"flex items-center gap-4"},s0={class:"flex justify-end gap-3 mt-6 pt-4 border-t border-surface-200 dark:border-surface-700"},d0={class:"space-y-4"},c0={key:1,class:"p-4 text-surface-500"};function f0(t,u,o,e,s,a){return d(),h("div",Y4,[n("div",J4,[n("div",Q4,[n("div",null,[u[20]||(u[20]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Системное имя * ",-1)),r(e.VInputText,{modelValue:e.form.name,"onUpdate:modelValue":u[0]||(u[0]=l=>e.form.name=l),placeholder:"inner_diameter",class:L(["w-full",{"p-invalid":e.errors.name}])},null,8,["modelValue","class"]),e.errors.name?(d(),h("small",Z4,D(e.errors.name),1)):F("",!0),u[21]||(u[21]=n("small",{class:"text-surface-500 dark:text-surface-400"}," Только строчные буквы, цифры и подчеркивания ",-1))]),n("div",null,[u[22]||(u[22]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Отображаемое название * ",-1)),r(e.VInputText,{modelValue:e.form.title,"onUpdate:modelValue":u[1]||(u[1]=l=>e.form.title=l),placeholder:"Внутренний диаметр",class:L(["w-full",{"p-invalid":e.errors.title}])},null,8,["modelValue","class"]),e.errors.title?(d(),h("small",$4,D(e.errors.title),1)):F("",!0)])]),n("div",null,[u[23]||(u[23]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Описание ",-1)),r(e.VTextarea,{modelValue:e.form.description,"onUpdate:modelValue":u[2]||(u[2]=l=>e.form.description=l),placeholder:"Подробное описание атрибута...",rows:"3",class:"w-full"},null,8,["modelValue"])]),n("div",e0,[n("div",null,[u[24]||(u[24]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Тип данных * ",-1)),r(e.VAutoComplete,{modelValue:e.selectedDataType,"onUpdate:modelValue":u[3]||(u[3]=l=>e.selectedDataType=l),suggestions:e.filteredDataTypeOptions,onComplete:e.filterDataTypes,onDropdownClick:u[4]||(u[4]=()=>e.filterDataTypes({query:""})),"option-label":"label","option-value":"value",placeholder:"Выберите тип",class:L(e.dataTypeClass),dropdown:""},null,8,["modelValue","suggestions","class"]),e.errors.dataType?(d(),h("small",u0,D(e.errors.dataType),1)):F("",!0)]),n("div",null,[u[25]||(u[25]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Единица измерения ",-1)),r(e.VAutoComplete,{modelValue:e.selectedUnit,"onUpdate:modelValue":u[5]||(u[5]=l=>e.selectedUnit=l),suggestions:e.filteredUnitOptions,onComplete:e.filterUnits,onDropdownClick:u[6]||(u[6]=()=>e.filterUnits({query:""})),"option-label":"label","option-value":"value",placeholder:"Выберите единицу",class:"w-full",dropdown:"","show-clear":""},null,8,["modelValue","suggestions"])])]),n("div",null,[u[27]||(u[27]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Группа ",-1)),n("div",t0,[r(e.GroupTreeAutoComplete,{modelValue:e.form.groupId,"onUpdate:modelValue":u[7]||(u[7]=l=>e.form.groupId=l),groups:o.hierarchyGroups,placeholder:"Поиск группы...",class:"flex-1",invalid:!!e.errors.groupId,onGroupSelect:e.onGroupSelect,onGroupClear:e.onGroupClear},null,8,["modelValue","groups","invalid"]),r(e.VButton,{onClick:u[8]||(u[8]=l=>e.showCreateGroupDialog=!0),severity:"secondary",outlined:"",size:"small"},{default:g(()=>[u[26]||(u[26]=de(" Создать ")),r(e.PlusIcon,{class:"w-5 h-5"})]),_:1,__:[26]})]),e.errors.groupId?(d(),h("small",l0,D(e.errors.groupId),1)):F("",!0)]),e.form.dataType==="NUMBER"?(d(),h("div",o0,[n("div",null,[u[28]||(u[28]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Минимальное значение ",-1)),r(e.VInputNumber,{modelValue:e.form.minValue,"onUpdate:modelValue":u[9]||(u[9]=l=>e.form.minValue=l),placeholder:"0",class:"w-full","use-grouping":!1,"min-fraction-digits":2,"max-fraction-digits":2},null,8,["modelValue"])]),n("div",null,[u[29]||(u[29]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Максимальное значение ",-1)),r(e.VInputNumber,{modelValue:e.form.maxValue,"onUpdate:modelValue":u[10]||(u[10]=l=>e.form.maxValue=l),placeholder:"100",class:"w-full","use-grouping":!1,"min-fraction-digits":2,"max-fraction-digits":2},null,8,["modelValue"])])])):F("",!0),e.form.dataType==="NUMBER"?(d(),h("div",n0,[u[30]||(u[30]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Допустимое отклонение (tolerance) ",-1)),r(e.VInputNumber,{modelValue:e.form.tolerance,"onUpdate:modelValue":u[11]||(u[11]=l=>e.form.tolerance=l),placeholder:"0.1",class:"w-full","use-grouping":!1,"min-fraction-digits":1,"max-fraction-digits":4,min:0},null,8,["modelValue"]),u[31]||(u[31]=n("small",{class:"text-surface-500 dark:text-surface-400"}," Допустимое отклонение при сопоставлении числовых значений. Например: если эталон = 30.0 и допуск = 0.1, то значения от 29.9 до 30.1 будут считаться эквивалентными. ",-1))])):F("",!0),e.form.dataType==="STRING"?(d(),h("div",a0,[u[34]||(u[34]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Допустимые значения ",-1)),r(e.InputChips,{modelValue:e.form.allowedValues,"onUpdate:modelValue":u[12]||(u[12]=l=>e.form.allowedValues=l),placeholder:"Введите значение и нажмите Enter",class:"w-full"},null,8,["modelValue"]),u[35]||(u[35]=n("small",{class:"text-surface-500 dark:text-surface-400"}," Оставьте пустым для любых значений. Например: steel, aluminum, plastic ",-1)),n("div",r0,[r(e.VButton,{severity:"secondary",outlined:"",onClick:e.openSynonyms},{default:g(()=>[u[32]||(u[32]=de(" Синонимы ")),r(e.TagsIcon,{class:"w-5 h-5"})]),_:1,__:[32]}),u[33]||(u[33]=n("small",{class:"ml-2 text-surface-500"},"Доступно после сохранения шаблона",-1))])])):F("",!0),n("div",i0,[r(e.VCheckbox,{modelValue:e.form.isRequired,"onUpdate:modelValue":u[13]||(u[13]=l=>e.form.isRequired=l),"input-id":"required",binary:""},null,8,["modelValue"]),u[36]||(u[36]=n("label",{for:"required",class:"text-sm text-surface-700 dark:text-surface-300"}," Обязательный атрибут ",-1))])]),n("div",s0,[r(e.VButton,{label:"Отмена",severity:"secondary",onClick:u[14]||(u[14]=l=>t.$emit("cancel"))}),r(e.VButton,{label:e.isEditing?"Обновить":"Создать",onClick:e.save,loading:o.loading,disabled:!e.isValid},null,8,["label","loading","disabled"])]),r(e.VDialog,{visible:e.showCreateGroupDialog,"onUpdate:visible":u[18]||(u[18]=l=>e.showCreateGroupDialog=l),modal:"",header:"Создать группу атрибутов",style:{width:"30rem"}},{footer:g(()=>[r(e.VButton,{label:"Отмена",severity:"secondary",onClick:u[17]||(u[17]=l=>e.showCreateGroupDialog=!1)}),r(e.VButton,{label:"Создать",onClick:e.createGroup,loading:e.creatingGroup,disabled:!e.newGroupForm.name},null,8,["loading","disabled"])]),default:g(()=>[n("div",d0,[n("div",null,[u[37]||(u[37]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Название группы * ",-1)),r(e.VInputText,{modelValue:e.newGroupForm.name,"onUpdate:modelValue":u[15]||(u[15]=l=>e.newGroupForm.name=l),placeholder:"Размеры",class:"w-full"},null,8,["modelValue"])]),n("div",null,[u[38]||(u[38]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Описание ",-1)),r(e.VTextarea,{modelValue:e.newGroupForm.description,"onUpdate:modelValue":u[16]||(u[16]=l=>e.newGroupForm.description=l),placeholder:"Описание группы...",rows:"2",class:"w-full"},null,8,["modelValue"])])])]),_:1},8,["visible"]),r(e.VDialog,{visible:e.showSynonymsDialog,"onUpdate:visible":u[19]||(u[19]=l=>e.showSynonymsDialog=l),modal:"",header:"Синонимы значения",style:{width:"80rem"},breakpoints:{"1199px":"90vw","575px":"98vw"}},{default:g(()=>[e.form.id&&e.form.dataType==="STRING"?(d(),E(e.AttributeSynonymManager,{key:0,template:{id:e.form.id,dataType:e.form.dataType,title:e.form.title,name:e.form.name}},null,8,["template"])):(d(),h("div",c0,"Сохраните шаблон, чтобы управлять синонимами."))]),_:1},8,["visible"])])}const p0=ue(W4,[["render",f0]]),m0=ee({__name:"AttributeTemplateManager",setup(t,{expose:u}){u();const{attributeTemplates:o,loading:e}=ce(),s=v([]),a=v([]),l=v([]),m=v(0),c=v(25),f=v(0),I=v(!1);let C=0;const V=v(""),_=v(null),N=v(null),B=v("table"),w=Wu({search:"",groupId:void 0,dataType:void 0},{prefix:"attr_",numberParams:["groupId"],debounceMs:300});H(V,i=>{w.updateFilter("search",i||void 0)}),H(_,i=>{const b=i&&typeof i=="object"?i.id:i;w.updateFilter("groupId",b??void 0)}),H(N,i=>{const b=i&&typeof i=="object"?i.value:i;w.updateFilter("dataType",b??void 0)}),H(w.filters,i=>{const b=i.search||"",M=i.groupId??null,O=i.dataType??null;V.value!==b&&(V.value=b),_.value!==M&&(_.value=M),N.value!==O&&(N.value=O),f.value=0,oe()});const K=v(!1),G=v(null),A=v({}),k=v(!1),X=v(!1),U=v(null),R=v(!1),P=v(null),y=v({name:"",description:"",parentId:null}),p=v({}),T=v(!1),S=v(null),q=v({}),W=v(!1),Y=v([]),pe=v(!1),te=[{label:"Строка",value:"STRING"},{label:"Число",value:"NUMBER"},{label:"Логическое",value:"BOOLEAN"},{label:"Дата",value:"DATE"},{label:"JSON",value:"JSON"}],x=[{label:"Таблица",value:"table"},{label:"Карточки",value:"cards"}],J=v([]),le=v([]),Ce=v([]),bu=i=>{const b=i.query?.toLowerCase()||"",M={id:null,name:"Без группы",isSpecial:!0};if(!b.trim())J.value=[M,...a.value];else{const O=a.value.filter(zu=>zu.name.toLowerCase().includes(b)),Z=b.includes("без")||b.includes("группы")||b.includes("группа")?[M,...O]:O;J.value=Z}},hu=i=>{const b=i.query?.toLowerCase()||"";b.trim()?le.value=te.filter(M=>M.label.toLowerCase().includes(b)):le.value=[...te]},xu=i=>{const b=i.query?.toLowerCase()||"";b.trim()?Ce.value=x.filter(M=>M.label.toLowerCase().includes(b)):Ce.value=[...x]},Cu=j(()=>s.value.filter(i=>Ve(i._count)>0).length),ku=j(()=>s.value.filter(i=>Ve(i._count)===0).length),Fe=j(()=>{const i=new Map;for(const b of a.value)i.set(b.id,b);return i}),Du=j(()=>{const i=new Map,b=[],M={key:"no-group",label:"Без группы",data:{id:null,name:"Без группы",isSpecial:!0},children:[]};b.push(M);for(const O of a.value)i.set(O.id,{key:String(O.id),label:O.name,data:O,children:[]});for(const O of a.value){const Z=i.get(O.id);O.parentId&&i.has(O.parentId)?i.get(O.parentId).children.push(Z):b.push(Z)}return b}),Eu=j(()=>[{id:null,name:"Нет родителя"},...a.value.filter(i=>!P.value||i.id!==P.value.id)]),wu=i=>te.find(M=>M.value===i)?.label||i,Tu=i=>({MM:"мм",INCH:"дюймы",FT:"футы",G:"г",KG:"кг",T:"т",LB:"фунты",ML:"мл",L:"л",GAL:"галлоны",PCS:"шт",SET:"комплект",PAIR:"пара",BAR:"бар",PSI:"PSI",KW:"кВт",HP:"л.с.",NM:"Н⋅м",RPM:"об/мин",C:"°C",F:"°F",PERCENT:"%"})[i]||i,Ve=i=>i?(i.partAttributes||0)+(i.catalogItemAttributes||0)+(i.equipmentAttributes||0):0,Fu=i=>{if(!i)return"";const b=i.partAttributes||0,M=i.catalogItemAttributes||0,O=i.equipmentAttributes||0,Z=[];return b>0&&Z.push(`${b} зап.`),M>0&&Z.push(`${M} кат.`),O>0&&Z.push(`${O} тех.`),Z.join(", ")},oe=async()=>{const i=++C;I.value=!0;try{let b;_.value&&(typeof _.value=="object"?b=_.value.id:b=_.value);const M=N.value&&typeof N.value=="object"?N.value.value:void 0,O=await o.findMany({groupId:b,search:V.value||void 0,dataType:M,limit:c.value,offset:f.value*c.value});i===C&&O&&typeof O=="object"&&(s.value=O.templates||[],m.value=O.total||0)}catch(b){console.error("Ошибка загрузки шаблонов:",b),console.error("Не удалось загрузить шаблоны")}finally{i===C&&(I.value=!1)}},ne=async()=>{try{const[i,b]=await Promise.all([o.findAllGroups(),o.findGroupsHierarchy()]);if(i&&Array.isArray(i)){a.value=i;const M={id:null,name:"Без группы",isSpecial:!0};J.value=[M,...i]}b&&Array.isArray(b)&&(l.value=b)}catch(i){console.error("Ошибка загрузки групп:",i)}};let ke;const Vu=()=>{clearTimeout(ke),ke=setTimeout(()=>{f.value=0,w.updateFilter("search",V.value||""),oe()},500)},Bu=async()=>{await ne(),await oe()},Iu=i=>{f.value=i.page,c.value=i.rows,oe()},Su=i=>{G.value=i,A.value={...i},K.value=!0},_u=i=>{U.value=i,X.value=!0},Au=()=>{G.value=null,A.value={dataType:"STRING",isRequired:!1,allowedValues:[]},K.value=!0},Gu=async i=>{if(window.confirm(`Вы уверены, что хотите удалить шаблон "${i.title}"?`))try{await o.delete({id:i.id}),console.log("Шаблон успешно удален"),oe()}catch(M){console.error("Ошибка удаления шаблона:",M),alert(M.message||"Не удалось удалить шаблон")}},Mu=async i=>{try{k.value=!0,G.value?(await o.update({id:G.value.id,...i}),console.log("Шаблон успешно обновлен")):(await o.create(i),console.log("Шаблон успешно создан")),K.value=!1,G.value=null,A.value={},await ne(),await oe()}catch(b){console.error("Ошибка сохранения шаблона:",b),alert(b.message||"Не удалось сохранить шаблон")}finally{k.value=!1}};xe(async()=>{le.value=[...te],Ce.value=[...x];const i=w.filters.value;V.value=i.search||"",_.value=i.groupId??null,N.value=i.dataType??null,await ne(),await oe();const b=a.value.find(M=>!M.parentId);b&&(S.value=b,Be())});const Ku=i=>{S.value=i,Be(),W.value=!1,Y.value=[]},Nu=i=>q.value[String(i.id)]===!0,Pu=i=>{const b=String(i.id),M={...q.value};M[b]?delete M[b]:M[b]=!0,q.value=M},Be=()=>{const i={};let b=S.value?Fe.value.get(S.value.id):null;for(;b&&b.parentId;)i[String(b.parentId)]=!0,b=Fe.value.get(b.parentId);q.value=i},Ou=async()=>{await ne()},Lu=i=>{P.value=i,y.value={name:i.name,description:i.description||"",parentId:i.parentId??null},R.value=!0},ju=async i=>{if(confirm(`Удалить группу "${i.name}"?`))try{await o.deleteGroup({id:i.id}),await ne(),S.value?.id===i.id&&(S.value=null)}catch(b){console.error("Ошибка удаления группы:",b)}},Uu=async()=>{if(!y.value.name.trim()){p.value.name="Название обязательно";return}T.value=!0,p.value={};try{P.value?await o.updateGroup({id:P.value.id,name:y.value.name.trim(),description:y.value.description||void 0,parentId:y.value.parentId}):await o.createGroup({name:y.value.name.trim(),description:y.value.description||void 0,parentId:y.value.parentId}),Le(),await ne()}catch(i){i.message?.includes("уже существует")?p.value.name="Группа с таким именем уже существует":console.error("Ошибка сохранения группы:",i)}finally{T.value=!1}},Ru=async()=>{await ne()},Le=()=>{R.value=!1,P.value=null,y.value={name:"",description:"",parentId:null},p.value={}},je={attributeTemplates:o,loading:e,templates:s,groups:a,hierarchyGroups:l,totalCount:m,pageSize:c,currentPage:f,tableLoading:I,get lastRequestId(){return C},set lastRequestId(i){C=i},searchQuery:V,selectedGroup:_,selectedDataType:N,viewMode:B,urlSync:w,showCreateDialog:K,editingTemplate:G,templateForm:A,saving:k,showSynonymsDialog:X,selectedTemplateForSynonyms:U,showGroupDialog:R,editingGroup:P,groupForm:y,groupErrors:p,savingGroup:T,selectedTreeGroup:S,expandedKeys:q,showGroupTemplates:W,groupTemplates:Y,loadingGroupTemplates:pe,dataTypeOptions:te,viewModeOptions:x,groupSuggestions:J,dataTypeSuggestions:le,viewModeSuggestions:Ce,filterGroups:bu,filterDataTypes:hu,filterViewModes:xu,usedTemplatesCount:Cu,unusedTemplatesCount:ku,idMap:Fe,treeNodes:Du,parentSelectOptions:Eu,getDataTypeLabel:wu,getUnitLabel:Tu,getTotalUsage:Ve,getUsageDetails:Fu,loadTemplates:oe,loadGroups:ne,get searchTimeout(){return ke},set searchTimeout(i){ke=i},debouncedSearch:Vu,refreshData:Bu,onPageChange:Iu,editTemplate:Su,openSynonyms:_u,createNewTemplate:Au,deleteTemplate:Gu,saveTemplate:Mu,selectTreeGroup:Ku,isGroupExpanded:Nu,toggleGroupExpansion:Pu,updateExpandedForSelection:Be,refreshGroupData:Ou,editGroup:Lu,deleteGroup:ju,saveGroup:Uu,onGroupCreated:Ru,closeGroupDialog:Le,toggleGroupTemplates:async()=>{if(S.value&&(W.value=!W.value,W.value&&Y.value.length===0)){pe.value=!0;try{const i=await o.findMany({groupId:S.value.id,limit:100});Y.value=i?.templates||[]}catch(i){console.error("Ошибка загрузки шаблонов группы:",i)}finally{pe.value=!1}}},VCard:Qe,VButton:he,VInputText:Ee,VTextarea:Ge,VSelect:Me,VDataTable:Ze,VTag:eu,VDialog:we,VTree:vu,get Column(){return $e},get Paginator(){return Yu},TemplateForm:p0,VAutoComplete:Ke,AttributeSynonymManager:yu,Icon:bt,get TagsIcon(){return su},get PencilIcon(){return Oe},get TrashIcon(){return Pe},get RefreshCwIcon(){return qu},get TagIcon(){return Dt},get PencilLineIcon(){return kt},get LoaderCircleIcon(){return Ct},get FolderIcon(){return cu},get ChevronDownIcon(){return iu},get ChevronRightIcon(){return du},DangerButton:Ne};return Object.defineProperty(je,"__isScriptSetup",{enumerable:!1,value:!0}),je}}),g0={class:"attribute-template-manager"},v0={key:0,class:"py-12 text-center"},y0={key:1},b0={class:"mb-6 flex items-center justify-between"},h0={class:"flex gap-3"},x0={class:"mb-6 grid grid-cols-1 gap-4 lg:grid-cols-3"},C0={class:"p-4 space-y-3"},k0={class:"flex items-center justify-between"},D0=["onClick"],E0={class:"font-medium"},w0={class:"flex gap-1"},T0={class:"p-4 space-y-3"},F0={class:"flex items-center justify-between"},V0={class:"text-lg font-semibold text-surface-900 dark:text-surface-0"},B0={key:0,class:"flex gap-2"},I0={key:0,class:"text-surface-500 text-center py-8"},S0={key:1,class:"space-y-4"},_0={class:"flex items-center gap-3"},A0={class:"flex-1"},G0={class:"font-medium"},M0={key:0,class:"text-sm text-surface-500"},K0={key:0,class:"border-t border-surface-200 dark:border-surface-700 pt-4"},N0={class:"flex items-center justify-between mb-3"},P0={key:0,class:"space-y-2"},O0={key:0,class:"grid gap-2"},L0={class:"flex items-center gap-3"},j0={class:"font-medium text-surface-900 dark:text-surface-0"},U0={class:"text-sm text-surface-600 dark:text-surface-400"},R0={class:"flex gap-1"},z0={key:1,class:"text-center py-4"},q0={key:2,class:"text-center py-4 text-surface-500"},H0={class:"p-4"},X0={class:"grid grid-cols-1 gap-4 md:grid-cols-4"},W0={class:"mb-6 grid grid-cols-1 gap-4 md:grid-cols-4"},Y0={class:"p-4 text-center"},J0={class:"text-primary mb-2 text-2xl font-bold"},Q0={class:"p-4 text-center"},Z0={class:"mb-2 text-2xl font-bold text-green-600"},$0={class:"p-4 text-center"},el={class:"mb-2 text-2xl font-bold text-blue-600"},ul={class:"p-4 text-center"},tl={class:"mb-2 text-2xl font-bold text-orange-600"},ll={class:"text-surface-700 dark:text-surface-300 font-mono text-sm"},ol={class:"text-surface-900 dark:text-surface-0 font-medium"},nl={class:"text-surface-600 dark:text-surface-400 font-mono text-sm"},al={key:1,class:"text-surface-400 dark:text-surface-600"},rl={key:1,class:"text-surface-400 dark:text-surface-600"},il={class:"text-sm"},sl={key:0},dl={class:"text-surface-700 dark:text-surface-300"},cl={class:"text-surface-500 dark:text-surface-400 text-xs"},fl={key:1,class:"text-surface-400 dark:text-surface-600"},pl={class:"flex gap-2"},ml={key:1,class:"grid gap-4"},gl={class:"p-6"},vl={class:"mb-4 flex items-start justify-between"},yl={class:"flex-1"},bl={class:"mb-2 flex items-center gap-3"},hl={class:"text-surface-900 dark:text-surface-0 text-lg font-semibold"},xl={class:"text-surface-600 dark:text-surface-400 mb-2 font-mono text-sm"},Cl={key:0,class:"text-surface-600 dark:text-surface-400 mb-3"},kl={class:"ml-4 flex gap-2"},Dl={class:"mb-4 flex items-center gap-4"},El={key:0,class:"border-surface-200 dark:border-surface-700 border-t pt-4"},wl={class:"grid grid-cols-3 gap-4 text-center"},Tl={class:"text-surface-900 dark:text-surface-0 text-lg font-semibold"},Fl={class:"text-surface-900 dark:text-surface-0 text-lg font-semibold"},Vl={class:"text-surface-900 dark:text-surface-0 text-lg font-semibold"},Bl={class:"p-4"},Il={class:"space-y-4"},Sl={key:0,class:"p-error"},_l={class:"flex justify-end gap-2"};function Al(t,u,o,e,s,a){return d(),h("div",g0,[e.loading?(d(),h("div",v0,[r(e.Icon,{name:"pi pi-spinner pi-spin",class:"text-primary mb-4 inline-block text-4xl"}),u[15]||(u[15]=n("p",{class:"text-surface-600 dark:text-surface-400"},"Загрузка шаблонов атрибутов...",-1))])):(d(),h("div",y0,[n("div",b0,[u[16]||(u[16]=n("div",null,[n("h2",{class:"text-surface-900 dark:text-surface-0 text-xl font-semibold"},"Управление атрибутами"),n("p",{class:"text-surface-600 dark:text-surface-400 mt-1 text-sm"},"Группы и шаблоны атрибутов для запчастей, каталожных позиций и техники")],-1)),n("div",h0,[r(e.VButton,{onClick:e.refreshData,disabled:e.loading,severity:"secondary",outlined:"",label:"Обновить"},null,8,["disabled"]),r(e.VButton,{onClick:u[0]||(u[0]=l=>e.showGroupDialog=!0),severity:"secondary",outlined:"",label:"Создать группу"}),r(e.VButton,{onClick:e.createNewTemplate,label:"Создать шаблон"})])]),n("div",x0,[r(e.VCard,null,{content:g(()=>[n("div",C0,[n("div",k0,[u[17]||(u[17]=n("h3",{class:"text-lg font-semibold text-surface-900 dark:text-surface-0"},"Дерево групп",-1)),r(e.VButton,{size:"small",severity:"secondary",outlined:"",onClick:e.refreshGroupData},{default:g(()=>[r(e.RefreshCwIcon,{class:"w-4 h-4"})]),_:1})]),r(e.VTree,{value:e.treeNodes,expandedKeys:e.expandedKeys,"onUpdate:expandedKeys":u[2]||(u[2]=l=>e.expandedKeys=l),filter:"",filterPlaceholder:"Поиск групп..."},{default:g(({node:l})=>[n("div",{class:"flex items-center justify-between w-full",onClick:u[1]||(u[1]=Q(()=>{},["stop"]))},[n("div",{class:L(["flex items-center gap-2 flex-1 cursor-pointer rounded px-2 py-1 transition-colors",{"bg-primary text-primary-contrast":e.selectedTreeGroup?.id===l.data.id,"hover:bg-surface-100 dark:hover:bg-surface-800":e.selectedTreeGroup?.id!==l.data.id}]),onClick:m=>e.selectTreeGroup(l.data)},[n("span",E0,D(l.data.name),1),r(e.VTag,{value:l.data._count?.templates||0,severity:"secondary",size:"small"},null,8,["value"])],10,D0),n("div",w0,[r(e.VButton,{size:"small",severity:"secondary",outlined:"",onClick:Q(m=>e.toggleGroupExpansion(l.data),["stop"]),title:e.isGroupExpanded(l.data)?"Свернуть группу":"Развернуть группу"},{default:g(()=>[e.isGroupExpanded(l.data)?(d(),E(e.ChevronDownIcon,{key:0,class:"w-3 h-3"})):(d(),E(e.ChevronRightIcon,{key:1,class:"w-3 h-3"}))]),_:2},1032,["onClick","title"]),r(e.VButton,{size:"small",severity:"secondary",outlined:"",onClick:Q(m=>e.editGroup(l.data),["stop"])},{default:g(()=>[r(e.PencilIcon,{class:"w-3 h-3"})]),_:2},1032,["onClick"]),r(e.VButton,{size:"small",severity:"danger",outlined:"",onClick:Q(m=>e.deleteGroup(l.data),["stop"]),disabled:(l.data._count?.templates||0)>0||(l.data._count?.children||0)>0},{default:g(()=>[r(e.TrashIcon,{class:"w-3 h-3"})]),_:2},1032,["onClick","disabled"])])])]),_:1},8,["value","expandedKeys"])])]),_:1}),r(e.VCard,{class:"lg:col-span-2"},{content:g(()=>[n("div",T0,[n("div",F0,[n("h3",V0,D(e.selectedTreeGroup?`Группа: ${e.selectedTreeGroup.name}`:"Выберите группу"),1),e.selectedTreeGroup?(d(),h("div",B0,[r(e.VButton,{outlined:"",onClick:u[3]||(u[3]=l=>e.editGroup(e.selectedTreeGroup))},{default:g(()=>[r(e.PencilLineIcon,{class:"w-4 h-4"}),u[18]||(u[18]=de(" Редактировать "))]),_:1,__:[18]})])):F("",!0)]),e.selectedTreeGroup?(d(),h("div",S0,[n("div",_0,[r(e.FolderIcon,{class:"text-blue-600 w-5 h-5"}),n("div",A0,[n("div",G0,D(e.selectedTreeGroup.name),1),e.selectedTreeGroup.description?(d(),h("div",M0,D(e.selectedTreeGroup.description),1)):F("",!0)]),r(e.VTag,{value:`${e.selectedTreeGroup._count?.templates||0} шаблонов`,severity:"secondary"},null,8,["value"]),e.selectedTreeGroup._count?.children?(d(),E(e.VTag,{key:0,value:`${e.selectedTreeGroup._count.children} дочерних`,severity:"info"},null,8,["value"])):F("",!0)]),e.selectedTreeGroup._count?.templates>0?(d(),h("div",K0,[n("div",N0,[u[19]||(u[19]=n("span",{class:"text-sm font-medium"},"Шаблоны в группе:",-1)),r(e.VButton,{label:e.showGroupTemplates?"Скрыть":"Показать",onClick:e.toggleGroupTemplates,severity:"secondary",text:"",size:"small"},null,8,["label"])]),e.showGroupTemplates?(d(),h("div",P0,[e.groupTemplates.length>0?(d(),h("div",O0,[(d(!0),h(ae,null,fe(e.groupTemplates,l=>(d(),h("div",{key:l.id,class:"flex items-center justify-between p-3 bg-surface-50 dark:bg-surface-900 rounded-lg"},[n("div",L0,[r(e.TagIcon,{class:"text-green-600 w-4 h-4"}),n("div",null,[n("div",j0,D(l.title),1),n("div",U0,D(l.name)+" • "+D(e.getDataTypeLabel(l.dataType)),1)])]),n("div",R0,[r(e.VTag,{value:e.getDataTypeLabel(l.dataType),severity:"info",size:"small"},null,8,["value"]),l.unit?(d(),E(e.VTag,{key:0,value:e.getUnitLabel(l.unit),severity:"success",size:"small"},null,8,["value"])):F("",!0)])]))),128))])):e.loadingGroupTemplates?(d(),h("div",z0,[r(e.LoaderCircleIcon,{class:"w-4 h-4 animate-spin"}),u[20]||(u[20]=de(" Загрузка... "))])):(d(),h("div",q0," Нет шаблонов в группе "))])):F("",!0)])):F("",!0)])):(d(),h("div",I0," Выберите группу в дереве слева для просмотра деталей и шаблонов "))])]),_:1})]),r(e.VCard,{class:"mb-6"},{content:g(()=>[n("div",H0,[n("div",X0,[n("div",null,[u[21]||(u[21]=n("label",{class:"text-surface-700 dark:text-surface-300 mb-2 block text-sm font-medium"}," Поиск ",-1)),r(e.VInputText,{modelValue:e.searchQuery,"onUpdate:modelValue":u[4]||(u[4]=l=>e.searchQuery=l),placeholder:"Поиск по названию, имени или описанию...",class:"w-full",onInput:e.debouncedSearch},null,8,["modelValue"])]),n("div",null,[u[22]||(u[22]=n("label",{class:"text-surface-700 dark:text-surface-300 mb-2 block text-sm font-medium"}," Группа ",-1)),r(e.VAutoComplete,{modelValue:e.selectedGroup,"onUpdate:modelValue":u[5]||(u[5]=l=>e.selectedGroup=l),suggestions:e.groupSuggestions,onComplete:e.filterGroups,"option-label":"name","option-value":"id",placeholder:"Все группы",class:"w-full",dropdown:"","show-clear":"",onChange:e.loadTemplates},null,8,["modelValue","suggestions"])]),n("div",null,[u[23]||(u[23]=n("label",{class:"text-surface-700 dark:text-surface-300 mb-2 block text-sm font-medium"}," Тип данных ",-1)),r(e.VAutoComplete,{modelValue:e.selectedDataType,"onUpdate:modelValue":u[6]||(u[6]=l=>e.selectedDataType=l),suggestions:e.dataTypeSuggestions,onComplete:e.filterDataTypes,"option-label":"label","option-value":"value",placeholder:"Все типы",class:"w-full",dropdown:"","show-clear":"",onChange:e.loadTemplates},null,8,["modelValue","suggestions"])])])])]),_:1}),n("div",W0,[r(e.VCard,null,{content:g(()=>[n("div",Y0,[n("div",J0,D(e.totalCount),1),u[24]||(u[24]=n("div",{class:"text-surface-600 dark:text-surface-400 text-sm"},"Всего шаблонов",-1))])]),_:1}),r(e.VCard,null,{content:g(()=>[n("div",Q0,[n("div",Z0,D(e.groups.length),1),u[25]||(u[25]=n("div",{class:"text-surface-600 dark:text-surface-400 text-sm"},"Групп",-1))])]),_:1}),r(e.VCard,null,{content:g(()=>[n("div",$0,[n("div",el,D(e.usedTemplatesCount),1),u[26]||(u[26]=n("div",{class:"text-surface-600 dark:text-surface-400 text-sm"},"Используется",-1))])]),_:1}),r(e.VCard,null,{content:g(()=>[n("div",ul,[n("div",tl,D(e.unusedTemplatesCount),1),u[27]||(u[27]=n("div",{class:"text-surface-600 dark:text-surface-400 text-sm"},"Не используется",-1))])]),_:1})]),e.viewMode==="table"?(d(),E(e.VCard,{key:0},{content:g(()=>[r(e.VDataTable,{value:e.templates,loading:e.tableLoading&&e.templates.length===0,paginator:"",rows:e.pageSize,"total-records":e.totalCount,"rows-per-page-options":[10,25,50],lazy:"",onPage:e.onPageChange,"table-style":"min-width: 50rem",class:"p-datatable-sm","striped-rows":""},{default:g(()=>[r(e.Column,{field:"id",header:"ID",sortable:"",style:{width:"80px"}},{body:g(({data:l})=>[n("span",ll,"#"+D(l.id),1)]),_:1}),r(e.Column,{field:"title",header:"Название",sortable:""},{body:g(({data:l})=>[n("div",null,[n("div",ol,D(l.title),1),n("div",nl,D(l.name),1)])]),_:1}),r(e.Column,{field:"group.name",header:"Группа",sortable:""},{body:g(({data:l})=>[l.group?(d(),E(e.VTag,{key:0,value:l.group.name,severity:"secondary"},null,8,["value"])):(d(),h("span",al,"—"))]),_:1}),r(e.Column,{field:"dataType",header:"Тип",sortable:""},{body:g(({data:l})=>[r(e.VTag,{value:e.getDataTypeLabel(l.dataType),severity:"info"},null,8,["value"])]),_:1}),r(e.Column,{field:"unit",header:"Единица"},{body:g(({data:l})=>[l.unit?(d(),E(e.VTag,{key:0,value:e.getUnitLabel(l.unit),severity:"success"},null,8,["value"])):(d(),h("span",rl,"—"))]),_:1}),r(e.Column,{header:"Использование",style:{width:"120px"}},{body:g(({data:l})=>[n("div",il,[l._count?(d(),h("div",sl,[n("div",dl,D(e.getTotalUsage(l._count))+" исп.",1),n("div",cl,D(e.getUsageDetails(l._count)),1)])):(d(),h("span",fl,"—"))])]),_:1}),r(e.Column,{header:"Действия",style:{width:"120px"}},{body:g(({data:l})=>[n("div",pl,[l.dataType==="STRING"?(d(),E(e.VButton,{key:0,onClick:m=>e.openSynonyms(l),severity:"secondary",outlined:"",size:"small"},{default:g(()=>[u[28]||(u[28]=de(" Синонимы ")),r(e.TagsIcon,{class:"h-5 w-5"})]),_:2,__:[28]},1032,["onClick"])):F("",!0),r(e.VButton,{onClick:m=>e.editTemplate(l),severity:"secondary",outlined:"",size:"small"},{default:g(()=>[r(e.PencilIcon,{class:"h-5 w-5"})]),_:2},1032,["onClick"]),r(e.DangerButton,{onClick:m=>e.deleteTemplate(l),severity:"danger",outlined:"",size:"small",disabled:e.getTotalUsage(l._count)>0},{default:g(()=>[r(e.TrashIcon,{class:"h-5 w-5"})]),_:2},1032,["onClick","disabled"])])]),_:1})]),_:1},8,["value","loading","rows","total-records"])]),_:1})):e.viewMode==="cards"?(d(),h("div",ml,[(d(!0),h(ae,null,fe(e.templates,l=>(d(),E(e.VCard,{key:l.id,class:"border-surface-200 dark:border-surface-700 hover:border-primary border transition-colors"},{content:g(()=>[n("div",gl,[n("div",vl,[n("div",yl,[n("div",bl,[r(e.TagsIcon,{class:"h-5 w-5"}),n("h3",hl,D(l.title),1),l.isRequired?(d(),E(e.VTag,{key:0,value:"Обязательный",severity:"danger",size:"small"})):F("",!0)]),n("div",xl,D(l.name),1),l.description?(d(),h("p",Cl,D(l.description),1)):F("",!0)]),n("div",kl,[r(e.VButton,{onClick:m=>e.editTemplate(l),severity:"secondary",outlined:"",size:"small"},{default:g(()=>[r(e.PencilIcon,{class:"h-5 w-5"})]),_:2},1032,["onClick"]),r(e.VButton,{onClick:m=>e.deleteTemplate(l),severity:"danger",outlined:"",size:"small",disabled:e.getTotalUsage(l._count)>0},{default:g(()=>[r(e.TrashIcon,{class:"h-5 w-5"})]),_:2},1032,["onClick","disabled"])])]),n("div",Dl,[r(e.VTag,{value:e.getDataTypeLabel(l.dataType),severity:"info"},null,8,["value"]),l.unit?(d(),E(e.VTag,{key:0,value:e.getUnitLabel(l.unit),severity:"success"},null,8,["value"])):F("",!0),l.group?(d(),E(e.VTag,{key:1,value:l.group.name,severity:"secondary"},null,8,["value"])):F("",!0)]),l._count?(d(),h("div",El,[u[32]||(u[32]=n("div",{class:"text-surface-600 dark:text-surface-400 mb-2 text-sm"},"Использование:",-1)),n("div",wl,[n("div",null,[n("div",Tl,D(l._count.partAttributes||0),1),u[29]||(u[29]=n("div",{class:"text-surface-500 text-xs"},"Запчасти",-1))]),n("div",null,[n("div",Fl,D(l._count.catalogItemAttributes||0),1),u[30]||(u[30]=n("div",{class:"text-surface-500 text-xs"},"Каталог",-1))]),n("div",null,[n("div",Vl,D(l._count.equipmentAttributes||0),1),u[31]||(u[31]=n("div",{class:"text-surface-500 text-xs"},"Техника",-1))])])])):F("",!0)])]),_:2},1024))),128)),e.totalCount>e.pageSize?(d(),E(e.VCard,{key:0},{content:g(()=>[n("div",Bl,[r(e.Paginator,{rows:e.pageSize,"total-records":e.totalCount,"rows-per-page-options":[10,25,50],onPage:e.onPageChange},null,8,["rows","total-records"])])]),_:1})):F("",!0)])):F("",!0),r(e.VDialog,{visible:e.showCreateDialog,"onUpdate:visible":u[9]||(u[9]=l=>e.showCreateDialog=l),modal:"",header:e.editingTemplate?"Редактировать шаблон":"Создать шаблон",style:{width:"50rem"},breakpoints:{"1199px":"75vw","575px":"90vw"}},{default:g(()=>[r(e.TemplateForm,{modelValue:e.templateForm,"onUpdate:modelValue":u[7]||(u[7]=l=>e.templateForm=l),groups:e.groups,"hierarchy-groups":e.hierarchyGroups,loading:e.saving,onSave:e.saveTemplate,onCancel:u[8]||(u[8]=l=>e.showCreateDialog=!1),onGroupCreated:e.onGroupCreated},null,8,["modelValue","groups","hierarchy-groups","loading"])]),_:1},8,["visible","header"]),r(e.VDialog,{visible:e.showSynonymsDialog,"onUpdate:visible":u[10]||(u[10]=l=>e.showSynonymsDialog=l),modal:"",header:"Управление синонимами",style:{width:"80rem"},breakpoints:{"1199px":"90vw","575px":"98vw"}},{default:g(()=>[e.selectedTemplateForSynonyms?(d(),E(e.AttributeSynonymManager,{key:0,template:e.selectedTemplateForSynonyms},null,8,["template"])):F("",!0)]),_:1},8,["visible"]),r(e.VDialog,{visible:e.showGroupDialog,"onUpdate:visible":u[14]||(u[14]=l=>e.showGroupDialog=l),modal:"",header:e.editingGroup?"Редактировать группу":"Создать группу",style:{width:"500px"}},{footer:g(()=>[n("div",_l,[r(e.VButton,{onClick:e.closeGroupDialog,severity:"secondary",outlined:"",label:"Отмена"}),r(e.VButton,{onClick:e.saveGroup,loading:e.savingGroup,label:e.editingGroup?"Сохранить":"Создать"},null,8,["loading","label"])])]),default:g(()=>[n("div",Il,[n("div",null,[u[33]||(u[33]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Название группы * ",-1)),r(e.VInputText,{modelValue:e.groupForm.name,"onUpdate:modelValue":u[11]||(u[11]=l=>e.groupForm.name=l),placeholder:"Введите название группы...",class:L(["w-full",{"p-invalid":e.groupErrors.name}])},null,8,["modelValue","class"]),e.groupErrors.name?(d(),h("small",Sl,D(e.groupErrors.name),1)):F("",!0)]),n("div",null,[u[34]||(u[34]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Описание ",-1)),r(e.VTextarea,{modelValue:e.groupForm.description,"onUpdate:modelValue":u[12]||(u[12]=l=>e.groupForm.description=l),placeholder:"Подробное описание группы атрибутов...",rows:"3",class:"w-full"},null,8,["modelValue"])]),n("div",null,[u[35]||(u[35]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Родительская группа ",-1)),r(e.VSelect,{modelValue:e.groupForm.parentId,"onUpdate:modelValue":u[13]||(u[13]=l=>e.groupForm.parentId=l),options:e.parentSelectOptions,"option-label":"name","option-value":"id",class:"w-full",placeholder:"Не выбрано"},null,8,["modelValue","options"])])])]),_:1},8,["visible","header"])]))])}const Gl=ue(m0,[["render",Al]]),Ml=ee({__name:"AttributeTemplateManagerBoundary",setup(t,{expose:u}){u();const o=v(0),s={key:o,onRetry:()=>{o.value++},ErrorBoundary:Hu,AttributeTemplateManager:Gl};return Object.defineProperty(s,"__isScriptSetup",{enumerable:!1,value:!0}),s}});function Kl(t,u,o,e,s,a){return d(),E(e.ErrorBoundary,{variant:"detailed",onRetry:e.onRetry,title:"Проблема при загрузке шаблонов",message:"Попробуйте обновить список или повторить попытку."},{default:g(()=>[(d(),E(e.AttributeTemplateManager,{key:e.key}))]),_:1})}const Ao=ue(Ml,[["render",Kl]]);export{Ao as default};
