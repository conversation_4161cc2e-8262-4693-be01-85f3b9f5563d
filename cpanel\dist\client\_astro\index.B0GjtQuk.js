import{t as d,l as t,g as r,b as s,o as l,T as u}from"./index.BglzLLgy.js";var i={name:"Portal",props:{appendTo:{type:[String,Object],default:"body"},disabled:{type:Boolean,default:!1}},data:function(){return{mounted:!1}},mounted:function(){this.mounted=d()},computed:{inline:function(){return this.disabled||this.appendTo==="self"}}};function p(e,c,n,f,o,a){return a.inline?t(e.$slots,"default",{key:0}):o.mounted?(l(),r(u,{key:1,to:n.appendTo},[t(e.$slots,"default")],8,["to"])):s("",!0)}i.render=p;export{i as s};
