<template>
  <div class="group-tree-autocomplete">
    <VAutoComplete 
      v-model="selectedGroup" 
      :suggestions="filteredGroups" 
      @complete="searchGroups"
      @dropdown-click="() => searchGroups({ query: '' })" 
      option-label="displayName" 
      :placeholder="placeholder"
      :class="inputClass"
      dropdown 
      dropdown-mode="current" 
      show-clear
      @item-select="onGroupSelect" 
      @clear="onGroupClear"
    >
      <template #option="slotProps">
        <div class="flex items-center gap-2 py-1">
          <!-- Отступ для показа уровня вложенности -->
          <div :style="{ paddingLeft: `${slotProps.option.level * 16}px` }" class="flex items-center gap-2 w-full">
            <!-- Иконка папки для групп с детьми -->
            <FolderIcon v-if="slotProps.option.hasChildren" class="w-4 h-4 text-surface-500" />
            <FileIcon v-else class="w-4 h-4 text-surface-400" />
            
            <!-- Название группы -->
            <span class="flex-1">{{ slotProps.option.name }}</span>
            
            <!-- Количество шаблонов -->
            <span v-if="slotProps.option.templatesCount > 0" 
                  class="text-xs text-surface-500 bg-surface-100 dark:bg-surface-800 px-2 py-1 rounded">
              {{ slotProps.option.templatesCount }}
            </span>
          </div>
        </div>
      </template>
      
      <template #empty>
        <div class="p-3 text-surface-500 text-center">
          Группы не найдены
        </div>
      </template>
    </VAutoComplete>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import VAutoComplete from '@/volt/AutoComplete.vue';
import { FolderIcon, FileIcon } from 'lucide-vue-next';

// Props
interface Props {
  modelValue: any;
  groups: any[];
  placeholder?: string;
  class?: string;
  invalid?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: 'Поиск группы...',
  class: '',
  invalid: false
});

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: any];
  'group-select': [group: any];
  'group-clear': [];
}>();

// Локальное состояние
const selectedGroup = ref<any>(null);
const filteredGroups = ref<any[]>([]);
const searchTimeout = ref<NodeJS.Timeout | null>(null);

// Computed
const inputClass = computed(() => {
  let classes = 'w-full';
  if (props.class) classes += ` ${props.class}`;
  if (props.invalid) classes += ' p-invalid';
  return classes;
});

// Функция для преобразования иерархии в плоский список с уровнями
const flattenGroupsHierarchy = (groups: any[], level = 0, parentPath = ''): any[] => {
  const result: any[] = [];
  
  for (const group of groups) {
    const displayName = parentPath ? `${parentPath} / ${group.name}` : group.name;
    
    result.push({
      ...group,
      level,
      displayName,
      hasChildren: group.children && group.children.length > 0,
      templatesCount: group._count?.templates || 0
    });
    
    // Рекурсивно добавляем дочерние группы
    if (group.children && group.children.length > 0) {
      result.push(...flattenGroupsHierarchy(group.children, level + 1, displayName));
    }
  }
  
  return result;
};

// Функции поиска
const searchGroups = async (event: any) => {
  console.log('searchGroups вызван в GroupTreeAutoComplete', { event, groupsLength: props.groups.length });
  const query = event.query || '';
  
  // Очищаем предыдущий таймаут
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value);
  }
  
  // Преобразуем иерархию в плоский список
  const flatGroups = flattenGroupsHierarchy(props.groups);
  
  // Если запрос пустой, показываем все группы
  if (!query.trim()) {
    filteredGroups.value = flatGroups;
    console.log('Показываем все группы:', filteredGroups.value.length);
    return;
  }
  
  // Debounce поиск через 300ms
  searchTimeout.value = setTimeout(() => {
    try {
      // Фильтруем группы по названию и описанию
      filteredGroups.value = flatGroups.filter(group => 
        group.name.toLowerCase().includes(query.toLowerCase()) ||
        (group.description && group.description.toLowerCase().includes(query.toLowerCase())) ||
        group.displayName.toLowerCase().includes(query.toLowerCase())
      );
      
      console.log('Фильтруем группы:', filteredGroups.value.length);
    } catch (error: any) {
      console.error('Ошибка поиска групп:', error);
      // В случае ошибки показываем все группы
      filteredGroups.value = flatGroups;
    }
  }, 300);
};

// Обработчики событий
const onGroupSelect = (event: any) => {
  const group = event.value;
  emit('update:modelValue', group.id);
  emit('group-select', group);
  selectedGroup.value = group;
};

const onGroupClear = () => {
  emit('update:modelValue', null);
  emit('group-clear');
  selectedGroup.value = null;
};

// Находим выбранную группу по ID
const findSelectedGroup = () => {
  if (props.modelValue) {
    const flatGroups = flattenGroupsHierarchy(props.groups);
    selectedGroup.value = flatGroups.find(g => g.id === props.modelValue) || null;
  } else {
    selectedGroup.value = null;
  }
};

// Watchers
watch(() => props.modelValue, () => {
  findSelectedGroup();
}, { immediate: true });

watch(() => props.groups, () => {
  const flatGroups = flattenGroupsHierarchy(props.groups);
  filteredGroups.value = flatGroups;
  findSelectedGroup();
}, { immediate: true });

// Lifecycle
onMounted(() => {
  console.log('GroupTreeAutoComplete монтируется');
  const flatGroups = flattenGroupsHierarchy(props.groups);
  filteredGroups.value = flatGroups;
  findSelectedGroup();
  
  console.log('Инициализированы группы в GroupTreeAutoComplete:', {
    hierarchyGroups: props.groups.length,
    flatGroups: filteredGroups.value.length
  });
});

// Очищаем таймауты при размонтировании
import { onUnmounted } from 'vue';
onUnmounted(() => {
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value);
  }
});
</script>

<style scoped>
.group-tree-autocomplete {
  /* Дополнительные стили при необходимости */
}
</style>
