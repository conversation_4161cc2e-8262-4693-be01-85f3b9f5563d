import{j as e}from"./jsx-runtime.D_zvdyIk.js";import{r as t}from"./index.0yr9KlQE.js";import{a as x}from"./auth-client.DHpPh7L9.js";import{I as m}from"./input.aGo1BNk4.js";import{B as f}from"./button.BfGcEciz.js";import"./utils.CBfrqCZ4.js";import"./index.BWNh2K4G.js";import"./index.3rXK4OIH.js";function S(){const[a,c]=t.useState(""),[r,u]=t.useState(""),[o,l]=t.useState(!1),[i,n]=t.useState(null),d=async s=>{s.preventDefault(),n(null),l(!0);try{await x.signIn.email({email:a,password:r},{onError:p=>n(p.error.message??"Ошибка входа"),onSuccess:()=>{window.location.href="/account"}})}finally{l(!1)}};return e.jsxs("form",{className:"space-y-4",onSubmit:d,children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm",children:"Email"}),e.jsx(m,{type:"email",value:a,onChange:s=>c(s.target.value),required:!0})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm",children:"Пароль"}),e.jsx(m,{type:"password",value:r,onChange:s=>u(s.target.value),required:!0})]}),i&&e.jsx("div",{className:"text-sm text-red-500",children:i}),e.jsx(f,{type:"submit",disabled:o,children:o?"Входим...":"Войти"})]})}export{S as default};
