<template>
  <div :class="containerClasses">
    <div :class="contentClasses">
      <!-- Icon or Custom Illustration -->
      <div v-if="!$slots.icon" :class="iconContainerClasses">
        <component :is="iconComponent" :class="iconClasses" />
      </div>
      <div v-else :class="iconContainerClasses">
        <slot name="icon" />
      </div>
      
      <!-- Title -->
      <h3 :class="titleClasses">
        <slot name="title">
          {{ title }}
        </slot>
      </h3>
      
      <!-- Description -->
      <p v-if="description || $slots.description" :class="descriptionClasses">
        <slot name="description">
          {{ description }}
        </slot>
      </p>
      
      <!-- Actions -->
      <div v-if="$slots.actions || primaryAction || secondaryAction" :class="actionsClasses">
        <slot name="actions">
          <button
            v-if="primaryAction"
            @click="$emit('primaryAction')"
            :class="primaryButtonClasses"
          >
            <component v-if="primaryAction.icon" :is="primaryAction.icon" class="w-4 h-4" />
            {{ primaryAction.label }}
          </button>
          
          <button
            v-if="secondaryAction"
            @click="$emit('secondaryAction')"
            :class="secondaryButtonClasses"
          >
            <component v-if="secondaryAction.icon" :is="secondaryAction.icon" class="w-4 h-4" />
            {{ secondaryAction.label }}
          </button>
        </slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  Search, 
  Package, 
  FileX, 
  Database, 
  Inbox, 
  AlertCircle,
  Settings,
  Users,
  ShoppingCart,
  Filter
} from 'lucide-vue-next'

interface ActionButton {
  label: string
  icon?: any
}

interface Props {
  title: string
  description?: string
  icon?: 'search' | 'package' | 'file' | 'database' | 'inbox' | 'alert' | 'settings' | 'users' | 'cart' | 'filter' | 'custom'
  variant?: 'default' | 'compact' | 'detailed'
  primaryAction?: ActionButton
  secondaryAction?: ActionButton
}

const props = withDefaults(defineProps<Props>(), {
  icon: 'inbox',
  variant: 'default'
})

defineEmits<{
  primaryAction: []
  secondaryAction: []
}>()

// Icon mapping
const iconMap = {
  search: Search,
  package: Package,
  file: FileX,
  database: Database,
  inbox: Inbox,
  alert: AlertCircle,
  settings: Settings,
  users: Users,
  cart: ShoppingCart,
  filter: Filter
}

const iconComponent = computed(() => {
  return iconMap[props.icon as keyof typeof iconMap] || Inbox
})

// Computed classes
const containerClasses = computed(() => {
  const base = 'flex items-center justify-center w-full'
  const variants = {
    default: 'min-h-[300px] p-8',
    compact: 'min-h-[200px] p-6',
    detailed: 'min-h-[400px] p-12'
  }
  
  return `${base} ${variants[props.variant]}`
})

const contentClasses = computed(() => {
  const base = 'flex flex-col items-center text-center'
  const variants = {
    default: 'max-w-md',
    compact: 'max-w-sm',
    detailed: 'max-w-lg'
  }
  
  return `${base} ${variants[props.variant]}`
})

const iconContainerClasses = computed(() => {
  const variants = {
    default: 'mb-6 p-4 bg-[--color-background] border border-[--color-border] rounded-full',
    compact: 'mb-4 p-3 bg-[--color-background] border border-[--color-border] rounded-full',
    detailed: 'mb-8 p-6 bg-gradient-to-br from-[--color-primary]/10 to-[--color-primary]/5 border border-[--color-primary]/20 rounded-2xl'
  }
  
  return variants[props.variant]
})

const iconClasses = computed(() => {
  const base = 'text-[--color-muted]'
  const variants = {
    default: 'w-8 h-8',
    compact: 'w-6 h-6',
    detailed: 'w-12 h-12 text-[--color-primary]'
  }
  
  return `${base} ${variants[props.variant]}`
})

const titleClasses = computed(() => {
  const base = 'font-semibold text-[--color-foreground]'
  const variants = {
    default: 'text-lg mb-2',
    compact: 'text-base mb-2',
    detailed: 'text-xl mb-3'
  }
  
  return `${base} ${variants[props.variant]}`
})

const descriptionClasses = computed(() => {
  const base = 'text-[--color-muted] leading-relaxed'
  const variants = {
    default: 'text-sm mb-6',
    compact: 'text-sm mb-4',
    detailed: 'text-base mb-8'
  }
  
  return `${base} ${variants[props.variant]}`
})

const actionsClasses = computed(() => {
  const base = 'flex gap-3'
  const variants = {
    default: 'flex-col sm:flex-row',
    compact: 'flex-row',
    detailed: 'flex-col sm:flex-row'
  }
  
  return `${base} ${variants[props.variant]}`
})

const primaryButtonClasses = computed(() => {
  return 'inline-flex items-center justify-center gap-2 px-4 py-2 bg-[--color-primary] text-[--color-primary-foreground] rounded-md hover:bg-[--color-primary-hover] transition-colors text-sm font-medium min-w-[120px]'
})

const secondaryButtonClasses = computed(() => {
  return 'inline-flex items-center justify-center gap-2 px-4 py-2 bg-[--color-card] text-[--color-foreground] border border-[--color-border] rounded-md hover:bg-[--color-hover] transition-colors text-sm font-medium min-w-[120px]'
})
</script>