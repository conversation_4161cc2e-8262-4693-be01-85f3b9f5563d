import { d as defineMiddleware, s as sequence } from './chunks/index_BYVtJ9ki.mjs';
import { a as authClient } from './chunks/auth-client_BsqqHDQx.mjs';
import 'es-module-lexer';
import './chunks/astro-designed-error-pages_NtkQcIoZ.mjs';
import 'kleur/colors';
import './chunks/astro/server_D7mwM5eH.mjs';
import 'clsx';
import 'cookie';

const PROTECTED_ROUTES = [
  "/account"
];
const GUEST_ONLY_ROUTES = [
  "/login",
  "/register"
];
function isProtectedRoute(pathname) {
  return PROTECTED_ROUTES.some((route) => pathname === route || pathname.startsWith(route + "/"));
}
function isGuestOnlyRoute(pathname) {
  return GUEST_ONLY_ROUTES.some((route) => pathname === route || pathname.startsWith(route + "/"));
}
const onRequest$1 = defineMiddleware(async (context, next) => {
  const { url, request, redirect } = context;
  const pathname = url.pathname;
  try {
    const sessionResult = await authClient.getSession({
      fetchOptions: {
        headers: Object.fromEntries(request.headers.entries())
      }
    });
    const user = sessionResult?.data?.user;
    const session = sessionResult?.data?.session;
    const isAuthenticated = !!user;
    if (false) ;
    context.locals.user = user ?? null;
    context.locals.session = session ?? null;
    if (isGuestOnlyRoute(pathname)) {
      if (isAuthenticated) return redirect("/account");
      return next();
    }
    if (isProtectedRoute(pathname)) {
      if (!isAuthenticated) return redirect("/login");
      return next();
    }
    return next();
  } catch (e) {
    console.error("Auth middleware error (/site):", e);
    if (isProtectedRoute(pathname)) return redirect("/login");
    return next();
  }
});

const onRequest = sequence(
	
	onRequest$1
	
);

export { onRequest };
