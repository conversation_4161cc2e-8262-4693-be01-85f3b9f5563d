<template>
  <div class="min-h-screen flex items-center justify-center bg-surface-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <!-- Header -->
      <div>
        <div class="mx-auto h-12 w-12 flex items-center justify-center">
          <img class="h-12 w-12" src="/favicon.svg" alt="PartTec" />
        </div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-surface-900">
          Вход в админ панель
        </h2>
        <p class="mt-2 text-center text-sm text-surface-600">
          Система управления каталогом PartTec
        </p>
      </div>

      <!-- Форма -->
      <Card class="mt-8">
        <template #content>
          <form class="space-y-6" @submit.prevent="handleSubmit">
            <!-- Общая ошибка -->
            <Message
              v-if="generalError"
              severity="error"
              :closable="false"
            >
              {{ generalError }}
            </Message>

            <!-- Email -->
            <div>
              <InputText
                id="email"
                v-model="formData.email"
                type="email"
                autocomplete="email"
                class="w-full"
                :invalid="!!fieldErrors.email"
                @blur="validateField('email')"
                @input="clearFieldError('email')"
              />
              <label for="email">Email адрес</label>
            </div>
            <small v-if="fieldErrors.email" class="text-red-500">
              {{ fieldErrors.email }}
            </small>

            <!-- Password -->
            <div>
              <Password
                id="password"
                v-model="formData.password"
                autocomplete="current-password"
                class="w-full"
                :invalid="!!fieldErrors.password"
                :feedback="false"
                toggleMask
                @blur="validateField('password')"
                @input="clearFieldError('password')"
              />
              <label for="password">Пароль</label>
            </div>
            <small v-if="fieldErrors.password" class="text-red-500">
              {{ fieldErrors.password }}
            </small>

            <!-- Remember me -->
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <Checkbox
                  id="remember-me"
                  v-model="formData.rememberMe"
                  :binary="true"
                />
                <label for="remember-me" class="ml-2 block text-sm text-surface-700">
                  Запомнить меня
                </label>
              </div>

              <div class="text-sm">
                <a href="#" class="font-medium text-primary-600 hover:text-primary-500">
                  Забыли пароль?
                </a>
              </div>
            </div>

            <!-- Submit button -->
            <div>
              <Button
                type="submit"
                :disabled="isSubmitting || !isFormValid"
                :loading="isSubmitting"
                label="Войти"
                class="w-full"
                size="large"
              />
            </div>

            <!-- Register link -->
            <div class="text-center">
              <p class="text-sm text-surface-600">
                Нет аккаунта?
                <a href="/admin/register" class="font-medium text-primary-600 hover:text-primary-500">
                  Зарегистрироваться
                </a>
              </p>
            </div>
          </form>
        </template>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from 'vue'
import { useAuth, type LoginFormData } from '@/composables/useAuth'
import Button from '@/volt/Button.vue'
import Card from '@/volt/Card.vue'
import InputText from '@/volt/InputText.vue'
import Password from '@/volt/Password.vue'
import Message from '@/volt/Message.vue'
import Checkbox from '@/volt/Checkbox.vue'
import { navigate } from 'astro:transitions/client'

// Composables
const { signIn, LoginFormSchema } = useAuth()

// Локальное состояние
const isSubmitting = ref(false)
const generalError = ref('')

// Данные формы
const formData = reactive<LoginFormData>({
  email: '',
  password: '',
  rememberMe: false
})

// Ошибки полей
const fieldErrors = reactive<Record<string, string>>({})

// Вычисляемые свойства
const isFormValid = computed(() => {
  return formData.email && formData.password && Object.keys(fieldErrors).length === 0
})

// Методы
const validateField = (field: keyof LoginFormData) => {
  try {
    const fieldSchema = LoginFormSchema.shape[field]
    const result = fieldSchema.safeParse(formData[field])
    if (!result.success) {
      fieldErrors[field] = result.error.issues[0]?.message || 'Ошибка валидации'
    } else {
      delete fieldErrors[field]
    }
  } catch (error) {
    console.error('Validation error for field:', field, error)
    delete fieldErrors[field]
  }
}

const clearFieldError = (field: string) => {
  if (fieldErrors[field]) {
    delete fieldErrors[field]
  }
  if (generalError.value) {
    generalError.value = ''
  }
}

const handleSubmit = async () => {
  // Очищаем предыдущие ошибки
  generalError.value = ''
  Object.keys(fieldErrors).forEach(key => delete fieldErrors[key])

  // Валидируем форму
  const validationResult = LoginFormSchema.safeParse(formData)
  if (!validationResult.success) {
    validationResult.error.issues.forEach(issue => {
      if (issue.path[0]) {
        fieldErrors[issue.path[0] as string] = issue.message
      }
    })
    return
  }

  isSubmitting.value = true

  try {
    const result = await signIn(formData)

    if (result.error) {
      // Обрабатываем различные типы ошибок
      if (result.error.message.includes('Invalid email or password')) {
        generalError.value = 'Неверный email или пароль'
      } else if (result.error.message.includes('Too many requests')) {
        generalError.value = 'Слишком много попыток входа. Попробуйте позже.'
      } else {
        generalError.value = result.error.message || 'Ошибка входа в систему'
      }
    } else {
      // Успешный вход - перенаправляем на дашборд
      console.log('✅ Успешный вход в систему')

      // Небольшая задержка для обновления сессии
      setTimeout(() => {
        // window.location.href = '/admin'
        navigate('/admin')
      }, 100)
    }
  } catch (error) {
    console.error('Login error:', error)
    generalError.value = 'Произошла неожиданная ошибка. Попробуйте еще раз.'
  } finally {
    isSubmitting.value = false
  }
}
</script>

<style scoped>
/* Дополнительные стили для формы */
.login-form-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
</style>
