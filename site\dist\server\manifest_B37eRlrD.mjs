import 'kleur/colors';
import { p as decodeKey } from './chunks/astro/server_D7mwM5eH.mjs';
import 'clsx';
import 'cookie';
import './chunks/astro-designed-error-pages_NtkQcIoZ.mjs';
import 'es-module-lexer';
import { N as NOOP_MIDDLEWARE_FN } from './chunks/noop-middleware_CzuyjZdh.mjs';

function sanitizeParams(params) {
  return Object.fromEntries(
    Object.entries(params).map(([key, value]) => {
      if (typeof value === "string") {
        return [key, value.normalize().replace(/#/g, "%23").replace(/\?/g, "%3F")];
      }
      return [key, value];
    })
  );
}
function getParameter(part, params) {
  if (part.spread) {
    return params[part.content.slice(3)] || "";
  }
  if (part.dynamic) {
    if (!params[part.content]) {
      throw new TypeError(`Missing parameter: ${part.content}`);
    }
    return params[part.content];
  }
  return part.content.normalize().replace(/\?/g, "%3F").replace(/#/g, "%23").replace(/%5B/g, "[").replace(/%5D/g, "]");
}
function getSegment(segment, params) {
  const segmentPath = segment.map((part) => getParameter(part, params)).join("");
  return segmentPath ? "/" + segmentPath : "";
}
function getRouteGenerator(segments, addTrailingSlash) {
  return (params) => {
    const sanitizedParams = sanitizeParams(params);
    let trailing = "";
    if (addTrailingSlash === "always" && segments.length) {
      trailing = "/";
    }
    const path = segments.map((segment) => getSegment(segment, sanitizedParams)).join("") + trailing;
    return path || "/";
  };
}

function deserializeRouteData(rawRouteData) {
  return {
    route: rawRouteData.route,
    type: rawRouteData.type,
    pattern: new RegExp(rawRouteData.pattern),
    params: rawRouteData.params,
    component: rawRouteData.component,
    generate: getRouteGenerator(rawRouteData.segments, rawRouteData._meta.trailingSlash),
    pathname: rawRouteData.pathname || void 0,
    segments: rawRouteData.segments,
    prerender: rawRouteData.prerender,
    redirect: rawRouteData.redirect,
    redirectRoute: rawRouteData.redirectRoute ? deserializeRouteData(rawRouteData.redirectRoute) : void 0,
    fallbackRoutes: rawRouteData.fallbackRoutes.map((fallback) => {
      return deserializeRouteData(fallback);
    }),
    isIndex: rawRouteData.isIndex,
    origin: rawRouteData.origin
  };
}

function deserializeManifest(serializedManifest) {
  const routes = [];
  for (const serializedRoute of serializedManifest.routes) {
    routes.push({
      ...serializedRoute,
      routeData: deserializeRouteData(serializedRoute.routeData)
    });
    const route = serializedRoute;
    route.routeData = deserializeRouteData(serializedRoute.routeData);
  }
  const assets = new Set(serializedManifest.assets);
  const componentMetadata = new Map(serializedManifest.componentMetadata);
  const inlinedScripts = new Map(serializedManifest.inlinedScripts);
  const clientDirectives = new Map(serializedManifest.clientDirectives);
  const serverIslandNameMap = new Map(serializedManifest.serverIslandNameMap);
  const key = decodeKey(serializedManifest.key);
  return {
    // in case user middleware exists, this no-op middleware will be reassigned (see plugin-ssr.ts)
    middleware() {
      return { onRequest: NOOP_MIDDLEWARE_FN };
    },
    ...serializedManifest,
    assets,
    componentMetadata,
    inlinedScripts,
    clientDirectives,
    routes,
    serverIslandNameMap,
    key
  };
}

const manifest = deserializeManifest({"hrefRoot":"file:///D:/Dev/parttec/site/","cacheDir":"file:///D:/Dev/parttec/site/node_modules/.astro/","outDir":"file:///D:/Dev/parttec/site/dist/","srcDir":"file:///D:/Dev/parttec/site/src/","publicDir":"file:///D:/Dev/parttec/site/public/","buildClientDir":"file:///D:/Dev/parttec/site/dist/client/","buildServerDir":"file:///D:/Dev/parttec/site/dist/server/","adapterName":"@astrojs/node","routes":[{"file":"","links":[],"scripts":[],"styles":[],"routeData":{"type":"page","component":"_server-islands.astro","params":["name"],"segments":[[{"content":"_server-islands","dynamic":false,"spread":false}],[{"content":"name","dynamic":true,"spread":false}]],"pattern":"^\\/_server-islands\\/([^/]+?)\\/?$","prerender":false,"isIndex":false,"fallbackRoutes":[],"route":"/_server-islands/[name]","origin":"internal","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[],"routeData":{"type":"endpoint","isIndex":false,"route":"/_image","pattern":"^\\/_image\\/?$","segments":[[{"content":"_image","dynamic":false,"spread":false}]],"params":[],"component":"node_modules/astro/dist/assets/endpoint/node.js","pathname":"/_image","prerender":false,"fallbackRoutes":[],"origin":"internal","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/account.Gbimn-ZV.css"}],"routeData":{"route":"/account","isIndex":false,"type":"page","pattern":"^\\/account\\/?$","segments":[[{"content":"account","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/account.astro","pathname":"/account","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/account.Gbimn-ZV.css"}],"routeData":{"route":"/brands","isIndex":false,"type":"page","pattern":"^\\/brands\\/?$","segments":[[{"content":"brands","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/brands.astro","pathname":"/brands","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/account.Gbimn-ZV.css"}],"routeData":{"route":"/catalog/brands/[slug]","isIndex":false,"type":"page","pattern":"^\\/catalog\\/brands\\/([^/]+?)\\/?$","segments":[[{"content":"catalog","dynamic":false,"spread":false}],[{"content":"brands","dynamic":false,"spread":false}],[{"content":"slug","dynamic":true,"spread":false}]],"params":["slug"],"component":"src/pages/catalog/brands/[slug].astro","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/account.Gbimn-ZV.css"}],"routeData":{"route":"/catalog/categories/[slug]","isIndex":false,"type":"page","pattern":"^\\/catalog\\/categories\\/([^/]+?)\\/?$","segments":[[{"content":"catalog","dynamic":false,"spread":false}],[{"content":"categories","dynamic":false,"spread":false}],[{"content":"slug","dynamic":true,"spread":false}]],"params":["slug"],"component":"src/pages/catalog/categories/[slug].astro","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/account.Gbimn-ZV.css"}],"routeData":{"route":"/catalog/parts/[id]","isIndex":false,"type":"page","pattern":"^\\/catalog\\/parts\\/([^/]+?)\\/?$","segments":[[{"content":"catalog","dynamic":false,"spread":false}],[{"content":"parts","dynamic":false,"spread":false}],[{"content":"id","dynamic":true,"spread":false}]],"params":["id"],"component":"src/pages/catalog/parts/[id].astro","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/account.Gbimn-ZV.css"}],"routeData":{"route":"/catalog","isIndex":false,"type":"page","pattern":"^\\/catalog\\/?$","segments":[[{"content":"catalog","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/catalog.astro","pathname":"/catalog","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/account.Gbimn-ZV.css"}],"routeData":{"route":"/categories","isIndex":false,"type":"page","pattern":"^\\/categories\\/?$","segments":[[{"content":"categories","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/categories.astro","pathname":"/categories","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/account.Gbimn-ZV.css"}],"routeData":{"route":"/login","isIndex":false,"type":"page","pattern":"^\\/login\\/?$","segments":[[{"content":"login","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/login.astro","pathname":"/login","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/account.Gbimn-ZV.css"}],"routeData":{"route":"/register","isIndex":false,"type":"page","pattern":"^\\/register\\/?$","segments":[[{"content":"register","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/register.astro","pathname":"/register","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/account.Gbimn-ZV.css"}],"routeData":{"route":"/search","isIndex":false,"type":"page","pattern":"^\\/search\\/?$","segments":[[{"content":"search","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/search.astro","pathname":"/search","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/account.Gbimn-ZV.css"}],"routeData":{"route":"/","isIndex":true,"type":"page","pattern":"^\\/$","segments":[],"params":[],"component":"src/pages/index.astro","pathname":"/","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}}],"base":"/","trailingSlash":"ignore","compressHTML":true,"componentMetadata":[["D:/Dev/parttec/site/src/pages/account.astro",{"propagation":"none","containsHead":true}],["D:/Dev/parttec/site/src/pages/brands.astro",{"propagation":"none","containsHead":true}],["D:/Dev/parttec/site/src/pages/catalog.astro",{"propagation":"none","containsHead":true}],["D:/Dev/parttec/site/src/pages/catalog/brands/[slug].astro",{"propagation":"none","containsHead":true}],["D:/Dev/parttec/site/src/pages/catalog/categories/[slug].astro",{"propagation":"none","containsHead":true}],["D:/Dev/parttec/site/src/pages/catalog/parts/[id].astro",{"propagation":"none","containsHead":true}],["D:/Dev/parttec/site/src/pages/categories.astro",{"propagation":"none","containsHead":true}],["D:/Dev/parttec/site/src/pages/index.astro",{"propagation":"none","containsHead":true}],["D:/Dev/parttec/site/src/pages/login.astro",{"propagation":"none","containsHead":true}],["D:/Dev/parttec/site/src/pages/register.astro",{"propagation":"none","containsHead":true}],["D:/Dev/parttec/site/src/pages/search.astro",{"propagation":"none","containsHead":true}]],"renderers":[],"clientDirectives":[["idle","(()=>{var l=(n,t)=>{let i=async()=>{await(await n())()},e=typeof t.value==\"object\"?t.value:void 0,s={timeout:e==null?void 0:e.timeout};\"requestIdleCallback\"in window?window.requestIdleCallback(i,s):setTimeout(i,s.timeout||200)};(self.Astro||(self.Astro={})).idle=l;window.dispatchEvent(new Event(\"astro:idle\"));})();"],["load","(()=>{var e=async t=>{await(await t())()};(self.Astro||(self.Astro={})).load=e;window.dispatchEvent(new Event(\"astro:load\"));})();"],["media","(()=>{var n=(a,t)=>{let i=async()=>{await(await a())()};if(t.value){let e=matchMedia(t.value);e.matches?i():e.addEventListener(\"change\",i,{once:!0})}};(self.Astro||(self.Astro={})).media=n;window.dispatchEvent(new Event(\"astro:media\"));})();"],["only","(()=>{var e=async t=>{await(await t())()};(self.Astro||(self.Astro={})).only=e;window.dispatchEvent(new Event(\"astro:only\"));})();"],["visible","(()=>{var a=(s,i,o)=>{let r=async()=>{await(await s())()},t=typeof i.value==\"object\"?i.value:void 0,c={rootMargin:t==null?void 0:t.rootMargin},n=new IntersectionObserver(e=>{for(let l of e)if(l.isIntersecting){n.disconnect(),r();break}},c);for(let e of o.children)n.observe(e)};(self.Astro||(self.Astro={})).visible=a;window.dispatchEvent(new Event(\"astro:visible\"));})();"]],"entryModules":{"\u0000astro-internal:middleware":"_astro-internal_middleware.mjs","\u0000noop-actions":"_noop-actions.mjs","\u0000@astro-page:src/pages/account@_@astro":"pages/account.astro.mjs","\u0000@astro-page:src/pages/brands@_@astro":"pages/brands.astro.mjs","\u0000@astro-page:src/pages/catalog/brands/[slug]@_@astro":"pages/catalog/brands/_slug_.astro.mjs","\u0000@astro-page:src/pages/catalog/categories/[slug]@_@astro":"pages/catalog/categories/_slug_.astro.mjs","\u0000@astro-page:src/pages/catalog/parts/[id]@_@astro":"pages/catalog/parts/_id_.astro.mjs","\u0000@astro-page:src/pages/catalog@_@astro":"pages/catalog.astro.mjs","\u0000@astro-page:src/pages/categories@_@astro":"pages/categories.astro.mjs","\u0000@astro-page:src/pages/login@_@astro":"pages/login.astro.mjs","\u0000@astro-page:src/pages/register@_@astro":"pages/register.astro.mjs","\u0000@astro-page:src/pages/search@_@astro":"pages/search.astro.mjs","\u0000@astro-page:src/pages/index@_@astro":"pages/index.astro.mjs","\u0000@astrojs-ssr-virtual-entry":"entry.mjs","\u0000@astro-renderers":"renderers.mjs","\u0000@astro-page:node_modules/astro/dist/assets/endpoint/node@_@js":"pages/_image.astro.mjs","\u0000@astrojs-ssr-adapter":"<EMAIL>","\u0000@astrojs-manifest":"manifest_B37eRlrD.mjs","D:/Dev/parttec/site/node_modules/unstorage/drivers/fs-lite.mjs":"chunks/fs-lite_COtHaKzy.mjs","D:/Dev/parttec/site/node_modules/astro/dist/assets/services/sharp.js":"chunks/sharp_BE11tTb-.mjs","@/components/auth/AccountCard":"_astro/AccountCard.C2eN4acN.js","@/components/providers/TrpcProvider":"_astro/TrpcProvider.C7wRouY7.js","@/components/catalog/BrandCard":"_astro/BrandCard.D5c4t764.js","@/components/catalog/CategoryCard":"_astro/CategoryCard.ricYve-i.js","@/components/catalog/PartCard":"_astro/PartCard.CxmBREk1.js","@/components/catalog/islands/SearchHeaderIsland":"_astro/SearchHeaderIsland.BQ4Awgq4.js","@/components/catalog/islands/FiltersIsland":"_astro/FiltersIsland.DzRvhK33.js","@/components/catalog/islands/ResultsIsland":"_astro/ResultsIsland.CayU7xyL.js","@/components/catalog/islands/AIAssistantIsland":"_astro/AIAssistantIsland.BcWbkhlu.js","@/components/catalog/islands/ItemDetailsIsland":"_astro/ItemDetailsIsland.DAz842eD.js","@/components/auth/LoginForm":"_astro/LoginForm.BD4C1fNb.js","@/components/auth/RegisterForm":"_astro/RegisterForm.CgpmKvMP.js","@/components/catalog/SearchForm":"_astro/SearchForm.BXRSYkxz.js","@/components/navigation/AuthNav":"_astro/AuthNav.C01zl1Eu.js","@astrojs/react/client.js":"_astro/client.3bM_UWZ3.js","D:/Dev/parttec/site/node_modules/astro/components/ClientRouter.astro?astro&type=script&index=0&lang.ts":"_astro/ClientRouter.astro_astro_type_script_index_0_lang.B0JFCTGp.js","astro:scripts/before-hydration.js":""},"inlinedScripts":[],"assets":["/_astro/account.Gbimn-ZV.css","/favicon.svg","/placeholder.svg","/_astro/AccountCard.C2eN4acN.js","/_astro/AIAssistantIsland.BcWbkhlu.js","/_astro/auth-client.DHpPh7L9.js","/_astro/AuthNav.C01zl1Eu.js","/_astro/badge.CrhVNNKw.js","/_astro/bot.zF5v5Aoc.js","/_astro/BrandCard.D5c4t764.js","/_astro/button.BfGcEciz.js","/_astro/card.AN6mqHU1.js","/_astro/CategoryCard.ricYve-i.js","/_astro/client.3bM_UWZ3.js","/_astro/ClientRouter.astro_astro_type_script_index_0_lang.B0JFCTGp.js","/_astro/createLucideIcon.DdiNmGRb.js","/_astro/dialog.BXJ4m5WQ.js","/_astro/download.D1J5CNLA.js","/_astro/FiltersIsland.DzRvhK33.js","/_astro/funnel.Ck0Ax3XR.js","/_astro/image.BtzVA0_o.js","/_astro/index.0yr9KlQE.js","/_astro/index.3rXK4OIH.js","/_astro/index.BWNh2K4G.js","/_astro/index.Di12BYGB.js","/_astro/index.k1eM7uTc.js","/_astro/index.NogD4Y5M.js","/_astro/index.ViApDAiE.js","/_astro/input.aGo1BNk4.js","/_astro/ItemDetailsIsland.DAz842eD.js","/_astro/jsx-runtime.D_zvdyIk.js","/_astro/LoginForm.BD4C1fNb.js","/_astro/modern-input.Cd5D-6fr.js","/_astro/package.CV5wAU3g.js","/_astro/PartCard.CxmBREk1.js","/_astro/RegisterForm.CgpmKvMP.js","/_astro/ResultsIsland.CayU7xyL.js","/_astro/router.WLLD8StG.js","/_astro/search.ooeMVQga.js","/_astro/SearchForm.BXRSYkxz.js","/_astro/SearchHeaderIsland.BQ4Awgq4.js","/_astro/separator.1hI8CDE4.js","/_astro/status-badge.BsGsEBtY.js","/_astro/trpc.9HS4D061.js","/_astro/TrpcBoundary.g7wmOpBw.js","/_astro/TrpcProvider.C7wRouY7.js","/_astro/useCatalogSearch.3hHK2Zsb.js","/_astro/utils.CBfrqCZ4.js","/_astro/zap.hS7937cF.js"],"buildFormat":"directory","checkOrigin":true,"serverIslandNameMap":[],"key":"6I2JRm0MSj3ZpGeriYwh0FPI9Pz1zFg4w6Tbb7wu/hg=","sessionConfig":{"driver":"fs-lite","options":{"base":"D:\\Dev\\parttec\\site\\node_modules\\.astro\\sessions"}}});
if (manifest.sessionConfig) manifest.sessionConfig.driverModule = () => import('./chunks/fs-lite_COtHaKzy.mjs');

export { manifest };
