import { z } from 'zod'
import { router, expertProcedure } from '../trpc'
import { MatchingService } from '../services/matching.service'
import {
  FindMatchingPartsInput,
  FindMatchingCatalogItemsInput,
  ProposeLinkInput,
  ListProposalsInput,
  RejectProposalInput,
  ApproveProposalInput,
  CreatePartFromItemsInput,
  GenerateProposalsInput,
} from '../schemas/matching'

export const matchingRouter = router({
  /**
   * Находит кандидатов Part для указанного CatalogItem с учетом:
   * - числовых допусков (AttributeTemplate.tolerance)
   * - групп синонимов строк (AttributeSynonymGroup/AttributeSynonym)
   */
  findMatchingParts: expertProcedure
    .input(FindMatchingPartsInput)
    .query(async ({ input }: { input: z.infer<typeof FindMatchingPartsInput> }) => MatchingService.findMatchingParts(input))
  ,
  /**
   * Находит подходящие CatalogItem для указанного Part с учетом допусков и групп синонимов.
   */
  findMatchingCatalogItems: expertProcedure
    .input(FindMatchingCatalogItemsInput)
    .query(async ({ input }: { input: z.infer<typeof FindMatchingCatalogItemsInput> }) => MatchingService.findMatchingCatalogItems(input))
  ,
  /**
   * Сохранить предложение (MatchingProposal) на связь CatalogItem ↔ Part
   */
  proposeLink: expertProcedure
    .input(ProposeLinkInput)
    .mutation(async ({ input }: { input: z.infer<typeof ProposeLinkInput> }) => MatchingService.proposeLink(input))
  ,
  /**
   * Получить список предложений с пагинацией и фильтрами
   */
  listProposals: expertProcedure
    .input(ListProposalsInput)
    .query(async ({ input }: { input: z.infer<typeof ListProposalsInput> }) => MatchingService.listProposals(input))
  ,
  /**
   * Отклонить предложение
   */
  rejectProposal: expertProcedure
    .input(RejectProposalInput)
    .mutation(async ({ input }: { input: z.infer<typeof RejectProposalInput> }) => MatchingService.rejectProposal(input))
  ,
  /**
   * Подтвердить предложение — создаёт PartApplicability
   */
  approveProposal: expertProcedure
    .input(ApproveProposalInput)
    .mutation(async ({ input }: { input: z.infer<typeof ApproveProposalInput> }) => MatchingService.approveProposal(input))
  ,
  /**
   * Создать новую группу Part из набора CatalogItems
   */
  createPartFromItems: expertProcedure
    .input(CreatePartFromItemsInput)
    .mutation(async ({ input }: { input: z.infer<typeof CreatePartFromItemsInput> }) => MatchingService.createPartFromItems(input))
  ,
  /**
   * Автоматическая генерация предложений по заданным параметрам
   * Пробегает по отобранным CatalogItem, ищет кандидатов Part и сохраняет PENDING предложения
   */
  generateProposals: expertProcedure
    .input(GenerateProposalsInput)
    .mutation(async ({ input }: { input: z.infer<typeof GenerateProposalsInput> }) => MatchingService.generateProposals(input))
})


