# Персистентность данных в Parttec Docker

## 📁 Структура данных

```
parttec/
├── data/                           # Персистентные данные (НЕ в Git)
│   ├── postgres/                  # Файлы базы данных PostgreSQL
│   ├── media/                     # MediaAsset файлы (изображения, PDF)
│   │   ├── schema-images/         # Изображения схем
│   │   ├── part-images/           # Основные изображения деталей
│   │   ├── category-images/       # Изображения категорий
│   │   ├── part-media/            # Галерея деталей (изображения + PDF)
│   │   ├── catalogitem-images/    # Основные изображения позиций каталога
│   │   └── catalogitem-media/     # Галерея позиций каталога
│   └── .gitkeep                   # Сохраняет структуру в Git
├── docker-compose.yml             # Использует bind mounts
└── docker-compose.prod.yml        # Использует bind mounts
```

## 🔒 Что сохраняется между деплоями

### ✅ Персистентные данные:
- **База данных PostgreSQL** → `./data/postgres/`
- **MediaAsset файлы** → `./data/media/`
  - Изображения схем → `./data/media/schema-images/`
  - Изображения деталей → `./data/media/part-images/`
  - Изображения категорий → `./data/media/category-images/`
  - Медиа галерея деталей → `./data/media/part-media/`
  - Изображения позиций каталога → `./data/media/catalogitem-images/`
  - Медиа галерея позиций → `./data/media/catalogitem-media/`
- **Конфигурация** → `.env` файл

### ❌ Что НЕ сохраняется:
- Docker образы (пересобираются)
- Код приложений (обновляется из Git)
- Временные файлы контейнеров

## 🚀 Как это работает

### Bind Mounts vs Docker Volumes

**Раньше (Docker Volumes):**
```yaml
volumes:
  - postgres_data:/var/lib/postgresql/data  # Скрыто в Docker
```

**Сейчас (Bind Mounts):**
```yaml
volumes:
  - ./data/postgres:/var/lib/postgresql/data  # База данных
  - ./data/media:/app/uploads                 # MediaAsset файлы
```

### Преимущества Bind Mounts:
1. **Прозрачность** - данные видны в файловой системе
2. **Простота бэкапов** - обычное копирование файлов
3. **Отладка** - можно посмотреть файлы напрямую
4. **Миграция** - легко перенести на другой сервер

## 📋 Управление данными

### Создание директорий

Скрипты деплоя автоматически создают нужные директории:

```bash
# Linux/macOS
./deploy.sh dev

# Windows
deploy.bat dev
```

Или вручную:
```bash
mkdir -p data/postgres data/uploads
```

### Проверка данных

```bash
# Размер базы данных
du -sh data/postgres/

# MediaAsset файлы
ls -la data/media/
du -sh data/media/*/

# Общий размер данных
du -sh data/
```

### Права доступа

PostgreSQL требует специальных прав:
```bash
# Если возникают проблемы с правами
sudo chown -R 999:999 data/postgres/
sudo chown -R 1001:1001 data/media/
```

## 💾 Резервное копирование

### Автоматический бэкап БД

```bash
#!/bin/bash
# backup-db.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="./backups"

mkdir -p $BACKUP_DIR

# SQL дамп
docker exec parttec-postgres pg_dump -U postgres parttec > $BACKUP_DIR/db_$DATE.sql

# Архив файлов данных
tar -czf $BACKUP_DIR/postgres_files_$DATE.tar.gz data/postgres/

# MediaAsset файлы
tar -czf $BACKUP_DIR/media_$DATE.tar.gz data/media/

echo "Бэкап создан: $BACKUP_DIR/"
```

### Восстановление

```bash
# Остановить контейнеры
docker-compose down

# Восстановить файлы БД
tar -xzf backups/postgres_files_YYYYMMDD_HHMMSS.tar.gz

# Восстановить media файлы
tar -xzf backups/media_YYYYMMDD_HHMMSS.tar.gz

# Запустить контейнеры
docker-compose up -d
```

### Восстановление из SQL дампа

```bash
# Запустить только PostgreSQL
docker-compose up -d postgres

# Дождаться запуска
sleep 10

# Восстановить из SQL
docker exec -i parttec-postgres psql -U postgres parttec < backups/db_YYYYMMDD_HHMMSS.sql

# Запустить остальные сервисы
docker-compose up -d
```

## 🔄 Миграция между серверами

### Экспорт данных

```bash
# Создать полный архив
tar -czf parttec-data-$(date +%Y%m%d).tar.gz data/ .env

# Или отдельно
tar -czf postgres-data.tar.gz data/postgres/
tar -czf uploads-data.tar.gz data/uploads/
```

### Импорт на новом сервере

```bash
# Распаковать данные
tar -xzf parttec-data-YYYYMMDD.tar.gz

# Установить права
sudo chown -R 999:999 data/postgres/
sudo chown -R 1001:1001 data/uploads/

# Запустить
./deploy.sh prod
```

## ⚠️ Важные моменты

### Безопасность

1. **Никогда не коммитьте** директорию `data/` в Git
2. **Регулярно делайте бэкапы** критических данных
3. **Проверяйте права доступа** к файлам данных

### Мониторинг

```bash
# Проверка места на диске
df -h

# Размер данных PostgreSQL
du -sh data/postgres/

# Логи PostgreSQL
docker-compose logs postgres
```

### Очистка

```bash
# Очистка старых логов PostgreSQL (осторожно!)
docker exec parttec-postgres find /var/lib/postgresql/data/log -name "*.log" -mtime +7 -delete

# Очистка временных файлов uploads
find data/uploads/ -name "*.tmp" -delete
```

## 🆘 Устранение проблем

### PostgreSQL не запускается

```bash
# Проверить права доступа
ls -la data/postgres/

# Исправить права
sudo chown -R 999:999 data/postgres/

# Проверить логи
docker-compose logs postgres
```

### Потеря данных

```bash
# Если данные повреждены, восстановить из бэкапа
docker-compose down
rm -rf data/postgres/*
tar -xzf backups/postgres_files_LATEST.tar.gz
docker-compose up -d
```

### Нет места на диске

```bash
# Очистить Docker кэш
docker system prune -a

# Очистить старые бэкапы
find backups/ -name "*.tar.gz" -mtime +30 -delete

# Проверить размер данных
du -sh data/*
```

## 📊 Мониторинг в production

### Автоматический мониторинг

```bash
#!/bin/bash
# monitor-data.sh

# Проверка размера данных
DATA_SIZE=$(du -sm data/ | cut -f1)
if [ $DATA_SIZE -gt 10000 ]; then  # 10GB
    echo "WARN: Данные превышают 10GB: ${DATA_SIZE}MB"
fi

# Проверка доступности БД
if ! docker exec parttec-postgres pg_isready -U postgres; then
    echo "ERROR: PostgreSQL недоступен"
fi

# Проверка места на диске
DISK_USAGE=$(df -h . | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "WARN: Диск заполнен на ${DISK_USAGE}%"
fi
```

### Cron задачи

```bash
# Добавить в crontab
# Ежедневный бэкап в 2:00
0 2 * * * /path/to/parttec/backup-db.sh

# Мониторинг каждые 30 минут
*/30 * * * * /path/to/parttec/monitor-data.sh
```
