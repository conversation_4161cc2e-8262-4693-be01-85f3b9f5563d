/**
 * Composable для debounce значений
 */

import { ref, watch, type Ref } from 'vue';

export function useDebounce<T>(source: Ref<T>, delay: number = 300): Ref<T> {
  const debounced = ref(source.value) as Ref<T>;
  let timeout: NodeJS.Timeout | null = null;

  watch(source, (newValue) => {
    if (timeout) {
      clearTimeout(timeout);
    }
    
    timeout = setTimeout(() => {
      debounced.value = newValue;
    }, delay);
  }, { immediate: false });

  return debounced;
}