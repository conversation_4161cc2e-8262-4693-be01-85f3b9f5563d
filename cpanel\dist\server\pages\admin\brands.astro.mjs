import { e as createComponent, f as createAstro, k as renderComponent, r as renderTemplate, m as maybeRenderHead } from '../../chunks/astro/server_DbndhTWv.mjs';
import 'kleur/colors';
import { defineComponent, useSSRContext, mergeModels, ref, useModel, watch, onMounted, mergeProps, withCtx, createVNode, createTextVNode, createBlock, openBlock, Fragment, renderList } from 'vue';
import { E as ErrorBoundary } from '../../chunks/ErrorBoundary_CGDiV8up.mjs';
import { t as trpc } from '../../chunks/trpc_B_6Rt2Mk.mjs';
import { V as VButton } from '../../chunks/Button_CuwpNmer.mjs';
import { D as DataTable, s as script } from '../../chunks/index_BK2zpZlm.mjs';
import { PlusIcon, TrashIcon, PencilIcon } from 'lucide-vue-next';
import { D as Dialog } from '../../chunks/Dialog_g9jHgXMZ.mjs';
import { I as InputText } from '../../chunks/InputText_CtReD0EA.mjs';
import { S as SecondaryButton, T as Toast, $ as $$AdminLayout } from '../../chunks/AdminLayout_b20tykPC.mjs';
import { V as VAutoComplete } from '../../chunks/AutoComplete_CPZ3fUb-.mjs';
import '../../chunks/Checkbox_aVHeO0Hn.mjs';
import { V as VToggleSwitch } from '../../chunks/ToggleSwitch_C8GWpSWf.mjs';
import { ssrRenderComponent, ssrRenderAttrs, ssrRenderList } from 'vue/server-renderer';
import { _ as _export_sfc, n as navigate } from '../../chunks/ClientRouter_B8Zzhk9G.mjs';
export { r as renderers } from '../../chunks/_@astro-renderers_CicWY1rm.mjs';

const _sfc_main$2 = /* @__PURE__ */ defineComponent({
  __name: "EditBrandDialog",
  props: /* @__PURE__ */ mergeModels({
    brand: {}
  }, {
    "isVisible": { type: Boolean, ...{ required: true } },
    "isVisibleModifiers": {}
  }),
  emits: /* @__PURE__ */ mergeModels(["save", "cancel"], ["update:isVisible"]),
  setup(__props, { expose: __expose, emit: __emit }) {
    __expose();
    const countryOptions = ref([]);
    const allCountries = ref([]);
    const isVisible = useModel(__props, "isVisible");
    const props = __props;
    const emit = __emit;
    const localBrand = ref({});
    const dialogTitle = ref("\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u0431\u0440\u0435\u043D\u0434");
    const filterCountries = (event) => {
      const query = event.query.toLowerCase();
      if (!query) {
        countryOptions.value = [...allCountries.value];
      } else {
        countryOptions.value = allCountries.value.filter(
          (country) => country.toLowerCase().includes(query)
        );
      }
    };
    watch(isVisible, (newValue) => {
      if (newValue) {
        localBrand.value = { ...props.brand || {} };
        dialogTitle.value = props.brand?.id ? `\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C: ${props.brand.name}` : "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u0431\u0440\u0435\u043D\u0434";
      }
    });
    function handleCancel() {
      isVisible.value = false;
      emit("cancel");
    }
    function handleSave() {
      emit("save", { ...localBrand.value });
      isVisible.value = false;
    }
    async function loadCountries() {
      const countries = await trpc.crud.brand.findMany.query({
        where: {
          country: {
            not: null
          }
        },
        select: {
          country: true
        },
        distinct: ["country"],
        orderBy: {
          country: "asc"
        }
      });
      allCountries.value = countries.map((item) => item.country);
      countryOptions.value = [...allCountries.value];
    }
    onMounted(() => {
      loadCountries();
    });
    const __returned__ = { countryOptions, allCountries, isVisible, props, emit, localBrand, dialogTitle, filterCountries, handleCancel, handleSave, loadCountries, Button: VButton, Dialog, InputText, SecondaryButton, AutoComplete: VAutoComplete, ToggleSwitch: VToggleSwitch };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$2(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(ssrRenderComponent($setup["Dialog"], mergeProps({
    visible: $setup.isVisible,
    "onUpdate:visible": ($event) => $setup.isVisible = $event,
    modal: "",
    header: $setup.dialogTitle,
    class: "sm:w-100 w-9/10"
  }, _attrs), {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="flex flex-col gap-4 py-4"${_scopeId}><div class="flex flex-col"${_scopeId}><label for="name"${_scopeId}>\u041D\u0430\u0438\u043C\u0435\u043D\u043E\u0432\u0430\u043D\u0438\u0435</label>`);
        _push2(ssrRenderComponent($setup["InputText"], {
          id: "name",
          modelValue: $setup.localBrand.name,
          "onUpdate:modelValue": ($event) => $setup.localBrand.name = $event
        }, null, _parent2, _scopeId));
        _push2(`</div><div class="flex flex-col"${_scopeId}><label for="slug"${_scopeId}>URL \u0441\u043B\u0430\u0433</label>`);
        _push2(ssrRenderComponent($setup["InputText"], {
          id: "slug",
          modelValue: $setup.localBrand.slug,
          "onUpdate:modelValue": ($event) => $setup.localBrand.slug = $event
        }, null, _parent2, _scopeId));
        _push2(`</div><div class="flex flex-col"${_scopeId}><label for="country"${_scopeId}>\u0421\u0442\u0440\u0430\u043D\u0430</label>`);
        _push2(ssrRenderComponent($setup["AutoComplete"], {
          onComplete: $setup.filterCountries,
          id: "country",
          dropdownMode: "current",
          modelValue: $setup.localBrand.country,
          "onUpdate:modelValue": ($event) => $setup.localBrand.country = $event,
          dropdown: "",
          suggestions: $setup.countryOptions
        }, null, _parent2, _scopeId));
        _push2(`</div><div class="flex gap-3"${_scopeId}><label for="isOem"${_scopeId}>OEM</label>`);
        _push2(ssrRenderComponent($setup["ToggleSwitch"], {
          id: "isOem",
          modelValue: $setup.localBrand.isOem,
          "onUpdate:modelValue": ($event) => $setup.localBrand.isOem = $event
        }, null, _parent2, _scopeId));
        _push2(`</div></div><div class="flex justify-end gap-2"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["SecondaryButton"], {
          type: "button",
          label: "Cancel",
          onClick: $setup.handleCancel
        }, null, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["Button"], {
          type: "button",
          label: "Save",
          onClick: $setup.handleSave
        }, null, _parent2, _scopeId));
        _push2(`</div>`);
      } else {
        return [
          createVNode("div", { class: "flex flex-col gap-4 py-4" }, [
            createVNode("div", { class: "flex flex-col" }, [
              createVNode("label", { for: "name" }, "\u041D\u0430\u0438\u043C\u0435\u043D\u043E\u0432\u0430\u043D\u0438\u0435"),
              createVNode($setup["InputText"], {
                id: "name",
                modelValue: $setup.localBrand.name,
                "onUpdate:modelValue": ($event) => $setup.localBrand.name = $event
              }, null, 8, ["modelValue", "onUpdate:modelValue"])
            ]),
            createVNode("div", { class: "flex flex-col" }, [
              createVNode("label", { for: "slug" }, "URL \u0441\u043B\u0430\u0433"),
              createVNode($setup["InputText"], {
                id: "slug",
                modelValue: $setup.localBrand.slug,
                "onUpdate:modelValue": ($event) => $setup.localBrand.slug = $event
              }, null, 8, ["modelValue", "onUpdate:modelValue"])
            ]),
            createVNode("div", { class: "flex flex-col" }, [
              createVNode("label", { for: "country" }, "\u0421\u0442\u0440\u0430\u043D\u0430"),
              createVNode($setup["AutoComplete"], {
                onComplete: $setup.filterCountries,
                id: "country",
                dropdownMode: "current",
                modelValue: $setup.localBrand.country,
                "onUpdate:modelValue": ($event) => $setup.localBrand.country = $event,
                dropdown: "",
                suggestions: $setup.countryOptions
              }, null, 8, ["modelValue", "onUpdate:modelValue", "suggestions"])
            ]),
            createVNode("div", { class: "flex gap-3" }, [
              createVNode("label", { for: "isOem" }, "OEM"),
              createVNode($setup["ToggleSwitch"], {
                id: "isOem",
                modelValue: $setup.localBrand.isOem,
                "onUpdate:modelValue": ($event) => $setup.localBrand.isOem = $event
              }, null, 8, ["modelValue", "onUpdate:modelValue"])
            ])
          ]),
          createVNode("div", { class: "flex justify-end gap-2" }, [
            createVNode($setup["SecondaryButton"], {
              type: "button",
              label: "Cancel",
              onClick: $setup.handleCancel
            }),
            createVNode($setup["Button"], {
              type: "button",
              label: "Save",
              onClick: $setup.handleSave
            })
          ])
        ];
      }
    }),
    _: 1
  }, _parent));
}
const _sfc_setup$2 = _sfc_main$2.setup;
_sfc_main$2.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/brands/EditBrandDialog.vue");
  return _sfc_setup$2 ? _sfc_setup$2(props, ctx) : void 0;
};
const EditBrandDialog = /* @__PURE__ */ _export_sfc(_sfc_main$2, [["ssrRender", _sfc_ssrRender$2]]);

const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "BrandList",
  props: {
    initialData: {}
  },
  setup(__props, { expose: __expose }) {
    __expose();
    const searchValue = ref("");
    const dialogVisible = ref(false);
    const editingBrand = ref(null);
    const props = __props;
    const items = ref(props.initialData);
    const keyMapping = {
      id: "ID",
      name: "\u041D\u0430\u0438\u043C\u0435\u043D\u043E\u0432\u0430\u043D\u0438\u0435",
      slug: "url \u0441\u043B\u0430\u0433",
      country: "\u0421\u0442\u0440\u0430\u043D\u0430",
      isOem: "OEM"
    };
    const columnKeys = [
      "id",
      "name",
      "slug",
      "country",
      "isOem"
    ];
    function createBrand() {
      editingBrand.value = {};
      dialogVisible.value = true;
    }
    function editBrand(data) {
      editingBrand.value = { ...data };
      dialogVisible.value = true;
    }
    async function handleSave(brandData) {
      if (!brandData) return;
      try {
        if (brandData.id) {
          const { id, ...dataToUpdate } = brandData;
          await trpc.crud.brand.update.mutate({
            where: { id },
            data: dataToUpdate
          });
        } else {
          if (brandData.name && brandData.slug) {
            await trpc.crud.brand.create.mutate({
              data: {
                name: brandData.name,
                slug: brandData.slug,
                country: brandData.country,
                isOem: brandData.isOem || false
              }
            });
          } else {
            console.error("Name and slug are required to create a brand.");
            return;
          }
        }
        navigate(window.location.href);
      } catch (error) {
        console.error("Failed to save brand:", error);
      } finally {
        dialogVisible.value = false;
      }
    }
    function handleCancel() {
      dialogVisible.value = false;
      editingBrand.value = null;
    }
    async function deleteBrand(data) {
      dialogVisible.value = false;
      await trpc.crud.brand.delete.mutate({
        where: {
          id: data.id
        }
      });
      navigate();
    }
    watch(searchValue, (newValue) => {
      debouncedSearch(newValue);
    });
    async function debouncedSearch(value = "") {
      console.log("value", value);
      items.value = await trpc.crud.brand.findMany.query({
        where: {
          OR: [
            {
              name: {
                contains: value
              }
            },
            {
              slug: {
                contains: value
              }
            },
            {
              country: {
                contains: value
              }
            }
          ]
        }
      });
    }
    const __returned__ = { searchValue, dialogVisible, editingBrand, props, items, keyMapping, columnKeys, createBrand, editBrand, handleSave, handleCancel, deleteBrand, debouncedSearch, Button: VButton, DataTable, get PencilIcon() {
      return PencilIcon;
    }, get TrashIcon() {
      return TrashIcon;
    }, get PlusIcon() {
      return PlusIcon;
    }, get Column() {
      return script;
    }, EditBrandDialog, InputText, Toast };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$1(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(_attrs)}><div class="flex justify-between items-center mb-4"><h1 class="text-2xl font-bold">\u0411\u0440\u0435\u043D\u0434\u044B</h1>`);
  _push(ssrRenderComponent($setup["Button"], { onClick: $setup.createBrand }, {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["PlusIcon"], { class: "w-5 h-5 mr-2" }, null, _parent2, _scopeId));
        _push2(` \u0421\u043E\u0437\u0434\u0430\u0442\u044C \u0431\u0440\u0435\u043D\u0434 `);
      } else {
        return [
          createVNode($setup["PlusIcon"], { class: "w-5 h-5 mr-2" }),
          createTextVNode(" \u0421\u043E\u0437\u0434\u0430\u0442\u044C \u0431\u0440\u0435\u043D\u0434 ")
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</div>`);
  _push(ssrRenderComponent($setup["DataTable"], {
    "show-headers": "",
    value: $setup.items
  }, {
    header: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="flex justify-end"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["InputText"], {
          modelValue: $setup.searchValue,
          "onUpdate:modelValue": ($event) => $setup.searchValue = $event,
          placeholder: "\u041F\u043E\u0438\u0441\u043A"
        }, null, _parent2, _scopeId));
        _push2(`</div>`);
      } else {
        return [
          createVNode("div", { class: "flex justify-end" }, [
            createVNode($setup["InputText"], {
              modelValue: $setup.searchValue,
              "onUpdate:modelValue": ($event) => $setup.searchValue = $event,
              placeholder: "\u041F\u043E\u0438\u0441\u043A"
            }, null, 8, ["modelValue", "onUpdate:modelValue"])
          ])
        ];
      }
    }),
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<!--[-->`);
        ssrRenderList($setup.columnKeys, (key) => {
          _push2(ssrRenderComponent($setup["Column"], {
            key,
            field: key,
            header: $setup.keyMapping[key] || key
          }, null, _parent2, _scopeId));
        });
        _push2(`<!--]-->`);
        _push2(ssrRenderComponent($setup["Column"], {
          field: "_count.catalogItems",
          header: "\u041A\u043E\u043B-\u0432\u043E \u043A\u0430\u0442.\u043F\u043E\u0437."
        }, null, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["Column"], { header: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F" }, {
          body: withCtx(({ data }, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(`<div class="flex gap-2"${_scopeId2}>`);
              _push3(ssrRenderComponent($setup["Button"], {
                onClick: ($event) => $setup.editBrand(data),
                outlined: "",
                size: "small"
              }, {
                default: withCtx((_2, _push4, _parent4, _scopeId3) => {
                  if (_push4) {
                    _push4(ssrRenderComponent($setup["PencilIcon"], { class: "w-5 h-5" }, null, _parent4, _scopeId3));
                  } else {
                    return [
                      createVNode($setup["PencilIcon"], { class: "w-5 h-5" })
                    ];
                  }
                }),
                _: 2
              }, _parent3, _scopeId2));
              _push3(ssrRenderComponent($setup["Button"], {
                onClick: ($event) => $setup.deleteBrand(data),
                outlined: "",
                severity: "danger",
                size: "small"
              }, {
                default: withCtx((_2, _push4, _parent4, _scopeId3) => {
                  if (_push4) {
                    _push4(ssrRenderComponent($setup["TrashIcon"], { class: "w-5 h-5" }, null, _parent4, _scopeId3));
                  } else {
                    return [
                      createVNode($setup["TrashIcon"], { class: "w-5 h-5" })
                    ];
                  }
                }),
                _: 2
              }, _parent3, _scopeId2));
              _push3(`</div>`);
            } else {
              return [
                createVNode("div", { class: "flex gap-2" }, [
                  createVNode($setup["Button"], {
                    onClick: ($event) => $setup.editBrand(data),
                    outlined: "",
                    size: "small"
                  }, {
                    default: withCtx(() => [
                      createVNode($setup["PencilIcon"], { class: "w-5 h-5" })
                    ]),
                    _: 2
                  }, 1032, ["onClick"]),
                  createVNode($setup["Button"], {
                    onClick: ($event) => $setup.deleteBrand(data),
                    outlined: "",
                    severity: "danger",
                    size: "small"
                  }, {
                    default: withCtx(() => [
                      createVNode($setup["TrashIcon"], { class: "w-5 h-5" })
                    ]),
                    _: 2
                  }, 1032, ["onClick"])
                ])
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
      } else {
        return [
          (openBlock(), createBlock(Fragment, null, renderList($setup.columnKeys, (key) => {
            return createVNode($setup["Column"], {
              key,
              field: key,
              header: $setup.keyMapping[key] || key
            }, null, 8, ["field", "header"]);
          }), 64)),
          createVNode($setup["Column"], {
            field: "_count.catalogItems",
            header: "\u041A\u043E\u043B-\u0432\u043E \u043A\u0430\u0442.\u043F\u043E\u0437."
          }),
          createVNode($setup["Column"], { header: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F" }, {
            body: withCtx(({ data }) => [
              createVNode("div", { class: "flex gap-2" }, [
                createVNode($setup["Button"], {
                  onClick: ($event) => $setup.editBrand(data),
                  outlined: "",
                  size: "small"
                }, {
                  default: withCtx(() => [
                    createVNode($setup["PencilIcon"], { class: "w-5 h-5" })
                  ]),
                  _: 2
                }, 1032, ["onClick"]),
                createVNode($setup["Button"], {
                  onClick: ($event) => $setup.deleteBrand(data),
                  outlined: "",
                  severity: "danger",
                  size: "small"
                }, {
                  default: withCtx(() => [
                    createVNode($setup["TrashIcon"], { class: "w-5 h-5" })
                  ]),
                  _: 2
                }, 1032, ["onClick"])
              ])
            ]),
            _: 1
          })
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(ssrRenderComponent($setup["EditBrandDialog"], {
    isVisible: $setup.dialogVisible,
    "onUpdate:isVisible": ($event) => $setup.dialogVisible = $event,
    brand: $setup.editingBrand,
    onSave: $setup.handleSave,
    onCancel: $setup.handleCancel
  }, null, _parent));
  _push(ssrRenderComponent($setup["Toast"], null, null, _parent));
  _push(`</div>`);
}
const _sfc_setup$1 = _sfc_main$1.setup;
_sfc_main$1.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/brands/BrandList.vue");
  return _sfc_setup$1 ? _sfc_setup$1(props, ctx) : void 0;
};
const BrandList = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["ssrRender", _sfc_ssrRender$1]]);

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "BrandListBoundary",
  props: {
    initialData: {}
  },
  setup(__props, { expose: __expose }) {
    __expose();
    const props = __props;
    const key = ref(0);
    const onRetry = () => {
      key.value++;
    };
    const __returned__ = { props, key, onRetry, ErrorBoundary, BrandList };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(ssrRenderComponent($setup["ErrorBoundary"], mergeProps({
    variant: "minimal",
    title: "\u041E\u0448\u0438\u0431\u043A\u0430 \u0431\u0440\u0435\u043D\u0434\u043E\u0432",
    message: "\u0421\u043F\u0438\u0441\u043E\u043A \u0431\u0440\u0435\u043D\u0434\u043E\u0432 \u043D\u0435 \u043E\u0442\u0440\u0438\u0441\u043E\u0432\u0430\u043B\u0441\u044F. \u041F\u043E\u0432\u0442\u043E\u0440\u0438\u0442\u0435 \u043F\u043E\u043F\u044B\u0442\u043A\u0443.",
    onRetry: $setup.onRetry
  }, _attrs), {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["BrandList"], {
          initialData: $props.initialData,
          key: $setup.key
        }, null, _parent2, _scopeId));
      } else {
        return [
          (openBlock(), createBlock($setup["BrandList"], {
            initialData: $props.initialData,
            key: $setup.key
          }, null, 8, ["initialData"]))
        ];
      }
    }),
    _: 1
  }, _parent));
}
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/brands/BrandListBoundary.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const BrandListBoundary = /* @__PURE__ */ _export_sfc(_sfc_main, [["ssrRender", _sfc_ssrRender]]);

const $$Astro = createAstro();
const $$Index = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Index;
  const page = Astro2.url.searchParams.get("page") || "1";
  const pageSize = Astro2.url.searchParams.get("pageSize") || "100";
  const brands = await trpc.crud.brand.findMany.query({
    take: Number(pageSize),
    skip: (Number(page) - 1) * Number(pageSize),
    include: {
      _count: {
        select: {
          catalogItems: true
        }
      }
    }
  });
  return renderTemplate`${renderComponent($$result, "AdminLayout", $$AdminLayout, {}, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<h1>Brands</h1> ${renderComponent($$result2, "BrandListBoundary", BrandListBoundary, { "client:load": true, "initialData": brands, "client:component-hydration": "load", "client:component-path": "@/components/admin/brands/BrandListBoundary.vue", "client:component-export": "default" })} ` })}`;
}, "D:/Dev/parttec/cpanel/src/pages/admin/brands/index.astro", void 0);

const $$file = "D:/Dev/parttec/cpanel/src/pages/admin/brands/index.astro";
const $$url = "/admin/brands";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Index,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
