import { d as defineMiddleware, s as sequence } from './chunks/index_D2BW3Wex.mjs';
import { a as authClient } from './chunks/auth-client_Bjfuc3hF.mjs';
import 'es-module-lexer';
import './chunks/astro-designed-error-pages_V7kVn5wI.mjs';
import 'kleur/colors';
import './chunks/astro/server_DbndhTWv.mjs';
import 'clsx';
import 'cookie';

const PROTECTED_ROUTES = [
  "/admin",
  "/admin/"
];
const ADMIN_ONLY_ROUTES = [
  "/admin/users",
  "/admin/settings"
];
const SHOP_ROUTES = [
  "/admin/catalog",
  "/admin/parts"
];
const GUEST_ONLY_ROUTES = [
  "/admin/login",
  "/admin/register"
];
function isProtectedRoute(pathname) {
  return PROTECTED_ROUTES.some(
    (route) => pathname === route || pathname.startsWith(route + "/")
  );
}
function isAdminOnlyRoute(pathname) {
  return ADMIN_ONLY_ROUTES.some(
    (route) => pathname === route || pathname.startsWith(route + "/")
  );
}
function isShopRoute(pathname) {
  return SHOP_ROUTES.some(
    (route) => pathname === route || pathname.startsWith(route + "/")
  );
}
function isGuestOnlyRoute(pathname) {
  return GUEST_ONLY_ROUTES.some(
    (route) => pathname === route || pathname.startsWith(route + "/")
  );
}
function hasAccess(user, pathname) {
  if (!user) return false;
  if (isAdminOnlyRoute(pathname)) {
    return user.role === "ADMIN";
  }
  if (isShopRoute(pathname)) {
    return user.role === "SHOP" || user.role === "ADMIN";
  }
  return user.role === "ADMIN" || user.role === "SHOP";
}
const onRequest$1 = defineMiddleware(async (context, next) => {
  const { url, request, redirect } = context;
  const pathname = url.pathname;
  try {
    const sessionResult = await authClient.getSession({
      fetchOptions: {
        headers: Object.fromEntries(request.headers.entries())
      }
    });
    const user = sessionResult?.data?.user;
    const isAuthenticated = !!user;
    if (false) ;
    if (isGuestOnlyRoute(pathname)) {
      if (isAuthenticated && hasAccess(user, "/admin")) {
        console.log(`🔄 Redirecting authenticated user from ${pathname} to /admin`);
        return redirect("/admin");
      }
      return next();
    }
    if (isProtectedRoute(pathname)) {
      if (!isAuthenticated) {
        console.log(`🔒 Unauthorized access to ${pathname}, redirecting to login`);
        return redirect("/admin/login");
      }
      if (!hasAccess(user, pathname)) {
        console.log(`🚫 Access denied for user ${user.email} to ${pathname}`);
        if (user.role === "SHOP" || user.role === "ADMIN") {
          return redirect("/admin/forbidden");
        } else {
          return redirect("/admin/login");
        }
      }
      context.locals.user = user;
      context.locals.isAuthenticated = true;
    }
    return next();
  } catch (error) {
    console.error("❌ Auth middleware error:", error);
    if (isGuestOnlyRoute(pathname)) {
      return next();
    }
    if (isProtectedRoute(pathname)) {
      return redirect("/admin/login");
    }
    return next();
  }
});

const onRequest = sequence(
	
	onRequest$1
	
);

export { onRequest };
