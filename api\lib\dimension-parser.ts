/**
 * Утилиты для парсинга составных размеров сальников
 * Поддерживает форматы типа "14*30.5/38.4*5/11"
 */

export interface ParsedDimension {
  innerDiameter?: number;
  outerDiameter?: number;
  outerDiameter2?: number;
  height?: number;
  height2?: number;
  originalString: string;
  isValid: boolean;
  components: DimensionComponent[];
}

export interface DimensionComponent {
  name: string;
  value: number;
  order: number;
  tolerance?: number;
}

export interface DimensionSearchCriteria {
  innerDiameter?: {
    min: number;
    max: number;
  };
  outerDiameter?: {
    min: number;
    max: number;
  };
  height?: {
    min: number;
    max: number;
  };
  outerDiameter2?: {
    min: number;
    max: number;
  };
  height2?: {
    min: number;
    max: number;
  };
}

/**
 * Парсит составной размер сальника
 * Поддерживаемые форматы:
 * - "14*30.5*5" (внутренний*внешний*высота)
 * - "14*30.5/38.4*5/11" (внутренний*внешний1/внешний2*высота1/высота2)
 * - "14x30.5x5" (альтернативный разделитель)
 */
export function parseSealDimension(dimensionString: string): ParsedDimension {
  const result: ParsedDimension = {
    originalString: dimensionString,
    isValid: false,
    components: []
  };

  if (!dimensionString || typeof dimensionString !== 'string') {
    return result;
  }

  try {
    // Очистка строки от лишних символов
    const cleaned = dimensionString
      .trim()
      .replace(/\s+/g, '')
      .replace(/[xх]/gi, '*') // Заменяем x на *
      .replace(/[,]/g, '.'); // Заменяем запятые на точки

    // Регулярное выражение для парсинга размеров
    // Поддерживает форматы: 14*30.5*5, 14*30.5/38.4*5/11
    const dimensionRegex = /^(\d+(?:\.\d+)?)\*(\d+(?:\.\d+)?)(?:\/(\d+(?:\.\d+)?))?\*(\d+(?:\.\d+)?)(?:\/(\d+(?:\.\d+)?))?$/;
    
    const match = cleaned.match(dimensionRegex);
    
    if (!match) {
      return result;
    }

    const [, inner, outer1, outer2, height1, height2] = match;

    // Парсинг компонентов
    result.innerDiameter = parseFloat(inner);
    result.outerDiameter = parseFloat(outer1);
    result.height = parseFloat(height1);

    if (outer2) {
      result.outerDiameter2 = parseFloat(outer2);
    }
    if (height2) {
      result.height2 = parseFloat(height2);
    }

    // Создание компонентов
    result.components = [
      {
        name: 'inner_diameter',
        value: result.innerDiameter,
        order: 1,
        tolerance: 0.3
      },
      {
        name: 'outer_diameter',
        value: result.outerDiameter,
        order: 2,
        tolerance: 0.3
      }
    ];

    if (result.outerDiameter2) {
      result.components.push({
        name: 'outer_diameter_2',
        value: result.outerDiameter2,
        order: 3,
        tolerance: 0.3
      });
    }

    result.components.push({
      name: 'height',
      value: result.height,
      order: result.outerDiameter2 ? 4 : 3,
      tolerance: 3.0
    });

    if (result.height2) {
      result.components.push({
        name: 'height_2',
        value: result.height2,
        order: result.outerDiameter2 ? 5 : 4,
        tolerance: 3.0
      });
    }

    result.isValid = true;
    return result;

  } catch (error) {
    console.error('Ошибка парсинга размера:', error);
    return result;
  }
}

/**
 * Создает критерии поиска с учетом допусков
 */
export function createSearchCriteria(
  dimensions: Partial<ParsedDimension>,
  customTolerances?: Partial<Record<keyof ParsedDimension, number>>
): DimensionSearchCriteria {
  const criteria: DimensionSearchCriteria = {};

  const defaultTolerances = {
    innerDiameter: 0.3,
    outerDiameter: 0.3,
    outerDiameter2: 0.3,
    height: 3.0,
    height2: 3.0
  };

  const tolerances = { ...defaultTolerances, ...customTolerances };

  // Создание диапазонов для каждого размера
  if (dimensions.innerDiameter !== undefined) {
    const tolerance = tolerances.innerDiameter;
    criteria.innerDiameter = {
      min: dimensions.innerDiameter - tolerance,
      max: dimensions.innerDiameter + tolerance
    };
  }

  if (dimensions.outerDiameter !== undefined) {
    const tolerance = tolerances.outerDiameter;
    criteria.outerDiameter = {
      min: dimensions.outerDiameter - tolerance,
      max: dimensions.outerDiameter + tolerance
    };
  }

  if (dimensions.outerDiameter2 !== undefined) {
    const tolerance = tolerances.outerDiameter2;
    criteria.outerDiameter2 = {
      min: dimensions.outerDiameter2 - tolerance,
      max: dimensions.outerDiameter2 + tolerance
    };
  }

  if (dimensions.height !== undefined) {
    const tolerance = tolerances.height;
    criteria.height = {
      min: dimensions.height - tolerance,
      max: dimensions.height + tolerance
    };
  }

  if (dimensions.height2 !== undefined) {
    const tolerance = tolerances.height2;
    criteria.height2 = {
      min: dimensions.height2 - tolerance,
      max: dimensions.height2 + tolerance
    };
  }

  return criteria;
}

/**
 * Конвертирует единицы измерения
 */
export function convertUnit(value: number, fromUnit: string, toUnit: string): number {
  const conversions: Record<string, number> = {
    'мм': 1,
    'mm': 1,
    'см': 10,
    'cm': 10,
    'дм': 100,
    'dm': 100,
    'дюйм': 25.4,
    'inch': 25.4,
    'in': 25.4
  };

  const fromFactor = conversions[fromUnit] || 1;
  const toFactor = conversions[toUnit] || 1;

  return (value * fromFactor) / toFactor;
}

/**
 * Форматирует размер для отображения
 */
export function formatDimension(dimensions: ParsedDimension, unit: string = 'мм'): string {
  if (!dimensions.isValid) {
    return dimensions.originalString;
  }

  let formatted = `${dimensions.innerDiameter}*${dimensions.outerDiameter}`;
  
  if (dimensions.outerDiameter2) {
    formatted += `/${dimensions.outerDiameter2}`;
  }
  
  formatted += `*${dimensions.height}`;
  
  if (dimensions.height2) {
    formatted += `/${dimensions.height2}`;
  }

  return `${formatted} ${unit}`;
}

/**
 * Валидирует размеры сальника
 */
export function validateSealDimensions(dimensions: ParsedDimension): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (!dimensions.innerDiameter || dimensions.innerDiameter <= 0) {
    errors.push('Внутренний диаметр должен быть больше 0');
  }

  if (!dimensions.outerDiameter || dimensions.outerDiameter <= 0) {
    errors.push('Внешний диаметр должен быть больше 0');
  }

  if (!dimensions.height || dimensions.height <= 0) {
    errors.push('Высота должна быть больше 0');
  }

  if (dimensions.innerDiameter && dimensions.outerDiameter) {
    if (dimensions.innerDiameter >= dimensions.outerDiameter) {
      errors.push('Внутренний диаметр должен быть меньше внешнего');
    }
  }

  if (dimensions.outerDiameter2 && dimensions.outerDiameter) {
    if (dimensions.outerDiameter2 <= dimensions.outerDiameter) {
      errors.push('Второй внешний диаметр должен быть больше первого');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}
