import { e as createComponent, f as createAstro, k as renderComponent, r as renderTemplate, m as maybeRenderHead } from '../../chunks/astro/server_DbndhTWv.mjs';
import 'kleur/colors';
import { defineComponent, useSSRContext, mergeModels, ref, useModel, watch, onMounted, computed, mergeProps, withCtx, createTextVNode, createVNode, createBlock, openBlock, createCommentVNode, createSlots, toDisplayString, Fragment, renderList } from 'vue';
import { E as ErrorBoundary } from '../../chunks/ErrorBoundary_CGDiV8up.mjs';
import { t as trpc } from '../../chunks/trpc_B_6Rt2Mk.mjs';
import { V as VButton } from '../../chunks/Button_CuwpNmer.mjs';
import { D as DataTable, s as script } from '../../chunks/index_BK2zpZlm.mjs';
import { PlusIcon, TrashIcon, PencilIcon } from 'lucide-vue-next';
import { D as Dialog } from '../../chunks/Dialog_g9jHgXMZ.mjs';
import { I as InputText } from '../../chunks/InputText_CtReD0EA.mjs';
import { V as VTextarea } from '../../chunks/Textarea_DrL1j52W.mjs';
import { D as Dropdown } from '../../chunks/Dropdown_CKrwMlpa.mjs';
import { S as SecondaryButton, T as Toast, $ as $$AdminLayout } from '../../chunks/AdminLayout_b20tykPC.mjs';
import { s as slugify, r as resolveMediaUrl, f as fileToBase64 } from '../../chunks/utils_C3NgBSzL.mjs';
import { u as useTrpc } from '../../chunks/useTrpc_B-zNiBto.mjs';
import { ssrRenderComponent, ssrRenderAttr, ssrRenderAttrs, ssrRenderList, ssrRenderStyle, ssrInterpolate } from 'vue/server-renderer';
import { _ as _export_sfc, n as navigate } from '../../chunks/ClientRouter_B8Zzhk9G.mjs';
export { r as renderers } from '../../chunks/_@astro-renderers_CicWY1rm.mjs';

const _sfc_main$2 = /* @__PURE__ */ defineComponent({
  __name: "EditCategoryDialog",
  props: /* @__PURE__ */ mergeModels({
    category: {}
  }, {
    "isVisible": { type: Boolean, ...{ required: true } },
    "isVisibleModifiers": {}
  }),
  emits: /* @__PURE__ */ mergeModels(["save", "cancel"], ["update:isVisible"]),
  setup(__props, { expose: __expose, emit: __emit }) {
    __expose();
    const parentOptions = ref([]);
    const isVisible = useModel(__props, "isVisible");
    const props = __props;
    const emit = __emit;
    const localCategory = ref({});
    const userEditedSlug = ref(false);
    const dialogTitle = ref("\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u044E");
    watch(isVisible, (newValue) => {
      if (newValue) {
        localCategory.value = { ...props.category || {} };
        dialogTitle.value = props.category?.id ? `\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C: ${props.category.name}` : "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u044E";
        userEditedSlug.value = false;
      }
    });
    watch(
      () => localCategory.value.name,
      (name) => {
        const currentSlug = String(localCategory.value.slug ?? "");
        if (!userEditedSlug.value || currentSlug.length === 0) {
          localCategory.value.slug = slugify(String(name ?? ""));
        }
      }
    );
    function onSlugInput() {
      userEditedSlug.value = true;
      localCategory.value.slug = slugify(String(localCategory.value.slug ?? ""));
    }
    function handleCancel() {
      isVisible.value = false;
      emit("cancel");
    }
    function handleSave() {
      if ((!localCategory.value.slug || String(localCategory.value.slug).trim() === "") && localCategory.value.name) {
        localCategory.value.slug = slugify(String(localCategory.value.name));
      }
      emit("save", { ...localCategory.value });
      isVisible.value = false;
    }
    async function loadParentCategories() {
      const categories = await trpc.crud.partCategory.findMany.query({
        select: {
          id: true,
          name: true
        },
        orderBy: {
          name: "asc"
        }
      });
      parentOptions.value = categories;
    }
    onMounted(() => {
      loadParentCategories();
    });
    const { media } = useTrpc();
    const selectedImage = ref(null);
    const uploading = ref(false);
    const categoryImageUrl = computed(() => props.category?.image?.url || localCategory.value?.image?.url || null);
    function onSelectImage(e) {
      const input = e.target;
      if (input.files && input.files[0]) selectedImage.value = input.files[0];
    }
    async function uploadImage() {
      if (!localCategory.value?.id || !selectedImage.value) return;
      uploading.value = true;
      try {
        const dataUrl = await fileToBase64(selectedImage.value);
        await media.uploadPartCategoryImage({
          partCategoryId: localCategory.value.id,
          fileName: selectedImage.value.name,
          fileData: dataUrl,
          mimeType: selectedImage.value.type || "image/png"
        });
        const updated = await trpc.crud.partCategory.findUnique.query({ where: { id: localCategory.value.id }, include: { image: true } });
        if (updated) localCategory.value.image = updated.image;
        selectedImage.value = null;
      } finally {
        uploading.value = false;
      }
    }
    async function deleteImage() {
      if (!localCategory.value?.id) return;
      uploading.value = true;
      try {
        await media.deletePartCategoryImage({ partCategoryId: localCategory.value.id });
        const updated = await trpc.crud.partCategory.findUnique.query({ where: { id: localCategory.value.id }, include: { image: true } });
        if (updated) localCategory.value.image = updated.image;
      } finally {
        uploading.value = false;
      }
    }
    const __returned__ = { parentOptions, isVisible, props, emit, localCategory, userEditedSlug, dialogTitle, onSlugInput, handleCancel, handleSave, loadParentCategories, media, selectedImage, uploading, categoryImageUrl, onSelectImage, uploadImage, deleteImage, Button: VButton, Dialog, InputText, Textarea: VTextarea, Dropdown, SecondaryButton, get resolveMediaUrl() {
      return resolveMediaUrl;
    } };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$2(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(ssrRenderComponent($setup["Dialog"], mergeProps({
    visible: $setup.isVisible,
    "onUpdate:visible": ($event) => $setup.isVisible = $event,
    modal: "",
    header: $setup.dialogTitle,
    class: "sm:w-100 w-9/10"
  }, _attrs), {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="flex flex-col gap-4 py-4"${_scopeId}><div class="flex flex-col"${_scopeId}><label for="name"${_scopeId}>\u041D\u0430\u0438\u043C\u0435\u043D\u043E\u0432\u0430\u043D\u0438\u0435</label>`);
        _push2(ssrRenderComponent($setup["InputText"], {
          id: "name",
          modelValue: $setup.localCategory.name,
          "onUpdate:modelValue": ($event) => $setup.localCategory.name = $event
        }, null, _parent2, _scopeId));
        _push2(`</div><div class="flex flex-col"${_scopeId}><label for="slug"${_scopeId}>URL \u0441\u043B\u0430\u0433</label>`);
        _push2(ssrRenderComponent($setup["InputText"], {
          id: "slug",
          modelValue: $setup.localCategory.slug,
          "onUpdate:modelValue": ($event) => $setup.localCategory.slug = $event,
          onInput: $setup.onSlugInput
        }, null, _parent2, _scopeId));
        _push2(`<small class="text-surface-500"${_scopeId}>\u0410\u0432\u0442\u043E\u0433\u0435\u043D\u0435\u0440\u0430\u0446\u0438\u044F \u0438\u0437 \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u044F, \u043C\u043E\u0436\u043D\u043E \u043E\u0442\u0440\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0432\u0440\u0443\u0447\u043D\u0443\u044E</small></div><div class="flex flex-col"${_scopeId}><label for="description"${_scopeId}>\u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435</label>`);
        _push2(ssrRenderComponent($setup["Textarea"], {
          id: "description",
          modelValue: $setup.localCategory.description,
          "onUpdate:modelValue": ($event) => $setup.localCategory.description = $event,
          rows: "3"
        }, null, _parent2, _scopeId));
        _push2(`</div><div class="flex flex-col"${_scopeId}><label for="parent"${_scopeId}>\u0420\u043E\u0434\u0438\u0442\u0435\u043B\u044C\u0441\u043A\u0430\u044F \u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u044F</label>`);
        _push2(ssrRenderComponent($setup["Dropdown"], {
          id: "parent",
          modelValue: $setup.localCategory.parentId,
          "onUpdate:modelValue": ($event) => $setup.localCategory.parentId = $event,
          options: $setup.parentOptions,
          optionLabel: "name",
          optionValue: "id",
          placeholder: "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0440\u043E\u0434\u0438\u0442\u0435\u043B\u044C\u0441\u043A\u0443\u044E \u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u044E",
          showClear: ""
        }, null, _parent2, _scopeId));
        _push2(`</div><div class="flex flex-col"${_scopeId}><label for="icon"${_scopeId}>\u0418\u043A\u043E\u043D\u043A\u0430</label>`);
        _push2(ssrRenderComponent($setup["InputText"], {
          id: "icon",
          modelValue: $setup.localCategory.icon,
          "onUpdate:modelValue": ($event) => $setup.localCategory.icon = $event,
          placeholder: "\u041D\u0430\u043F\u0440\u0438\u043C\u0435\u0440: engine, filter, etc."
        }, null, _parent2, _scopeId));
        _push2(`</div><div class="flex flex-col"${_scopeId}><label${_scopeId}>\u0418\u0437\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u0435 \u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438</label><div class="flex items-center gap-3"${_scopeId}><div class="w-24 h-24 border rounded bg-surface-50 dark:bg-surface-900 flex items-center justify-center overflow-hidden"${_scopeId}>`);
        if ($setup.categoryImageUrl) {
          _push2(`<img${ssrRenderAttr("src", $setup.resolveMediaUrl($setup.categoryImageUrl))} class="object-cover w-full h-full"${_scopeId}>`);
        } else {
          _push2(`<span class="text-surface-500 text-xs"${_scopeId}>\u041D\u0435\u0442</span>`);
        }
        _push2(`</div><div class="flex flex-col gap-2"${_scopeId}><input type="file" accept="image/*"${_scopeId}><div class="flex gap-2"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["Button"], {
          size: "small",
          disabled: !$setup.selectedImage || $setup.uploading,
          onClick: $setup.uploadImage
        }, {
          default: withCtx((_2, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(`\u0417\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u044C`);
            } else {
              return [
                createTextVNode("\u0417\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u044C")
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["Button"], {
          size: "small",
          severity: "danger",
          outlined: "",
          disabled: !$setup.categoryImageUrl || $setup.uploading,
          onClick: $setup.deleteImage
        }, {
          default: withCtx((_2, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(`\u0423\u0434\u0430\u043B\u0438\u0442\u044C`);
            } else {
              return [
                createTextVNode("\u0423\u0434\u0430\u043B\u0438\u0442\u044C")
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(`</div>`);
        if ($setup.uploading) {
          _push2(`<small class="text-surface-500"${_scopeId}>\u0417\u0430\u0433\u0440\u0443\u0437\u043A\u0430...</small>`);
        } else {
          _push2(`<!---->`);
        }
        _push2(`</div></div></div></div><div class="flex justify-end gap-2"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["SecondaryButton"], {
          type: "button",
          label: "Cancel",
          onClick: $setup.handleCancel
        }, null, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["Button"], {
          type: "button",
          label: "Save",
          onClick: $setup.handleSave
        }, null, _parent2, _scopeId));
        _push2(`</div>`);
      } else {
        return [
          createVNode("div", { class: "flex flex-col gap-4 py-4" }, [
            createVNode("div", { class: "flex flex-col" }, [
              createVNode("label", { for: "name" }, "\u041D\u0430\u0438\u043C\u0435\u043D\u043E\u0432\u0430\u043D\u0438\u0435"),
              createVNode($setup["InputText"], {
                id: "name",
                modelValue: $setup.localCategory.name,
                "onUpdate:modelValue": ($event) => $setup.localCategory.name = $event
              }, null, 8, ["modelValue", "onUpdate:modelValue"])
            ]),
            createVNode("div", { class: "flex flex-col" }, [
              createVNode("label", { for: "slug" }, "URL \u0441\u043B\u0430\u0433"),
              createVNode($setup["InputText"], {
                id: "slug",
                modelValue: $setup.localCategory.slug,
                "onUpdate:modelValue": ($event) => $setup.localCategory.slug = $event,
                onInput: $setup.onSlugInput
              }, null, 8, ["modelValue", "onUpdate:modelValue"]),
              createVNode("small", { class: "text-surface-500" }, "\u0410\u0432\u0442\u043E\u0433\u0435\u043D\u0435\u0440\u0430\u0446\u0438\u044F \u0438\u0437 \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u044F, \u043C\u043E\u0436\u043D\u043E \u043E\u0442\u0440\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0432\u0440\u0443\u0447\u043D\u0443\u044E")
            ]),
            createVNode("div", { class: "flex flex-col" }, [
              createVNode("label", { for: "description" }, "\u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435"),
              createVNode($setup["Textarea"], {
                id: "description",
                modelValue: $setup.localCategory.description,
                "onUpdate:modelValue": ($event) => $setup.localCategory.description = $event,
                rows: "3"
              }, null, 8, ["modelValue", "onUpdate:modelValue"])
            ]),
            createVNode("div", { class: "flex flex-col" }, [
              createVNode("label", { for: "parent" }, "\u0420\u043E\u0434\u0438\u0442\u0435\u043B\u044C\u0441\u043A\u0430\u044F \u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u044F"),
              createVNode($setup["Dropdown"], {
                id: "parent",
                modelValue: $setup.localCategory.parentId,
                "onUpdate:modelValue": ($event) => $setup.localCategory.parentId = $event,
                options: $setup.parentOptions,
                optionLabel: "name",
                optionValue: "id",
                placeholder: "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0440\u043E\u0434\u0438\u0442\u0435\u043B\u044C\u0441\u043A\u0443\u044E \u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u044E",
                showClear: ""
              }, null, 8, ["modelValue", "onUpdate:modelValue", "options"])
            ]),
            createVNode("div", { class: "flex flex-col" }, [
              createVNode("label", { for: "icon" }, "\u0418\u043A\u043E\u043D\u043A\u0430"),
              createVNode($setup["InputText"], {
                id: "icon",
                modelValue: $setup.localCategory.icon,
                "onUpdate:modelValue": ($event) => $setup.localCategory.icon = $event,
                placeholder: "\u041D\u0430\u043F\u0440\u0438\u043C\u0435\u0440: engine, filter, etc."
              }, null, 8, ["modelValue", "onUpdate:modelValue"])
            ]),
            createVNode("div", { class: "flex flex-col" }, [
              createVNode("label", null, "\u0418\u0437\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u0435 \u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438"),
              createVNode("div", { class: "flex items-center gap-3" }, [
                createVNode("div", { class: "w-24 h-24 border rounded bg-surface-50 dark:bg-surface-900 flex items-center justify-center overflow-hidden" }, [
                  $setup.categoryImageUrl ? (openBlock(), createBlock("img", {
                    key: 0,
                    src: $setup.resolveMediaUrl($setup.categoryImageUrl),
                    class: "object-cover w-full h-full"
                  }, null, 8, ["src"])) : (openBlock(), createBlock("span", {
                    key: 1,
                    class: "text-surface-500 text-xs"
                  }, "\u041D\u0435\u0442"))
                ]),
                createVNode("div", { class: "flex flex-col gap-2" }, [
                  createVNode("input", {
                    type: "file",
                    accept: "image/*",
                    onChange: $setup.onSelectImage
                  }, null, 32),
                  createVNode("div", { class: "flex gap-2" }, [
                    createVNode($setup["Button"], {
                      size: "small",
                      disabled: !$setup.selectedImage || $setup.uploading,
                      onClick: $setup.uploadImage
                    }, {
                      default: withCtx(() => [
                        createTextVNode("\u0417\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u044C")
                      ]),
                      _: 1
                    }, 8, ["disabled"]),
                    createVNode($setup["Button"], {
                      size: "small",
                      severity: "danger",
                      outlined: "",
                      disabled: !$setup.categoryImageUrl || $setup.uploading,
                      onClick: $setup.deleteImage
                    }, {
                      default: withCtx(() => [
                        createTextVNode("\u0423\u0434\u0430\u043B\u0438\u0442\u044C")
                      ]),
                      _: 1
                    }, 8, ["disabled"])
                  ]),
                  $setup.uploading ? (openBlock(), createBlock("small", {
                    key: 0,
                    class: "text-surface-500"
                  }, "\u0417\u0430\u0433\u0440\u0443\u0437\u043A\u0430...")) : createCommentVNode("", true)
                ])
              ])
            ])
          ]),
          createVNode("div", { class: "flex justify-end gap-2" }, [
            createVNode($setup["SecondaryButton"], {
              type: "button",
              label: "Cancel",
              onClick: $setup.handleCancel
            }),
            createVNode($setup["Button"], {
              type: "button",
              label: "Save",
              onClick: $setup.handleSave
            })
          ])
        ];
      }
    }),
    _: 1
  }, _parent));
}
const _sfc_setup$2 = _sfc_main$2.setup;
_sfc_main$2.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/categories/EditCategoryDialog.vue");
  return _sfc_setup$2 ? _sfc_setup$2(props, ctx) : void 0;
};
const EditCategoryDialog = /* @__PURE__ */ _export_sfc(_sfc_main$2, [["ssrRender", _sfc_ssrRender$2]]);

const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "CategoryList",
  props: {
    initialData: {}
  },
  setup(__props, { expose: __expose }) {
    __expose();
    const searchValue = ref("");
    const dialogVisible = ref(false);
    const editingCategory = ref(null);
    const expandedRows = ref([]);
    const childrenCache = ref({});
    const props = __props;
    const items = ref(props.initialData);
    const keyMapping = {
      id: "ID",
      name: "\u041D\u0430\u0438\u043C\u0435\u043D\u043E\u0432\u0430\u043D\u0438\u0435",
      slug: "URL \u0441\u043B\u0430\u0433",
      description: "\u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435",
      parentId: "\u0420\u043E\u0434\u0438\u0442\u0435\u043B\u044C ID",
      level: "\u0423\u0440\u043E\u0432\u0435\u043D\u044C",
      path: "\u041F\u0443\u0442\u044C",
      icon: "\u0418\u043A\u043E\u043D\u043A\u0430"
    };
    const columnKeys = [
      "id",
      "name",
      "slug",
      "description",
      "level",
      "icon"
    ];
    function createCategory() {
      editingCategory.value = {};
      dialogVisible.value = true;
    }
    function editCategory(data) {
      editingCategory.value = { ...data };
      dialogVisible.value = true;
    }
    async function handleSave(categoryData) {
      if (!categoryData) return;
      try {
        if (categoryData.id) {
          const { id } = categoryData;
          const allowed = {
            name: categoryData.name,
            slug: categoryData.slug,
            description: categoryData.description,
            parentId: categoryData.parentId ?? null,
            icon: categoryData.icon
          };
          const sanitizedData = Object.fromEntries(
            Object.entries(allowed).filter(([_, v]) => v !== void 0)
          );
          await trpc.crud.partCategory.update.mutate({
            where: { id },
            data: sanitizedData
          });
        } else {
          if (categoryData.name && categoryData.slug) {
            let path = "01";
            let level = 0;
            if (categoryData.parentId) {
              const parentCategory = await trpc.crud.partCategory.findUnique.query({
                where: { id: categoryData.parentId }
              });
              if (parentCategory) {
                level = parentCategory.level + 1;
                const childrenCount = await trpc.crud.partCategory.count.query({
                  where: { parentId: categoryData.parentId }
                });
                const nextNumber = String(childrenCount + 1).padStart(2, "0");
                path = `${parentCategory.path}/${nextNumber}`;
              }
            }
            await trpc.crud.partCategory.create.mutate({
              data: {
                name: categoryData.name,
                slug: categoryData.slug,
                description: categoryData.description,
                parentId: categoryData.parentId,
                level,
                path,
                icon: categoryData.icon
              }
            });
          } else {
            console.error("Name and slug are required to create a category.");
            return;
          }
        }
        navigate(window.location.href);
      } catch (error) {
        console.error("Failed to save category:", error);
      } finally {
        dialogVisible.value = false;
      }
    }
    function handleCancel() {
      dialogVisible.value = false;
      editingCategory.value = null;
    }
    async function deleteCategory(data) {
      dialogVisible.value = false;
      await trpc.crud.partCategory.delete.mutate({
        where: {
          id: data.id
        }
      });
      navigate();
    }
    watch(searchValue, (newValue) => {
      debouncedSearch(newValue);
    });
    async function debouncedSearch(value = "") {
      console.log("value", value);
      const searchConditions = value ? {
        AND: [
          { level: 0 },
          // Только корневые категории
          {
            OR: [
              {
                name: {
                  contains: value
                }
              },
              {
                slug: {
                  contains: value
                }
              },
              {
                description: {
                  contains: value
                }
              }
            ]
          }
        ]
      } : { level: 0 };
      items.value = await trpc.crud.partCategory.findMany.query({
        where: searchConditions,
        include: {
          image: true,
          _count: {
            select: {
              parts: true,
              children: true
            }
          }
        },
        orderBy: {
          name: "asc"
        }
      });
    }
    async function loadChildren(parentId) {
      if (childrenCache.value[parentId]) {
        return childrenCache.value[parentId];
      }
      const children = await trpc.crud.partCategory.findMany.query({
        where: {
          parentId
        },
        include: {
          image: true,
          _count: {
            select: {
              parts: true,
              children: true
            }
          }
        },
        orderBy: {
          name: "asc"
        }
      });
      childrenCache.value[parentId] = children;
      return children;
    }
    function hasChildren(data) {
      return data._count.children > 0;
    }
    async function onRowExpand(event) {
      if (event.data._count.children > 0) {
        await loadChildren(event.data.id);
      }
    }
    const __returned__ = { searchValue, dialogVisible, editingCategory, expandedRows, childrenCache, props, items, keyMapping, columnKeys, createCategory, editCategory, handleSave, handleCancel, deleteCategory, debouncedSearch, loadChildren, hasChildren, onRowExpand, Button: VButton, DataTable, get PencilIcon() {
      return PencilIcon;
    }, get TrashIcon() {
      return TrashIcon;
    }, get PlusIcon() {
      return PlusIcon;
    }, get Column() {
      return script;
    }, EditCategoryDialog, InputText, Toast };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$1(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(_attrs)}><div class="flex justify-between items-center mb-4"><h1 class="text-2xl font-bold">\u041A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438 \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439</h1>`);
  _push(ssrRenderComponent($setup["Button"], { onClick: $setup.createCategory }, {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["PlusIcon"], { class: "w-5 h-5 mr-2" }, null, _parent2, _scopeId));
        _push2(` \u0421\u043E\u0437\u0434\u0430\u0442\u044C \u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u044E `);
      } else {
        return [
          createVNode($setup["PlusIcon"], { class: "w-5 h-5 mr-2" }),
          createTextVNode(" \u0421\u043E\u0437\u0434\u0430\u0442\u044C \u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u044E ")
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</div>`);
  _push(ssrRenderComponent($setup["DataTable"], {
    "show-headers": "",
    value: $setup.items,
    expandedRows: $setup.expandedRows,
    "onUpdate:expandedRows": ($event) => $setup.expandedRows = $event,
    onRowExpand: $setup.onRowExpand,
    rowHover: true
  }, {
    header: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="flex justify-end"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["InputText"], {
          modelValue: $setup.searchValue,
          "onUpdate:modelValue": ($event) => $setup.searchValue = $event,
          placeholder: "\u041F\u043E\u0438\u0441\u043A"
        }, null, _parent2, _scopeId));
        _push2(`</div>`);
      } else {
        return [
          createVNode("div", { class: "flex justify-end" }, [
            createVNode($setup["InputText"], {
              modelValue: $setup.searchValue,
              "onUpdate:modelValue": ($event) => $setup.searchValue = $event,
              placeholder: "\u041F\u043E\u0438\u0441\u043A"
            }, null, 8, ["modelValue", "onUpdate:modelValue"])
          ])
        ];
      }
    }),
    expansion: withCtx(({ data }, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="p-4 bg-surface-50 dark:bg-surface-800"${_scopeId}><h5 class="mb-3 font-semibold"${_scopeId}>\u041F\u043E\u0434\u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438: ${ssrInterpolate(data.name)}</h5>`);
        if ($setup.childrenCache[data.id] && $setup.childrenCache[data.id].length > 0) {
          _push2(`<div${_scopeId}>`);
          _push2(ssrRenderComponent($setup["DataTable"], {
            value: $setup.childrenCache[data.id],
            class: "mb-4"
          }, {
            default: withCtx((_, _push3, _parent3, _scopeId2) => {
              if (_push3) {
                _push3(ssrRenderComponent($setup["Column"], {
                  field: "name",
                  header: "\u041D\u0430\u0438\u043C\u0435\u043D\u043E\u0432\u0430\u043D\u0438\u0435"
                }, {
                  body: withCtx(({ data: child }, _push4, _parent4, _scopeId3) => {
                    if (_push4) {
                      _push4(`<div class="flex items-center"${_scopeId3}><div style="${ssrRenderStyle({ marginLeft: `${(child.level - data.level - 1) * 20}px` })}"${_scopeId3}>${ssrInterpolate(child.name)}</div></div>`);
                    } else {
                      return [
                        createVNode("div", { class: "flex items-center" }, [
                          createVNode("div", {
                            style: { marginLeft: `${(child.level - data.level - 1) * 20}px` }
                          }, toDisplayString(child.name), 5)
                        ])
                      ];
                    }
                  }),
                  _: 2
                }, _parent3, _scopeId2));
                _push3(ssrRenderComponent($setup["Column"], {
                  field: "slug",
                  header: "URL \u0441\u043B\u0430\u0433"
                }, null, _parent3, _scopeId2));
                _push3(ssrRenderComponent($setup["Column"], {
                  field: "description",
                  header: "\u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435"
                }, {
                  body: withCtx(({ data: child }, _push4, _parent4, _scopeId3) => {
                    if (_push4) {
                      _push4(`<div class="max-w-xs truncate"${ssrRenderAttr("title", child.description)}${_scopeId3}>${ssrInterpolate(child.description || "-")}</div>`);
                    } else {
                      return [
                        createVNode("div", {
                          class: "max-w-xs truncate",
                          title: child.description
                        }, toDisplayString(child.description || "-"), 9, ["title"])
                      ];
                    }
                  }),
                  _: 2
                }, _parent3, _scopeId2));
                _push3(ssrRenderComponent($setup["Column"], {
                  field: "_count.parts",
                  header: "\u041A\u043E\u043B-\u0432\u043E \u0434\u0435\u0442\u0430\u043B\u0435\u0439"
                }, null, _parent3, _scopeId2));
                _push3(ssrRenderComponent($setup["Column"], {
                  field: "_count.children",
                  header: "\u041F\u043E\u0434\u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438"
                }, null, _parent3, _scopeId2));
                _push3(ssrRenderComponent($setup["Column"], { header: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F" }, {
                  body: withCtx(({ data: child }, _push4, _parent4, _scopeId3) => {
                    if (_push4) {
                      _push4(`<div class="flex gap-2"${_scopeId3}>`);
                      _push4(ssrRenderComponent($setup["Button"], {
                        onClick: ($event) => $setup.editCategory(child),
                        outlined: "",
                        size: "small"
                      }, {
                        default: withCtx((_2, _push5, _parent5, _scopeId4) => {
                          if (_push5) {
                            _push5(ssrRenderComponent($setup["PencilIcon"], { class: "w-4 h-4" }, null, _parent5, _scopeId4));
                          } else {
                            return [
                              createVNode($setup["PencilIcon"], { class: "w-4 h-4" })
                            ];
                          }
                        }),
                        _: 2
                      }, _parent4, _scopeId3));
                      _push4(ssrRenderComponent($setup["Button"], {
                        onClick: ($event) => $setup.deleteCategory(child),
                        outlined: "",
                        severity: "danger",
                        size: "small"
                      }, {
                        default: withCtx((_2, _push5, _parent5, _scopeId4) => {
                          if (_push5) {
                            _push5(ssrRenderComponent($setup["TrashIcon"], { class: "w-4 h-4" }, null, _parent5, _scopeId4));
                          } else {
                            return [
                              createVNode($setup["TrashIcon"], { class: "w-4 h-4" })
                            ];
                          }
                        }),
                        _: 2
                      }, _parent4, _scopeId3));
                      _push4(`</div>`);
                    } else {
                      return [
                        createVNode("div", { class: "flex gap-2" }, [
                          createVNode($setup["Button"], {
                            onClick: ($event) => $setup.editCategory(child),
                            outlined: "",
                            size: "small"
                          }, {
                            default: withCtx(() => [
                              createVNode($setup["PencilIcon"], { class: "w-4 h-4" })
                            ]),
                            _: 2
                          }, 1032, ["onClick"]),
                          createVNode($setup["Button"], {
                            onClick: ($event) => $setup.deleteCategory(child),
                            outlined: "",
                            severity: "danger",
                            size: "small"
                          }, {
                            default: withCtx(() => [
                              createVNode($setup["TrashIcon"], { class: "w-4 h-4" })
                            ]),
                            _: 2
                          }, 1032, ["onClick"])
                        ])
                      ];
                    }
                  }),
                  _: 2
                }, _parent3, _scopeId2));
              } else {
                return [
                  createVNode($setup["Column"], {
                    field: "name",
                    header: "\u041D\u0430\u0438\u043C\u0435\u043D\u043E\u0432\u0430\u043D\u0438\u0435"
                  }, {
                    body: withCtx(({ data: child }) => [
                      createVNode("div", { class: "flex items-center" }, [
                        createVNode("div", {
                          style: { marginLeft: `${(child.level - data.level - 1) * 20}px` }
                        }, toDisplayString(child.name), 5)
                      ])
                    ]),
                    _: 2
                  }, 1024),
                  createVNode($setup["Column"], {
                    field: "slug",
                    header: "URL \u0441\u043B\u0430\u0433"
                  }),
                  createVNode($setup["Column"], {
                    field: "description",
                    header: "\u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435"
                  }, {
                    body: withCtx(({ data: child }) => [
                      createVNode("div", {
                        class: "max-w-xs truncate",
                        title: child.description
                      }, toDisplayString(child.description || "-"), 9, ["title"])
                    ]),
                    _: 1
                  }),
                  createVNode($setup["Column"], {
                    field: "_count.parts",
                    header: "\u041A\u043E\u043B-\u0432\u043E \u0434\u0435\u0442\u0430\u043B\u0435\u0439"
                  }),
                  createVNode($setup["Column"], {
                    field: "_count.children",
                    header: "\u041F\u043E\u0434\u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438"
                  }),
                  createVNode($setup["Column"], { header: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F" }, {
                    body: withCtx(({ data: child }) => [
                      createVNode("div", { class: "flex gap-2" }, [
                        createVNode($setup["Button"], {
                          onClick: ($event) => $setup.editCategory(child),
                          outlined: "",
                          size: "small"
                        }, {
                          default: withCtx(() => [
                            createVNode($setup["PencilIcon"], { class: "w-4 h-4" })
                          ]),
                          _: 2
                        }, 1032, ["onClick"]),
                        createVNode($setup["Button"], {
                          onClick: ($event) => $setup.deleteCategory(child),
                          outlined: "",
                          severity: "danger",
                          size: "small"
                        }, {
                          default: withCtx(() => [
                            createVNode($setup["TrashIcon"], { class: "w-4 h-4" })
                          ]),
                          _: 2
                        }, 1032, ["onClick"])
                      ])
                    ]),
                    _: 1
                  })
                ];
              }
            }),
            _: 2
          }, _parent2, _scopeId));
          _push2(`</div>`);
        } else {
          _push2(`<div class="text-surface-500"${_scopeId}> \u041F\u043E\u0434\u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438 \u043E\u0442\u0441\u0443\u0442\u0441\u0442\u0432\u0443\u044E\u0442 </div>`);
        }
        _push2(`</div>`);
      } else {
        return [
          createVNode("div", { class: "p-4 bg-surface-50 dark:bg-surface-800" }, [
            createVNode("h5", { class: "mb-3 font-semibold" }, "\u041F\u043E\u0434\u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438: " + toDisplayString(data.name), 1),
            $setup.childrenCache[data.id] && $setup.childrenCache[data.id].length > 0 ? (openBlock(), createBlock("div", { key: 0 }, [
              createVNode($setup["DataTable"], {
                value: $setup.childrenCache[data.id],
                class: "mb-4"
              }, {
                default: withCtx(() => [
                  createVNode($setup["Column"], {
                    field: "name",
                    header: "\u041D\u0430\u0438\u043C\u0435\u043D\u043E\u0432\u0430\u043D\u0438\u0435"
                  }, {
                    body: withCtx(({ data: child }) => [
                      createVNode("div", { class: "flex items-center" }, [
                        createVNode("div", {
                          style: { marginLeft: `${(child.level - data.level - 1) * 20}px` }
                        }, toDisplayString(child.name), 5)
                      ])
                    ]),
                    _: 2
                  }, 1024),
                  createVNode($setup["Column"], {
                    field: "slug",
                    header: "URL \u0441\u043B\u0430\u0433"
                  }),
                  createVNode($setup["Column"], {
                    field: "description",
                    header: "\u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435"
                  }, {
                    body: withCtx(({ data: child }) => [
                      createVNode("div", {
                        class: "max-w-xs truncate",
                        title: child.description
                      }, toDisplayString(child.description || "-"), 9, ["title"])
                    ]),
                    _: 1
                  }),
                  createVNode($setup["Column"], {
                    field: "_count.parts",
                    header: "\u041A\u043E\u043B-\u0432\u043E \u0434\u0435\u0442\u0430\u043B\u0435\u0439"
                  }),
                  createVNode($setup["Column"], {
                    field: "_count.children",
                    header: "\u041F\u043E\u0434\u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438"
                  }),
                  createVNode($setup["Column"], { header: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F" }, {
                    body: withCtx(({ data: child }) => [
                      createVNode("div", { class: "flex gap-2" }, [
                        createVNode($setup["Button"], {
                          onClick: ($event) => $setup.editCategory(child),
                          outlined: "",
                          size: "small"
                        }, {
                          default: withCtx(() => [
                            createVNode($setup["PencilIcon"], { class: "w-4 h-4" })
                          ]),
                          _: 2
                        }, 1032, ["onClick"]),
                        createVNode($setup["Button"], {
                          onClick: ($event) => $setup.deleteCategory(child),
                          outlined: "",
                          severity: "danger",
                          size: "small"
                        }, {
                          default: withCtx(() => [
                            createVNode($setup["TrashIcon"], { class: "w-4 h-4" })
                          ]),
                          _: 2
                        }, 1032, ["onClick"])
                      ])
                    ]),
                    _: 1
                  })
                ]),
                _: 2
              }, 1032, ["value"])
            ])) : (openBlock(), createBlock("div", {
              key: 1,
              class: "text-surface-500"
            }, " \u041F\u043E\u0434\u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438 \u043E\u0442\u0441\u0443\u0442\u0441\u0442\u0432\u0443\u044E\u0442 "))
          ])
        ];
      }
    }),
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["Column"], {
          expander: true,
          headerStyle: "width: 3rem"
        }, null, _parent2, _scopeId));
        _push2(`<!--[-->`);
        ssrRenderList($setup.columnKeys, (key) => {
          _push2(ssrRenderComponent($setup["Column"], {
            key,
            field: key,
            header: $setup.keyMapping[key] || key
          }, createSlots({ _: 2 }, [
            key === "name" ? {
              name: "body",
              fn: withCtx(({ data }, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(`<div class="flex items-center"${_scopeId2}><div style="${ssrRenderStyle({ marginLeft: `${data.level * 20}px` })}"${_scopeId2}>${ssrInterpolate(data[key])}</div></div>`);
                } else {
                  return [
                    createVNode("div", { class: "flex items-center" }, [
                      createVNode("div", {
                        style: { marginLeft: `${data.level * 20}px` }
                      }, toDisplayString(data[key]), 5)
                    ])
                  ];
                }
              }),
              key: "0"
            } : key === "description" ? {
              name: "body",
              fn: withCtx(({ data }, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(`<div class="max-w-xs truncate"${ssrRenderAttr("title", data[key])}${_scopeId2}>${ssrInterpolate(data[key] || "-")}</div>`);
                } else {
                  return [
                    createVNode("div", {
                      class: "max-w-xs truncate",
                      title: data[key]
                    }, toDisplayString(data[key] || "-"), 9, ["title"])
                  ];
                }
              }),
              key: "1"
            } : void 0
          ]), _parent2, _scopeId));
        });
        _push2(`<!--]-->`);
        _push2(ssrRenderComponent($setup["Column"], {
          field: "_count.parts",
          header: "\u041A\u043E\u043B-\u0432\u043E \u0434\u0435\u0442\u0430\u043B\u0435\u0439"
        }, null, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["Column"], {
          field: "_count.children",
          header: "\u041F\u043E\u0434\u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438"
        }, null, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["Column"], { header: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F" }, {
          body: withCtx(({ data }, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(`<div class="flex gap-2"${_scopeId2}>`);
              _push3(ssrRenderComponent($setup["Button"], {
                onClick: ($event) => $setup.editCategory(data),
                outlined: "",
                size: "small"
              }, {
                default: withCtx((_2, _push4, _parent4, _scopeId3) => {
                  if (_push4) {
                    _push4(ssrRenderComponent($setup["PencilIcon"], { class: "w-5 h-5" }, null, _parent4, _scopeId3));
                  } else {
                    return [
                      createVNode($setup["PencilIcon"], { class: "w-5 h-5" })
                    ];
                  }
                }),
                _: 2
              }, _parent3, _scopeId2));
              _push3(ssrRenderComponent($setup["Button"], {
                onClick: ($event) => $setup.deleteCategory(data),
                outlined: "",
                severity: "danger",
                size: "small"
              }, {
                default: withCtx((_2, _push4, _parent4, _scopeId3) => {
                  if (_push4) {
                    _push4(ssrRenderComponent($setup["TrashIcon"], { class: "w-5 h-5" }, null, _parent4, _scopeId3));
                  } else {
                    return [
                      createVNode($setup["TrashIcon"], { class: "w-5 h-5" })
                    ];
                  }
                }),
                _: 2
              }, _parent3, _scopeId2));
              _push3(`</div>`);
            } else {
              return [
                createVNode("div", { class: "flex gap-2" }, [
                  createVNode($setup["Button"], {
                    onClick: ($event) => $setup.editCategory(data),
                    outlined: "",
                    size: "small"
                  }, {
                    default: withCtx(() => [
                      createVNode($setup["PencilIcon"], { class: "w-5 h-5" })
                    ]),
                    _: 2
                  }, 1032, ["onClick"]),
                  createVNode($setup["Button"], {
                    onClick: ($event) => $setup.deleteCategory(data),
                    outlined: "",
                    severity: "danger",
                    size: "small"
                  }, {
                    default: withCtx(() => [
                      createVNode($setup["TrashIcon"], { class: "w-5 h-5" })
                    ]),
                    _: 2
                  }, 1032, ["onClick"])
                ])
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["Column"], {
            expander: true,
            headerStyle: "width: 3rem"
          }),
          (openBlock(), createBlock(Fragment, null, renderList($setup.columnKeys, (key) => {
            return createVNode($setup["Column"], {
              key,
              field: key,
              header: $setup.keyMapping[key] || key
            }, createSlots({ _: 2 }, [
              key === "name" ? {
                name: "body",
                fn: withCtx(({ data }) => [
                  createVNode("div", { class: "flex items-center" }, [
                    createVNode("div", {
                      style: { marginLeft: `${data.level * 20}px` }
                    }, toDisplayString(data[key]), 5)
                  ])
                ]),
                key: "0"
              } : key === "description" ? {
                name: "body",
                fn: withCtx(({ data }) => [
                  createVNode("div", {
                    class: "max-w-xs truncate",
                    title: data[key]
                  }, toDisplayString(data[key] || "-"), 9, ["title"])
                ]),
                key: "1"
              } : void 0
            ]), 1032, ["field", "header"]);
          }), 64)),
          createVNode($setup["Column"], {
            field: "_count.parts",
            header: "\u041A\u043E\u043B-\u0432\u043E \u0434\u0435\u0442\u0430\u043B\u0435\u0439"
          }),
          createVNode($setup["Column"], {
            field: "_count.children",
            header: "\u041F\u043E\u0434\u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438"
          }),
          createVNode($setup["Column"], { header: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F" }, {
            body: withCtx(({ data }) => [
              createVNode("div", { class: "flex gap-2" }, [
                createVNode($setup["Button"], {
                  onClick: ($event) => $setup.editCategory(data),
                  outlined: "",
                  size: "small"
                }, {
                  default: withCtx(() => [
                    createVNode($setup["PencilIcon"], { class: "w-5 h-5" })
                  ]),
                  _: 2
                }, 1032, ["onClick"]),
                createVNode($setup["Button"], {
                  onClick: ($event) => $setup.deleteCategory(data),
                  outlined: "",
                  severity: "danger",
                  size: "small"
                }, {
                  default: withCtx(() => [
                    createVNode($setup["TrashIcon"], { class: "w-5 h-5" })
                  ]),
                  _: 2
                }, 1032, ["onClick"])
              ])
            ]),
            _: 1
          })
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(ssrRenderComponent($setup["EditCategoryDialog"], {
    isVisible: $setup.dialogVisible,
    "onUpdate:isVisible": ($event) => $setup.dialogVisible = $event,
    category: $setup.editingCategory,
    onSave: $setup.handleSave,
    onCancel: $setup.handleCancel
  }, null, _parent));
  _push(ssrRenderComponent($setup["Toast"], null, null, _parent));
  _push(`</div>`);
}
const _sfc_setup$1 = _sfc_main$1.setup;
_sfc_main$1.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/categories/CategoryList.vue");
  return _sfc_setup$1 ? _sfc_setup$1(props, ctx) : void 0;
};
const CategoryList = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["ssrRender", _sfc_ssrRender$1]]);

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "CategoryListBoundary",
  props: {
    initialData: {}
  },
  setup(__props, { expose: __expose }) {
    __expose();
    const props = __props;
    const key = ref(0);
    const onRetry = () => {
      key.value++;
    };
    const __returned__ = { props, key, onRetry, ErrorBoundary, CategoryList };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(ssrRenderComponent($setup["ErrorBoundary"], mergeProps({
    variant: "minimal",
    title: "\u041E\u0448\u0438\u0431\u043A\u0430 \u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0439",
    message: "\u0421\u043F\u0438\u0441\u043E\u043A \u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0439 \u043D\u0435 \u043E\u0442\u0440\u0438\u0441\u043E\u0432\u0430\u043B\u0441\u044F. \u041F\u043E\u0432\u0442\u043E\u0440\u0438\u0442\u0435 \u043F\u043E\u043F\u044B\u0442\u043A\u0443.",
    onRetry: $setup.onRetry
  }, _attrs), {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["CategoryList"], {
          initialData: $props.initialData,
          key: $setup.key
        }, null, _parent2, _scopeId));
      } else {
        return [
          (openBlock(), createBlock($setup["CategoryList"], {
            initialData: $props.initialData,
            key: $setup.key
          }, null, 8, ["initialData"]))
        ];
      }
    }),
    _: 1
  }, _parent));
}
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/categories/CategoryListBoundary.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const CategoryListBoundary = /* @__PURE__ */ _export_sfc(_sfc_main, [["ssrRender", _sfc_ssrRender]]);

const $$Astro = createAstro();
const $$Index = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Index;
  const page = Astro2.url.searchParams.get("page") || "1";
  const pageSize = Astro2.url.searchParams.get("pageSize") || "100";
  const categories = await trpc.crud.partCategory.findMany.query({
    take: Number(pageSize),
    skip: (Number(page) - 1) * Number(pageSize),
    where: {
      level: 0
      // Загружаем только корневые категории
    },
    include: {
      image: true,
      _count: {
        select: {
          parts: true,
          children: true
        }
      }
    },
    orderBy: {
      name: "asc"
    }
  });
  return renderTemplate`${renderComponent($$result, "AdminLayout", $$AdminLayout, {}, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<h1>Categories</h1> ${renderComponent($$result2, "CategoryListBoundary", CategoryListBoundary, { "client:load": true, "initialData": categories, "client:component-hydration": "load", "client:component-path": "@/components/admin/categories/CategoryListBoundary.vue", "client:component-export": "default" })} ` })}`;
}, "D:/Dev/parttec/cpanel/src/pages/admin/categories/index.astro", void 0);

const $$file = "D:/Dev/parttec/cpanel/src/pages/admin/categories/index.astro";
const $$url = "/admin/categories";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Index,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
