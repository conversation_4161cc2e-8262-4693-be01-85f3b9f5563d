{"name": "site", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro"}, "dependencies": {"@astrojs/node": "^9.4.2", "@astrojs/react": "^4.3.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.13", "@tailwindcss/vite": "^4.1.12", "@tanstack/react-query": "^5.85.3", "@trpc/client": "^11.4.4", "@trpc/react-query": "^11.4.4", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "astro": "^5.13.2", "better-auth": "^1.3.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "lucide-react": "^0.540.0", "react": "^19.1.1", "react-dom": "^19.1.1", "superjson": "^2.2.2", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.12", "tw-animate-css": "^1.3.7"}}