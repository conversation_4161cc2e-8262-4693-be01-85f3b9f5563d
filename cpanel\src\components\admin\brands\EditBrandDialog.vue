<template>
  <Dialog v-model:visible="isVisible" modal :header="dialogTitle" class="sm:w-100 w-9/10">
    <!-- Форма для редактирования данных -->
    <div class="flex flex-col gap-4 py-4">
      <div class="flex flex-col">
        <label for="name">Наименование</label>
        <InputText id="name" v-model="localBrand.name" />
      </div>
      <div class="flex flex-col">
        <label for="slug">URL слаг</label>
        <InputText id="slug" v-model="localBrand.slug" />
      </div>
      <div class="flex flex-col">
        <label for="country">Страна</label>
        <!-- <InputText id="country" v-model="localBrand.country" /> -->
        <AutoComplete @complete="filterCountries" id="country" dropdownMode="current" v-model="localBrand.country"
          dropdown :suggestions="countryOptions" />
      </div>
      <div class="flex gap-3">
        <label for="isOem">OEM</label>
        <ToggleSwitch id="isOem" v-model="localBrand.isOem" />
      </div>
    </div>
    <div class="flex justify-end gap-2">
      <SecondaryButton type="button" label="Cancel" @click="handleCancel" />
      <Button type="button" label="Save" @click="handleSave" />
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import Button from '@/volt/Button.vue'
import Dialog from '@/volt/Dialog.vue'
import InputText from '@/volt/InputText.vue'
import SecondaryButton from '@/volt/SecondaryButton.vue'
import type { BrandFormData } from '@/types/brand'
import { trpc } from '@/lib/trpc'
import AutoComplete from '@/volt/AutoComplete.vue'
import Checkbox from '@/volt/Checkbox.vue'
import ToggleSwitch from '@/volt/ToggleSwitch.vue'

const countryOptions = ref<string[]>([])
const allCountries = ref<string[]>([])

// v-model для управления видимостью диалога
const isVisible = defineModel<boolean>('isVisible', { required: true })

// Входящие данные бренда для редактирования
const props = defineProps<{
  brand: BrandFormData | null
}>()

// Определяем события, которые компонент может отправлять родителю
const emit = defineEmits<{
  (e: 'save', brandData: BrandFormData): void
  (e: 'cancel'): void
}>()

// Локальное состояние для данных формы.
// Инициализируется пустым объектом или данными входящего бренда.
const localBrand = ref<BrandFormData>({})

// Заголовок диалогового окна
const dialogTitle = ref('Создать бренд')

const filterCountries = (event: any) => {
  const query = event.query.toLowerCase();
  if (!query) {
    countryOptions.value = [...allCountries.value]; // Показываем все страны
  } else {
    countryOptions.value = allCountries.value.filter(country =>
      country.toLowerCase().includes(query)
    );
  }
};


// Отслеживаем изменения isVisible.
// При открытии диалога (новое значение true), инициализируем локальные данные
watch(isVisible, (newValue) => {
  if (newValue) {
    // Копируем данные из props в локальное состояние, чтобы избежать прямого изменения props.
    // Если props.brand не предоставлен (создание нового), localBrand будет пустым объектом.
    localBrand.value = { ...(props.brand || {}) }
    dialogTitle.value = props.brand?.id ? `Редактировать: ${props.brand.name}` : 'Создать бренд'
  }
})


function handleCancel() {
  isVisible.value = false
  emit('cancel')
}

function handleSave() {
  // Отправляем копию локальных данных, чтобы избежать
  // случайных изменений после отправки события.
  emit('save', { ...localBrand.value })
  isVisible.value = false
}

async function loadCountries() {
  const countries = await trpc.crud.brand.findMany.query({
    where: {
      country: {
        not: null
      }
    },
    select: {
      country: true
    },
    distinct: ['country'],
    orderBy: {
      country: 'asc'
    }
  })

  allCountries.value = countries.map(item => item.country)
  countryOptions.value = [...allCountries.value]
}

onMounted(() => {
  loadCountries()
})
</script>
