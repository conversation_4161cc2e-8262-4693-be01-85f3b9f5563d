// Глобальная инициализация темы для предотвращения мигания
(function() {
  const STORAGE_KEY = 'parttec-theme';
  
  function initTheme() {
    const theme = localStorage.getItem(STORAGE_KEY) || 'system';
    const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    const shouldBeDark = theme === 'dark' || (theme === 'system' && systemPrefersDark);

    console.log('Theme init:', { theme, systemPrefersDark, shouldBeDark });

    if (shouldBeDark) {
      document.documentElement.setAttribute('data-theme', 'dark');
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.removeAttribute('data-theme');
      document.documentElement.classList.remove('dark');
    }
  }

  // Инициализируем тему сразу
  initTheme();

  // Слушаем изменения системной темы
  if (window.matchMedia) {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    mediaQuery.addEventListener('change', function() {
      const currentTheme = localStorage.getItem(STORAGE_KEY) || 'system';
      if (currentTheme === 'system') {
        initTheme();
      }
    });
  }

  // Слушаем изменения в localStorage (для синхронизации между вкладками)
  window.addEventListener('storage', function(e) {
    if (e.key === STORAGE_KEY) {
      initTheme();
    }
  });

  // Слушаем изменения data-theme атрибута для синхронизации
  const observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
      if (mutation.type === 'attributes' && mutation.attributeName === 'data-theme') {
        const isDark = document.documentElement.getAttribute('data-theme') === 'dark';
        if (isDark) {
          document.documentElement.classList.add('dark');
        } else {
          document.documentElement.classList.remove('dark');
        }
      }
    });
  });

  observer.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ['data-theme']
  });
})();