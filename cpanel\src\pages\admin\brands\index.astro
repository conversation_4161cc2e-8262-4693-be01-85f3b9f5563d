---
import BrandListBoundary from "@/components/admin/brands/BrandListBoundary.vue";
import AdminLayout from "../../../layouts/AdminLayout.astro";
import { trpc } from "@/lib/trpc";

const page = Astro.url.searchParams.get('page') || '1';
const pageSize = Astro.url.searchParams.get('pageSize') || '100';

const brands = await trpc.crud.brand.findMany.query({
    take: Number(pageSize),
    skip: (Number(page) - 1) * Number(pageSize),
    include: {
        _count: {
            select: {
                catalogItems: true,
            }
        }
    }
})
---

<AdminLayout>
  <h1>Brands</h1>
  <BrandListBoundary client:load initialData={brands} />
</AdminLayout>  