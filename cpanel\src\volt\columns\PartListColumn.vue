<template>
  <Column v-bind="$props">
    <template v-for="(_, slotName) in $slots" #[slotName]="slotProps">
      <slot :name="slotName" v-bind="slotProps ?? {}" />
    </template>
  </Column>
</template>

<script setup lang="ts">
import Column, { type ColumnProps } from 'primevue/column'
import type { PartListItem } from '@/types/catalog'

interface Props extends /* @vue-ignore */ ColumnProps {}
defineProps<Props>()

// Объявляем типы слотов, чтобы Volar подсвечивал типы slot props в потребителе
// Важно: это касается только слотов body/header/footer.
// data: PartListItem — строка таблицы каталога
// Остальные поля slotProps (column, field, index, frozen) — как у PrimeVue
// Используем any для свойств, которые PrimeVue не типизирует детально
// (они не критичны для нашего каталога)
// eslint-disable-next-line @typescript-eslint/consistent-type-definitions
defineSlots<{
  header?(props: any): any
  footer?(props: any): any
  body(props: { data: PartListItem; column?: any; field?: string; index?: number; frozen?: boolean }): any
}>()
</script>

