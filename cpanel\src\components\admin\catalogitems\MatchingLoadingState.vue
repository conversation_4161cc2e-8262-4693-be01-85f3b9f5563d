<template>
  <div :class="['text-center flex justify-center items-center gap-3', paddingClass]">
    <LoaderIcon class="animate-spin" />
    <div class="mt-2 text-surface-500">{{ message }}</div>
  </div>
  
</template>

<script setup lang="ts">
import { LoaderIcon } from 'lucide-vue-next'

withDefaults(defineProps<{ message?: string; paddingClass?: string }>(), {
  message: 'Выполняется подбор...',
  paddingClass: 'py-10',
})
</script>


