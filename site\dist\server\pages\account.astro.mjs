import { e as createComponent, f as createAstro, k as renderComponent, r as renderTemplate, m as maybeRenderHead } from '../chunks/astro/server_D7mwM5eH.mjs';
import 'kleur/colors';
import { B as Button, $ as $$MainLayout } from '../chunks/MainLayout_M77LEN4m.mjs';
import { jsx, jsxs } from 'react/jsx-runtime';
import 'react';
import { a as authClient } from '../chunks/auth-client_BsqqHDQx.mjs';
import { C as Card, a as CardContent } from '../chunks/card_ZM99lQrJ.mjs';
export { renderers } from '../renderers.mjs';

function AccountCard({ initialUser }) {
  const { data: session } = authClient.useSession();
  const user = session?.user ?? initialUser;
  return /* @__PURE__ */ jsx(Card, { children: /* @__PURE__ */ jsxs(CardContent, { className: "p-6 space-y-4", children: [
    /* @__PURE__ */ jsxs("div", { className: "space-y-1", children: [
      /* @__PURE__ */ jsx("div", { className: "text-sm text-muted-foreground", children: "Email" }),
      /* @__PURE__ */ jsx("div", { className: "font-medium", children: user?.email })
    ] }),
    user?.name && /* @__PURE__ */ jsxs("div", { className: "space-y-1", children: [
      /* @__PURE__ */ jsx("div", { className: "text-sm text-muted-foreground", children: "Имя" }),
      /* @__PURE__ */ jsx("div", { className: "font-medium", children: user?.name })
    ] }),
    user?.role && /* @__PURE__ */ jsxs("div", { className: "space-y-1", children: [
      /* @__PURE__ */ jsx("div", { className: "text-sm text-muted-foreground", children: "Роль" }),
      /* @__PURE__ */ jsx("div", { className: "font-medium", children: user?.role })
    ] }),
    /* @__PURE__ */ jsx("div", { children: /* @__PURE__ */ jsx(
      Button,
      {
        variant: "secondary",
        onClick: async () => {
          await authClient.signOut({
            fetchOptions: {
              onSuccess: () => window.location.href = "/"
            }
          });
        },
        children: "Выйти"
      }
    ) })
  ] }) });
}

const $$Astro = createAstro();
const $$Account = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Account;
  if (!Astro2.locals.user) {
    return Astro2.redirect("/login");
  }
  const user = Astro2.locals.user;
  return renderTemplate`${renderComponent($$result, "MainLayout", $$MainLayout, { "title": "\u041B\u0438\u0447\u043D\u044B\u0439 \u043A\u0430\u0431\u0438\u043D\u0435\u0442" }, { "default": ($$result2) => renderTemplate` ${maybeRenderHead()}<section class="container py-10"> <div class="mx-auto max-w-2xl"> <h1 class="mb-6 text-2xl font-bold">Личный кабинет</h1> ${renderComponent($$result2, "AccountCard", AccountCard, { "client:load": true, "initialUser": user, "client:component-hydration": "load", "client:component-path": "@/components/auth/AccountCard", "client:component-export": "default" })} </div> </section> ` })}`;
}, "D:/Dev/parttec/site/src/pages/account.astro", void 0);

const $$file = "D:/Dev/parttec/site/src/pages/account.astro";
const $$url = "/account";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Account,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
