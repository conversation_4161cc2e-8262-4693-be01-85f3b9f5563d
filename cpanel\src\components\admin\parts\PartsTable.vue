<template>
  <div class="space-y-6">
    <!-- Заголовок и действия -->
    <div class="flex items-center justify-between">
      <div>
        <h2 class="text-surface-900 dark:text-surface-0 text-xl font-semibold">Запчасти</h2>
        <p class="text-surface-600 dark:text-surface-400 mt-1 text-sm">Управление группами взаимозаменяемости</p>
      </div>

      <div class="flex gap-3">
        <SecondaryButton @click="refreshData" :disabled="loading" outlined>
          Обновить
          <RefreshCcwIcon class="h-4 w-4" />
        </SecondaryButton>

        <a href="/admin/parts/create">
          <VButton outlined>
            Создать
            <PlusIcon />
          </VButton>
        </a>
      </div>
    </div>

    <!-- Поиск и фильтры -->
    <VCard>
      <template #content>
        <div class="p-6">
          <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
            <!-- Поиск по названию -->
            <div>
              <label class="text-surface-700 dark:text-surface-300 mb-2 block text-sm font-medium"> Поиск по названию </label>
              <VInputText v-model="searchQuery" placeholder="Введите название запчасти..." class="w-full" @input="debouncedSearch" />
            </div>

            <!-- Фильтр по категории -->
            <div class="flex-1">
              <label class="text-surface-700 dark:text-surface-300 mb-2 block text-sm font-medium"> Фильтр по категории </label>
              <VAutoComplete
                v-model="selectedCategory"
                :suggestions="categorySuggestions"
                @complete="filterCategories"
                option-label="name"
                option-value="id"
                placeholder="Поиск категории..."
                class="w-full"
                dropdown
                show-clear
                @change="applyFilters"
              />
            </div>

            <!-- Статистика -->
            <div class="flex items-end">
              <div class="text-surface-600 dark:text-surface-400 text-sm">
                <div>
                  Всего запчастей: <span class="text-surface-900 dark:text-surface-100 font-medium">{{ totalCount }}</span>
                </div>
                <div>
                  Показано: <span class="text-surface-900 dark:text-surface-100 font-medium">{{ parts.length }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </VCard>

    <!-- Таблица запчастей -->
    <VCard>
      <template #content>
        <VDataTable
          :value="parts"
          :loading="loading"
          paginator
          :rows="pageSize"
          :total-records="totalCount"
          :rows-per-page-options="[10, 25, 50]"
          lazy
          @page="onPageChange"
          @sort="onSort"
          table-style="min-width: 50rem"
          class="p-datatable-sm"
          striped-rows
          v-model:expandedRows="expandedRows"
          @row-expand="onRowExpand"
        >
          <!-- Кнопка расширения -->
          <VColumn expander style="width: 50px" />

          <!-- ID -->
          <VColumn field="id" header="ID" sortable style="width: 80px">
            <template #body="{ data }">
              <span class="text-surface-700 dark:text-surface-300 font-mono text-sm">#{{ data.id }}</span>
            </template>
          </VColumn>

          <!-- Название -->
          <VColumn field="name" header="Название" sortable style="width: 30%">
            <template #body="{ data }">
              <div>
                <div class="text-surface-900 dark:text-surface-100 font-medium">
                  <a :href="`/admin/parts/${data.id}`" class="hover:underline text-primary">
                    {{ data.name || 'Без названия' }}
                  </a>
                </div>
                <div class="text-surface-600 dark:text-surface-400 mt-1 text-sm">Уровень: {{ data.level }} | Путь: {{ data.path }}</div>
              </div>
            </template>
          </VColumn>

          <!-- Категория -->
          <VColumn field="partCategory.name" header="Категория" style="width: 20%">
            <template #body="{ data }">
              <VTag v-if="data.partCategory" severity="info" class="text-sm">
                {{ data.partCategory.name }}
              </VTag>
              <span v-else class="text-surface-500 dark:text-surface-400 text-sm">Не указана</span>
            </template>
          </VColumn>

          <!-- Краткая информация -->
          <VColumn header="Детали" style="width: 20%">
            <template #body="{ data }">
              <div class="flex items-center gap-2">
                <VTag severity="secondary" class="text-sm"> {{ partAttributes[data.id]?.length || data.attributes?.length || 0 }} атр. </VTag>
                <VTag severity="success" class="text-sm"> {{ data.applicabilities?.length || 0 }} поз. </VTag>
              </div>
            </template>
          </VColumn>

          <!-- Дата создания -->
          <VColumn field="createdAt" header="Создано" sortable style="width: 120px">
            <template #body="{ data }">
              <span class="text-surface-600 dark:text-surface-400 text-sm">
                {{ formatDate(data.createdAt) }}
              </span>
            </template>
          </VColumn>

          <!-- Действия -->
          <VColumn header="Действия" style="width: 140px">
            <template #body="{ data }">
              <div class="flex gap-2">
                <a :href="`/admin/parts/${data.id}`">
                  <VButton size="small" outlined>
                   <EyeIcon class="h-4 w-4" />
                  </VButton>
                </a>
                <a :href="`/admin/parts/${data.id}/edit`">
                  <VButton outlined>
                    <PencilIcon class="h-4 w-4" />
                  </VButton>
                </a>
                <VButton size="small" severity="secondary" outlined @click="openMatching(data)">
                  Подобрать
                  <LinkIcon class="h-4 w-4" />
                </VButton>
                <DangerButton
                  size="small"
                  outlined
                  @click="deletePart(data)"
                >
                   <TrashIcon class="h-4 w-4" />
                </DangerButton>
              </div>
            </template>
          </VColumn>

          <!-- Расширенное содержимое -->
          <template #expansion="{ data }">
            <div class="bg-surface-50 dark:bg-surface-800 border-surface-200 dark:border-surface-700 border-t p-4">
              <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
                <!-- Атрибуты -->
                <div>
                  <h4 class="text-surface-900 dark:text-surface-100 mb-3 flex items-center gap-2 text-lg font-medium">
                    <Icon name="pi pi-list" class="h-4 w-4 text-blue-600" />
                    Атрибуты запчасти
                  </h4>

                  <div v-if="partAttributes[data.id]?.length > 0" class="space-y-2">
                    <div
                      v-for="attr in partAttributes[data.id]"
                      :key="attr.id"
                      class="bg-surface-0 dark:bg-surface-900 border-surface-200 dark:border-surface-700 flex items-start justify-between rounded border p-3"
                    >
                      <div class="flex-1">
                        <div class="text-surface-900 dark:text-surface-100 text-sm font-medium">
                          {{ attr.template?.title || 'Без названия' }}
                        </div>
                        <div v-if="attr.description" class="text-surface-500 dark:text-surface-400 mt-1 text-sm">
                          {{ attr.description }}
                        </div>
                      </div>
                      <div class="ml-3 text-right">
                        <div class="text-surface-700 dark:text-surface-300 font-medium">
                          {{ attr.value || 'Не указано' }}
                        </div>
                        <div v-if="attr.unit" class="text-surface-500 dark:text-surface-400 text-sm">
                          {{ getUnitLabel(attr.unit) }}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div v-else class="text-surface-500 dark:text-surface-400 py-6 text-center">
                    <Icon name="pi pi-info-circle" class="mb-2 inline-block text-2xl" />
                    Атрибуты не заданы
                  </div>
                </div>

                <!-- Каталожные позиции -->
                <div>
                  <h4 class="text-surface-900 dark:text-surface-100 mb-3 flex items-center gap-2 text-lg font-medium">
                    <Icon name="pi pi-box" class="h-4 w-4 text-green-600" />
                    Каталожные позиции
                  </h4>

                  <div v-if="data.applicabilities?.length > 0" class="space-y-2">
                    <div
                      v-for="applicability in data.applicabilities"
                      :key="applicability.id"
                      class="bg-surface-0 dark:bg-surface-900 border-surface-200 dark:border-surface-700 rounded border p-3"
                    >
                      <div class="flex items-start justify-between">
                        <div class="flex-1">
                          <div class="text-surface-900 dark:text-surface-100 font-medium">
                            {{ applicability.catalogItem?.sku || 'N/A' }}
                          </div>
                          <div class="text-surface-600 dark:text-surface-400 mt-1 text-sm">
                            {{ applicability.catalogItem?.brand?.name || 'Неизвестный бренд' }}
                          </div>
                          <div v-if="applicability.catalogItem?.description" class="text-surface-500 dark:text-surface-400 mt-1 text-sm">
                            {{ applicability.catalogItem.description }}
                          </div>
                        </div>
                        <div class="ml-3">
                          <VTag :severity="getAccuracySeverity(applicability.accuracy)" class="text-sm">
                            {{ getAccuracyLabel(applicability.accuracy) }}
                          </VTag>
                        </div>
                      </div>
                      <div
                        v-if="applicability.notes"
                        class="bg-surface-100 dark:bg-surface-800 text-surface-600 dark:text-surface-400 mt-2 rounded p-2 text-sm"
                      >
                        <Icon name="pi pi-info-circle" class="mr-1 inline-block h-4 w-4" />
                        {{ applicability.notes }}
                      </div>
                    </div>
                  </div>

                  <div v-else class="text-surface-500 dark:text-surface-400 py-6 text-center">
                    <Icon name="pi pi-info-circle" class="mb-2 inline-block text-2xl" />
                    Каталожные позиции не добавлены
                  </div>
                </div>

                <!-- Применимость к технике -->
                <div class="lg:col-span-2">
                  <h4 class="text-surface-900 dark:text-surface-100 mb-3 flex items-center gap-2 text-lg font-medium">Применимость к технике</h4>

                  <div v-if="data.equipmentApplicabilities?.length > 0" class="grid grid-cols-1 gap-3 md:grid-cols-2">
                    <div
                      v-for="equipmentApplicability in data.equipmentApplicabilities"
                      :key="equipmentApplicability.id"
                      class="bg-surface-0 dark:bg-surface-900 border-surface-200 dark:border-surface-700 rounded border p-3"
                    >
                      <div class="flex items-start justify-between">
                        <div class="flex-1">
                          <div class="text-surface-900 dark:text-surface-100 font-medium">
                            {{ equipmentApplicability.equipmentModel?.name || 'N/A' }}
                          </div>
                          <div v-if="equipmentApplicability.equipmentModel?.brand" class="text-surface-600 dark:text-surface-400 mt-1 text-sm">
                            {{ equipmentApplicability.equipmentModel.brand.name }}
                          </div>
                        </div>
                        <div class="ml-3">
                          <VTag severity="info" class="text-sm"> Техника </VTag>
                        </div>
                      </div>
                      <div
                        v-if="equipmentApplicability.notes"
                        class="bg-surface-100 dark:bg-surface-800 text-surface-600 dark:text-surface-400 mt-2 rounded p-2 text-sm"
                      >
                        <Icon name="pi pi-info-circle" class="mr-1 inline-block h-4 w-4" />
                        {{ equipmentApplicability.notes }}
                      </div>
                    </div>
                  </div>

                  <div v-else class="text-surface-500 dark:text-surface-400 py-6 text-center">
                    <Icon name="pi pi-info-circle" class="mb-2 inline-block text-2xl" />
                    Применимость к технике не указана
                  </div>
                </div>
              </div>
            </div>
          </template>
        </VDataTable>
      </template>
    </VCard>

    <!-- Сообщения об ошибках -->
    <VMessage v-if="error" severity="error" class="mt-4">
      {{ error }}
    </VMessage>

    <!-- Диалог редактирования запчасти -->
    <VDialog v-model:visible="editDialogVisible" modal header="Редактировать запчасть" class="">
      <PartWizard v-if="selectedPartForEdit" :part="selectedPartForEdit" mode="edit" @updated="onPartSaved" />
    </VDialog>

    <!-- Диалог подбора каталожных позиций для запчасти -->
    <VDialog v-model:visible="matchingDialogVisible" modal header="Подбор каталожных позиций" class="w-full max-w-4xl">
      <div class="p-4">
        <div class="mb-4 flex items-center justify-between">
          <div class="text-surface-500 text-sm">Группа</div>
          <div class="font-semibold">{{ selectedPartForMatching?.name || '#' + selectedPartForMatching?.id }}</div>
          <VButton severity="secondary" outlined size="small" :loading="matchingLoading" @click="runPartMatching">
            <RefreshCcwIcon />
          </VButton>
        </div>
        <MatchingLoadingState v-if="matchingLoading" paddingClass="py-8" />
        <MatchingEmptyState v-else-if="!matchingResults || matchingResults.length === 0" />
        <div v-else class="space-y-3">
          <VCard v-for="c in matchingResults" :key="c.catalogItem.id" class="border">
            <template #content>
              <div class="grid grid-cols-1 items-start gap-3 p-4 md:grid-cols-3">
                <div class="md:col-span-1">
                  <div class="font-mono font-semibold">{{ c.catalogItem.sku }}</div>
                  <div class="text-surface-500">{{ c.catalogItem.brand?.name }}</div>
                  <div class="mt-2">
                    <VTag :value="getAccuracyLabel(c.accuracySuggestion)" :severity="getAccuracySeverity(c.accuracySuggestion)" />
                  </div>
                  <div class="mt-3">
                    <VButton size="small" label="Привязать" @click="openLinkConfirmDialog(c)"
                      ><template #icon>
                        <Icon name="pi pi-link" class="h-5 w-5" /> </template
                    ></VButton>
                  </div>
                </div>
                <div class="md:col-span-2">
                  <div class="text-surface-500 mb-2 text-sm">Детали сопоставления</div>
                  <MatchingDetailsGrid :details="c.details" />
                </div>
              </div>
            </template>
          </VCard>
        </div>
      </div>
    </VDialog>

    <!-- Диалог подтверждения привязки -->
    <VDialog v-model:visible="showLinkConfirmDialog" modal header="Подтверждение привязки" class="w-full max-w-3xl">
      <div v-if="selectedLinkCandidate" class="space-y-4">
        <!-- Информация о связываемых элементах -->
        <div class="bg-surface-50 dark:bg-surface-900 grid grid-cols-1 gap-4 rounded p-4 md:grid-cols-2">
          <div>
            <div class="text-surface-500 text-sm">Группа взаимозаменяемости</div>
            <div class="font-semibold">{{ selectedPartForMatching?.name || `Группа #${selectedPartForMatching?.id}` }}</div>
          </div>
          <div>
            <div class="text-surface-500 text-sm">Каталожная позиция</div>
            <div class="font-semibold">{{ selectedLinkCandidate.catalogItem.sku }}</div>
            <div class="text-sm">{{ selectedLinkCandidate.catalogItem.brand?.name }}</div>
          </div>
        </div>

        <!-- Детали сопоставления -->
        <div>
          <h3 class="mb-3 text-lg font-semibold">Детали сопоставления</h3>
          <MatchingDetailsGrid :details="selectedLinkCandidate.details" />
        </div>

        <!-- Форма подтверждения -->
        <div class="space-y-3">
          <div>
            <label class="text-surface-700 dark:text-surface-300 mb-2 block text-sm font-medium"> Точность совпадения </label>
            <VSelect
              v-model="linkConfirmForm.accuracy"
              :options="accuracyOptions"
              option-label="label"
              option-value="value"
              placeholder="Выберите точность"
              class="w-full"
            />
          </div>

          <div>
            <label class="text-surface-700 dark:text-surface-300 mb-2 block text-sm font-medium"> Примечания </label>
            <VTextarea v-model="linkConfirmForm.notes" rows="3" placeholder="Дополнительная информация о совместимости..." class="w-full" />
            <small class="text-surface-500"> Укажите особенности применения, ограничения или условия замены </small>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-between">
          <VButton label="Отмена" severity="secondary" @click="closeLinkConfirmDialog" />
          <VButton label="Создать связь" severity="success" @click="confirmLinkItem" :loading="linking" />
        </div>
      </template>
    </VDialog>

    <!-- Toast контейнер для изолята таблицы -->
    <!-- Диалог подтверждения удаления -->
    <VConfirmDialog />
    <Toast />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useTrpc } from '@/composables/useTrpc'
import { useConfirm } from '@/composables/useConfirm'
import { useToast } from '@/composables/useToast'
import { useMatchingLabels } from '@/composables/useMatchingLabels'
import { trpc } from '@/lib/trpc'
import { resolveMediaUrl } from '@/lib/utils'

// Импорт компонентов
import VCard from '@/volt/Card.vue'
import VButton from '@/volt/Button.vue'
import VInputText from '@/volt/InputText.vue'
// VDropdown removed - replaced with VAutoComplete
import VDataTable from '@/volt/DataTable.vue'
import VColumn from 'primevue/column'
import VTag from '@/volt/Tag.vue'
import VMessage from '@/volt/Message.vue'
import VDialog from '@/volt/Dialog.vue'
import PartWizard from './PartWizard.vue'
import VConfirmDialog from '@/volt/ConfirmDialog.vue'
import VAutoComplete from '@/volt/AutoComplete.vue'
import VSelect from '@/volt/Select.vue'
import VTextarea from '@/volt/Textarea.vue'
import Icon from '@/components/ui/Icon.vue'
import MatchingDetailsGrid from '@/components/admin/catalogitems/MatchingDetailsGrid.vue'
import MatchingEmptyState from '@/components/admin/catalogitems/MatchingEmptyState.vue'
import MatchingLoadingState from '@/components/admin/catalogitems/MatchingLoadingState.vue'
import { RefreshCcwIcon } from 'lucide-vue-next'
import { PlusIcon } from 'lucide-vue-next'
import SecondaryButton from '@/volt/SecondaryButton.vue'
import { LinkIcon } from 'lucide-vue-next'
import { TrashIcon } from 'lucide-vue-next'
import DangerButton from '@/volt/DangerButton.vue'
import { PencilIcon } from 'lucide-vue-next'
import Toast from '@/volt/Toast.vue'
import { EyeIcon } from 'lucide-vue-next'

// Composables
const { loading, error, clearError, parts: partsApi, partCategories, matching, partApplicability } = useTrpc()
const confirm = useConfirm()
const toast = useToast()
const { getAccuracyLabel, getAccuracySeverity } = useMatchingLabels()

// Состояние компонента
const parts = ref<any[]>([] as any[])
const categories = ref<any[]>([] as any[])
const totalCount = ref(0)
const pageSize = ref(25)
const currentPage = ref(0)
const expandedRows = ref<any[]>([])
const partAttributes = ref<Record<number, any[]>>({})

// Фильтры и поиск
const searchQuery = ref('')
const selectedCategory = ref<number | null>(null)

// Данные для автокомплита категорий
const categorySuggestions = ref<any[]>([])

// Диалоги
const editDialogVisible = ref(false)
const selectedPartForEdit = ref<any>(null)
const matchingDialogVisible = ref(false)
const selectedPartForMatching = ref<any | null>(null)
const matchingLoading = ref(false)
const matchingResults = ref<any[] | null>(null)

// Диалог подтверждения привязки
const showLinkConfirmDialog = ref(false)
const selectedLinkCandidate = ref<any>(null)
const linking = ref(false)
const linkConfirmForm = ref({
  accuracy: 'EXACT_MATCH' as string,
  notes: ''
})

// Опции для выбора точности
const accuracyOptions = [
  { label: 'Точное совпадение', value: 'EXACT_MATCH' },
  { label: 'Совпадение с примечаниями', value: 'MATCH_WITH_NOTES' },
  { label: 'Требуется модификация', value: 'REQUIRES_MODIFICATION' },
  { label: 'Частичное совпадение', value: 'PARTIAL_MATCH' }
]

// Сортировка
const sortField = ref<string>('createdAt')
const sortOrder = ref<number>(-1) // -1 для DESC, 1 для ASC

// Загрузка данных
const loadParts = async () => {
  try {
    clearError()

    const filters: any = {
      skip: currentPage.value * pageSize.value,
      take: pageSize.value,
      orderBy: {
        [sortField.value]: sortOrder.value === 1 ? 'asc' : 'desc'
      },
      include: {
        image: true,
        partCategory: true,
        attributes: true,
        applicabilities: {
          include: {
            catalogItem: {
              include: {
                brand: true
              }
            }
          }
        },
        equipmentApplicabilities: {
          include: {
            equipmentModel: {
              include: {
                brand: true
              }
            }
          }
        }
      }
    }

    // Добавляем поиск
    if (searchQuery.value.trim()) {
      filters.where = {
        ...filters.where,
        name: {
          contains: searchQuery.value.trim(),
          mode: 'insensitive'
        }
      }
    }

    // Добавляем фильтр по категории
    if (selectedCategory.value) {
      // Извлекаем ID из выбранной категории
      const categoryId = typeof selectedCategory.value === 'object' ? (selectedCategory.value as any).id : selectedCategory.value

      filters.where = {
        ...filters.where,
        partCategoryId: categoryId
      }
    }

    const result = await partsApi.findMany(filters)
    if (result) {
      parts.value = result as any[]
    }

    // Получаем общее количество для пагинации
    const countResult = await partsApi.findMany({
      where: filters.where,
      select: { id: true }
    })
    if (countResult) {
      totalCount.value = (countResult as unknown as any[]).length
    }
  } catch (err) {
    console.error('Ошибка загрузки запчастей:', err)
  }
}

// Загрузка категорий
const loadCategories = async () => {
  try {
    const result = await partCategories.findMany({
      orderBy: { name: 'asc' }
    })
    if (result) {
      categories.value = result as any[]
    }
  } catch (err) {
    console.error('Ошибка загрузки категорий:', err)
  }
}

// Загрузка атрибутов для конкретной запчасти
const loadPartAttributes = async (partId: number) => {
  try {
    const result = await trpc.partAttributes.findByPartId.query({ partId })
    if (result) {
      partAttributes.value[partId] = result
    }
  } catch (err) {
    console.error('Ошибка загрузки атрибутов запчасти:', err)
  }
}

// Обработчики событий
const onPageChange = (event: any) => {
  currentPage.value = event.page
  pageSize.value = event.rows
  loadParts()
}

const onSort = (event: any) => {
  sortField.value = event.sortField
  sortOrder.value = event.sortOrder
  loadParts()
}

const onRowExpand = (event: any) => {
  const partId = event.data.id
  if (!partAttributes.value[partId]) {
    loadPartAttributes(partId)
  }
}

const refreshData = () => {
  loadParts()
}

const applyFilters = () => {
  currentPage.value = 0
  loadParts()
}

// Debounced поиск
let searchTimeout: NodeJS.Timeout
const debouncedSearch = () => {
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(() => {
    currentPage.value = 0
    loadParts()
  }, 500)
}

// Действия с запчастями
const editPart = (part: any) => {
  selectedPartForEdit.value = part
  editDialogVisible.value = true
}

const deletePart = (part: any) => {
  confirm.confirmDelete(
    'запчасть',
    async () => {
      try {
        await partsApi.delete({ where: { id: part.id } })
        // глобальный успех в useTrpc
        loadParts()
      } catch (err) {
        toast.error('Ошибка', 'Не удалось удалить запчасть', 5000)
      }
    }
  )
}

const onPartSaved = (part: any) => {
  // Обновляем список запчастей после сохранения
  loadParts()
  selectedPartForEdit.value = null
  editDialogVisible.value = false

  // глобальный успех в useTrpc
}

// Подбор для Part
const openMatching = (part: any) => {
  selectedPartForMatching.value = part
  matchingDialogVisible.value = true
  runPartMatching()
}

const runPartMatching = async () => {
  if (!selectedPartForMatching.value) return
  matchingLoading.value = true
  matchingResults.value = null
  try {
    const res = await matching.findMatchingCatalogItems({ partId: selectedPartForMatching.value.id })
    matchingResults.value = res ? (res as any).candidates || [] : []
  } catch (err) {
    console.error('Ошибка подбора для Part:', err)
    matchingResults.value = []
  } finally {
    matchingLoading.value = false
  }
}

// Открытие диалога подтверждения привязки
const openLinkConfirmDialog = (candidate: any) => {
  selectedLinkCandidate.value = candidate

  // Устанавливаем начальные значения формы
  linkConfirmForm.value.accuracy = candidate.accuracySuggestion
  linkConfirmForm.value.notes = ''

  // Если есть примечания из сопоставления - предзаполняем
  const withNotes = (candidate.details || []).find((d: any) => String(d.kind).includes('NEAR') || String(d.kind).includes('LEGACY'))
  if (withNotes?.notes) {
    linkConfirmForm.value.notes = withNotes.notes
  }

  // При совпадении по допуску тоже добавляем заметку
  const tol = (candidate.details || []).find((d: any) => d.kind === 'NUMBER_WITHIN_TOLERANCE')
  if (tol && !linkConfirmForm.value.notes) {
    linkConfirmForm.value.notes = 'Совпадение по допуску'
  }

  showLinkConfirmDialog.value = true
}

// Закрытие диалога подтверждения
const closeLinkConfirmDialog = () => {
  showLinkConfirmDialog.value = false
  selectedLinkCandidate.value = null
  linkConfirmForm.value.accuracy = 'EXACT_MATCH'
  linkConfirmForm.value.notes = ''
}

// Подтверждение привязки
const confirmLinkItem = async () => {
  if (!selectedPartForMatching.value || !selectedLinkCandidate.value) return

  linking.value = true

  try {
    await partApplicability.upsert({
      where: {
        partId_catalogItemId: {
          partId: selectedPartForMatching.value.id,
          catalogItemId: selectedLinkCandidate.value.catalogItem.id
        }
      },
      create: {
        partId: selectedPartForMatching.value.id,
        catalogItemId: selectedLinkCandidate.value.catalogItem.id,
        accuracy: linkConfirmForm.value.accuracy,
        notes: linkConfirmForm.value.notes || undefined
      },
      update: {
        accuracy: linkConfirmForm.value.accuracy,
        notes: linkConfirmForm.value.notes || undefined
      }
    })

    matchingDialogVisible.value = false
    showLinkConfirmDialog.value = false
    toast.success('Успешно', 'Позиция привязана к группе')
    loadParts()
    closeLinkConfirmDialog()
  } catch (err) {
    console.error('Ошибка привязки:', err)
    toast.error('Ошибка', 'Не удалось привязать позицию')
  } finally {
    linking.value = false
  }
}

// Утилиты
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('ru-RU', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  })
}

const getUnitLabel = (unit: string) => {
  const labels: Record<string, string> = {
    'MM': 'мм',
    'INCH': 'дюймы',
    'FT': 'футы',
    'G': 'г',
    'KG': 'кг',
    'T': 'т',
    'LB': 'фунты',
    'ML': 'мл',
    'L': 'л',
    'GAL': 'галлоны',
    'PCS': 'шт',
    'SET': 'комплект',
    'PAIR': 'пара',
    'BAR': 'бар',
    'PSI': 'PSI',
    'KW': 'кВт',
    'HP': 'л.с.',
    'NM': 'Н⋅м',
    'RPM': 'об/мин',
    'C': '°C',
    'F': '°F',
    'PERCENT': '%'
  }
  return labels[unit] || unit
}

// Данные для автокомплита категорий
const filterCategories = (event: any) => {
  const query = event.query.toLowerCase()
  if (!query) {
    categorySuggestions.value = categories.value
  } else {
    categorySuggestions.value = categories.value.filter((category) => category.name.toLowerCase().includes(query))
  }
}

// Инициализация автокомплита категорий
const initializeCategorySuggestions = () => {
  categorySuggestions.value = categories.value
}

// Инициализация
onMounted(() => {
  loadCategories()
  loadParts()
  initializeCategorySuggestions()
})
</script>
