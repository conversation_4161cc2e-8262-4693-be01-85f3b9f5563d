import{c as Qe}from"./createLucideIcon.DdiNmGRb.js";import{j as l}from"./jsx-runtime.D_zvdyIk.js";import{r as i}from"./index.0yr9KlQE.js";import{P as N,a as A,h as Je,d as et,R as tt,u as nt,c as rt,b as se,i as ot}from"./index.Di12BYGB.js";import{u as _,c as at}from"./index.BWNh2K4G.js";import{u as q,X as it}from"./index.NogD4Y5M.js";import{c as V}from"./utils.CBfrqCZ4.js";/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const st=[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]],jn=Qe("chevron-right",st);function W(e){const t=i.useRef(e);return i.useEffect(()=>{t.current=e}),i.useMemo(()=>(...n)=>t.current?.(...n),[])}function ct(e,t=globalThis?.document){const n=W(e);i.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var ut="DismissableLayer",ae="dismissableLayer.update",lt="dismissableLayer.pointerDownOutside",dt="dismissableLayer.focusOutside",de,Se=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),De=i.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:a,onInteractOutside:u,onDismiss:s,...y}=e,d=i.useContext(Se),[f,h]=i.useState(null),g=f?.ownerDocument??globalThis?.document,[,E]=i.useState({}),D=_(t,p=>h(p)),c=Array.from(d.layers),[v]=[...d.layersWithOutsidePointerEventsDisabled].slice(-1),m=c.indexOf(v),b=f?c.indexOf(f):-1,C=d.layersWithOutsidePointerEventsDisabled.size>0,w=b>=m,S=ht(p=>{const O=p.target,j=[...d.branches].some(Z=>Z.contains(O));!w||j||(o?.(p),u?.(p),p.defaultPrevented||s?.())},g),P=gt(p=>{const O=p.target;[...d.branches].some(Z=>Z.contains(O))||(a?.(p),u?.(p),p.defaultPrevented||s?.())},g);return ct(p=>{b===d.layers.size-1&&(r?.(p),!p.defaultPrevented&&s&&(p.preventDefault(),s()))},g),i.useEffect(()=>{if(f)return n&&(d.layersWithOutsidePointerEventsDisabled.size===0&&(de=g.body.style.pointerEvents,g.body.style.pointerEvents="none"),d.layersWithOutsidePointerEventsDisabled.add(f)),d.layers.add(f),fe(),()=>{n&&d.layersWithOutsidePointerEventsDisabled.size===1&&(g.body.style.pointerEvents=de)}},[f,g,n,d]),i.useEffect(()=>()=>{f&&(d.layers.delete(f),d.layersWithOutsidePointerEventsDisabled.delete(f),fe())},[f,d]),i.useEffect(()=>{const p=()=>E({});return document.addEventListener(ae,p),()=>document.removeEventListener(ae,p)},[]),l.jsx(N.div,{...y,ref:D,style:{pointerEvents:C?w?"auto":"none":void 0,...e.style},onFocusCapture:A(e.onFocusCapture,P.onFocusCapture),onBlurCapture:A(e.onBlurCapture,P.onBlurCapture),onPointerDownCapture:A(e.onPointerDownCapture,S.onPointerDownCapture)})});De.displayName=ut;var ft="DismissableLayerBranch",vt=i.forwardRef((e,t)=>{const n=i.useContext(Se),r=i.useRef(null),o=_(t,r);return i.useEffect(()=>{const a=r.current;if(a)return n.branches.add(a),()=>{n.branches.delete(a)}},[n.branches]),l.jsx(N.div,{...e,ref:o})});vt.displayName=ft;function ht(e,t=globalThis?.document){const n=W(e),r=i.useRef(!1),o=i.useRef(()=>{});return i.useEffect(()=>{const a=s=>{if(s.target&&!r.current){let y=function(){xe(lt,n,d,{discrete:!0})};const d={originalEvent:s};s.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=y,t.addEventListener("click",o.current,{once:!0})):y()}else t.removeEventListener("click",o.current);r.current=!1},u=window.setTimeout(()=>{t.addEventListener("pointerdown",a)},0);return()=>{window.clearTimeout(u),t.removeEventListener("pointerdown",a),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function gt(e,t=globalThis?.document){const n=W(e),r=i.useRef(!1);return i.useEffect(()=>{const o=a=>{a.target&&!r.current&&xe(dt,n,{originalEvent:a},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function fe(){const e=new CustomEvent(ae);document.dispatchEvent(e)}function xe(e,t,n,{discrete:r}){const o=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?Je(o,a):o.dispatchEvent(a)}var Q="focusScope.autoFocusOnMount",J="focusScope.autoFocusOnUnmount",ve={bubbles:!1,cancelable:!0},mt="FocusScope",Re=i.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:a,...u}=e,[s,y]=i.useState(null),d=W(o),f=W(a),h=i.useRef(null),g=_(t,c=>y(c)),E=i.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;i.useEffect(()=>{if(r){let c=function(C){if(E.paused||!s)return;const w=C.target;s.contains(w)?h.current=w:T(h.current,{select:!0})},v=function(C){if(E.paused||!s)return;const w=C.relatedTarget;w!==null&&(s.contains(w)||T(h.current,{select:!0}))},m=function(C){if(document.activeElement===document.body)for(const S of C)S.removedNodes.length>0&&T(s)};document.addEventListener("focusin",c),document.addEventListener("focusout",v);const b=new MutationObserver(m);return s&&b.observe(s,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",c),document.removeEventListener("focusout",v),b.disconnect()}}},[r,s,E.paused]),i.useEffect(()=>{if(s){ge.add(E);const c=document.activeElement;if(!s.contains(c)){const m=new CustomEvent(Q,ve);s.addEventListener(Q,d),s.dispatchEvent(m),m.defaultPrevented||(pt(wt(Pe(s)),{select:!0}),document.activeElement===c&&T(s))}return()=>{s.removeEventListener(Q,d),setTimeout(()=>{const m=new CustomEvent(J,ve);s.addEventListener(J,f),s.dispatchEvent(m),m.defaultPrevented||T(c??document.body,{select:!0}),s.removeEventListener(J,f),ge.remove(E)},0)}}},[s,d,f,E]);const D=i.useCallback(c=>{if(!n&&!r||E.paused)return;const v=c.key==="Tab"&&!c.altKey&&!c.ctrlKey&&!c.metaKey,m=document.activeElement;if(v&&m){const b=c.currentTarget,[C,w]=yt(b);C&&w?!c.shiftKey&&m===w?(c.preventDefault(),n&&T(C,{select:!0})):c.shiftKey&&m===C&&(c.preventDefault(),n&&T(w,{select:!0})):m===b&&c.preventDefault()}},[n,r,E.paused]);return l.jsx(N.div,{tabIndex:-1,...u,ref:g,onKeyDown:D})});Re.displayName=mt;function pt(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(T(r,{select:t}),document.activeElement!==n)return}function yt(e){const t=Pe(e),n=he(t,e),r=he(t.reverse(),e);return[n,r]}function Pe(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function he(e,t){for(const n of e)if(!Et(n,{upTo:t}))return n}function Et(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function bt(e){return e instanceof HTMLInputElement&&"select"in e}function T(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&bt(e)&&t&&e.select()}}var ge=Ct();function Ct(){let e=[];return{add(t){const n=e[0];t!==n&&n?.pause(),e=me(e,t),e.unshift(t)},remove(t){e=me(e,t),e[0]?.resume()}}}function me(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function wt(e){return e.filter(t=>t.tagName!=="A")}var St="Portal",Oe=i.forwardRef((e,t)=>{const{container:n,...r}=e,[o,a]=i.useState(!1);et(()=>a(!0),[]);const u=n||o&&globalThis?.document?.body;return u?tt.createPortal(l.jsx(N.div,{...r,ref:t}),u):null});Oe.displayName=St;var ee=0;function Dt(){i.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??pe()),document.body.insertAdjacentElement("beforeend",e[1]??pe()),ee++,()=>{ee===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),ee--}},[])}function pe(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var R=function(){return R=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(t[a]=n[a])}return t},R.apply(this,arguments)};function Ne(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function xt(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,a;r<o;r++)(a||!(r in t))&&(a||(a=Array.prototype.slice.call(t,0,r)),a[r]=t[r]);return e.concat(a||Array.prototype.slice.call(t))}var z="right-scroll-bar-position",$="width-before-scroll-bar",Rt="with-scroll-bars-hidden",Pt="--removed-body-scroll-bar-size";function te(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function Ot(e,t){var n=i.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var o=n.value;o!==r&&(n.value=r,n.callback(r,o))}}}})[0];return n.callback=t,n.facade}var Nt=typeof window<"u"?i.useLayoutEffect:i.useEffect,ye=new WeakMap;function Tt(e,t){var n=Ot(null,function(r){return e.forEach(function(o){return te(o,r)})});return Nt(function(){var r=ye.get(n);if(r){var o=new Set(r),a=new Set(e),u=n.current;o.forEach(function(s){a.has(s)||te(s,null)}),a.forEach(function(s){o.has(s)||te(s,u)})}ye.set(n,e)},[e]),n}function At(e){return e}function It(e,t){t===void 0&&(t=At);var n=[],r=!1,o={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(a){var u=t(a,r);return n.push(u),function(){n=n.filter(function(s){return s!==u})}},assignSyncMedium:function(a){for(r=!0;n.length;){var u=n;n=[],u.forEach(a)}n={push:function(s){return a(s)},filter:function(){return n}}},assignMedium:function(a){r=!0;var u=[];if(n.length){var s=n;n=[],s.forEach(a),u=n}var y=function(){var f=u;u=[],f.forEach(a)},d=function(){return Promise.resolve().then(y)};d(),n={push:function(f){u.push(f),d()},filter:function(f){return u=u.filter(f),n}}}};return o}function Lt(e){e===void 0&&(e={});var t=It(null);return t.options=R({async:!0,ssr:!1},e),t}var Te=function(e){var t=e.sideCar,n=Ne(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return i.createElement(r,R({},n))};Te.isSideCarExport=!0;function Mt(e,t){return e.useMedium(t),Te}var Ae=Lt(),ne=function(){},X=i.forwardRef(function(e,t){var n=i.useRef(null),r=i.useState({onScrollCapture:ne,onWheelCapture:ne,onTouchMoveCapture:ne}),o=r[0],a=r[1],u=e.forwardProps,s=e.children,y=e.className,d=e.removeScrollBar,f=e.enabled,h=e.shards,g=e.sideCar,E=e.noRelative,D=e.noIsolation,c=e.inert,v=e.allowPinchZoom,m=e.as,b=m===void 0?"div":m,C=e.gapMode,w=Ne(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),S=g,P=Tt([n,t]),p=R(R({},w),o);return i.createElement(i.Fragment,null,f&&i.createElement(S,{sideCar:Ae,removeScrollBar:d,shards:h,noRelative:E,noIsolation:D,inert:c,setCallbacks:a,allowPinchZoom:!!v,lockRef:n,gapMode:C}),u?i.cloneElement(i.Children.only(s),R(R({},p),{ref:P})):i.createElement(b,R({},p,{className:y,ref:P}),s))});X.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};X.classNames={fullWidth:$,zeroRight:z};var kt=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function Ft(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=kt();return t&&e.setAttribute("nonce",t),e}function _t(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function jt(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var Wt=function(){var e=0,t=null;return{add:function(n){e==0&&(t=Ft())&&(_t(t,n),jt(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},Bt=function(){var e=Wt();return function(t,n){i.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},Ie=function(){var e=Bt(),t=function(n){var r=n.styles,o=n.dynamic;return e(r,o),null};return t},Ut={left:0,top:0,right:0,gap:0},re=function(e){return parseInt(e||"",10)||0},Ht=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],o=t[e==="padding"?"paddingRight":"marginRight"];return[re(n),re(r),re(o)]},Kt=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return Ut;var t=Ht(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},zt=Ie(),F="data-scroll-locked",$t=function(e,t,n,r){var o=e.left,a=e.top,u=e.right,s=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(Rt,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(s,"px ").concat(r,`;
  }
  body[`).concat(F,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(a,`px;
    padding-right: `).concat(u,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(s,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(s,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(z,` {
    right: `).concat(s,"px ").concat(r,`;
  }
  
  .`).concat($,` {
    margin-right: `).concat(s,"px ").concat(r,`;
  }
  
  .`).concat(z," .").concat(z,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat($," .").concat($,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(F,`] {
    `).concat(Pt,": ").concat(s,`px;
  }
`)},Ee=function(){var e=parseInt(document.body.getAttribute(F)||"0",10);return isFinite(e)?e:0},Gt=function(){i.useEffect(function(){return document.body.setAttribute(F,(Ee()+1).toString()),function(){var e=Ee()-1;e<=0?document.body.removeAttribute(F):document.body.setAttribute(F,e.toString())}},[])},Vt=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=r===void 0?"margin":r;Gt();var a=i.useMemo(function(){return Kt(o)},[o]);return i.createElement(zt,{styles:$t(a,!t,o,n?"":"!important")})},ie=!1;if(typeof window<"u")try{var B=Object.defineProperty({},"passive",{get:function(){return ie=!0,!0}});window.addEventListener("test",B,B),window.removeEventListener("test",B,B)}catch{ie=!1}var L=ie?{passive:!1}:!1,Xt=function(e){return e.tagName==="TEXTAREA"},Le=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!Xt(e)&&n[t]==="visible")},Yt=function(e){return Le(e,"overflowY")},Zt=function(e){return Le(e,"overflowX")},be=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var o=Me(e,r);if(o){var a=ke(e,r),u=a[1],s=a[2];if(u>s)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},qt=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},Qt=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},Me=function(e,t){return e==="v"?Yt(t):Zt(t)},ke=function(e,t){return e==="v"?qt(t):Qt(t)},Jt=function(e,t){return e==="h"&&t==="rtl"?-1:1},en=function(e,t,n,r,o){var a=Jt(e,window.getComputedStyle(t).direction),u=a*r,s=n.target,y=t.contains(s),d=!1,f=u>0,h=0,g=0;do{if(!s)break;var E=ke(e,s),D=E[0],c=E[1],v=E[2],m=c-v-a*D;(D||m)&&Me(e,s)&&(h+=m,g+=D);var b=s.parentNode;s=b&&b.nodeType===Node.DOCUMENT_FRAGMENT_NODE?b.host:b}while(!y&&s!==document.body||y&&(t.contains(s)||t===s));return(f&&Math.abs(h)<1||!f&&Math.abs(g)<1)&&(d=!0),d},U=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Ce=function(e){return[e.deltaX,e.deltaY]},we=function(e){return e&&"current"in e?e.current:e},tn=function(e,t){return e[0]===t[0]&&e[1]===t[1]},nn=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},rn=0,M=[];function on(e){var t=i.useRef([]),n=i.useRef([0,0]),r=i.useRef(),o=i.useState(rn++)[0],a=i.useState(Ie)[0],u=i.useRef(e);i.useEffect(function(){u.current=e},[e]),i.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var c=xt([e.lockRef.current],(e.shards||[]).map(we),!0).filter(Boolean);return c.forEach(function(v){return v.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),c.forEach(function(v){return v.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var s=i.useCallback(function(c,v){if("touches"in c&&c.touches.length===2||c.type==="wheel"&&c.ctrlKey)return!u.current.allowPinchZoom;var m=U(c),b=n.current,C="deltaX"in c?c.deltaX:b[0]-m[0],w="deltaY"in c?c.deltaY:b[1]-m[1],S,P=c.target,p=Math.abs(C)>Math.abs(w)?"h":"v";if("touches"in c&&p==="h"&&P.type==="range")return!1;var O=be(p,P);if(!O)return!0;if(O?S=p:(S=p==="v"?"h":"v",O=be(p,P)),!O)return!1;if(!r.current&&"changedTouches"in c&&(C||w)&&(r.current=S),!S)return!0;var j=r.current||S;return en(j,v,c,j==="h"?C:w)},[]),y=i.useCallback(function(c){var v=c;if(!(!M.length||M[M.length-1]!==a)){var m="deltaY"in v?Ce(v):U(v),b=t.current.filter(function(S){return S.name===v.type&&(S.target===v.target||v.target===S.shadowParent)&&tn(S.delta,m)})[0];if(b&&b.should){v.cancelable&&v.preventDefault();return}if(!b){var C=(u.current.shards||[]).map(we).filter(Boolean).filter(function(S){return S.contains(v.target)}),w=C.length>0?s(v,C[0]):!u.current.noIsolation;w&&v.cancelable&&v.preventDefault()}}},[]),d=i.useCallback(function(c,v,m,b){var C={name:c,delta:v,target:m,should:b,shadowParent:an(m)};t.current.push(C),setTimeout(function(){t.current=t.current.filter(function(w){return w!==C})},1)},[]),f=i.useCallback(function(c){n.current=U(c),r.current=void 0},[]),h=i.useCallback(function(c){d(c.type,Ce(c),c.target,s(c,e.lockRef.current))},[]),g=i.useCallback(function(c){d(c.type,U(c),c.target,s(c,e.lockRef.current))},[]);i.useEffect(function(){return M.push(a),e.setCallbacks({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:g}),document.addEventListener("wheel",y,L),document.addEventListener("touchmove",y,L),document.addEventListener("touchstart",f,L),function(){M=M.filter(function(c){return c!==a}),document.removeEventListener("wheel",y,L),document.removeEventListener("touchmove",y,L),document.removeEventListener("touchstart",f,L)}},[]);var E=e.removeScrollBar,D=e.inert;return i.createElement(i.Fragment,null,D?i.createElement(a,{styles:nn(o)}):null,E?i.createElement(Vt,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}function an(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const sn=Mt(Ae,on);var Fe=i.forwardRef(function(e,t){return i.createElement(X,R({},e,{ref:t,sideCar:sn}))});Fe.classNames=X.classNames;var cn=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},k=new WeakMap,H=new WeakMap,K={},oe=0,_e=function(e){return e&&(e.host||_e(e.parentNode))},un=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=_e(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},ln=function(e,t,n,r){var o=un(t,Array.isArray(e)?e:[e]);K[n]||(K[n]=new WeakMap);var a=K[n],u=[],s=new Set,y=new Set(o),d=function(h){!h||s.has(h)||(s.add(h),d(h.parentNode))};o.forEach(d);var f=function(h){!h||y.has(h)||Array.prototype.forEach.call(h.children,function(g){if(s.has(g))f(g);else try{var E=g.getAttribute(r),D=E!==null&&E!=="false",c=(k.get(g)||0)+1,v=(a.get(g)||0)+1;k.set(g,c),a.set(g,v),u.push(g),c===1&&D&&H.set(g,!0),v===1&&g.setAttribute(n,"true"),D||g.setAttribute(r,"true")}catch(m){console.error("aria-hidden: cannot operate on ",g,m)}})};return f(t),s.clear(),oe++,function(){u.forEach(function(h){var g=k.get(h)-1,E=a.get(h)-1;k.set(h,g),a.set(h,E),g||(H.has(h)||h.removeAttribute(r),H.delete(h)),E||h.removeAttribute(n)}),oe--,oe||(k=new WeakMap,k=new WeakMap,H=new WeakMap,K={})}},dn=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=cn(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),ln(r,o,n,"aria-hidden")):function(){return null}},Y="Dialog",[je,Wn]=rt(Y),[fn,x]=je(Y),We=e=>{const{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:a,modal:u=!0}=e,s=i.useRef(null),y=i.useRef(null),[d,f]=nt({prop:r,defaultProp:o??!1,onChange:a,caller:Y});return l.jsx(fn,{scope:t,triggerRef:s,contentRef:y,contentId:q(),titleId:q(),descriptionId:q(),open:d,onOpenChange:f,onOpenToggle:i.useCallback(()=>f(h=>!h),[f]),modal:u,children:n})};We.displayName=Y;var Be="DialogTrigger",vn=i.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=x(Be,n),a=_(t,o.triggerRef);return l.jsx(N.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":le(o.open),...r,ref:a,onClick:A(e.onClick,o.onOpenToggle)})});vn.displayName=Be;var ce="DialogPortal",[hn,Ue]=je(ce,{forceMount:void 0}),He=e=>{const{__scopeDialog:t,forceMount:n,children:r,container:o}=e,a=x(ce,t);return l.jsx(hn,{scope:t,forceMount:n,children:i.Children.map(r,u=>l.jsx(se,{present:n||a.open,children:l.jsx(Oe,{asChild:!0,container:o,children:u})}))})};He.displayName=ce;var G="DialogOverlay",Ke=i.forwardRef((e,t)=>{const n=Ue(G,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=x(G,e.__scopeDialog);return a.modal?l.jsx(se,{present:r||a.open,children:l.jsx(mn,{...o,ref:t})}):null});Ke.displayName=G;var gn=at("DialogOverlay.RemoveScroll"),mn=i.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=x(G,n);return l.jsx(Fe,{as:gn,allowPinchZoom:!0,shards:[o.contentRef],children:l.jsx(N.div,{"data-state":le(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),I="DialogContent",ze=i.forwardRef((e,t)=>{const n=Ue(I,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=x(I,e.__scopeDialog);return l.jsx(se,{present:r||a.open,children:a.modal?l.jsx(pn,{...o,ref:t}):l.jsx(yn,{...o,ref:t})})});ze.displayName=I;var pn=i.forwardRef((e,t)=>{const n=x(I,e.__scopeDialog),r=i.useRef(null),o=_(t,n.contentRef,r);return i.useEffect(()=>{const a=r.current;if(a)return dn(a)},[]),l.jsx($e,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:A(e.onCloseAutoFocus,a=>{a.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:A(e.onPointerDownOutside,a=>{const u=a.detail.originalEvent,s=u.button===0&&u.ctrlKey===!0;(u.button===2||s)&&a.preventDefault()}),onFocusOutside:A(e.onFocusOutside,a=>a.preventDefault())})}),yn=i.forwardRef((e,t)=>{const n=x(I,e.__scopeDialog),r=i.useRef(!1),o=i.useRef(!1);return l.jsx($e,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:a=>{e.onCloseAutoFocus?.(a),a.defaultPrevented||(r.current||n.triggerRef.current?.focus(),a.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:a=>{e.onInteractOutside?.(a),a.defaultPrevented||(r.current=!0,a.detail.originalEvent.type==="pointerdown"&&(o.current=!0));const u=a.target;n.triggerRef.current?.contains(u)&&a.preventDefault(),a.detail.originalEvent.type==="focusin"&&o.current&&a.preventDefault()}})}),$e=i.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:a,...u}=e,s=x(I,n),y=i.useRef(null),d=_(t,y);return Dt(),l.jsxs(l.Fragment,{children:[l.jsx(Re,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:a,children:l.jsx(De,{role:"dialog",id:s.contentId,"aria-describedby":s.descriptionId,"aria-labelledby":s.titleId,"data-state":le(s.open),...u,ref:d,onDismiss:()=>s.onOpenChange(!1)})}),l.jsxs(l.Fragment,{children:[l.jsx(bn,{titleId:s.titleId}),l.jsx(wn,{contentRef:y,descriptionId:s.descriptionId})]})]})}),ue="DialogTitle",Ge=i.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=x(ue,n);return l.jsx(N.h2,{id:o.titleId,...r,ref:t})});Ge.displayName=ue;var Ve="DialogDescription",En=i.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=x(Ve,n);return l.jsx(N.p,{id:o.descriptionId,...r,ref:t})});En.displayName=Ve;var Xe="DialogClose",Ye=i.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=x(Xe,n);return l.jsx(N.button,{type:"button",...r,ref:t,onClick:A(e.onClick,()=>o.onOpenChange(!1))})});Ye.displayName=Xe;function le(e){return e?"open":"closed"}var Ze="DialogTitleWarning",[Bn,qe]=ot(Ze,{contentName:I,titleName:ue,docsSlug:"dialog"}),bn=({titleId:e})=>{const t=qe(Ze),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return i.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},Cn="DialogDescriptionWarning",wn=({contentRef:e,descriptionId:t})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${qe(Cn).contentName}}.`;return i.useEffect(()=>{const o=e.current?.getAttribute("aria-describedby");t&&o&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},Sn=We,Dn=He,xn=Ke,Rn=ze,Pn=Ge,On=Ye;function Un({...e}){return l.jsx(Sn,{"data-slot":"dialog",...e})}function Nn({...e}){return l.jsx(Dn,{"data-slot":"dialog-portal",...e})}function Tn({className:e,...t}){return l.jsx(xn,{"data-slot":"dialog-overlay",className:V("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function Hn({className:e,children:t,showCloseButton:n=!0,...r}){return l.jsxs(Nn,{"data-slot":"dialog-portal",children:[l.jsx(Tn,{}),l.jsxs(Rn,{"data-slot":"dialog-content",className:V("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...r,children:[t,n&&l.jsxs(On,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[l.jsx(it,{}),l.jsx("span",{className:"sr-only",children:"Close"})]})]})]})}function Kn({className:e,...t}){return l.jsx("div",{"data-slot":"dialog-header",className:V("flex flex-col gap-2 text-center sm:text-left",e),...t})}function zn({className:e,...t}){return l.jsx(Pn,{"data-slot":"dialog-title",className:V("text-lg leading-none font-semibold",e),...t})}export{jn as C,Un as D,Hn as a,Kn as b,zn as c,W as u};
