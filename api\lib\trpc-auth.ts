/**
 * tRPC middleware для интеграции с Better-Auth и ZenStack
 */

import { TRPCError } from '@trpc/server'
import { getCurrentUser, getAuthenticatedDB, hasRole } from './auth-db'
import { getPublicDB } from '../db'
import type { ExtendedUser } from '../types/auth'

/**
 * Контекст tRPC с авторизацией
 */
export interface AuthContext {
  user: ExtendedUser | null
  db: ReturnType<typeof getAuthenticatedDB> | ReturnType<typeof getPublicDB>
}

/**
 * Создает контекст для tRPC с автоматической авторизацией
 */
export async function createAuthContext(request?: Request): Promise<AuthContext> {
  const user = await getCurrentUser(request)
  const db = user ? await getAuthenticatedDB(request) : getPublicDB()

  return { user, db }
}

/**
 * Middleware для проверки авторизации
 */
export const authMiddleware = async (opts: any) => {
  const { ctx } = opts
  
  if (!ctx.user) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'Требуется авторизация'
    })
  }
  
  return opts.next({
    ctx: {
      ...ctx,
      user: ctx.user as ExtendedUser
    }
  })
}

/**
 * Middleware для проверки роли пользователя
 */
export const roleMiddleware = (requiredRole: string) => async (opts: any) => {
  const { ctx } = opts
  
  if (!ctx.user) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'Требуется авторизация'
    })
  }
  
  if (!hasRole(ctx.user, requiredRole)) {
    throw new TRPCError({
      code: 'FORBIDDEN',
      message: `Требуется роль: ${requiredRole}`
    })
  }
  
  return opts.next({
    ctx: {
      ...ctx,
      user: ctx.user as ExtendedUser
    }
  })
}

/**
 * Middleware для владельцев магазинов
 */
export const shopOwnerMiddleware = roleMiddleware('SHOP')

/**
 * Middleware для экспертов
 */
export const expertMiddleware = roleMiddleware('ADMIN')

/**
 * Хелпер для создания процедур с авторизацией
 */
export function createAuthProcedure(t: any) {
  return {
    // Публичная процедура (без авторизации)
    public: t.procedure,
    
    // Процедура для авторизованных пользователей
    auth: t.procedure.use(authMiddleware),
    
    // Процедура для владельцев магазинов
    shopOwner: t.procedure.use(authMiddleware).use(shopOwnerMiddleware),
    
    // Процедура для экспертов
    expert: t.procedure.use(authMiddleware).use(expertMiddleware)
  }
}
