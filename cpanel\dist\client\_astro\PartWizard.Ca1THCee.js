import{s as Te,_ as R,p as de}from"./_plugin-vue_export-helper.BX07OBiL.js";import{U as te,c as p,o as l,l as L,m as I,d as K,g as w,j as v,av as Be,au as It,a9 as De,aw as St,ax as re,ad as Ft,aO as Ne,p as ce,q as $,b as _,a as o,V as Ve,u as ie,aa as Pt,as as se,e as m,f as A,h as H,w as je,x as Re,v as le,ag as Lt,F as N,r as q,k as We,n as Ot,i as Mt}from"./index.BglzLLgy.js";import{r as k,n as Ie,t as y,b as qt}from"./reactivity.esm-bundler.D5IypM4U.js";import{s as zt}from"./AttributeValueInput.CSCilM_R.js";import{c as Ut}from"./index.D6LCJW96.js";import{R as pe,f as be}from"./index.B_yc9D3m.js";import{u as fe}from"./useTrpc.CAkGIEe7.js";import He from"./Card.CAr8QcdG.js";import me from"./Button.oEwD-lSq.js";import{I as ge}from"./InputText.COaPodMV.js";import{V as ve}from"./AutoComplete.XhB-0aUS.js";import{M as Qe}from"./Message.BOpJRjRi.js";import{D as Nt}from"./Dialog.Dcz8Sg9i.js";import{V as Ge}from"./Textarea.Bgj6qPNm.js";import{Q as Xe,A as Rt}from"./QuickCreateBrand.BfvKWKIq.js";import{b as Je}from"./index.CLkTvMlq.js";import{a as Ye}from"./trash.BzwTNQJC.js";import{T as Kt}from"./Tag.DvN1X7lb.js";import{S as jt}from"./Select.CKfyRLxl.js";import{I as Ze}from"./Icon.Ci-mb2Ee.js";import{f as Ke,r as Wt}from"./utils.NP7rd5-k.js";import"./InputNumber.BtccV88f.js";import"./Checkbox.B9YJ1Jrl.js";import"./index.D7_1DwEX.js";import"./index.CXDqTVvt.js";import"./index.CLs7nh7g.js";import"./index.Tc5ZRw49.js";import"./index.CJuyVe3p.js";import"./index.BHZvt6Rq.js";import"./index.DQmIdHOH.js";import"./index.irlSZ_18.js";import"./index.B0GjtQuk.js";import"./runtime-dom.esm-bundler.C-dfRCGi.js";import"./index.CRcBj2l1.js";import"./trpc.BpyaUO08.js";import"./useToast.DbdIHNOo.js";import"./index.CzaMXvxd.js";import"./index.CBatl9QV.js";import"./index.BsVcoVHU.js";import"./SecondaryButton.DjJyItVx.js";import"./DangerButton.PrtrqpTW.js";import"./createLucideIcon.3yDVQAYz.js";/* empty css                       */var Ht=`
    .p-tabs {
        display: flex;
        flex-direction: column;
    }

    .p-tablist {
        display: flex;
        position: relative;
    }

    .p-tabs-scrollable > .p-tablist {
        overflow: hidden;
    }

    .p-tablist-viewport {
        overflow-x: auto;
        overflow-y: hidden;
        scroll-behavior: smooth;
        scrollbar-width: none;
        overscroll-behavior: contain auto;
    }

    .p-tablist-viewport::-webkit-scrollbar {
        display: none;
    }

    .p-tablist-tab-list {
        position: relative;
        display: flex;
        background: dt('tabs.tablist.background');
        border-style: solid;
        border-color: dt('tabs.tablist.border.color');
        border-width: dt('tabs.tablist.border.width');
    }

    .p-tablist-content {
        flex-grow: 1;
    }

    .p-tablist-nav-button {
        all: unset;
        position: absolute !important;
        flex-shrink: 0;
        inset-block-start: 0;
        z-index: 2;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: dt('tabs.nav.button.background');
        color: dt('tabs.nav.button.color');
        width: dt('tabs.nav.button.width');
        transition:
            color dt('tabs.transition.duration'),
            outline-color dt('tabs.transition.duration'),
            box-shadow dt('tabs.transition.duration');
        box-shadow: dt('tabs.nav.button.shadow');
        outline-color: transparent;
        cursor: pointer;
    }

    .p-tablist-nav-button:focus-visible {
        z-index: 1;
        box-shadow: dt('tabs.nav.button.focus.ring.shadow');
        outline: dt('tabs.nav.button.focus.ring.width') dt('tabs.nav.button.focus.ring.style') dt('tabs.nav.button.focus.ring.color');
        outline-offset: dt('tabs.nav.button.focus.ring.offset');
    }

    .p-tablist-nav-button:hover {
        color: dt('tabs.nav.button.hover.color');
    }

    .p-tablist-prev-button {
        inset-inline-start: 0;
    }

    .p-tablist-next-button {
        inset-inline-end: 0;
    }

    .p-tablist-prev-button:dir(rtl),
    .p-tablist-next-button:dir(rtl) {
        transform: rotate(180deg);
    }

    .p-tab {
        flex-shrink: 0;
        cursor: pointer;
        user-select: none;
        position: relative;
        border-style: solid;
        white-space: nowrap;
        gap: dt('tabs.tab.gap');
        background: dt('tabs.tab.background');
        border-width: dt('tabs.tab.border.width');
        border-color: dt('tabs.tab.border.color');
        color: dt('tabs.tab.color');
        padding: dt('tabs.tab.padding');
        font-weight: dt('tabs.tab.font.weight');
        transition:
            background dt('tabs.transition.duration'),
            border-color dt('tabs.transition.duration'),
            color dt('tabs.transition.duration'),
            outline-color dt('tabs.transition.duration'),
            box-shadow dt('tabs.transition.duration');
        margin: dt('tabs.tab.margin');
        outline-color: transparent;
    }

    .p-tab:not(.p-disabled):focus-visible {
        z-index: 1;
        box-shadow: dt('tabs.tab.focus.ring.shadow');
        outline: dt('tabs.tab.focus.ring.width') dt('tabs.tab.focus.ring.style') dt('tabs.tab.focus.ring.color');
        outline-offset: dt('tabs.tab.focus.ring.offset');
    }

    .p-tab:not(.p-tab-active):not(.p-disabled):hover {
        background: dt('tabs.tab.hover.background');
        border-color: dt('tabs.tab.hover.border.color');
        color: dt('tabs.tab.hover.color');
    }

    .p-tab-active {
        background: dt('tabs.tab.active.background');
        border-color: dt('tabs.tab.active.border.color');
        color: dt('tabs.tab.active.color');
    }

    .p-tabpanels {
        background: dt('tabs.tabpanel.background');
        color: dt('tabs.tabpanel.color');
        padding: dt('tabs.tabpanel.padding');
        outline: 0 none;
    }

    .p-tabpanel:focus-visible {
        box-shadow: dt('tabs.tabpanel.focus.ring.shadow');
        outline: dt('tabs.tabpanel.focus.ring.width') dt('tabs.tabpanel.focus.ring.style') dt('tabs.tabpanel.focus.ring.color');
        outline-offset: dt('tabs.tabpanel.focus.ring.offset');
    }

    .p-tablist-active-bar {
        z-index: 1;
        display: block;
        position: absolute;
        inset-block-end: dt('tabs.active.bar.bottom');
        height: dt('tabs.active.bar.height');
        background: dt('tabs.active.bar.background');
        transition: 250ms cubic-bezier(0.35, 0, 0.25, 1);
    }
`,Qt={root:function(t){var n=t.props;return["p-tabs p-component",{"p-tabs-scrollable":n.scrollable}]}},Gt=te.extend({name:"tabs",style:Ht,classes:Qt}),Xt={name:"BaseTabs",extends:Te,props:{value:{type:[String,Number],default:void 0},lazy:{type:Boolean,default:!1},scrollable:{type:Boolean,default:!1},showNavigators:{type:Boolean,default:!0},tabindex:{type:Number,default:0},selectOnFocus:{type:Boolean,default:!1}},style:Gt,provide:function(){return{$pcTabs:this,$parentInstance:this}}},$e={name:"Tabs",extends:Xt,inheritAttrs:!1,emits:["update:value"],data:function(){return{d_value:this.value}},watch:{value:function(t){this.d_value=t}},methods:{updateValue:function(t){this.d_value!==t&&(this.d_value=t,this.$emit("update:value",t))},isVertical:function(){return this.orientation==="vertical"}}};function Jt(u,t,n,e,c,r){return l(),p("div",I({class:u.cx("root")},u.ptmi("root")),[L(u.$slots,"default")],16)}$e.render=Jt;const Yt=K({__name:"Tabs",setup(u,{expose:t}){t();const n=u,e=k({root:"flex flex-col"}),c={props:n,theme:e,get Tabs(){return $e},get ptViewMerge(){return de}};return Object.defineProperty(c,"__isScriptSetup",{enumerable:!1,value:!0}),c}});function Zt(u,t,n,e,c,r){return l(),w(e.Tabs,{value:e.props.value,unstyled:"",pt:e.theme,ptOptions:{mergeProps:e.ptViewMerge}},{default:v(()=>[L(u.$slots,"default")]),_:3},8,["value","pt","ptOptions"])}const $t=R(Yt,[["render",Zt]]);var eu={root:"p-tablist",content:function(t){var n=t.instance;return["p-tablist-content",{"p-tablist-viewport":n.$pcTabs.scrollable}]},tabList:"p-tablist-tab-list",activeBar:"p-tablist-active-bar",prevButton:"p-tablist-prev-button p-tablist-nav-button",nextButton:"p-tablist-next-button p-tablist-nav-button"},tu=te.extend({name:"tablist",classes:eu}),uu={name:"BaseTabList",extends:Te,props:{},style:tu,provide:function(){return{$pcTabList:this,$parentInstance:this}}},et={name:"TabList",extends:uu,inheritAttrs:!1,inject:["$pcTabs"],data:function(){return{isPrevButtonEnabled:!1,isNextButtonEnabled:!0}},resizeObserver:void 0,watch:{showNavigators:function(t){t?this.bindResizeObserver():this.unbindResizeObserver()},activeValue:{flush:"post",handler:function(){this.updateInkBar()}}},mounted:function(){var t=this;setTimeout(function(){t.updateInkBar()},150),this.showNavigators&&(this.updateButtonState(),this.bindResizeObserver())},updated:function(){this.showNavigators&&this.updateButtonState()},beforeUnmount:function(){this.unbindResizeObserver()},methods:{onScroll:function(t){this.showNavigators&&this.updateButtonState(),t.preventDefault()},onPrevButtonClick:function(){var t=this.$refs.content,n=this.getVisibleButtonWidths(),e=Be(t)-n,c=Math.abs(t.scrollLeft),r=e*.8,a=c-r,s=Math.max(a,0);t.scrollLeft=Ne(t)?-1*s:s},onNextButtonClick:function(){var t=this.$refs.content,n=this.getVisibleButtonWidths(),e=Be(t)-n,c=Math.abs(t.scrollLeft),r=e*.8,a=c+r,s=t.scrollWidth-e,f=Math.min(a,s);t.scrollLeft=Ne(t)?-1*f:f},bindResizeObserver:function(){var t=this;this.resizeObserver=new ResizeObserver(function(){return t.updateButtonState()}),this.resizeObserver.observe(this.$refs.list)},unbindResizeObserver:function(){var t;(t=this.resizeObserver)===null||t===void 0||t.unobserve(this.$refs.list),this.resizeObserver=void 0},updateInkBar:function(){var t=this.$refs,n=t.content,e=t.inkbar,c=t.tabs;if(e){var r=De(n,'[data-pc-name="tab"][data-p-active="true"]');this.$pcTabs.isVertical()?(e.style.height=St(r)+"px",e.style.top=re(r).top-re(c).top+"px"):(e.style.width=Ft(r)+"px",e.style.left=re(r).left-re(c).left+"px")}},updateButtonState:function(){var t=this.$refs,n=t.list,e=t.content,c=e.scrollTop,r=e.scrollWidth,a=e.scrollHeight,s=e.offsetWidth,f=e.offsetHeight,b=Math.abs(e.scrollLeft),x=[Be(e),It(e)],O=x[0],P=x[1];this.$pcTabs.isVertical()?(this.isPrevButtonEnabled=c!==0,this.isNextButtonEnabled=n.offsetHeight>=f&&parseInt(c)!==a-P):(this.isPrevButtonEnabled=b!==0,this.isNextButtonEnabled=n.offsetWidth>=s&&parseInt(b)!==r-O)},getVisibleButtonWidths:function(){var t=this.$refs,n=t.prevButton,e=t.nextButton,c=0;return this.showNavigators&&(c=(n?.offsetWidth||0)+(e?.offsetWidth||0)),c}},computed:{templates:function(){return this.$pcTabs.$slots},activeValue:function(){return this.$pcTabs.d_value},showNavigators:function(){return this.$pcTabs.scrollable&&this.$pcTabs.showNavigators},prevButtonAriaLabel:function(){return this.$primevue.config.locale.aria?this.$primevue.config.locale.aria.previous:void 0},nextButtonAriaLabel:function(){return this.$primevue.config.locale.aria?this.$primevue.config.locale.aria.next:void 0},dataP:function(){return be({scrollable:this.$pcTabs.scrollable})}},components:{ChevronLeftIcon:zt,ChevronRightIcon:Ut},directives:{ripple:pe}},au=["data-p"],nu=["aria-label","tabindex"],ou=["data-p"],ru=["aria-orientation"],su=["aria-label","tabindex"];function lu(u,t,n,e,c,r){var a=ce("ripple");return l(),p("div",I({ref:"list",class:u.cx("root"),"data-p":r.dataP},u.ptmi("root")),[r.showNavigators&&c.isPrevButtonEnabled?$((l(),p("button",I({key:0,ref:"prevButton",type:"button",class:u.cx("prevButton"),"aria-label":r.prevButtonAriaLabel,tabindex:r.$pcTabs.tabindex,onClick:t[0]||(t[0]=function(){return r.onPrevButtonClick&&r.onPrevButtonClick.apply(r,arguments)})},u.ptm("prevButton"),{"data-pc-group-section":"navigator"}),[(l(),w(Ve(r.templates.previcon||"ChevronLeftIcon"),I({"aria-hidden":"true"},u.ptm("prevIcon")),null,16))],16,nu)),[[a]]):_("",!0),o("div",I({ref:"content",class:u.cx("content"),onScroll:t[1]||(t[1]=function(){return r.onScroll&&r.onScroll.apply(r,arguments)}),"data-p":r.dataP},u.ptm("content")),[o("div",I({ref:"tabs",class:u.cx("tabList"),role:"tablist","aria-orientation":r.$pcTabs.orientation||"horizontal"},u.ptm("tabList")),[L(u.$slots,"default"),o("span",I({ref:"inkbar",class:u.cx("activeBar"),role:"presentation","aria-hidden":"true"},u.ptm("activeBar")),null,16)],16,ru)],16,ou),r.showNavigators&&c.isNextButtonEnabled?$((l(),p("button",I({key:1,ref:"nextButton",type:"button",class:u.cx("nextButton"),"aria-label":r.nextButtonAriaLabel,tabindex:r.$pcTabs.tabindex,onClick:t[2]||(t[2]=function(){return r.onNextButtonClick&&r.onNextButtonClick.apply(r,arguments)})},u.ptm("nextButton"),{"data-pc-group-section":"navigator"}),[(l(),w(Ve(r.templates.nexticon||"ChevronRightIcon"),I({"aria-hidden":"true"},u.ptm("nextIcon")),null,16))],16,su)),[[a]]):_("",!0)],16,au)}et.render=lu;const we=`!absolute flex-shrink-0 top-0 z-20 h-full flex items-center justify-center cursor-pointer
        bg-surface-0 dark:bg-surface-900 text-surface-500 dark:text-surface-400 hover:text-surface-700 dark:hover:text-surface-0 w-10
        shadow-[0px_0px_10px_50px_rgba(255,255,255,0.6)] dark:shadow-[0px_0px_10px_50px] dark:shadow-surface-900/50
        focus-visible:z-10 focus-visible:outline focus-visible:outline-1 focus-visible:outline-offset-[-1px] focus-visible:outline-primary
        transition-colors duration-200`,iu=K({__name:"TabList",setup(u,{expose:t}){t();const n=k({root:"flex relative",prevButton:we+" start-0",nextButton:we+" end-0",content:`flex-grow
        p-scrollable:overflow-x-auto p-scrollable:overflow-y-hidden p-scrollable:overscroll-y-contain p-scrollable:overscroll-x-auto
        scroll-smooth [scrollbar-width:none]`,tabList:`relative flex bg-surface-0 dark:bg-surface-900 border-b border-surface-200 dark:border-surface-700
        p-scrollable:overflow-hidden`,activeBar:"z-10 block absolute -bottom-px h-px bg-primary transition-[left] duration-200 ease-[cubic-bezier(0.35,0,0.25,1)]"}),e={navButton:we,theme:n,get TabList(){return et},get ptViewMerge(){return de}};return Object.defineProperty(e,"__isScriptSetup",{enumerable:!1,value:!0}),e}});function du(u,t,n,e,c,r){return l(),w(e.TabList,{unstyled:"",pt:e.theme,ptOptions:{mergeProps:e.ptViewMerge}},{default:v(()=>[L(u.$slots,"default")]),_:3},8,["pt","ptOptions"])}const cu=R(iu,[["render",du]]);var pu={root:function(t){var n=t.instance,e=t.props;return["p-tab",{"p-tab-active":n.active,"p-disabled":e.disabled}]}},bu=te.extend({name:"tab",classes:pu}),fu={name:"BaseTab",extends:Te,props:{value:{type:[String,Number],default:void 0},disabled:{type:Boolean,default:!1},as:{type:[String,Object],default:"BUTTON"},asChild:{type:Boolean,default:!1}},style:bu,provide:function(){return{$pcTab:this,$parentInstance:this}}},tt={name:"Tab",extends:fu,inheritAttrs:!1,inject:["$pcTabs","$pcTabList"],methods:{onFocus:function(){this.$pcTabs.selectOnFocus&&this.changeActiveValue()},onClick:function(){this.changeActiveValue()},onKeydown:function(t){switch(t.code){case"ArrowRight":this.onArrowRightKey(t);break;case"ArrowLeft":this.onArrowLeftKey(t);break;case"Home":this.onHomeKey(t);break;case"End":this.onEndKey(t);break;case"PageDown":this.onPageDownKey(t);break;case"PageUp":this.onPageUpKey(t);break;case"Enter":case"NumpadEnter":case"Space":this.onEnterKey(t);break}},onArrowRightKey:function(t){var n=this.findNextTab(t.currentTarget);n?this.changeFocusedTab(t,n):this.onHomeKey(t),t.preventDefault()},onArrowLeftKey:function(t){var n=this.findPrevTab(t.currentTarget);n?this.changeFocusedTab(t,n):this.onEndKey(t),t.preventDefault()},onHomeKey:function(t){var n=this.findFirstTab();this.changeFocusedTab(t,n),t.preventDefault()},onEndKey:function(t){var n=this.findLastTab();this.changeFocusedTab(t,n),t.preventDefault()},onPageDownKey:function(t){this.scrollInView(this.findLastTab()),t.preventDefault()},onPageUpKey:function(t){this.scrollInView(this.findFirstTab()),t.preventDefault()},onEnterKey:function(t){this.changeActiveValue(),t.preventDefault()},findNextTab:function(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,e=n?t:t.nextElementSibling;return e?se(e,"data-p-disabled")||se(e,"data-pc-section")==="activebar"?this.findNextTab(e):De(e,'[data-pc-name="tab"]'):null},findPrevTab:function(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,e=n?t:t.previousElementSibling;return e?se(e,"data-p-disabled")||se(e,"data-pc-section")==="activebar"?this.findPrevTab(e):De(e,'[data-pc-name="tab"]'):null},findFirstTab:function(){return this.findNextTab(this.$pcTabList.$refs.tabs.firstElementChild,!0)},findLastTab:function(){return this.findPrevTab(this.$pcTabList.$refs.tabs.lastElementChild,!0)},changeActiveValue:function(){this.$pcTabs.updateValue(this.value)},changeFocusedTab:function(t,n){Pt(n),this.scrollInView(n)},scrollInView:function(t){var n;t==null||(n=t.scrollIntoView)===null||n===void 0||n.call(t,{block:"nearest"})}},computed:{active:function(){var t;return ie((t=this.$pcTabs)===null||t===void 0?void 0:t.d_value,this.value)},id:function(){var t;return"".concat((t=this.$pcTabs)===null||t===void 0?void 0:t.$id,"_tab_").concat(this.value)},ariaControls:function(){var t;return"".concat((t=this.$pcTabs)===null||t===void 0?void 0:t.$id,"_tabpanel_").concat(this.value)},attrs:function(){return I(this.asAttrs,this.a11yAttrs,this.ptmi("root",this.ptParams))},asAttrs:function(){return this.as==="BUTTON"?{type:"button",disabled:this.disabled}:void 0},a11yAttrs:function(){return{id:this.id,tabindex:this.active?this.$pcTabs.tabindex:-1,role:"tab","aria-selected":this.active,"aria-controls":this.ariaControls,"data-pc-name":"tab","data-p-disabled":this.disabled,"data-p-active":this.active,onFocus:this.onFocus,onKeydown:this.onKeydown}},ptParams:function(){return{context:{active:this.active}}},dataP:function(){return be({active:this.active})}},directives:{ripple:pe}};function mu(u,t,n,e,c,r){var a=ce("ripple");return u.asChild?L(u.$slots,"default",{key:1,dataP:r.dataP,class:Ie(u.cx("root")),active:r.active,a11yAttrs:r.a11yAttrs,onClick:r.onClick}):$((l(),w(Ve(u.as),I({key:0,class:u.cx("root"),"data-p":r.dataP,onClick:r.onClick},r.attrs),{default:v(function(){return[L(u.$slots,"default")]}),_:3},16,["class","data-p","onClick"])),[[a]])}tt.render=mu;const gu=K({__name:"Tab",setup(u,{expose:t}){t();const n=u,e=k({root:`flex-shrink-0 cursor-pointer select-none relative whitespace-nowrap py-4 px-[1.125rem]
        border-b border-surface-200 dark:border-surface-700 font-semibold
        text-surface-500 dark:text-surface-400
        transition-colors duration-200 -mb-px
        not-p-active:enabled:hover:text-surface-700 dark:not-p-active:enabled:hover:text-surface-0
        p-active:border-primary p-active:text-primary
        disabled:pointer-events-none disabled:opacity-60
        focus-visible:z-10 focus-visible:outline focus-visible:outline-1 focus-visible:outline-offset-[-1px] focus-visible:outline-primary`}),c={props:n,theme:e,get Tab(){return tt},get ptViewMerge(){return de}};return Object.defineProperty(c,"__isScriptSetup",{enumerable:!1,value:!0}),c}});function vu(u,t,n,e,c,r){return l(),w(e.Tab,{value:e.props.value,unstyled:"",pt:e.theme,ptOptions:{mergeProps:e.ptViewMerge}},{default:v(()=>[L(u.$slots,"default")]),_:3},8,["value","pt","ptOptions"])}const hu=R(gu,[["render",vu]]),yu=K({__name:"QuickCreateCategory",props:{visible:{type:Boolean}},emits:["update:visible","created"],setup(u,{expose:t,emit:n}){t();const e=u,c=n,{loading:r,error:a,clearError:s,partCategories:f}=fe(),b=H({get:()=>e.visible,set:C=>c("update:visible",C)}),x=k({name:"",description:"",parent:null}),O=k({name:""}),P=k([]),Q=H(()=>x.value.name.trim().length>0&&!r.value),j=async C=>{const F=C.query.toLowerCase(),E=await f.findMany({where:{name:{contains:F,mode:"insensitive"}},take:10});E&&(P.value=E)},z=async()=>{if(s(),O.value={name:""},!x.value.name.trim()){O.value.name="Название обязательно";return}try{const C=x.value.name.toLowerCase().replace(/[^a-zа-я0-9\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim(),F={name:x.value.name.trim(),slug:C,description:x.value.description?.trim()||void 0,level:x.value.parent?x.value.parent.level+1:0,path:x.value.parent?`${x.value.parent.path}/${C}`:`/${C}`,parentId:x.value.parent?.id||void 0},E=await f.create({data:F});E&&(c("created",E),T(),b.value=!1)}catch(C){console.error("Ошибка создания категории:",C)}},T=()=>{x.value={name:"",description:"",parent:null},O.value={name:""},s()};je(b,C=>{C||T()});const S={props:e,emit:c,loading:r,error:a,clearError:s,partCategories:f,visible:b,formData:x,errors:O,parentSuggestions:P,canCreate:Q,searchParents:j,createCategory:z,resetForm:T,VDialog:Nt,VButton:me,VInputText:ge,VTextarea:Ge,VAutoComplete:ve,VMessage:Qe};return Object.defineProperty(S,"__isScriptSetup",{enumerable:!1,value:!0}),S}}),xu={class:"space-y-4"},Eu={key:0,class:"text-red-500"},ku={class:"flex justify-end gap-3"};function _u(u,t,n,e,c,r){return l(),w(e.VDialog,{visible:e.visible,"onUpdate:visible":t[4]||(t[4]=a=>e.visible=a),modal:"",header:"Создать новую категорию",class:"w-96"},{footer:v(()=>[o("div",ku,[m(e.VButton,{onClick:t[3]||(t[3]=a=>e.visible=!1),severity:"secondary",outlined:"",disabled:e.loading},{default:v(()=>t[8]||(t[8]=[A(" Отмена ")])),_:1,__:[8]},8,["disabled"]),m(e.VButton,{onClick:e.createCategory,loading:e.loading,disabled:!e.canCreate},{default:v(()=>t[9]||(t[9]=[A(" Создать ")])),_:1,__:[9]},8,["loading","disabled"])])]),default:v(()=>[o("div",xu,[o("div",null,[t[5]||(t[5]=o("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Название категории * ",-1)),m(e.VInputText,{modelValue:e.formData.name,"onUpdate:modelValue":t[0]||(t[0]=a=>e.formData.name=a),placeholder:"Например: Фильтры масляные",class:Ie(["w-full",{"p-invalid":e.errors.name}])},null,8,["modelValue","class"]),e.errors.name?(l(),p("small",Eu,y(e.errors.name),1)):_("",!0)]),o("div",null,[t[6]||(t[6]=o("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Описание ",-1)),m(e.VTextarea,{modelValue:e.formData.description,"onUpdate:modelValue":t[1]||(t[1]=a=>e.formData.description=a),placeholder:"Описание категории",rows:"3",class:"w-full"},null,8,["modelValue"])]),o("div",null,[t[7]||(t[7]=o("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Родительская категория ",-1)),m(e.VAutoComplete,{modelValue:e.formData.parent,"onUpdate:modelValue":t[2]||(t[2]=a=>e.formData.parent=a),suggestions:e.parentSuggestions,onComplete:e.searchParents,"option-label":"name",placeholder:"Поиск родительской категории",class:"w-full"},null,8,["modelValue","suggestions"])])]),e.error?(l(),w(e.VMessage,{key:0,severity:"error",class:"mt-4"},{default:v(()=>[A(y(e.error),1)]),_:1})):_("",!0)]),_:1},8,["visible"])}const Cu=R(yu,[["render",_u]]);var Bu=`
    .p-togglebutton {
        display: inline-flex;
        cursor: pointer;
        user-select: none;
        overflow: hidden;
        position: relative;
        color: dt('togglebutton.color');
        background: dt('togglebutton.background');
        border: 1px solid dt('togglebutton.border.color');
        padding: dt('togglebutton.padding');
        font-size: 1rem;
        font-family: inherit;
        font-feature-settings: inherit;
        transition:
            background dt('togglebutton.transition.duration'),
            color dt('togglebutton.transition.duration'),
            border-color dt('togglebutton.transition.duration'),
            outline-color dt('togglebutton.transition.duration'),
            box-shadow dt('togglebutton.transition.duration');
        border-radius: dt('togglebutton.border.radius');
        outline-color: transparent;
        font-weight: dt('togglebutton.font.weight');
    }

    .p-togglebutton-content {
        display: inline-flex;
        flex: 1 1 auto;
        align-items: center;
        justify-content: center;
        gap: dt('togglebutton.gap');
        padding: dt('togglebutton.content.padding');
        background: transparent;
        border-radius: dt('togglebutton.content.border.radius');
        transition:
            background dt('togglebutton.transition.duration'),
            color dt('togglebutton.transition.duration'),
            border-color dt('togglebutton.transition.duration'),
            outline-color dt('togglebutton.transition.duration'),
            box-shadow dt('togglebutton.transition.duration');
    }

    .p-togglebutton:not(:disabled):not(.p-togglebutton-checked):hover {
        background: dt('togglebutton.hover.background');
        color: dt('togglebutton.hover.color');
    }

    .p-togglebutton.p-togglebutton-checked {
        background: dt('togglebutton.checked.background');
        border-color: dt('togglebutton.checked.border.color');
        color: dt('togglebutton.checked.color');
    }

    .p-togglebutton-checked .p-togglebutton-content {
        background: dt('togglebutton.content.checked.background');
        box-shadow: dt('togglebutton.content.checked.shadow');
    }

    .p-togglebutton:focus-visible {
        box-shadow: dt('togglebutton.focus.ring.shadow');
        outline: dt('togglebutton.focus.ring.width') dt('togglebutton.focus.ring.style') dt('togglebutton.focus.ring.color');
        outline-offset: dt('togglebutton.focus.ring.offset');
    }

    .p-togglebutton.p-invalid {
        border-color: dt('togglebutton.invalid.border.color');
    }

    .p-togglebutton:disabled {
        opacity: 1;
        cursor: default;
        background: dt('togglebutton.disabled.background');
        border-color: dt('togglebutton.disabled.border.color');
        color: dt('togglebutton.disabled.color');
    }

    .p-togglebutton-label,
    .p-togglebutton-icon {
        position: relative;
        transition: none;
    }

    .p-togglebutton-icon {
        color: dt('togglebutton.icon.color');
    }

    .p-togglebutton:not(:disabled):not(.p-togglebutton-checked):hover .p-togglebutton-icon {
        color: dt('togglebutton.icon.hover.color');
    }

    .p-togglebutton.p-togglebutton-checked .p-togglebutton-icon {
        color: dt('togglebutton.icon.checked.color');
    }

    .p-togglebutton:disabled .p-togglebutton-icon {
        color: dt('togglebutton.icon.disabled.color');
    }

    .p-togglebutton-sm {
        padding: dt('togglebutton.sm.padding');
        font-size: dt('togglebutton.sm.font.size');
    }

    .p-togglebutton-sm .p-togglebutton-content {
        padding: dt('togglebutton.content.sm.padding');
    }

    .p-togglebutton-lg {
        padding: dt('togglebutton.lg.padding');
        font-size: dt('togglebutton.lg.font.size');
    }

    .p-togglebutton-lg .p-togglebutton-content {
        padding: dt('togglebutton.content.lg.padding');
    }

    .p-togglebutton-fluid {
        width: 100%;
    }
`,wu={root:function(t){var n=t.instance,e=t.props;return["p-togglebutton p-component",{"p-togglebutton-checked":n.active,"p-invalid":n.$invalid,"p-togglebutton-fluid":e.fluid,"p-togglebutton-sm p-inputfield-sm":e.size==="small","p-togglebutton-lg p-inputfield-lg":e.size==="large"}]},content:"p-togglebutton-content",icon:"p-togglebutton-icon",label:"p-togglebutton-label"},Du=te.extend({name:"togglebutton",style:Bu,classes:wu}),Vu={name:"BaseToggleButton",extends:Je,props:{onIcon:String,offIcon:String,onLabel:{type:String,default:"Yes"},offLabel:{type:String,default:"No"},iconPos:{type:String,default:"left"},readonly:{type:Boolean,default:!1},tabindex:{type:Number,default:null},ariaLabelledby:{type:String,default:null},ariaLabel:{type:String,default:null},size:{type:String,default:null},fluid:{type:Boolean,default:null}},style:Du,provide:function(){return{$pcToggleButton:this,$parentInstance:this}}};function ee(u){"@babel/helpers - typeof";return ee=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ee(u)}function Au(u,t,n){return(t=Tu(t))in u?Object.defineProperty(u,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):u[t]=n,u}function Tu(u){var t=Iu(u,"string");return ee(t)=="symbol"?t:t+""}function Iu(u,t){if(ee(u)!="object"||!u)return u;var n=u[Symbol.toPrimitive];if(n!==void 0){var e=n.call(u,t);if(ee(e)!="object")return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(u)}var ut={name:"ToggleButton",extends:Vu,inheritAttrs:!1,emits:["change"],methods:{getPTOptions:function(t){var n=t==="root"?this.ptmi:this.ptm;return n(t,{context:{active:this.active,disabled:this.disabled}})},onChange:function(t){!this.disabled&&!this.readonly&&(this.writeValue(!this.d_value,t),this.$emit("change",t))},onBlur:function(t){var n,e;(n=(e=this.formField).onBlur)===null||n===void 0||n.call(e,t)}},computed:{active:function(){return this.d_value===!0},hasLabel:function(){return Re(this.onLabel)&&Re(this.offLabel)},label:function(){return this.hasLabel?this.d_value?this.onLabel:this.offLabel:" "},dataP:function(){return be(Au({checked:this.active,invalid:this.$invalid},this.size,this.size))}},directives:{ripple:pe}},Su=["tabindex","disabled","aria-pressed","aria-label","aria-labelledby","data-p-checked","data-p-disabled","data-p"],Fu=["data-p"];function Pu(u,t,n,e,c,r){var a=ce("ripple");return $((l(),p("button",I({type:"button",class:u.cx("root"),tabindex:u.tabindex,disabled:u.disabled,"aria-pressed":u.d_value,onClick:t[0]||(t[0]=function(){return r.onChange&&r.onChange.apply(r,arguments)}),onBlur:t[1]||(t[1]=function(){return r.onBlur&&r.onBlur.apply(r,arguments)})},r.getPTOptions("root"),{"aria-label":u.ariaLabel,"aria-labelledby":u.ariaLabelledby,"data-p-checked":r.active,"data-p-disabled":u.disabled,"data-p":r.dataP}),[o("span",I({class:u.cx("content")},r.getPTOptions("content"),{"data-p":r.dataP}),[L(u.$slots,"default",{},function(){return[L(u.$slots,"icon",{value:u.d_value,class:Ie(u.cx("icon"))},function(){return[u.onIcon||u.offIcon?(l(),p("span",I({key:0,class:[u.cx("icon"),u.d_value?u.onIcon:u.offIcon]},r.getPTOptions("icon")),null,16)):_("",!0)]}),o("span",I({class:u.cx("label")},r.getPTOptions("label")),y(r.label),17)]})],16,Fu)],16,Su)),[[a]])}ut.render=Pu;var Lu=`
    .p-selectbutton {
        display: inline-flex;
        user-select: none;
        vertical-align: bottom;
        outline-color: transparent;
        border-radius: dt('selectbutton.border.radius');
    }

    .p-selectbutton .p-togglebutton {
        border-radius: 0;
        border-width: 1px 1px 1px 0;
    }

    .p-selectbutton .p-togglebutton:focus-visible {
        position: relative;
        z-index: 1;
    }

    .p-selectbutton .p-togglebutton:first-child {
        border-inline-start-width: 1px;
        border-start-start-radius: dt('selectbutton.border.radius');
        border-end-start-radius: dt('selectbutton.border.radius');
    }

    .p-selectbutton .p-togglebutton:last-child {
        border-start-end-radius: dt('selectbutton.border.radius');
        border-end-end-radius: dt('selectbutton.border.radius');
    }

    .p-selectbutton.p-invalid {
        outline: 1px solid dt('selectbutton.invalid.border.color');
        outline-offset: 0;
    }

    .p-selectbutton-fluid {
        width: 100%;
    }
    
    .p-selectbutton-fluid .p-togglebutton {
        flex: 1 1 0;
    }
`,Ou={root:function(t){var n=t.props,e=t.instance;return["p-selectbutton p-component",{"p-invalid":e.$invalid,"p-selectbutton-fluid":n.fluid}]}},Mu=te.extend({name:"selectbutton",style:Lu,classes:Ou}),qu={name:"BaseSelectButton",extends:Je,props:{options:Array,optionLabel:null,optionValue:null,optionDisabled:null,multiple:Boolean,allowEmpty:{type:Boolean,default:!0},dataKey:null,ariaLabelledby:{type:String,default:null},size:{type:String,default:null},fluid:{type:Boolean,default:null}},style:Mu,provide:function(){return{$pcSelectButton:this,$parentInstance:this}}};function zu(u,t){var n=typeof Symbol<"u"&&u[Symbol.iterator]||u["@@iterator"];if(!n){if(Array.isArray(u)||(n=at(u))||t){n&&(u=n);var e=0,c=function(){};return{s:c,n:function(){return e>=u.length?{done:!0}:{done:!1,value:u[e++]}},e:function(b){throw b},f:c}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var r,a=!0,s=!1;return{s:function(){n=n.call(u)},n:function(){var b=n.next();return a=b.done,b},e:function(b){s=!0,r=b},f:function(){try{a||n.return==null||n.return()}finally{if(s)throw r}}}}function Uu(u){return Ku(u)||Ru(u)||at(u)||Nu()}function Nu(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function at(u,t){if(u){if(typeof u=="string")return Ae(u,t);var n={}.toString.call(u).slice(8,-1);return n==="Object"&&u.constructor&&(n=u.constructor.name),n==="Map"||n==="Set"?Array.from(u):n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Ae(u,t):void 0}}function Ru(u){if(typeof Symbol<"u"&&u[Symbol.iterator]!=null||u["@@iterator"]!=null)return Array.from(u)}function Ku(u){if(Array.isArray(u))return Ae(u)}function Ae(u,t){(t==null||t>u.length)&&(t=u.length);for(var n=0,e=Array(t);n<t;n++)e[n]=u[n];return e}var nt={name:"SelectButton",extends:qu,inheritAttrs:!1,emits:["change"],methods:{getOptionLabel:function(t){return this.optionLabel?le(t,this.optionLabel):t},getOptionValue:function(t){return this.optionValue?le(t,this.optionValue):t},getOptionRenderKey:function(t){return this.dataKey?le(t,this.dataKey):this.getOptionLabel(t)},isOptionDisabled:function(t){return this.optionDisabled?le(t,this.optionDisabled):!1},isOptionReadonly:function(t){if(this.allowEmpty)return!1;var n=this.isSelected(t);return this.multiple?n&&this.d_value.length===1:n},onOptionSelect:function(t,n,e){var c=this;if(!(this.disabled||this.isOptionDisabled(n)||this.isOptionReadonly(n))){var r=this.isSelected(n),a=this.getOptionValue(n),s;if(this.multiple)if(r){if(s=this.d_value.filter(function(f){return!ie(f,a,c.equalityKey)}),!this.allowEmpty&&s.length===0)return}else s=this.d_value?[].concat(Uu(this.d_value),[a]):[a];else{if(r&&!this.allowEmpty)return;s=r?null:a}this.writeValue(s,t),this.$emit("change",{event:t,value:s})}},isSelected:function(t){var n=!1,e=this.getOptionValue(t);if(this.multiple){if(this.d_value){var c=zu(this.d_value),r;try{for(c.s();!(r=c.n()).done;){var a=r.value;if(ie(a,e,this.equalityKey)){n=!0;break}}}catch(s){c.e(s)}finally{c.f()}}}else n=ie(this.d_value,e,this.equalityKey);return n}},computed:{equalityKey:function(){return this.optionValue?null:this.dataKey},dataP:function(){return be({invalid:this.$invalid})}},directives:{ripple:pe},components:{ToggleButton:ut}},ju=["aria-labelledby","data-p"];function Wu(u,t,n,e,c,r){var a=Lt("ToggleButton");return l(),p("div",I({class:u.cx("root"),role:"group","aria-labelledby":u.ariaLabelledby},u.ptmi("root"),{"data-p":r.dataP}),[(l(!0),p(N,null,q(u.options,function(s,f){return l(),w(a,{key:r.getOptionRenderKey(s),modelValue:r.isSelected(s),onLabel:r.getOptionLabel(s),offLabel:r.getOptionLabel(s),disabled:u.disabled||r.isOptionDisabled(s),unstyled:u.unstyled,size:u.size,readonly:r.isOptionReadonly(s),onChange:function(x){return r.onOptionSelect(x,s,f)},pt:u.ptm("pcToggleButton")},We({_:2},[u.$slots.option?{name:"default",fn:v(function(){return[L(u.$slots,"option",{option:s,index:f},function(){return[o("span",I({ref_for:!0},u.ptm("pcToggleButton").label),y(r.getOptionLabel(s)),17)]})]}),key:"0"}:void 0]),1032,["modelValue","onLabel","offLabel","disabled","unstyled","size","readonly","onChange","pt"])}),128))],16,ju)}nt.render=Wu;const Hu=K({__name:"SelectButton",setup(u,{expose:t}){t();const e={theme:k({root:`inline-flex select-none rounded-md
        p-invalid:outline p-invalid:outline-offset-0 p-invalid:outline-red-400 dark:p-invalid:outline-red-300`,pcToggleButton:{root:`inline-flex items-center justify-center overflow-hidden relative cursor-pointer select-none grow
            border border-surface-100 dark:border-surface-950
            rounded-none first:rounded-s-md last:rounded-e-md
            bg-surface-100 dark:bg-surface-950
            text-surface-500 dark:text-surface-400
            p-checked:text-surface-700 dark:p-checked:text-surface-0
            text-base font-medium
            focus-visible:outline focus-visible:outline-1 focus-visible:outline-offset-2 focus-visible:outline-primary focus-visible:relative focus-visible:z-10
            disabled:cursor-default
            disabled:bg-surface-200 disabled:border-surface-200 disabled:text-surface-500
            disabled:dark:bg-surface-700 disabled:dark:border-surface-700 disabled:dark:text-surface-400
            p-invalid:border-red-400 dark:p-invalid:border-red-300
            transition-colors duration-200
            p-1 p-small:text-sm p-large:text-lg
        `,content:`relative flex-auto inline-flex items-center justify-center gap-2 py-1 px-3
            rounded-md transition-colors duration-200
            p-checked:bg-surface-0 dark:p-checked:bg-surface-800 p-checked:shadow-[0px_1px_2px_0px_rgba(0,0,0,0.02),0px_1px_2px_0px_rgba(0,0,0,0.04)]`,icon:"",label:""}}),get SelectButton(){return nt},get ptViewMerge(){return de}};return Object.defineProperty(e,"__isScriptSetup",{enumerable:!1,value:!0}),e}});function Qu(u,t,n,e,c,r){return l(),w(e.SelectButton,{unstyled:"",pt:e.theme,ptOptions:{mergeProps:e.ptViewMerge}},We({_:2},[q(u.$slots,(a,s)=>({name:s,fn:v(f=>[L(u.$slots,s,qt(Ot(f??{})))])}))]),1032,["pt","ptOptions"])}const ot=R(Hu,[["render",Qu]]),Gu=K({__name:"EquipmentSelector",props:{modelValue:{}},emits:["update:modelValue"],setup(u,{expose:t,emit:n}){t();const e=u,c=n,{equipmentModels:r,brands:a}=fe(),s=[{label:"Создать новую модель",value:!1},{label:"Найти существующую",value:!0}],f=k([]),b=k([]),x=H({get:()=>e.modelValue,set:T=>c("update:modelValue",T)}),z={props:e,emit:c,equipmentModels:r,brands:a,equipmentTypeOptions:s,equipmentSuggestions:f,brandSuggestions:b,modelValue:x,searchEquipmentModels:async(T,S)=>{const C=T.query.toLowerCase(),F=await r.findMany({where:{name:{contains:C,mode:"insensitive"}},include:{brand:!0},take:10});F&&Array.isArray(F)&&(f.value=F)},searchBrands:async T=>{const S=T.query.toLowerCase(),C=await a.findMany({where:{name:{contains:S,mode:"insensitive"}},take:10});C&&Array.isArray(C)&&(b.value=C)},addEquipment:()=>{const T={name:"",selectedBrand:null,isExisting:!1,existingEquipmentModel:null,notes:""};x.value=[...x.value,T]},removeEquipment:T=>{const S=[...x.value];S.splice(T,1),x.value=S},VButton:me,VInputText:ge,VAutoComplete:ve,VSelectButton:ot,get PlusIcon(){return Ye}};return Object.defineProperty(z,"__isScriptSetup",{enumerable:!1,value:!0}),z}}),Xu={class:"equipment-selector"},Ju={class:"flex items-center justify-between mb-4"},Yu={key:0,class:"text-center py-8 text-surface-500"},Zu={class:"flex items-center justify-between mb-4"},$u={class:"font-medium text-surface-900 dark:text-surface-0"},ea={class:"mb-4"},ta={key:0,class:"mb-4"},ua={class:"flex items-center gap-2"},aa={class:"flex-1"},na={class:"font-medium"},oa={key:0,class:"text-sm text-surface-600"},ra={key:1,class:"space-y-4"},sa={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},la={class:"mt-4"};function ia(u,t,n,e,c,r){return l(),p("div",Xu,[o("div",Ju,[t[1]||(t[1]=o("h3",{class:"text-lg font-medium text-surface-900 dark:text-surface-0"}," Применимость к технике ",-1)),m(e.VButton,{onClick:e.addEquipment,outlined:"",size:"small"},{default:v(()=>[t[0]||(t[0]=A(" Добавить технику ")),m(e.PlusIcon,{class:"h-4 w-4"})]),_:1,__:[0]})]),e.modelValue.length===0?(l(),p("div",Yu," Добавьте модели техники, к которым применима эта запчасть ")):_("",!0),(l(!0),p(N,null,q(e.modelValue,(a,s)=>(l(),p("div",{key:s,class:"border border-surface-200 dark:border-surface-700 rounded-lg p-4 mb-4"},[o("div",Zu,[o("h4",$u," Техника "+y(s+1),1),m(e.VButton,{onClick:f=>e.removeEquipment(s),severity:"danger",size:"small",text:""},{default:v(()=>t[2]||(t[2]=[A(" Удалить ")])),_:2,__:[2]},1032,["onClick"])]),o("div",ea,[t[3]||(t[3]=o("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Тип добавления ",-1)),m(e.VSelectButton,{modelValue:a.isExisting,"onUpdate:modelValue":f=>a.isExisting=f,options:e.equipmentTypeOptions,"option-label":"label","option-value":"value",class:"w-full"},null,8,["modelValue","onUpdate:modelValue"])]),a.isExisting?(l(),p("div",ta,[t[4]||(t[4]=o("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Поиск модели техники * ",-1)),m(e.VAutoComplete,{modelValue:a.existingEquipmentModel,"onUpdate:modelValue":f=>a.existingEquipmentModel=f,suggestions:e.equipmentSuggestions,onComplete:f=>e.searchEquipmentModels(f,s),"option-label":"name",placeholder:"Введите название модели для поиска...",class:"w-full",dropdown:""},{option:v(({option:f})=>[o("div",ua,[o("div",aa,[o("div",na,y(f.name),1),f.brand?(l(),p("div",oa,y(f.brand.name),1)):_("",!0)])])]),_:2},1032,["modelValue","onUpdate:modelValue","suggestions","onComplete"])])):(l(),p("div",ra,[o("div",sa,[o("div",null,[t[5]||(t[5]=o("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Название модели * ",-1)),m(e.VInputText,{modelValue:a.name,"onUpdate:modelValue":f=>a.name=f,placeholder:"Например: CAT 320D",class:"w-full p-3"},null,8,["modelValue","onUpdate:modelValue"])]),o("div",null,[t[6]||(t[6]=o("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Бренд ",-1)),m(e.VAutoComplete,{modelValue:a.selectedBrand,"onUpdate:modelValue":f=>a.selectedBrand=f,suggestions:e.brandSuggestions,onComplete:e.searchBrands,"option-label":"name",placeholder:"Поиск бренда...",class:"w-full",dropdown:""},null,8,["modelValue","onUpdate:modelValue","suggestions"])])])])),o("div",la,[t[7]||(t[7]=o("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Примечания ",-1)),m(e.VInputText,{modelValue:a.notes,"onUpdate:modelValue":f=>a.notes=f,placeholder:"Дополнительная информация о применимости к данной технике",class:"w-full p-3"},null,8,["modelValue","onUpdate:modelValue"])])]))),128))])}const da=R(Gu,[["render",ia]]),ca=K({__name:"CatalogItemWizardEditor",props:{modelValue:{}},emits:["update:modelValue"],setup(u,{expose:t,emit:n}){t();const e=u,c=n,{catalogItems:r,brands:a}=fe(),s=k([]),f=k([]),b=k(!1),F={props:e,emit:c,catalogItems:r,brands:a,catalogItemSuggestions:s,brandSuggestions:f,showCreateBrand:b,accuracyOptions:[{label:"Точное совпадение",value:"EXACT_MATCH"},{label:"Совпадение с примечаниями",value:"MATCH_WITH_NOTES"},{label:"Требует доработки",value:"REQUIRES_MODIFICATION"},{label:"Частичное совпадение",value:"PARTIAL_MATCH"}],itemTypeOptions:[{label:"Создать новую позицию",value:!1},{label:"Выбрать существующую",value:!0}],addCatalogItem:()=>{const E={sku:"",brandId:"",selectedBrand:null,description:"",isExisting:!1,existingCatalogItem:null,accuracy:"EXACT_MATCH",notes:""},B=[...e.modelValue,E];c("update:modelValue",B)},removeCatalogItem:E=>{const B=e.modelValue.filter((D,M)=>M!==E);c("update:modelValue",B)},getDisplayLabel:E=>E?typeof E=="object"?`${E.sku} (${E.brand?.name||"Без бренда"})`:E:"",onItemSelect:(E,B)=>{const D=[...e.modelValue];typeof B=="object"&&(D[E].existingCatalogItem=B),c("update:modelValue",D)},searchCatalogItems:async E=>{try{const B=E.query.toLowerCase(),D=await r.findMany({where:{OR:[{sku:{contains:B,mode:"insensitive"}},{brand:{name:{contains:B,mode:"insensitive"}}}]},include:{brand:!0},take:10});D&&(s.value=D.map(M=>({...M,displayLabel:`${M.sku} (${M.brand?.name||"Без бренда"})`})))}catch(B){console.error("Ошибка поиска каталожных позиций:",B)}},searchBrands:async E=>{try{const B=E.query.toLowerCase(),D=await a.findMany({where:{name:{contains:B,mode:"insensitive"}},take:10});D&&(f.value=D)}catch(B){console.error("Ошибка поиска брендов:",B)}},onBrandCreated:E=>{f.value=[E,...f.value]},VCard:He,VButton:me,VTag:Kt,VSelectButton:ot,VAutoComplete:ve,VInputText:ge,VTextarea:Ge,VSelect:jt,QuickCreateBrand:Xe,Icon:Ze,get PlusIcon(){return Ye}};return Object.defineProperty(F,"__isScriptSetup",{enumerable:!1,value:!0}),F}}),pa={class:"catalog-item-wizard-editor"},ba={class:"flex items-center justify-between mb-4"},fa={key:0,class:"text-center py-10"},ma={key:1,class:"space-y-4"},ga={class:"p-4"},va={class:"flex items-center justify-between mb-4"},ha={class:"flex items-center gap-3"},ya={class:"mb-4"},xa={key:0,class:"mb-4"},Ea={class:"flex items-center gap-2"},ka={class:"font-mono font-medium"},_a={key:1,class:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4"},Ca={class:"flex gap-2"},Ba={class:"md:col-span-2"},wa={class:"grid grid-cols-1 md:grid-cols-2 gap-4"};function Da(u,t,n,e,c,r){const a=ce("tooltip");return l(),p("div",pa,[o("div",ba,[t[3]||(t[3]=o("h3",{class:"text-lg font-medium text-surface-900 dark:text-surface-0"}," Каталожные позиции ",-1)),m(e.VButton,{onClick:e.addCatalogItem,outlined:"",size:"small"},{default:v(()=>[t[2]||(t[2]=A(" Добавить ")),m(e.PlusIcon)]),_:1,__:[2]})]),n.modelValue.length===0?(l(),p("div",fa,[t[5]||(t[5]=o("div",{class:"text-surface-600 dark:text-surface-400 mb-4"}," Вы можете добавить каталожные позиции сейчас или пропустить этот шаг. ",-1)),m(e.VButton,{size:"small",onClick:e.addCatalogItem,outlined:""},{default:v(()=>[t[4]||(t[4]=A(" Добавить ")),m(e.PlusIcon)]),_:1,__:[4]})])):(l(),p("div",ma,[(l(!0),p(N,null,q(n.modelValue,(s,f)=>(l(),w(e.VCard,{key:f,class:"border border-surface-200 dark:border-surface-700"},{content:v(()=>[o("div",ga,[o("div",va,[o("div",ha,[m(e.VTag,{value:`Позиция ${f+1}`,severity:"secondary",size:"small"},null,8,["value"]),s.isExisting?(l(),w(e.VTag,{key:0,value:"Существующая",severity:"info",size:"small"})):(l(),w(e.VTag,{key:1,value:"Новая",severity:"success",size:"small"}))]),m(e.VButton,{onClick:b=>e.removeCatalogItem(f),severity:"danger",size:"small",text:""},{default:v(()=>[m(e.Icon,{name:"pi pi-trash",class:"w-5 h-5"})]),_:2},1032,["onClick"])]),o("div",ya,[m(e.VSelectButton,{modelValue:s.isExisting,"onUpdate:modelValue":b=>s.isExisting=b,options:e.itemTypeOptions,optionLabel:"label",optionValue:"value",class:"w-full md:w-auto"},null,8,["modelValue","onUpdate:modelValue"])]),s.isExisting?(l(),p("div",xa,[t[6]||(t[6]=o("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Поиск каталожной позиции * ",-1)),m(e.VAutoComplete,{"model-value":e.getDisplayLabel(s.existingCatalogItem),"onUpdate:modelValue":b=>e.onItemSelect(f,b),suggestions:e.catalogItemSuggestions,onComplete:e.searchCatalogItems,field:"displayLabel",placeholder:"Поиск по артикулу или бренду...",class:"w-full",dropdown:""},{option:v(({option:b})=>[o("div",Ea,[o("span",ka,y(b.sku),1),m(e.VTag,{value:b.brand?.name,severity:"secondary",size:"small"},null,8,["value"])])]),_:2},1032,["model-value","onUpdate:modelValue","suggestions"])])):(l(),p("div",_a,[o("div",null,[t[7]||(t[7]=o("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Артикул (SKU) * ",-1)),m(e.VInputText,{modelValue:s.sku,"onUpdate:modelValue":b=>s.sku=b,placeholder:"Например: 12345-ABC",class:"w-full"},null,8,["modelValue","onUpdate:modelValue"])]),o("div",null,[t[8]||(t[8]=o("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Бренд * ",-1)),o("div",Ca,[m(e.VAutoComplete,{modelValue:s.selectedBrand,"onUpdate:modelValue":b=>s.selectedBrand=b,suggestions:e.brandSuggestions,onComplete:e.searchBrands,"option-label":"name",placeholder:"Поиск бренда...",class:"flex-1",dropdown:""},null,8,["modelValue","onUpdate:modelValue","suggestions"]),$((l(),w(e.VButton,{onClick:t[0]||(t[0]=b=>e.showCreateBrand=!0),severity:"secondary",outlined:"",size:"small"},{default:v(()=>[m(e.Icon,{name:"pi pi-plus",class:"w-5 h-5"})]),_:1})),[[a,"Создать новый бренд"]])])]),o("div",Ba,[t[9]||(t[9]=o("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Описание ",-1)),m(e.VInputText,{modelValue:s.description,"onUpdate:modelValue":b=>s.description=b,placeholder:"Описание каталожной позиции...",class:"w-full"},null,8,["modelValue","onUpdate:modelValue"])])])),o("div",wa,[o("div",null,[t[10]||(t[10]=o("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Точность применимости * ",-1)),m(e.VSelect,{modelValue:s.accuracy,"onUpdate:modelValue":b=>s.accuracy=b,options:e.accuracyOptions,"option-label":"label","option-value":"value",placeholder:"Выберите точность",class:"w-full"},null,8,["modelValue","onUpdate:modelValue"])]),o("div",null,[t[11]||(t[11]=o("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Примечания ",-1)),m(e.VTextarea,{modelValue:s.notes,"onUpdate:modelValue":b=>s.notes=b,"auto-resize":!0,rows:"2",placeholder:"Дополнительные примечания...",class:"w-full"},null,8,["modelValue","onUpdate:modelValue"])])])])]),_:2},1024))),128))])),m(e.QuickCreateBrand,{visible:e.showCreateBrand,"onUpdate:visible":t[1]||(t[1]=s=>e.showCreateBrand=s),onCreated:e.onBrandCreated},null,8,["visible"])])}const Va=R(ca,[["render",Da]]),Aa=K({__name:"PartWizard",props:{part:{default:null},mode:{default:"create"}},emits:["created","updated"],setup(u,{expose:t,emit:n}){t();const e=u,c=n,r=[{title:"Основная информация",key:"basic"},{title:"Атрибуты",key:"attributes"},{title:"Каталожные позиции",key:"catalog"},{title:"Применимость к технике",key:"equipment"},{title:"Подтверждение",key:"confirm"}],a=r.length,s=k(1),f=H({get:()=>r[s.value-1]?.key,set:d=>{const g=r.findIndex(h=>h.key===d);g>=0&&(s.value=g+1)}}),b=k({name:"",attributes:[],catalogItems:[],equipmentApplicabilities:[]}),{loading:x,error:O,clearError:P,partCategories:Q,parts:j,catalogItems:z,equipmentModels:T,partAttributes:S,client:C,partApplicability:F,equipmentApplicability:E,media:B}=fe(),D=k(null),M=k([]),rt=k(!1),st=k(!1),he=k(!1),ue=k(null),ye=k(e.part||null),lt=H(()=>ye.value?.image?.url||null),W=k(null),J=k(!1),it=d=>{const g=d.target;g.files&&g.files[0]&&(W.value=g.files[0])},dt=async()=>{if(!(!e.part?.id||!W.value))try{J.value=!0;const d=await Ke(W.value);await B.uploadPartImage({partId:e.part.id,fileName:W.value.name,fileData:d,mimeType:W.value.type||"image/png"}),await Y(),W.value=null}finally{J.value=!1}},ct=async()=>{if(e.part?.id)try{J.value=!0,await B.deletePartImage({partId:e.part.id}),await Y()}finally{J.value=!1}},G=k(null),pt=d=>{const g=d.target;G.value=g.files||null},xe=k(!1),bt=async()=>{if(!(!e.part?.id||!G.value||G.value.length===0))try{xe.value=!0;for(const d of Array.from(G.value)){const g=await Ke(d);await B.uploadPartMedia({partId:e.part.id,fileName:d.name,fileData:g,mimeType:d.type||"application/octet-stream"})}await Y(),G.value=null}finally{xe.value=!1}},ft=async d=>{e.part?.id&&(await B.removePartMedia({partId:e.part.id,mediaId:d}),await Y())},Y=async()=>{if(!e.part?.id)return;const d=await C.crud.part.findUnique.query({where:{id:e.part.id},include:{image:!0,mediaAssets:!0,partCategory:!0}});ye.value=d},mt=d=>{D.value=d,M.value=[d,...M.value]},gt=async d=>{const g=d.query.toLowerCase(),h=await Q.findMany({where:{name:{contains:g,mode:"insensitive"}},take:10});h&&Array.isArray(h)&&(M.value=h)},Z=H(()=>{switch(s.value){case 1:return b.value.name.trim()&&D.value;case 2:return b.value.attributes.every(d=>{if(!!!(d.template?.isRequired||d.isRequired))return!0;const h=d.template?.dataType||d.templateDataType,X=d.value;return h==="BOOLEAN"?X!=null:String(X??"").trim().length>0});case 3:return b.value.catalogItems.every(d=>d.isExisting?d.existingCatalogItem&&d.accuracy:d.sku.trim()&&d.selectedBrand&&d.accuracy);case 4:return b.value.equipmentApplicabilities.every(d=>d.isExisting?d.existingEquipmentModel:d.name.trim());case 5:return!0;default:return!1}}),vt=H(()=>Z.value&&s.value===a),ht=()=>{s.value<a&&Z.value&&(s.value++,P())},yt=()=>{s.value>1&&(s.value--,P())},xt=d=>{const g=r.findIndex(h=>h.key===d)+1;g!==s.value&&(g>s.value&&!Z.value||(s.value=g,P()))},Et=async()=>{if(Z.value)try{he.value=!0,ue.value=null,P();const d={name:b.value.name,partCategoryId:typeof D.value=="object"?D.value.id:D.value,level:e.part?.level||0,path:e.part?.path||"/"};let g=null;if(e.mode==="edit"&&e.part?g=await j.update({where:{id:e.part.id},data:d}):g=await j.create({data:d}),!g||!g.id)throw new Error(`Не удалось ${e.mode==="edit"?"обновить":"создать"} запчасть`);const h=g.id,X=new Map;for(const i of b.value.attributes)i.templateId&&X.set(i.templateId,i);const ke=[],_e=[];X.forEach(i=>{const V=i.value;V==null||String(V).trim()===""||(i.id?_e.push({id:i.id,value:String(V).trim()}):ke.push({templateId:i.templateId,value:String(V).trim()}))});const ae=[];ke.length>0&&ae.push(S.bulkCreate({partId:h,attributes:ke})),_e.length>0&&ae.push(Promise.all(_e.map(i=>S.update({id:i.id,value:i.value})))),ae.length>0&&await Promise.all(ae);const ne=[],Ce=[];for(const i of b.value.catalogItems)i.isExisting&&i.existingCatalogItem&&ne.push({id:i.existingCatalogItem.id,accuracy:i.accuracy,notes:i.notes||void 0});const Bt=b.value.catalogItems.filter(i=>!i.isExisting).map(async i=>{let V=null;if(typeof i.selectedBrand=="object"&&i.selectedBrand?.id?V=i.selectedBrand.id:typeof i.selectedBrand=="number"?V=i.selectedBrand:typeof i.brandId=="number"&&(V=i.brandId),!V)return Ce.push(`Не выбран бренд для каталожной позиции ${i.sku}`),null;const U=await z.create({data:{sku:i.sku.toUpperCase().trim(),brandId:V,description:i.description||void 0,isPublic:!0}});return!U||!U.id?(Ce.push(`Не удалось создать каталожную позицию ${i.sku}`),null):{id:U.id,accuracy:i.accuracy,notes:i.notes||void 0}}),wt=await Promise.all(Bt);for(const i of wt)i&&ne.push(i);const Me=[],Dt=ne.map(i=>F.upsert({where:{partId_catalogItemId:{partId:h,catalogItemId:i.id}},update:{accuracy:i.accuracy,notes:i.notes},create:{partId:h,catalogItemId:i.id,accuracy:i.accuracy,notes:i.notes}}));(await Promise.all(Dt)).forEach((i,V)=>{i||Me.push(`Связь с каталожной позицией #${ne[V].id} не сохранена`)});const oe=[],qe=[];for(const i of b.value.equipmentApplicabilities)i.isExisting&&i.existingEquipmentModel&&oe.push({id:i.existingEquipmentModel.id,notes:i.notes||void 0});const Vt=b.value.equipmentApplicabilities.filter(i=>!i.isExisting).map(async i=>{const V={name:i.name.trim()};i.selectedBrand&&(V.brandId=typeof i.selectedBrand=="object"?i.selectedBrand.id:i.selectedBrand);const U=await T.create({data:V});return!U||!U.id?(qe.push(`Не удалось создать модель техники ${i.name}`),null):{id:U.id,notes:i.notes||void 0}}),At=await Promise.all(Vt);for(const i of At)i&&oe.push(i);const ze=[],Tt=oe.map(i=>E.upsert({where:{partId_equipmentModelId:{partId:h,equipmentModelId:i.id}},update:{notes:i.notes},create:{partId:h,equipmentModelId:i.id,notes:i.notes}}));(await Promise.all(Tt)).forEach((i,V)=>{i||ze.push(`Связь с техникой #${oe[V].id} не сохранена`)});const Ue=[...Ce,...Me,...qe,...ze].filter(Boolean);Ue.length>0&&(ue.value=Ue.join("; ")),e.mode==="edit"?c("updated",g):c("created",g),e.mode==="create"&&Se()}catch(d){console.error(`Ошибка ${e.mode==="edit"?"обновления":"создания"} запчасти:`,d),ue.value=d?.message||"Произошла ошибка при сохранении запчасти"}finally{he.value=!1}},Se=()=>{b.value={name:"",attributes:[],catalogItems:[],equipmentApplicabilities:[]},D.value=null,s.value=1},kt=d=>d?{STRING:"Строка",NUMBER:"Число",BOOLEAN:"Логическое",DATE:"Дата",JSON:"JSON"}[d]||d:"",_t=d=>d?{MM:"мм",INCH:"дюймы",FT:"футы",G:"г",KG:"кг",T:"т",LB:"фунты",ML:"мл",L:"л",GAL:"галлоны",PCS:"шт",SET:"комплект",PAIR:"пара",BAR:"бар",PSI:"PSI",KW:"кВт",HP:"л.с.",NM:"Н⋅м",RPM:"об/мин",C:"°C",F:"°F",PERCENT:"%"}[d]||d:"",Ct=d=>({EXACT_MATCH:"Точное совпадение",MATCH_WITH_NOTES:"С примечаниями",REQUIRES_MODIFICATION:"Требует доработки",PARTIAL_MATCH:"Частичное совпадение"})[d]||d,Fe=async d=>{try{const g=await S.findByPartId({partId:d});g&&Array.isArray(g)&&(b.value.attributes=g.map(h=>({id:h.id,templateId:h.templateId,value:h.value,template:h.template,templateTitle:h.template?.title,templateDataType:h.template?.dataType,templateUnit:h.template?.unit,templateGroup:h.template?.group?.name,templateDescription:h.template?.description})))}catch(g){console.warn("Не удалось загрузить атрибуты запчасти:",g)}},Pe=async d=>{try{const g=await C.crud.partApplicability.findMany.query({where:{partId:d},include:{catalogItem:{include:{brand:!0}}}});g&&(b.value.catalogItems=g.map(h=>({isExisting:!0,existingCatalogItem:h.catalogItem,accuracy:h.accuracy,notes:h.notes||"",sku:"",brandId:"",selectedBrand:null,description:""})))}catch(g){console.warn("Не удалось загрузить каталожные позиции:",g)}},Le=async d=>{try{const g=await C.crud.equipmentApplicability.findMany.query({where:{partId:d},include:{equipmentModel:{include:{brand:!0}}}});g&&(b.value.equipmentApplicabilities=g.map(h=>({isExisting:!0,existingEquipmentModel:h.equipmentModel,notes:h.notes||"",name:"",selectedBrand:null})))}catch(g){console.warn("Не удалось загрузить применимость к технике:",g)}},Ee=async()=>{e.part&&e.mode==="edit"&&(b.value.name=e.part.name||"",e.part.partCategory&&(D.value=e.part.partCategory),e.part.id&&await Promise.all([Fe(e.part.id),Pe(e.part.id),Le(e.part.id)]))};je(()=>e.part,async()=>{e.mode==="edit"&&await Ee()},{immediate:!0}),Mt(async()=>{e.mode==="edit"&&e.part&&await Ee()});const Oe={props:e,emit:c,steps:r,totalSteps:a,currentStep:s,activeStepKey:f,formData:b,loading:x,error:O,clearError:P,partCategories:Q,parts:j,catalogItems:z,equipmentModels:T,partAttributes:S,client:C,partApplicability:F,equipmentApplicability:E,media:B,selectedCategory:D,categorySuggestions:M,showCreateCategory:rt,showCreateBrand:st,saving:he,saveError:ue,currentPart:ye,partImageUrl:lt,selectedFile:W,uploading:J,onSelectPartImage:it,uploadPartImage:dt,removePartImage:ct,selectedMediaFiles:G,onSelectPartMedia:pt,uploadingMedia:xe,uploadPartMedia:bt,removePartMedia:ft,refreshCurrentPart:Y,onCategoryCreated:mt,searchCategories:gt,canProceed:Z,canFinish:vt,nextStep:ht,previousStep:yt,onTabChange:xt,savePart:Et,resetForm:Se,getDataTypeLabel:kt,getUnitLabel:_t,getAccuracyLabel:Ct,loadExistingAttributes:Fe,loadExistingCatalogItems:Pe,loadExistingEquipmentApplicabilities:Le,loadPartData:Ee,VTabs:$t,VTabList:cu,VTab:hu,VCard:He,VButton:me,VInputText:ge,VAutoComplete:ve,VMessage:Qe,QuickCreateCategory:Cu,QuickCreateBrand:Xe,AttributeManager:Rt,EquipmentSelector:da,CatalogItemWizardEditor:Va,get resolveMediaUrl(){return Wt},Icon:Ze};return Object.defineProperty(Oe,"__isScriptSetup",{enumerable:!1,value:!0}),Oe}}),Ta={class:"part-wizard"},Ia={class:"flex items-center justify-between p-6 border-b border-surface-200 dark:border-surface-700"},Sa={class:"flex items-center gap-2 text-sm text-surface-600 dark:text-surface-400"},Fa={class:"p-6"},Pa={class:"mb-4"},La={class:"text-sm"},Oa={class:"min-h-96"},Ma={key:0,class:"space-y-6"},qa={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},za={class:"flex gap-2"},Ua={key:0,class:"mt-4 space-y-4"},Na={class:"flex items-center gap-4"},Ra={class:"w-32 h-32 border rounded bg-surface-50 dark:bg-surface-900 flex items-center justify-center overflow-hidden"},Ka=["src"],ja={key:1,class:"text-surface-500 text-sm"},Wa={class:"flex flex-col gap-2"},Ha={class:"flex gap-2"},Qa={key:0,class:"text-surface-500 text-xs"},Ga={class:"flex items-center gap-4"},Xa={class:"flex-1 grid grid-cols-2 gap-3"},Ja=["src","alt"],Ya=["href"],Za={class:"text-sm truncate"},$a={class:"p-2 border-t flex justify-end"},en={class:"flex flex-col gap-2 w-64"},tn={key:0,class:"text-surface-500 text-xs"},un={key:1,class:"space-y-6"},an={key:2,class:"space-y-6"},nn={key:3,class:"space-y-6"},on={key:4,class:"space-y-6"},rn={class:"bg-surface-50 dark:bg-surface-900 rounded-lg p-6"},sn={class:"space-y-2"},ln={class:"flex"},dn={class:"text-surface-900 dark:text-surface-0"},cn={class:"flex"},pn={class:"text-surface-900 dark:text-surface-0"},bn={key:0,class:"bg-surface-50 dark:bg-surface-900 rounded-lg p-6"},fn={class:"font-medium text-surface-900 dark:text-surface-0 mb-4"},mn={class:"space-y-3"},gn={class:"flex-1"},vn={class:"font-medium text-surface-900 dark:text-surface-0"},hn={key:0,class:"text-red-500 ml-1"},yn={class:"text-sm text-surface-600 dark:text-surface-400"},xn={key:0,class:"ml-2 px-2 py-1 bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 rounded text-xs"},En={class:"text-xs text-surface-500 dark:text-surface-400"},kn={class:"bg-surface-50 dark:bg-surface-900 rounded-lg p-6"},_n={class:"font-medium text-surface-900 dark:text-surface-0 mb-4"},Cn={class:"space-y-3"},Bn={class:"flex-1"},wn={class:"flex items-center gap-2"},Dn={class:"font-medium"},Vn={class:"text-surface-600 dark:text-surface-400"},An={key:0,class:"px-2 py-1 bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded text-xs"},Tn={key:1,class:"px-2 py-1 bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded text-xs"},In={class:"text-sm text-surface-600 dark:text-surface-400 mt-1"},Sn={key:0,class:"ml-2"},Fn={key:1,class:"bg-surface-50 dark:bg-surface-900 rounded-lg p-6"},Pn={class:"font-medium text-surface-900 dark:text-surface-0 mb-4"},Ln={class:"space-y-3"},On={class:"flex-1"},Mn={class:"flex items-center gap-2"},qn={class:"font-medium"},zn={key:0,class:"text-surface-600 dark:text-surface-400"},Un={key:1,class:"px-2 py-1 bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded text-xs"},Nn={key:2,class:"px-2 py-1 bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded text-xs"},Rn={key:0,class:"text-sm text-surface-600 dark:text-surface-400 mt-1"},Kn={class:"flex items-center justify-between mt-8 pt-6 border-t border-surface-200 dark:border-surface-700"},jn={key:1},Wn={class:"flex gap-3"};function Hn(u,t,n,e,c,r){return l(),p("div",Ta,[m(e.VCard,null,{header:v(()=>[o("div",Ia,[t[8]||(t[8]=o("h2",{class:"text-xl font-semibold text-surface-900 dark:text-surface-0"}," Мастер создания запчасти ",-1)),o("div",Sa," Шаг "+y(e.currentStep)+" из "+y(e.totalSteps),1)])]),content:v(()=>[o("div",Fa,[o("div",Pa,[m(e.VTabs,{value:e.activeStepKey,"onUpdate:value":e.onTabChange},{default:v(()=>[m(e.VTabList,null,{default:v(()=>[(l(),p(N,null,q(e.steps,(a,s)=>m(e.VTab,{key:a.key,value:a.key},{default:v(()=>[o("span",La,y(s+1)+". "+y(a.title),1)]),_:2},1032,["value"])),64))]),_:1})]),_:1},8,["value"])]),o("div",Oa,[e.currentStep===1?(l(),p("div",Ma,[t[18]||(t[18]=o("h3",{class:"text-lg font-medium text-surface-900 dark:text-surface-0 mb-4"}," Основная информация о запчасти ",-1)),o("div",qa,[o("div",null,[t[9]||(t[9]=o("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Название запчасти * ",-1)),m(e.VInputText,{modelValue:e.formData.name,"onUpdate:modelValue":t[0]||(t[0]=a=>e.formData.name=a),placeholder:"Например: Сальник коленвала передний",class:"w-full p-3"},null,8,["modelValue"])]),o("div",null,[t[11]||(t[11]=o("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Категория * ",-1)),o("div",za,[m(e.VAutoComplete,{modelValue:e.selectedCategory,"onUpdate:modelValue":t[1]||(t[1]=a=>e.selectedCategory=a),suggestions:e.categorySuggestions,onComplete:e.searchCategories,"option-label":"name",placeholder:"Поиск категории...",class:"flex-1",dropdown:""},null,8,["modelValue","suggestions"]),m(e.VButton,{onClick:t[2]||(t[2]=a=>e.showCreateCategory=!0),severity:"secondary",outlined:"",size:"small",label:"Создать новую категорию"},{default:v(()=>t[10]||(t[10]=[A(" + ")])),_:1,__:[10]})])])]),n.mode==="edit"&&e.props.part?(l(),p("div",Ua,[o("div",null,[t[14]||(t[14]=o("h4",{class:"text-surface-900 dark:text-surface-0 mb-2 font-medium"},"Основное изображение",-1)),o("div",Na,[o("div",Ra,[e.partImageUrl?(l(),p("img",{key:0,src:e.resolveMediaUrl(e.partImageUrl),alt:"Изображение запчасти",class:"object-cover w-full h-full"},null,8,Ka)):(l(),p("span",ja,"Нет изображения"))]),o("div",Wa,[o("input",{type:"file",accept:"image/*",onChange:e.onSelectPartImage},null,32),o("div",Ha,[m(e.VButton,{size:"small",disabled:!e.selectedFile||e.uploading,onClick:e.uploadPartImage},{default:v(()=>t[12]||(t[12]=[A("Загрузить")])),_:1,__:[12]},8,["disabled"]),m(e.VButton,{size:"small",severity:"danger",outlined:"",disabled:!e.partImageUrl||e.uploading,onClick:e.removePartImage},{default:v(()=>t[13]||(t[13]=[A("Удалить")])),_:1,__:[13]},8,["disabled"])]),e.uploading?(l(),p("div",Qa,"Загрузка...")):_("",!0)])])]),o("div",null,[t[17]||(t[17]=o("h4",{class:"text-surface-900 dark:text-surface-0 mb-2 font-medium"},"Дополнительные медиа (изображения, PDF)",-1)),o("div",Ga,[o("div",Xa,[(l(!0),p(N,null,q(e.currentPart?.mediaAssets||[],a=>(l(),p("div",{key:a.id,class:"border rounded overflow-hidden"},[a.mimeType?.startsWith("image/")?(l(),p("img",{key:0,src:e.resolveMediaUrl(a.url),alt:a.fileName,class:"w-full h-32 object-cover"},null,8,Ja)):(l(),p("a",{key:1,href:e.resolveMediaUrl(a.url),target:"_blank",class:"flex items-center gap-2 p-2 hover:bg-surface-50 dark:hover:bg-surface-900"},[m(e.Icon,{name:"pi pi-file-pdf",class:"text-red-600 w-4 h-4"}),o("span",Za,y(a.fileName),1)],8,Ya)),o("div",$a,[m(e.VButton,{size:"small",severity:"danger",text:"",onClick:s=>e.removePartMedia(a.id)},{default:v(()=>t[15]||(t[15]=[A("Удалить")])),_:2,__:[15]},1032,["onClick"])])]))),128))]),o("div",en,[o("input",{type:"file",multiple:"",accept:"image/*,application/pdf",onChange:e.onSelectPartMedia},null,32),m(e.VButton,{size:"small",disabled:!e.selectedMediaFiles||e.uploadingMedia,onClick:e.uploadPartMedia},{default:v(()=>t[16]||(t[16]=[A("Загрузить выбранные")])),_:1,__:[16]},8,["disabled"]),e.uploadingMedia?(l(),p("div",tn,"Загрузка...")):_("",!0)])])])])):_("",!0)])):_("",!0),e.currentStep===2?(l(),p("div",un,[m(e.AttributeManager,{modelValue:e.formData.attributes,"onUpdate:modelValue":t[3]||(t[3]=a=>e.formData.attributes=a)},null,8,["modelValue"])])):_("",!0),e.currentStep===3?(l(),p("div",an,[m(e.CatalogItemWizardEditor,{modelValue:e.formData.catalogItems,"onUpdate:modelValue":t[4]||(t[4]=a=>e.formData.catalogItems=a)},null,8,["modelValue"])])):_("",!0),e.currentStep===4?(l(),p("div",nn,[m(e.EquipmentSelector,{modelValue:e.formData.equipmentApplicabilities,"onUpdate:modelValue":t[5]||(t[5]=a=>e.formData.equipmentApplicabilities=a)},null,8,["modelValue"])])):_("",!0),e.currentStep===5?(l(),p("div",on,[t[22]||(t[22]=o("h3",{class:"text-lg font-medium text-surface-900 dark:text-surface-0 mb-4"}," Подтверждение создания ",-1)),o("div",rn,[t[21]||(t[21]=o("h4",{class:"font-medium text-surface-900 dark:text-surface-0 mb-4"}," Основная информация: ",-1)),o("dl",sn,[o("div",ln,[t[19]||(t[19]=o("dt",{class:"w-32 text-surface-600 dark:text-surface-400"}," Название: ",-1)),o("dd",dn,y(e.formData.name),1)]),o("div",cn,[t[20]||(t[20]=o("dt",{class:"w-32 text-surface-600 dark:text-surface-400"}," Категория: ",-1)),o("dd",pn,y(e.selectedCategory?.name),1)])])]),e.formData.attributes.length>0?(l(),p("div",bn,[o("h4",fn," Атрибуты ("+y(e.formData.attributes.length)+"): ",1),o("div",mn,[(l(!0),p(N,null,q(e.formData.attributes,(a,s)=>(l(),p("div",{key:s,class:"flex items-center justify-between p-3 bg-surface-0 dark:bg-surface-800 rounded border"},[o("div",gn,[o("div",vn,[A(y(a.template?.title||a.templateTitle)+" ",1),a.template?.isRequired?(l(),p("span",hn,"*")):_("",!0)]),o("div",yn,[A(y(a.value)+y(a.template?.unit||a.templateUnit?`
                        ${e.getUnitLabel(a.template?.unit||a.templateUnit)}`:"")+" ",1),a.template?.group?.name||a.templateGroup?(l(),p("span",xn,y(a.template?.group?.name||a.templateGroup),1)):_("",!0)])]),o("div",En,y(e.getDataTypeLabel(a.template?.dataType||a.templateDataType)),1)]))),128))])])):_("",!0),o("div",kn,[o("h4",_n," Каталожные позиции ("+y(e.formData.catalogItems.length)+"): ",1),o("div",Cn,[(l(!0),p(N,null,q(e.formData.catalogItems,(a,s)=>(l(),p("div",{key:s,class:"flex items-center justify-between p-3 bg-surface-0 dark:bg-surface-950 rounded border"},[o("div",Bn,[o("div",wn,[o("span",Dn,y(a.isExisting?a.existingCatalogItem?.sku:a.sku),1),o("span",Vn," ("+y(a.isExisting?a.existingCatalogItem?.brand?.name:a.selectedBrand?.name)+") ",1),a.isExisting?(l(),p("span",An," Существующая ")):(l(),p("span",Tn," Новая "))]),o("div",In,[A(" Точность: "+y(e.getAccuracyLabel(a.accuracy))+" ",1),a.notes?(l(),p("span",Sn,"• "+y(a.notes),1)):_("",!0)])])]))),128))])]),e.formData.equipmentApplicabilities.length>0?(l(),p("div",Fn,[o("h4",Pn," Применимость к технике ("+y(e.formData.equipmentApplicabilities.length)+"): ",1),o("div",Ln,[(l(!0),p(N,null,q(e.formData.equipmentApplicabilities,(a,s)=>(l(),p("div",{key:s,class:"flex items-center justify-between p-3 bg-surface-0 dark:bg-surface-950 rounded border"},[o("div",On,[o("div",Mn,[o("span",qn,y(a.isExisting?a.existingEquipmentModel?.name:a.name),1),a.selectedBrand||a.existingEquipmentModel?.brand?(l(),p("span",zn," ("+y(a.selectedBrand?.name||a.existingEquipmentModel?.brand?.name)+") ",1)):_("",!0),a.isExisting?(l(),p("span",Un," Существующая ")):(l(),p("span",Nn," Новая "))]),a.notes?(l(),p("div",Rn,y(a.notes),1)):_("",!0)])]))),128))])])):_("",!0)])):_("",!0)]),o("div",Kn,[e.currentStep>1?(l(),w(e.VButton,{key:0,onClick:e.previousStep,severity:"secondary",outlined:""},{default:v(()=>t[23]||(t[23]=[A(" Назад ")])),_:1,__:[23]})):(l(),p("div",jn)),o("div",Wn,[e.currentStep<e.totalSteps?(l(),w(e.VButton,{key:0,onClick:e.nextStep,disabled:!e.canProceed,label:"Далее",outlined:""},null,8,["disabled"])):(l(),w(e.VButton,{key:1,label:n.mode==="edit"?"Сохранить изменения":"Создать запчасть",onClick:e.savePart,loading:e.loading||e.saving,disabled:!e.canFinish||e.saving},null,8,["label","loading","disabled"]))])])])]),_:1}),e.error?(l(),w(e.VMessage,{key:0,severity:"error",class:"mt-4"},{default:v(()=>[A(y(e.error),1)]),_:1})):_("",!0),e.saveError?(l(),w(e.VMessage,{key:1,severity:"error",class:"mt-4"},{default:v(()=>[A(y(e.saveError),1)]),_:1})):_("",!0),m(e.QuickCreateCategory,{visible:e.showCreateCategory,"onUpdate:visible":t[6]||(t[6]=a=>e.showCreateCategory=a),onCreated:e.onCategoryCreated},null,8,["visible"]),m(e.QuickCreateBrand,{visible:e.showCreateBrand,"onUpdate:visible":t[7]||(t[7]=a=>e.showCreateBrand=a)},null,8,["visible"])])}const No=R(Aa,[["render",Hn]]);export{No as default};
