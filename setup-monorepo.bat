@echo off
echo === Настройка монорепозитория Parttec ===

REM Проверяем, что мы в правильной директории
if not exist ".gitmodules" (
    echo Ошибка: Запустите скрипт из корня репозитория parttec
    exit /b 1
)

echo 1. Отправляем код cpanel в его репозиторий...
cd cpanel
if not exist ".git" (
    echo Ошибка: cpanel не является git репозиторием
    exit /b 1
)

REM Проверяем, есть ли удаленный репозиторий
git remote get-url origin >nul 2>&1
if errorlevel 1 (
    echo Добавляем удаленный репозиторий для cpanel...
    git remote add origin https://github.com/xferqr/parttec3-cpanel.git
)

echo Отправляем cpanel в GitHub...
git push -u origin master

cd ..

echo 2. Отправляем код site в его репозиторий...
cd site
if not exist ".git" (
    echo Ошибка: site не является git репозиторием
    exit /b 1
)

REM Проверяем, есть ли удаленный репозиторий
git remote get-url origin >nul 2>&1
if errorlevel 1 (
    echo Добавляем удаленный репозиторий для site...
    git remote add origin https://github.com/xferqr/parttec3-site.git
)

echo Отправляем site в GitHub...
git push -u origin master

cd ..

echo 3. Удаляем локальные папки из git индекса...
git rm -r --cached cpanel site 2>nul

echo 4. Временно переименовываем папки...
move cpanel cpanel_temp
move site site_temp

echo 5. Добавляем подмодули...
git submodule add https://github.com/xferqr/parttec3-cpanel.git cpanel
git submodule add https://github.com/xferqr/parttec3-site.git site

echo 6. Инициализируем подмодули...
git submodule update --init --recursive

echo 7. Удаляем временные папки...
rmdir /s /q cpanel_temp
rmdir /s /q site_temp

echo 8. Коммитим изменения в основном репозитории...
git add .
git commit -m "Setup monorepo with submodules"

echo 9. Отправляем изменения в основной репозиторий...
git push origin master

echo === Монорепозиторий успешно настроен! ===
echo.
echo Структура:
echo   /api/    - API сервер (подмодуль)
echo   /cpanel/ - Админ панель (подмодуль)
echo   /site/   - Основной сайт (подмодуль)
echo.
echo Для работы с подмодулями используйте:
echo   git submodule update --remote  # Обновить все подмодули
echo   cd ^<submodule^> ^&^& git checkout main  # Переключиться на ветку в подмодуле

pause
