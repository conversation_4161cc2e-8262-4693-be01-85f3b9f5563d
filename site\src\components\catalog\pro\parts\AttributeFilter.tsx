"use client"

import { useState } from "react"
import { ChevronDown, ChevronUp, Search } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { <PERSON>lider } from "@/components/ui/slider"
import { Badge } from "@/components/ui/badge"
import { ModernInput } from "@/components/ui/modern-input"
import type { AttributeTemplate } from "@/types/catalog"

interface AttributeFilterProps {
  template: AttributeTemplate
  selectedValues: string[]
  numericRange?: [number, number]
  onValuesChange: (values: string[]) => void
  onRangeChange: (range: [number, number]) => void
  availableValues: string[]
  numericStats?: { min: number; max: number; avg: number }
}

export function AttributeFilter({ template, selectedValues, numericRange, onValuesChange, onRangeChange, availableValues, numericStats, }: AttributeFilterProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")

  const handleValueToggle = (value: string) => {
    if (selectedValues.includes(value)) onValuesChange(selectedValues.filter((v) => v !== value))
    else onValuesChange([...selectedValues, value])
  }

  const isNumeric = template.dataType === "NUMBER"
  const hasActiveFilters = selectedValues.length > 0 || (numericRange && numericStats && (numericRange[0] > numericStats.min || numericRange[1] < numericStats.max))
  const filteredValues = availableValues.filter((value) => value.toLowerCase().includes(searchQuery.toLowerCase()))

  return (
    <div className="border-2 border-border-strong rounded-lg bg-card animate-theme-transition">
      <Button variant="ghost" onClick={() => setIsExpanded(!isExpanded)} className="w-full justify-between p-4 text-foreground hover:bg-accent/50 transition-colors">
        <div className="flex items-center gap-2">
          <span className="font-medium">{template.title}</span>
          {template.unit && (<Badge variant="outline" className="text-xs border-border text-muted-foreground">{template.unit}</Badge>)}
          {hasActiveFilters && (<Badge variant="default" className="text-xs bg-primary text-primary-foreground">{selectedValues.length || "range"}</Badge>)}
        </div>
        {isExpanded ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
      </Button>

      {isExpanded && (
        <div className="p-4 pt-0 space-y-3 border-t border-border">
          {template.description && <p className="text-xs text-muted-foreground">{template.description}</p>}

          {isNumeric && numericStats ? (
            <div className="space-y-3">
              <div className="flex justify-between text-sm text-muted-foreground">
                <span>Диапазон: {numericStats.min} - {numericStats.max}</span>
                <span>Среднее: {numericStats.avg.toFixed(1)}</span>
              </div>
              <div className="space-y-2">
                <Label className="text-sm text-foreground">Выбранный диапазон: {numericRange?.[0] || numericStats.min} - {numericRange?.[1] || numericStats.max}</Label>
                <Slider value={numericRange || [numericStats.min, numericStats.max]} onValueChange={(value) => onRangeChange([value[0], value[1]])} min={numericStats.min} max={numericStats.max} step={template.tolerance || 0.1} className="w-full" />
              </div>
              {template.tolerance && (<p className="text-xs text-muted-foreground">Допуск: ±{template.tolerance} {template.unit}</p>)}
            </div>
          ) : (
            <div className="space-y-3">
              {availableValues.length > 5 && (
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 h-3 w-3 -translate-y-1/2 text-muted-foreground" />
                  <ModernInput placeholder="Поиск значений..." value={searchQuery} onChange={(e) => setSearchQuery(e.target.value)} className="pl-9 h-8 text-sm" variant="ghost" />
                </div>
              )}

              <div className="space-y-2 max-h-48 overflow-y-auto">
                {filteredValues.map((value) => (
                  <div key={value} className="flex items-center space-x-2 group">
                    <Checkbox id={`${template.id}-${value}`} checked={selectedValues.includes(value)} onCheckedChange={() => handleValueToggle(value)} className="border-border data-[state=checked]:bg-primary data-[state=checked]:border-primary" />
                    <Label htmlFor={`${template.id}-${value}`} className="text-sm text-foreground cursor-pointer flex-1 group-hover:text-primary transition-colors">{value}</Label>
                  </div>
                ))}
                {filteredValues.length === 0 && searchQuery && (<p className="text-sm text-muted-foreground text-center py-2">Ничего не найдено по запросу "{searchQuery}"</p>)}
                {availableValues.length === 0 && (<p className="text-sm text-muted-foreground text-center py-2">Нет доступных значений</p>)}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

