import{j as v}from"./jsx-runtime.D_zvdyIk.js";import{r as o,a as R,g as y}from"./index.0yr9KlQE.js";import{c as g}from"./utils.CBfrqCZ4.js";import{u as E,c as S}from"./index.BWNh2K4G.js";import{r as b}from"./index.ViApDAiE.js";const P=o.forwardRef(({className:t,variant:e="default",...n},r)=>v.jsx("div",{ref:r,className:g("rounded-md border transition-all duration-200 animate-theme-transition",{"bg-card text-card-foreground border-border shadow-professional hover:shadow-elevated hover:border-border-strong":e==="default","bg-card text-card-foreground border-border-strong shadow-elevated hover:shadow-strong":e==="elevated","glass-effect text-card-foreground border-border":e==="glass"},t),...n}));P.displayName="ModernCard";const A=o.forwardRef(({className:t,...e},n)=>v.jsx("div",{ref:n,className:g("flex flex-col space-y-1 p-4 pb-2",t),...e}));A.displayName="ModernCardHeader";const O=o.forwardRef(({className:t,...e},n)=>v.jsx("h3",{ref:n,className:g("font-semibold leading-none tracking-tight",t),...e}));O.displayName="ModernCardTitle";const T=o.forwardRef(({className:t,...e},n)=>v.jsx("p",{ref:n,className:g("text-sm text-muted-foreground leading-relaxed",t),...e}));T.displayName="ModernCardDescription";const j=o.forwardRef(({className:t,...e},n)=>v.jsx("div",{ref:n,className:g("p-4 pt-0",t),...e}));j.displayName="ModernCardContent";const D=o.forwardRef(({className:t,...e},n)=>v.jsx("div",{ref:n,className:g("flex items-center p-4 pt-0",t),...e}));D.displayName="ModernCardFooter";function J(t,e){const n=o.createContext(e),r=a=>{const{children:i,...c}=a,u=o.useMemo(()=>c,Object.values(c));return v.jsx(n.Provider,{value:u,children:i})};r.displayName=t+"Provider";function s(a){const i=o.useContext(n);if(i)return i;if(e!==void 0)return e;throw new Error(`\`${a}\` must be used within \`${t}\``)}return[r,s]}function K(t,e=[]){let n=[];function r(a,i){const c=o.createContext(i),u=n.length;n=[...n,i];const d=l=>{const{scope:p,children:m,...N}=l,C=p?.[t]?.[u]||c,h=o.useMemo(()=>N,Object.values(N));return v.jsx(C.Provider,{value:h,children:m})};d.displayName=a+"Provider";function f(l,p){const m=p?.[t]?.[u]||c,N=o.useContext(m);if(N)return N;if(i!==void 0)return i;throw new Error(`\`${l}\` must be used within \`${a}\``)}return[d,f]}const s=()=>{const a=n.map(i=>o.createContext(i));return function(c){const u=c?.[t]||a;return o.useMemo(()=>({[`__scope${t}`]:{...c,[t]:u}}),[c,u])}};return s.scopeName=t,[r,I(s,...e)]}function I(...t){const e=t[0];if(t.length===1)return e;const n=()=>{const r=t.map(s=>({useScope:s(),scopeName:s.scopeName}));return function(a){const i=r.reduce((c,{useScope:u,scopeName:d})=>{const l=u(a)[`__scope${d}`];return{...c,...l}},{});return o.useMemo(()=>({[`__scope${e.scopeName}`]:i}),[i])}};return n.scopeName=e.scopeName,n}function Q(t,e,{checkForDefaultPrevented:n=!0}={}){return function(s){if(t?.(s),n===!1||!s.defaultPrevented)return e?.(s)}}var M=globalThis?.document?o.useLayoutEffect:()=>{},U=R[" useInsertionEffect ".trim().toString()]||M;function X({prop:t,defaultProp:e,onChange:n=()=>{},caller:r}){const[s,a,i]=$({defaultProp:e,onChange:n}),c=t!==void 0,u=c?t:s;{const f=o.useRef(t!==void 0);o.useEffect(()=>{const l=f.current;l!==c&&console.warn(`${r} is changing from ${l?"controlled":"uncontrolled"} to ${c?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),f.current=c},[c,r])}const d=o.useCallback(f=>{if(c){const l=_(f)?f(t):f;l!==t&&i.current?.(l)}else a(f)},[c,t,a,i]);return[u,d]}function $({defaultProp:t,onChange:e}){const[n,r]=o.useState(t),s=o.useRef(n),a=o.useRef(e);return U(()=>{a.current=e},[e]),o.useEffect(()=>{s.current!==n&&(a.current?.(n),s.current=n)},[n,s]),[n,r,a]}function _(t){return typeof t=="function"}function F(t,e){return o.useReducer((n,r)=>e[n][r]??n,t)}var L=t=>{const{present:e,children:n}=t,r=W(e),s=typeof n=="function"?n({present:r.isPresent}):o.Children.only(n),a=E(r.ref,k(s));return typeof n=="function"||r.isPresent?o.cloneElement(s,{ref:a}):null};L.displayName="Presence";function W(t){const[e,n]=o.useState(),r=o.useRef(null),s=o.useRef(t),a=o.useRef("none"),i=t?"mounted":"unmounted",[c,u]=F(i,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return o.useEffect(()=>{const d=x(r.current);a.current=c==="mounted"?d:"none"},[c]),M(()=>{const d=r.current,f=s.current;if(f!==t){const p=a.current,m=x(d);t?u("MOUNT"):m==="none"||d?.display==="none"?u("UNMOUNT"):u(f&&p!==m?"ANIMATION_OUT":"UNMOUNT"),s.current=t}},[t,u]),M(()=>{if(e){let d;const f=e.ownerDocument.defaultView??window,l=m=>{const C=x(r.current).includes(CSS.escape(m.animationName));if(m.target===e&&C&&(u("ANIMATION_END"),!s.current)){const h=e.style.animationFillMode;e.style.animationFillMode="forwards",d=f.setTimeout(()=>{e.style.animationFillMode==="forwards"&&(e.style.animationFillMode=h)})}},p=m=>{m.target===e&&(a.current=x(r.current))};return e.addEventListener("animationstart",p),e.addEventListener("animationcancel",l),e.addEventListener("animationend",l),()=>{f.clearTimeout(d),e.removeEventListener("animationstart",p),e.removeEventListener("animationcancel",l),e.removeEventListener("animationend",l)}}else u("ANIMATION_END")},[e,u]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:o.useCallback(d=>{r.current=d?getComputedStyle(d):null,n(d)},[])}}function x(t){return t?.animationName||"none"}function k(t){let e=Object.getOwnPropertyDescriptor(t.props,"ref")?.get,n=e&&"isReactWarning"in e&&e.isReactWarning;return n?t.ref:(e=Object.getOwnPropertyDescriptor(t,"ref")?.get,n=e&&"isReactWarning"in e&&e.isReactWarning,n?t.props.ref:t.props.ref||t.ref)}var w=b();const Y=y(w);var H=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],Z=H.reduce((t,e)=>{const n=S(`Primitive.${e}`),r=o.forwardRef((s,a)=>{const{asChild:i,...c}=s,u=i?n:e;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),v.jsx(u,{...c,ref:a})});return r.displayName=`Primitive.${e}`,{...t,[e]:r}},{});function ee(t,e){t&&w.flushSync(()=>t.dispatchEvent(e))}export{P as M,Z as P,Y as R,Q as a,L as b,K as c,M as d,j as e,A as f,O as g,ee as h,J as i,X as u};
