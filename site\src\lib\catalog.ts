// Утилиты для построения where-фильтров каталога (DRY)
// Без зависимостей от @prisma/client на фронте
export type PartWhere = Record<string, unknown>;

export function buildPartWhere(params: {
  query?: string;
  categoryId?: number;
  brandId?: number; // зарезервировано для будущих join-ов
}): PartWhere {
  const where: PartWhere = {};

  if (params.query) {
    const q = params.query.trim();
    if (q) {
      (where as any).OR = [
        { name: { contains: q, mode: 'insensitive' } },
        { partCategory: { name: { contains: q, mode: 'insensitive' } } },
      ];
    }
  }

  if (params.categoryId) (where as any).partCategoryId = params.categoryId;

  // brandId можно будет применить при добавлении join-логики (через applicabilities)
  return where;
}

