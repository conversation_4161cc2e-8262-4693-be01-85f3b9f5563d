---
/**
 * Admin Layout - Основной layout для административной панели
 * Включает навигацию, header и основную структуру
 */

import '../styles/global.css'
import AdminToolbar from '../components/admin/AdminToolbar.vue'
import AdminSidebar from '../components/admin/AdminSidebar.vue'
import Toast from '@/volt/Toast.vue'
import GlobalErrorHandlerInit from '@/components/system/GlobalErrorHandlerInit.vue'
import { ClientRouter } from 'astro:transitions'
import { StagewiseToolbar } from '@stagewise/toolbar-vue'
import VuePlugin from '@stagewise-plugins/vue'

export interface Props {
  title?: string
  description?: string
  showSidebar?: boolean
}

const {
  title = "Админ панель - PartTec",
  description = "Система управления каталогом взаимозаменяемых запчастей",
  showSidebar = true
} = Astro.props
---

<!DOCTYPE html>
<html lang="ru" class="h-full">
<head>
  <meta charset="UTF-8" />
  <meta name="description" content={description} />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  
  <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
  <ClientRouter/>
  <title>{title}</title>

  <!-- Скрипт для предотвращения мигания темы -->
  <script is:inline src="/theme-init.js"></script>
</head>

<body class="h-full">
  <div class="min-h-screen bg-surface-50 dark:bg-surface-900">
    <!-- Header с Volt Toolbar -->
    <AdminToolbar client:load />
    
    <!-- Stagewise Toolbar - only in development mode -->
    <!-- {
      import.meta.env.DEV && (
        <StagewiseToolbar
          client:load
          config={{
            plugins: [VuePlugin]
          }}
        />
      )
    } -->

    <div class="flex bg-surface-50 dark:bg-surface-900 min-h-[calc(100vh-4rem)]">
      <!-- Sidebar с Volt компонентами -->
      {showSidebar && (
        <AdminSidebar client:load />
      )}

      <!-- Основной контент -->
      <main class="flex-1 bg-surface-50 dark:bg-surface-900">
        <div class="py-6">
          <div class="mx-auto flex justify-center py-2 px-5">
            <slot />
          </div>
        </div>
      </main>
    </div>

    {/* Глобальный контейнер Toast для админки */}
    <Toast client:load />
    <!-- <GlobalErrorHandlerInit client:load /> -->
  </div>
</body>
</html>


