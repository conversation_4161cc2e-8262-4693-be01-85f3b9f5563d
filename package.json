{"name": "parttec3", "version": "1.0.0", "description": "PartTec - система управления каталогом взаимозаменяемых запчастей", "scripts": {"dev": "concurrently \"npm run dev:api\" \"npm run dev:frontend\"", "dev:api": "cd api && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:frontend", "build:frontend": "cd frontend && npm run build", "preview": "cd frontend && npm run preview"}, "keywords": ["parttec", "astro", "vue", "tailwind", "primevue", "volt"], "author": "PartTec Team", "license": "MIT", "devDependencies": {"@playwright/test": "^1.54.1", "concurrently": "^9.2.0"}}