//////////////////////////////////////////////////////////////////////////////////////////////
// DO NOT MODIFY THIS FILE                                                                  //
// This file is automatically generated by ZenStack CLI and should not be manually updated. //
//////////////////////////////////////////////////////////////////////////////////////////////

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

enum VerificationLevel {
  UNVERIFIED
  COMMUNITY
  EXPERT
  OFFICIAL
}

enum EquivalenceType {
  IDENTICAL
  COMPATIBLE
  ALTERNATIVE
}

enum PartCondition {
  NEW
  USED
  REFURBISHED
}

enum OrderStatus {
  DRAFT
  PENDING
  CONFIRMED
  PROCESSING
  COMPLETED
  CANCELLED
}

enum OrderType {
  REGULAR
  PREORDER
  QUOTE
  EMERGENCY
}

enum NumberType {
  OEM
  AFTERMARKET
  INTERNAL
  SUPERSEDED
}

enum CrossReferenceType {
  OEM_TO_AFTERMARKET
  AFTERMARKET_TO_OEM
  SUPERSESSION
  INTERCHANGE
}

enum PartCategory {
  ENGINE
  TRANSMISSION
  HYDRAULICS
  ELECTRICAL
  FILTERS
  SEALS
  BEARINGS
  ATTACHMENTS
  OTHER
}

enum Role {
  USER
  SHOP
  SERVICE
}

model Account {
  id                    String    @id()
  accountId             String
  providerId            String
  userId                String
  accessToken           String?
  refreshToken          String?
  idToken               String?
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  password              String?
  createdAt             DateTime
  updatedAt             DateTime
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("account")
}

model Session {
  id             String   @id()
  expiresAt      DateTime
  token          String   @unique()
  createdAt      DateTime
  updatedAt      DateTime
  ipAddress      String?
  userAgent      String?
  userId         String
  impersonatedBy String?
  user           User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("session")
}

model Verification {
  id         String    @id()
  identifier String
  value      String
  expiresAt  DateTime
  createdAt  DateTime?
  updatedAt  DateTime?

  @@map("verification")
}

model User {
  id            String    @id() @default(uuid())
  name          String?
  email         String    @unique()
  emailVerified Boolean   @default(false)
  image         String?
  role          Role      @default(USER)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt()
  accounts      Account[]
  sessions      Session[]
  orders        Order[]
  shop          Shop?
  carts         Cart[]

  @@map("user")
}

model Address {
  id                 String              @id() @default(uuid())
  fullAddress        String
  city               String?
  region             String?
  country            String?
  postalCode         String?
  latitude           Float?
  longitude          Float?
  shops              Shop[]
  inventoryLocations InventoryLocation[]
  createdAt          DateTime            @default(now())
  updatedAt          DateTime            @updatedAt()

  @@index([latitude, longitude])
  @@index([city])
  @@index([country])
  @@map("address")
}

model Category {
  id          String              @id() @default(uuid())
  name        String
  slug        String              @unique()
  description String?
  parent      Category?           @relation("CategoryToParent", fields: [parentId], references: [id])
  parentId    String?
  children    Category[]          @relation("CategoryToParent")
  sortOrder   Int                 @default(0)
  isActive    Boolean             @default(true)
  units       Unit[]
  parts       Part[]
  attributes  CategoryAttribute[]
  createdAt   DateTime            @default(now())
  updatedAt   DateTime            @updatedAt()

  @@index([parentId])
  @@index([sortOrder])
  @@map("category")
}

model Brand {
  id              String           @id() @default(uuid())
  name            String
  slug            String           @unique()
  country         String?
  isOem           Boolean          @default(false)
  parts           Part[]
  partNumbers     PartNumber[]
  equipmentModels EquipmentModel[]
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt()

  @@index([isOem])
  @@map("brand")
}

model EquipmentModel {
  id                String              @id() @default(uuid())
  name              String
  brand             Brand?              @relation(fields: [brandId], references: [id])
  brandId           String?
  yearFrom          Int?
  yearTo            Int?
  category          String?
  unitCompatibility UnitCompatibility[]
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt()

  @@index([brandId])
  @@map("equipment_model")
}

model Unit {
  id                String              @id() @default(uuid())
  oemNumber         String?
  name              String
  description       String?
  specifications    Json?
  category          Category?           @relation(fields: [categoryId], references: [id])
  categoryId        String?
  parent            Unit?               @relation("UnitHierarchy", fields: [parentId], references: [id])
  parentId          String?
  children          Unit[]              @relation("UnitHierarchy")
  level             Int                 @default(0)
  verificationLevel VerificationLevel   @default(UNVERIFIED)
  verifiedBy        String?
  verifiedAt        DateTime?
  parts             Part[]
  partCompatibility PartCompatibility[]
  unitCompatibility UnitCompatibility[]
  schemas           AggregateSchema[]
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt()

  @@index([parentId])
  @@index([level])
  @@index([categoryId])
  @@index([verificationLevel])
  @@index([parentId, level])
  @@map("unit")
}

model Part {
  id                     String                          @id() @default(uuid())
  name                   String
  description            String?
  specifications         Json?
  slug                   String?                         @unique()
  commercialName         String?
  commercialDescription  String?
  category               PartCategory                    @default(OTHER)
  categoryRef            Category?                       @relation(fields: [categoryId], references: [id])
  categoryId             String?
  unit                   Unit?                           @relation(fields: [unitId], references: [id])
  unitId                 String?
  brand                  Brand?                          @relation(fields: [brandId], references: [id])
  brandId                String?
  verificationLevel      VerificationLevel               @default(UNVERIFIED)
  verifiedBy             String?
  verifiedAt             DateTime?
  partNumbers            PartNumber[]
  compatibility          PartCompatibility[]
  masterOfGroups         InterchangeabilityGroup[]       @relation("MasterPartGroup")
  groupMemberships       InterchangeabilityGroupMember[]
  standardizedAttributes StandardizedPartAttribute[]
  schemaPositions        SchemaPosition[]
  dimensionSearch        PartDimensionSearch?
  shopParts              ShopPart[]
  cartItems              CartItem[]
  createdAt              DateTime                        @default(now())
  updatedAt              DateTime                        @updatedAt()

  @@index([unitId])
  @@index([categoryId])
  @@index([brandId])
  @@index([verificationLevel])
  @@index([category])
  @@map("part")
}

model PartNumber {
  id                    String                     @id() @default(uuid())
  number                String
  numberType            NumberType                 @default(OEM)
  brand                 Brand?                     @relation(fields: [brandId], references: [id])
  brandId               String?
  isOriginal            Boolean                    @default(false)
  part                  Part                       @relation(fields: [partId], references: [id])
  partId                String
  description           String?
  isActive              Boolean                    @default(true)
  supersededBy          String?
  sourceCrossReferences PartNumberCrossReference[] @relation("SourceCrossReference")
  targetCrossReferences PartNumberCrossReference[] @relation("TargetCrossReference")
  createdAt             DateTime                   @default(now())
  updatedAt             DateTime                   @updatedAt()

  @@unique([number, brandId])
  @@index([partId])
  @@index([number])
  @@index([brandId])
  @@index([numberType])
  @@index([isActive])
  @@index([number, numberType])
  @@index([brandId, numberType])
  @@map("part_number")
}

model PartNumberCrossReference {
  id                 String             @id() @default(uuid())
  sourcePartNumber   PartNumber         @relation("SourceCrossReference", fields: [sourcePartNumberId], references: [id])
  sourcePartNumberId String
  targetPartNumber   PartNumber         @relation("TargetCrossReference", fields: [targetPartNumberId], references: [id])
  targetPartNumberId String
  referenceType      CrossReferenceType
  confidence         Float              @default(1.0)
  notes              String?
  restrictions       String?
  effectiveDate      DateTime?
  verificationLevel  VerificationLevel  @default(UNVERIFIED)
  verifiedBy         String?
  verifiedAt         DateTime?
  source             String?
  createdAt          DateTime           @default(now())
  updatedAt          DateTime           @updatedAt()

  @@unique([sourcePartNumberId, targetPartNumberId, referenceType])
  @@index([sourcePartNumberId])
  @@index([targetPartNumberId])
  @@index([referenceType])
  @@index([confidence])
  @@index([verificationLevel])
  @@map("part_number_cross_reference")
}

model InterchangeabilityGroup {
  id                String                          @id() @default(uuid())
  name              String
  description       String?
  masterPart        Part?                           @relation("MasterPartGroup", fields: [masterPartId], references: [id])
  masterPartId      String?
  equivalenceType   EquivalenceType                 @default(IDENTICAL)
  confidence        Float                           @default(1.0)
  verificationLevel VerificationLevel               @default(UNVERIFIED)
  verifiedBy        String?
  verifiedAt        DateTime?
  members           InterchangeabilityGroupMember[]
  createdAt         DateTime                        @default(now())
  updatedAt         DateTime                        @updatedAt()

  @@index([masterPartId])
  @@index([equivalenceType])
  @@index([verificationLevel])
  @@map("interchangeability_group")
}

model InterchangeabilityGroupMember {
  id              String                  @id() @default(uuid())
  group           InterchangeabilityGroup @relation(fields: [groupId], references: [id])
  groupId         String
  part            Part                    @relation(fields: [partId], references: [id])
  partId          String
  isMaster        Boolean                 @default(false)
  confidence      Float                   @default(1.0)
  equivalenceType EquivalenceType         @default(IDENTICAL)
  restrictions    String?
  notes           String?
  verifiedBy      String?
  verifiedAt      DateTime?
  createdAt       DateTime                @default(now())
  updatedAt       DateTime                @updatedAt()

  @@unique([groupId, partId])
  @@index([partId])
  @@index([isMaster])
  @@map("interchangeability_group_member")
}

model UnitCompatibility {
  id                String            @id() @default(uuid())
  unit              Unit              @relation(fields: [unitId], references: [id])
  unitId            String
  equipmentModel    EquipmentModel    @relation(fields: [equipmentModelId], references: [id])
  equipmentModelId  String
  yearFrom          Int?
  yearTo            Int?
  notes             String?
  verificationLevel VerificationLevel @default(UNVERIFIED)
  verifiedBy        String?
  verifiedAt        DateTime?
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt()

  @@unique([unitId, equipmentModelId])
  @@index([unitId])
  @@index([equipmentModelId])
  @@index([verificationLevel])
  @@map("unit_compatibility")
}

model PartCompatibility {
  id                String            @id() @default(uuid())
  part              Part              @relation(fields: [partId], references: [id])
  partId            String
  unit              Unit              @relation(fields: [unitId], references: [id])
  unitId            String
  yearFrom          Int?
  yearTo            Int?
  position          String?
  engineCode        String?
  serialNumberFrom  String?
  serialNumberTo    String?
  confidence        Float             @default(1.0)
  notes             String?
  source            String?
  verificationLevel VerificationLevel @default(UNVERIFIED)
  verifiedBy        String?
  verifiedAt        DateTime?
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt()

  @@unique([partId, unitId, position, engineCode])
  @@index([partId])
  @@index([unitId])
  @@index([engineCode])
  @@index([confidence])
  @@index([verificationLevel])
  @@map("part_compatibility")
}

model AttributeDefinition {
  id                 String                      @id() @default(uuid())
  name               String
  slug               String                      @unique()
  dataType           String
  unit               String?
  enumValues         String[]                    @default([])
  isRequired         Boolean                     @default(false)
  isFilterable       Boolean                     @default(true)
  isDimensional      Boolean                     @default(false)
  defaultTolerance   Float?
  baseUnit           String?
  conversionFactor   Float?                      @default(1.0)
  minValue           Float?
  maxValue           Float?
  precision          Int?                        @default(2)
  categories         CategoryAttribute[]
  standardizedValues StandardizedPartAttribute[]
  createdAt          DateTime                    @default(now())
  updatedAt          DateTime                    @updatedAt()

  @@index([slug])
  @@index([dataType])
  @@index([isFilterable])
  @@index([isDimensional])
  @@map("attribute_definition")
}

model CategoryAttribute {
  id           String              @id() @default(uuid())
  category     Category            @relation(fields: [categoryId], references: [id])
  categoryId   String
  attribute    AttributeDefinition @relation(fields: [attributeId], references: [id])
  attributeId  String
  isRequired   Boolean             @default(false)
  displayOrder Int                 @default(0)

  @@unique([categoryId, attributeId])
  @@map("category_attribute")
}

model StandardizedPartAttribute {
  id                String              @id() @default(uuid())
  part              Part                @relation(fields: [partId], references: [id])
  partId            String
  attribute         AttributeDefinition @relation(fields: [attributeId], references: [id])
  attributeId       String
  stringValue       String?
  numberValue       Float?
  booleanValue      Boolean?
  dateValue         DateTime?
  jsonValue         Json?
  minValue          Float?
  maxValue          Float?
  nominalValue      Float?
  unit              String?
  precision         Int?
  tolerance         Float?
  normalizedValue   Float?
  normalizedMin     Float?
  normalizedMax     Float?
  context           String?
  conditions        String?
  verificationLevel VerificationLevel   @default(UNVERIFIED)
  verifiedBy        String?
  verifiedAt        DateTime?
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt()

  @@unique([partId, attributeId, context])
  @@index([attributeId])
  @@index([stringValue])
  @@index([numberValue])
  @@index([normalizedValue])
  @@index([verificationLevel])
  @@index([partId, attributeId])
  @@map("standardized_part_attribute")
}

model PartDimensionSearch {
  id                     String   @id() @default(uuid())
  part                   Part     @relation(fields: [partId], references: [id])
  partId                 String   @unique()
  innerDiameter          Float?
  outerDiameter          Float?
  height                 Float?
  innerDiameter2         Float?
  outerDiameter2         Float?
  height2                Float?
  innerDiameterTolerance Float?   @default(0.3)
  outerDiameterTolerance Float?   @default(0.3)
  heightTolerance        Float?   @default(3.0)
  dimensionString        String?
  isComplete             Boolean  @default(false)
  isParsed               Boolean  @default(false)
  lastUpdated            DateTime @default(now())
  createdAt              DateTime @default(now())
  updatedAt              DateTime @updatedAt()

  @@index([innerDiameter])
  @@index([innerDiameter2])
  @@index([outerDiameter])
  @@index([outerDiameter2])
  @@index([height])
  @@index([height2])
  @@index([isComplete])
  @@index([innerDiameter, outerDiameter])
  @@index([innerDiameter, outerDiameter, height])
  @@map("part_dimension_search")
}

model AggregateSchema {
  id                String             @id() @default(uuid())
  name              String
  description       String?
  unit              Unit?              @relation(fields: [unitId], references: [id])
  unitId            String?
  imageUrl          String?
  imageWidth        Int?
  imageHeight       Int?
  svgContent        String?
  isActive          Boolean            @default(true)
  sortOrder         Int                @default(0)
  verificationLevel VerificationLevel  @default(UNVERIFIED)
  verifiedBy        String?
  verifiedAt        DateTime?
  positions         SchemaPosition[]
  annotations       SchemaAnnotation[]
  createdAt         DateTime           @default(now())
  updatedAt         DateTime           @updatedAt()

  @@index([unitId])
  @@index([isActive])
  @@index([verificationLevel])
  @@map("aggregate_schema")
}

model SchemaPosition {
  id                String          @id() @default(uuid())
  schema            AggregateSchema @relation(fields: [schemaId], references: [id])
  schemaId          String
  part              Part            @relation(fields: [partId], references: [id])
  partId            String
  positionNumber    String
  x                 Float
  y                 Float
  width             Float?
  height            Float?
  shape             String          @default("circle")
  color             String?
  label             String?
  quantity          Int             @default(1)
  isRequired        Boolean         @default(true)
  isHighlighted     Boolean         @default(false)
  installationOrder Int?
  notes             String?
  isVisible         Boolean         @default(true)
  sortOrder         Int             @default(0)
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt()

  @@unique([schemaId, partId])
  @@unique([schemaId, positionNumber])
  @@index([schemaId])
  @@index([partId])
  @@index([positionNumber])
  @@map("schema_position")
}

model SchemaAnnotation {
  id             String          @id() @default(uuid())
  schema         AggregateSchema @relation(fields: [schemaId], references: [id])
  schemaId       String
  x              Float
  y              Float
  width          Float?
  height         Float?
  text           String
  annotationType String          @default("note")
  color          String?
  fontSize       Int?            @default(12)
  strokeWidth    Int?            @default(1)
  opacity        Float?          @default(1.0)
  isVisible      Boolean         @default(true)
  sortOrder      Int             @default(0)
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt()

  @@index([schemaId])
  @@map("schema_annotation")
}

model Shop {
  id                 String              @id() @default(uuid())
  name               String
  description        String?
  owner              User                @relation(fields: [ownerId], references: [id])
  ownerId            String              @unique()
  address            Address             @relation(fields: [addressId], references: [id])
  addressId          String
  contactPhone       String?
  contactEmail       String?
  website            String?
  isActive           Boolean             @default(true)
  isVerified         Boolean             @default(false)
  inventoryLocations InventoryLocation[]
  shopParts          ShopPart[]
  orders             Order[]
  createdAt          DateTime            @default(now())
  updatedAt          DateTime            @updatedAt()

  @@index([ownerId])
  @@index([addressId])
  @@index([isActive])
  @@index([isVerified])
  @@map("shop")
}

model InventoryLocation {
  id           String     @id() @default(uuid())
  name         String
  description  String?
  shop         Shop       @relation(fields: [shopId], references: [id])
  shopId       String
  address      Address    @relation(fields: [addressId], references: [id])
  addressId    String
  isActive     Boolean    @default(true)
  isPrimary    Boolean    @default(false)
  locationType String     @default("WAREHOUSE")
  workingHours Json?
  contactPhone String?
  contactEmail String?
  shopParts    ShopPart[]
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt()

  @@index([shopId])
  @@index([addressId])
  @@index([isActive])
  @@index([isPrimary])
  @@map("inventory_location")
}

model ShopPart {
  id                  String             @id() @default(uuid())
  part                Part               @relation(fields: [partId], references: [id])
  partId              String
  shop                Shop               @relation(fields: [shopId], references: [id])
  shopId              String
  inventoryLocation   InventoryLocation? @relation(fields: [inventoryLocationId], references: [id])
  inventoryLocationId String?
  supplierPartNumber  String?
  price               Float
  currency            String             @default("RUB")
  quantity            Int                @default(0)
  condition           PartCondition      @default(NEW)
  isAvailable         Boolean            @default(true)
  leadTime            Int?
  minOrderQty         Int                @default(1)
  warrantyMonths      Int?
  notes               String?
  orderItems          OrderItem[]
  createdAt           DateTime           @default(now())
  updatedAt           DateTime           @updatedAt()

  @@unique([partId, shopId])
  @@unique([shopId, supplierPartNumber])
  @@index([partId])
  @@index([shopId])
  @@index([supplierPartNumber])
  @@index([inventoryLocationId])
  @@index([isAvailable])
  @@index([price])
  @@index([condition])
  @@map("shop_part")
}

model Cart {
  id        String     @id() @default(uuid())
  user      User       @relation(fields: [userId], references: [id])
  userId    String
  items     CartItem[]
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt()

  @@unique([userId])
  @@map("cart")
}

model CartItem {
  id        String   @id() @default(uuid())
  cart      Cart     @relation(fields: [cartId], references: [id])
  cartId    String
  part      Part     @relation(fields: [partId], references: [id])
  partId    String
  quantity  Int      @default(1)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt()

  @@unique([cartId, partId])
  @@index([cartId])
  @@index([partId])
  @@map("cart_item")
}

model Order {
  id          String      @id() @default(uuid())
  orderNumber String      @unique()
  user        User        @relation(fields: [userId], references: [id])
  userId      String
  shop        Shop        @relation(fields: [shopId], references: [id])
  shopId      String
  status      OrderStatus @default(DRAFT)
  orderType   OrderType   @default(REGULAR)
  totalAmount Float       @default(0)
  currency    String      @default("RUB")
  notes       String?
  items       OrderItem[]
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt()

  @@index([userId])
  @@index([shopId])
  @@index([status])
  @@index([orderType])
  @@index([createdAt])
  @@map("order")
}

model OrderItem {
  id         String   @id() @default(uuid())
  order      Order    @relation(fields: [orderId], references: [id])
  orderId    String
  shopPart   ShopPart @relation(fields: [shopPartId], references: [id])
  shopPartId String
  quantity   Int
  unitPrice  Float
  totalPrice Float
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt()

  @@index([orderId])
  @@index([shopPartId])
  @@map("order_item")
}

model AdminAuditLog {
  id         String   @id() @default(uuid())
  entityType String
  entityId   String
  action     String
  oldData    Json?
  newData    Json?
  userId     String?
  reason     String?
  source     String?
  createdAt  DateTime @default(now())

  @@index([entityType])
  @@index([entityId])
  @@index([action])
  @@index([userId])
  @@index([createdAt])
  @@index([entityType, entityId])
  @@map("admin_audit_log")
}
