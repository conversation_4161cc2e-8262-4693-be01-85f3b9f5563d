/**
* @vue/shared v3.5.17
* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function vt(t){const e=Object.create(null);for(const s of t.split(","))e[s]=1;return s=>s in e}const ee={},$e=[],se=()=>{},Fe=()=>!1,Ge=t=>t.charCodeAt(0)===111&&t.charCodeAt(1)===110&&(t.charCodeAt(2)>122||t.charCodeAt(2)<97),Je=t=>t.startsWith("onUpdate:"),ne=Object.assign,ie=(t,e)=>{const s=t.indexOf(e);s>-1&&t.splice(s,1)},re=Object.prototype.hasOwnProperty,tt=(t,e)=>re.call(t,e),m=Array.isArray,H=t=>F(t)==="[object Map]",Dt=t=>F(t)==="[object Set]",Rt=t=>F(t)==="[object Date]",M=t=>typeof t=="function",C=t=>typeof t=="string",O=t=>typeof t=="symbol",y=t=>t!==null&&typeof t=="object",qe=t=>(y(t)||M(t))&&M(t.then)&&M(t.catch),Et=Object.prototype.toString,F=t=>Et.call(t),oe=t=>F(t).slice(8,-1),At=t=>F(t)==="[object Object]",bt=t=>C(t)&&t!=="NaN"&&t[0]!=="-"&&""+parseInt(t,10)===t,Qe=vt(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),st=t=>{const e=Object.create(null);return s=>e[s]||(e[s]=t(s))},ce=/-(\w)/g,Ze=st(t=>t.replace(ce,(e,s)=>s?s.toUpperCase():"")),ae=/\B([A-Z])/g,Xe=st(t=>t.replace(ae,"-$1").toLowerCase()),fe=st(t=>t.charAt(0).toUpperCase()+t.slice(1)),ke=st(t=>t?`on${fe(t)}`:""),A=(t,e)=>!Object.is(t,e),ts=(t,...e)=>{for(let s=0;s<t.length;s++)t[s](...e)},le=(t,e,s,n=!1)=>{Object.defineProperty(t,e,{configurable:!0,enumerable:!1,writable:n,value:s})},es=t=>{const e=parseFloat(t);return isNaN(e)?t:e},ss=t=>{const e=C(t)?Number(t):NaN;return isNaN(e)?t:e};let xt;const ns=()=>xt||(xt=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Pt(t){if(m(t)){const e={};for(let s=0;s<t.length;s++){const n=t[s],i=C(n)?de(n):Pt(n);if(i)for(const r in i)e[r]=i[r]}return e}else if(C(t)||y(t))return t}const ue=/;(?![^(]*\))/g,he=/:([^]+)/,pe=/\/\*[^]*?\*\//g;function de(t){const e={};return t.replace(pe,"").split(ue).forEach(s=>{if(s){const n=s.split(he);n.length>1&&(e[n[0].trim()]=n[1].trim())}}),e}function It(t){let e="";if(C(t))e=t;else if(m(t))for(let s=0;s<t.length;s++){const n=It(t[s]);n&&(e+=n+" ")}else if(y(t))for(const s in t)t[s]&&(e+=s+" ");return e.trim()}function is(t){if(!t)return null;let{class:e,style:s}=t;return e&&!C(e)&&(t.class=It(e)),s&&(t.style=Pt(s)),t}const _e="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",rs=vt(_e);function os(t){return!!t||t===""}function ge(t,e){if(t.length!==e.length)return!1;let s=!0;for(let n=0;s&&n<t.length;n++)s=Mt(t[n],e[n]);return s}function Mt(t,e){if(t===e)return!0;let s=Rt(t),n=Rt(e);if(s||n)return s&&n?t.getTime()===e.getTime():!1;if(s=O(t),n=O(e),s||n)return t===e;if(s=m(t),n=m(e),s||n)return s&&n?ge(t,e):!1;if(s=y(t),n=y(e),s||n){if(!s||!n)return!1;const i=Object.keys(t).length,r=Object.keys(e).length;if(i!==r)return!1;for(const o in t){const c=t.hasOwnProperty(o),a=e.hasOwnProperty(o);if(c&&!a||!c&&a||!Mt(t[o],e[o]))return!1}}return String(t)===String(e)}const jt=t=>!!(t&&t.__v_isRef===!0),ve=t=>C(t)?t:t==null?"":m(t)||y(t)&&(t.toString===Et||!M(t.toString))?jt(t)?ve(t.value):JSON.stringify(t,Ct,2):String(t),Ct=(t,e)=>jt(e)?Ct(t,e.value):H(e)?{[`Map(${e.size})`]:[...e.entries()].reduce((s,[n,i],r)=>(s[at(n,r)+" =>"]=i,s),{})}:Dt(e)?{[`Set(${e.size})`]:[...e.values()].map(s=>at(s))}:O(e)?at(e):y(e)&&!m(e)&&!At(e)?String(e):e,at=(t,e="")=>{var s;return O(t)?`Symbol(${(s=t.description)!=null?s:e})`:t};/**
* @vue/reactivity v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let g;class cs{constructor(e=!1){this.detached=e,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=g,!e&&g&&(this.index=(g.scopes||(g.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let e,s;if(this.scopes)for(e=0,s=this.scopes.length;e<s;e++)this.scopes[e].pause();for(e=0,s=this.effects.length;e<s;e++)this.effects[e].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let e,s;if(this.scopes)for(e=0,s=this.scopes.length;e<s;e++)this.scopes[e].resume();for(e=0,s=this.effects.length;e<s;e++)this.effects[e].resume()}}run(e){if(this._active){const s=g;try{return g=this,e()}finally{g=s}}}on(){++this._on===1&&(this.prevScope=g,g=this)}off(){this._on>0&&--this._on===0&&(g=this.prevScope,this.prevScope=void 0)}stop(e){if(this._active){this._active=!1;let s,n;for(s=0,n=this.effects.length;s<n;s++)this.effects[s].stop();for(this.effects.length=0,s=0,n=this.cleanups.length;s<n;s++)this.cleanups[s]();if(this.cleanups.length=0,this.scopes){for(s=0,n=this.scopes.length;s<n;s++)this.scopes[s].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!e){const i=this.parent.scopes.pop();i&&i!==this&&(this.parent.scopes[this.index]=i,i.index=this.index)}this.parent=void 0}}}function be(){return g}function as(t,e=!1){g&&g.cleanups.push(t)}let h;const ft=new WeakSet;class me{constructor(e){this.fn=e,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,g&&g.active&&g.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,ft.has(this)&&(ft.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Lt(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Tt(this),Nt(this);const e=h,s=S;h=this,S=!0;try{return this.fn()}finally{Ht(this),h=e,S=s,this.flags&=-3}}stop(){if(this.flags&1){for(let e=this.deps;e;e=e.nextDep)yt(e);this.deps=this.depsTail=void 0,Tt(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?ft.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){ht(this)&&this.run()}get dirty(){return ht(this)}}let Kt=0,W,B;function Lt(t,e=!1){if(t.flags|=8,e){t.next=B,B=t;return}t.next=W,W=t}function mt(){Kt++}function wt(){if(--Kt>0)return;if(B){let e=B;for(B=void 0;e;){const s=e.next;e.next=void 0,e.flags&=-9,e=s}}let t;for(;W;){let e=W;for(W=void 0;e;){const s=e.next;if(e.next=void 0,e.flags&=-9,e.flags&1)try{e.trigger()}catch(n){t||(t=n)}e=s}}if(t)throw t}function Nt(t){for(let e=t.deps;e;e=e.nextDep)e.version=-1,e.prevActiveLink=e.dep.activeLink,e.dep.activeLink=e}function Ht(t){let e,s=t.depsTail,n=s;for(;n;){const i=n.prevDep;n.version===-1?(n===s&&(s=i),yt(n),we(n)):e=n,n.dep.activeLink=n.prevActiveLink,n.prevActiveLink=void 0,n=i}t.deps=e,t.depsTail=s}function ht(t){for(let e=t.deps;e;e=e.nextDep)if(e.dep.version!==e.version||e.dep.computed&&(Vt(e.dep.computed)||e.dep.version!==e.version))return!0;return!!t._dirty}function Vt(t){if(t.flags&4&&!(t.flags&16)||(t.flags&=-17,t.globalVersion===Y)||(t.globalVersion=Y,!t.isSSR&&t.flags&128&&(!t.deps&&!t._dirty||!ht(t))))return;t.flags|=2;const e=t.dep,s=h,n=S;h=t,S=!0;try{Nt(t);const i=t.fn(t._value);(e.version===0||A(i,t._value))&&(t.flags|=128,t._value=i,e.version++)}catch(i){throw e.version++,i}finally{h=s,S=n,Ht(t),t.flags&=-3}}function yt(t,e=!1){const{dep:s,prevSub:n,nextSub:i}=t;if(n&&(n.nextSub=i,t.prevSub=void 0),i&&(i.prevSub=n,t.nextSub=void 0),s.subs===t&&(s.subs=n,!n&&s.computed)){s.computed.flags&=-5;for(let r=s.computed.deps;r;r=r.nextDep)yt(r,!0)}!e&&!--s.sc&&s.map&&s.map.delete(s.key)}function we(t){const{prevDep:e,nextDep:s}=t;e&&(e.nextDep=s,t.prevDep=void 0),s&&(s.prevDep=e,t.nextDep=void 0)}let S=!0;const zt=[];function Wt(){zt.push(S),S=!1}function Bt(){const t=zt.pop();S=t===void 0?!0:t}function Tt(t){const{cleanup:e}=t;if(t.cleanup=void 0,e){const s=h;h=void 0;try{e()}finally{h=s}}}let Y=0;class ye{constructor(e,s){this.sub=e,this.dep=s,this.version=s.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class nt{constructor(e){this.computed=e,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(e){if(!h||!S||h===this.computed)return;let s=this.activeLink;if(s===void 0||s.sub!==h)s=this.activeLink=new ye(h,this),h.deps?(s.prevDep=h.depsTail,h.depsTail.nextDep=s,h.depsTail=s):h.deps=h.depsTail=s,Ut(s);else if(s.version===-1&&(s.version=this.version,s.nextDep)){const n=s.nextDep;n.prevDep=s.prevDep,s.prevDep&&(s.prevDep.nextDep=n),s.prevDep=h.depsTail,s.nextDep=void 0,h.depsTail.nextDep=s,h.depsTail=s,h.deps===s&&(h.deps=n)}return s}trigger(e){this.version++,Y++,this.notify(e)}notify(e){mt();try{for(let s=this.subs;s;s=s.prevSub)s.sub.notify()&&s.sub.dep.notify()}finally{wt()}}}function Ut(t){if(t.dep.sc++,t.sub.flags&4){const e=t.dep.computed;if(e&&!t.dep.subs){e.flags|=20;for(let n=e.deps;n;n=n.nextDep)Ut(n)}const s=t.dep.subs;s!==t&&(t.prevSub=s,s&&(s.nextSub=t)),t.dep.subs=t}}const pt=new WeakMap,j=Symbol(""),dt=Symbol(""),$=Symbol("");function b(t,e,s){if(S&&h){let n=pt.get(t);n||pt.set(t,n=new Map);let i=n.get(s);i||(n.set(s,i=new nt),i.map=n,i.key=s),i.track()}}function D(t,e,s,n,i,r){const o=pt.get(t);if(!o){Y++;return}const c=a=>{a&&a.trigger()};if(mt(),e==="clear")o.forEach(c);else{const a=m(t),d=a&&bt(s);if(a&&s==="length"){const f=Number(n);o.forEach((p,_)=>{(_==="length"||_===$||!O(_)&&_>=f)&&c(p)})}else switch((s!==void 0||o.has(void 0))&&c(o.get(s)),d&&c(o.get($)),e){case"add":a?d&&c(o.get("length")):(c(o.get(j)),H(t)&&c(o.get(dt)));break;case"delete":a||(c(o.get(j)),H(t)&&c(o.get(dt)));break;case"set":H(t)&&c(o.get(j));break}}wt()}function N(t){const e=u(t);return e===t?e:(b(e,"iterate",$),R(t)?e:e.map(v))}function St(t){return b(t=u(t),"iterate",$),t}const Se={__proto__:null,[Symbol.iterator](){return lt(this,Symbol.iterator,v)},concat(...t){return N(this).concat(...t.map(e=>m(e)?N(e):e))},entries(){return lt(this,"entries",t=>(t[1]=v(t[1]),t))},every(t,e){return T(this,"every",t,e,void 0,arguments)},filter(t,e){return T(this,"filter",t,e,s=>s.map(v),arguments)},find(t,e){return T(this,"find",t,e,v,arguments)},findIndex(t,e){return T(this,"findIndex",t,e,void 0,arguments)},findLast(t,e){return T(this,"findLast",t,e,v,arguments)},findLastIndex(t,e){return T(this,"findLastIndex",t,e,void 0,arguments)},forEach(t,e){return T(this,"forEach",t,e,void 0,arguments)},includes(...t){return ut(this,"includes",t)},indexOf(...t){return ut(this,"indexOf",t)},join(t){return N(this).join(t)},lastIndexOf(...t){return ut(this,"lastIndexOf",t)},map(t,e){return T(this,"map",t,e,void 0,arguments)},pop(){return z(this,"pop")},push(...t){return z(this,"push",t)},reduce(t,...e){return Ot(this,"reduce",t,e)},reduceRight(t,...e){return Ot(this,"reduceRight",t,e)},shift(){return z(this,"shift")},some(t,e){return T(this,"some",t,e,void 0,arguments)},splice(...t){return z(this,"splice",t)},toReversed(){return N(this).toReversed()},toSorted(t){return N(this).toSorted(t)},toSpliced(...t){return N(this).toSpliced(...t)},unshift(...t){return z(this,"unshift",t)},values(){return lt(this,"values",v)}};function lt(t,e,s){const n=St(t),i=n[e]();return n!==t&&!R(t)&&(i._next=i.next,i.next=()=>{const r=i._next();return r.value&&(r.value=s(r.value)),r}),i}const Re=Array.prototype;function T(t,e,s,n,i,r){const o=St(t),c=o!==t&&!R(t),a=o[e];if(a!==Re[e]){const p=a.apply(t,r);return c?v(p):p}let d=s;o!==t&&(c?d=function(p,_){return s.call(this,v(p),_,t)}:s.length>2&&(d=function(p,_){return s.call(this,p,_,t)}));const f=a.call(o,d,n);return c&&i?i(f):f}function Ot(t,e,s,n){const i=St(t);let r=s;return i!==t&&(R(t)?s.length>3&&(r=function(o,c,a){return s.call(this,o,c,a,t)}):r=function(o,c,a){return s.call(this,o,v(c),a,t)}),i[e](r,...n)}function ut(t,e,s){const n=u(t);b(n,"iterate",$);const i=n[e](...s);return(i===-1||i===!1)&&He(s[0])?(s[0]=u(s[0]),n[e](...s)):i}function z(t,e,s=[]){Wt(),mt();const n=u(t)[e].apply(t,s);return wt(),Bt(),n}const xe=vt("__proto__,__v_isRef,__isVue"),Yt=new Set(Object.getOwnPropertyNames(Symbol).filter(t=>t!=="arguments"&&t!=="caller").map(t=>Symbol[t]).filter(O));function Te(t){O(t)||(t=String(t));const e=u(this);return b(e,"has",t),e.hasOwnProperty(t)}class $t{constructor(e=!1,s=!1){this._isReadonly=e,this._isShallow=s}get(e,s,n){if(s==="__v_skip")return e.__v_skip;const i=this._isReadonly,r=this._isShallow;if(s==="__v_isReactive")return!i;if(s==="__v_isReadonly")return i;if(s==="__v_isShallow")return r;if(s==="__v_raw")return n===(i?r?Zt:Qt:r?qt:Jt).get(e)||Object.getPrototypeOf(e)===Object.getPrototypeOf(n)?e:void 0;const o=m(e);if(!i){let a;if(o&&(a=Se[s]))return a;if(s==="hasOwnProperty")return Te}const c=Reflect.get(e,s,w(e)?e:n);return(O(s)?Yt.has(s):xe(s))||(i||b(e,"get",s),r)?c:w(c)?o&&bt(s)?c:c.value:y(c)?i?kt(c):Xt(c):c}}class Ft extends $t{constructor(e=!1){super(!1,e)}set(e,s,n,i){let r=e[s];if(!this._isShallow){const a=K(r);if(!R(n)&&!K(n)&&(r=u(r),n=u(n)),!m(e)&&w(r)&&!w(n))return a?!1:(r.value=n,!0)}const o=m(e)&&bt(s)?Number(s)<e.length:tt(e,s),c=Reflect.set(e,s,n,w(e)?e:i);return e===u(i)&&(o?A(n,r)&&D(e,"set",s,n):D(e,"add",s,n)),c}deleteProperty(e,s){const n=tt(e,s);e[s];const i=Reflect.deleteProperty(e,s);return i&&n&&D(e,"delete",s,void 0),i}has(e,s){const n=Reflect.has(e,s);return(!O(s)||!Yt.has(s))&&b(e,"has",s),n}ownKeys(e){return b(e,"iterate",m(e)?"length":j),Reflect.ownKeys(e)}}class Gt extends $t{constructor(e=!1){super(!0,e)}set(e,s){return!0}deleteProperty(e,s){return!0}}const Oe=new Ft,De=new Gt,Ee=new Ft(!0),Ae=new Gt(!0),_t=t=>t,Z=t=>Reflect.getPrototypeOf(t);function Pe(t,e,s){return function(...n){const i=this.__v_raw,r=u(i),o=H(r),c=t==="entries"||t===Symbol.iterator&&o,a=t==="keys"&&o,d=i[t](...n),f=s?_t:e?gt:v;return!e&&b(r,"iterate",a?dt:j),{next(){const{value:p,done:_}=d.next();return _?{value:p,done:_}:{value:c?[f(p[0]),f(p[1])]:f(p),done:_}},[Symbol.iterator](){return this}}}}function X(t){return function(...e){return t==="delete"?!1:t==="clear"?void 0:this}}function Ie(t,e){const s={get(i){const r=this.__v_raw,o=u(r),c=u(i);t||(A(i,c)&&b(o,"get",i),b(o,"get",c));const{has:a}=Z(o),d=e?_t:t?gt:v;if(a.call(o,i))return d(r.get(i));if(a.call(o,c))return d(r.get(c));r!==o&&r.get(i)},get size(){const i=this.__v_raw;return!t&&b(u(i),"iterate",j),Reflect.get(i,"size",i)},has(i){const r=this.__v_raw,o=u(r),c=u(i);return t||(A(i,c)&&b(o,"has",i),b(o,"has",c)),i===c?r.has(i):r.has(i)||r.has(c)},forEach(i,r){const o=this,c=o.__v_raw,a=u(c),d=e?_t:t?gt:v;return!t&&b(a,"iterate",j),c.forEach((f,p)=>i.call(r,d(f),d(p),o))}};return ne(s,t?{add:X("add"),set:X("set"),delete:X("delete"),clear:X("clear")}:{add(i){!e&&!R(i)&&!K(i)&&(i=u(i));const r=u(this);return Z(r).has.call(r,i)||(r.add(i),D(r,"add",i,i)),this},set(i,r){!e&&!R(r)&&!K(r)&&(r=u(r));const o=u(this),{has:c,get:a}=Z(o);let d=c.call(o,i);d||(i=u(i),d=c.call(o,i));const f=a.call(o,i);return o.set(i,r),d?A(r,f)&&D(o,"set",i,r):D(o,"add",i,r),this},delete(i){const r=u(this),{has:o,get:c}=Z(r);let a=o.call(r,i);a||(i=u(i),a=o.call(r,i)),c&&c.call(r,i);const d=r.delete(i);return a&&D(r,"delete",i,void 0),d},clear(){const i=u(this),r=i.size!==0,o=i.clear();return r&&D(i,"clear",void 0,void 0),o}}),["keys","values","entries",Symbol.iterator].forEach(i=>{s[i]=Pe(i,t,e)}),s}function it(t,e){const s=Ie(t,e);return(n,i,r)=>i==="__v_isReactive"?!t:i==="__v_isReadonly"?t:i==="__v_raw"?n:Reflect.get(tt(s,i)&&i in n?s:n,i,r)}const Me={get:it(!1,!1)},je={get:it(!1,!0)},Ce={get:it(!0,!1)},Ke={get:it(!0,!0)},Jt=new WeakMap,qt=new WeakMap,Qt=new WeakMap,Zt=new WeakMap;function Le(t){switch(t){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Ne(t){return t.__v_skip||!Object.isExtensible(t)?0:Le(oe(t))}function Xt(t){return K(t)?t:rt(t,!1,Oe,Me,Jt)}function fs(t){return rt(t,!1,Ee,je,qt)}function kt(t){return rt(t,!0,De,Ce,Qt)}function ls(t){return rt(t,!0,Ae,Ke,Zt)}function rt(t,e,s,n,i){if(!y(t)||t.__v_raw&&!(e&&t.__v_isReactive))return t;const r=Ne(t);if(r===0)return t;const o=i.get(t);if(o)return o;const c=new Proxy(t,r===2?n:s);return i.set(t,c),c}function U(t){return K(t)?U(t.__v_raw):!!(t&&t.__v_isReactive)}function K(t){return!!(t&&t.__v_isReadonly)}function R(t){return!!(t&&t.__v_isShallow)}function He(t){return t?!!t.__v_raw:!1}function u(t){const e=t&&t.__v_raw;return e?u(e):t}function us(t){return!tt(t,"__v_skip")&&Object.isExtensible(t)&&le(t,"__v_skip",!0),t}const v=t=>y(t)?Xt(t):t,gt=t=>y(t)?kt(t):t;function w(t){return t?t.__v_isRef===!0:!1}function hs(t){return te(t,!1)}function ps(t){return te(t,!0)}function te(t,e){return w(t)?t:new Ve(t,e)}class Ve{constructor(e,s){this.dep=new nt,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=s?e:u(e),this._value=s?e:v(e),this.__v_isShallow=s}get value(){return this.dep.track(),this._value}set value(e){const s=this._rawValue,n=this.__v_isShallow||R(e)||K(e);e=n?e:u(e),A(e,s)&&(this._rawValue=e,this._value=n?e:v(e),this.dep.trigger())}}function ze(t){return w(t)?t.value:t}const We={get:(t,e,s)=>e==="__v_raw"?t:ze(Reflect.get(t,e,s)),set:(t,e,s,n)=>{const i=t[e];return w(i)&&!w(s)?(i.value=s,!0):Reflect.set(t,e,s,n)}};function ds(t){return U(t)?t:new Proxy(t,We)}class Be{constructor(e){this.__v_isRef=!0,this._value=void 0;const s=this.dep=new nt,{get:n,set:i}=e(s.track.bind(s),s.trigger.bind(s));this._get=n,this._set=i}get value(){return this._value=this._get()}set value(e){this._set(e)}}function _s(t){return new Be(t)}class Ue{constructor(e,s,n){this.fn=e,this.setter=s,this._value=void 0,this.dep=new nt(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Y-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!s,this.isSSR=n}notify(){if(this.flags|=16,!(this.flags&8)&&h!==this)return Lt(this,!0),!0}get value(){const e=this.dep.track();return Vt(this),e&&(e.version=this.dep.version),this._value}set value(e){this.setter&&this.setter(e)}}function gs(t,e,s=!1){let n,i;return M(t)?n=t:(n=t.get,i=t.set),new Ue(n,i,s)}const k={},et=new WeakMap;let I;function Ye(t,e=!1,s=I){if(s){let n=et.get(s);n||et.set(s,n=[]),n.push(t)}}function vs(t,e,s=ee){const{immediate:n,deep:i,once:r,scheduler:o,augmentJob:c,call:a}=s,d=l=>i?l:R(l)||i===!1||i===0?E(l,1):E(l);let f,p,_,G,J=!1,q=!1;if(w(t)?(p=()=>t.value,J=R(t)):U(t)?(p=()=>d(t),J=!0):m(t)?(q=!0,J=t.some(l=>U(l)||R(l)),p=()=>t.map(l=>{if(w(l))return l.value;if(U(l))return d(l);if(M(l))return a?a(l,2):l()})):M(t)?e?p=a?()=>a(t,2):t:p=()=>{if(_){Wt();try{_()}finally{Bt()}}const l=I;I=f;try{return a?a(t,3,[G]):t(G)}finally{I=l}}:p=se,e&&i){const l=p,x=i===!0?1/0:i;p=()=>E(l(),x)}const ot=be(),L=()=>{f.stop(),ot&&ot.active&&ie(ot.effects,f)};if(r&&e){const l=e;e=(...x)=>{l(...x),L()}}let P=q?new Array(t.length).fill(k):k;const V=l=>{if(!(!(f.flags&1)||!f.dirty&&!l))if(e){const x=f.run();if(i||J||(q?x.some((ct,Q)=>A(ct,P[Q])):A(x,P))){_&&_();const ct=I;I=f;try{const Q=[x,P===k?void 0:q&&P[0]===k?[]:P,G];P=x,a?a(e,3,Q):e(...Q)}finally{I=ct}}}else f.run()};return c&&c(V),f=new me(p),f.scheduler=o?()=>o(V,!1):V,G=l=>Ye(l,!1,f),_=f.onStop=()=>{const l=et.get(f);if(l){if(a)a(l,4);else for(const x of l)x();et.delete(f)}},e?n?V(!0):P=f.run():o?o(V.bind(null,!0),!0):f.run(),L.pause=f.pause.bind(f),L.resume=f.resume.bind(f),L.stop=L,L}function E(t,e=1/0,s){if(e<=0||!y(t)||t.__v_skip||(s=s||new Set,s.has(t)))return t;if(s.add(t),e--,w(t))E(t.value,e,s);else if(m(t))for(let n=0;n<t.length;n++)E(t[n],e,s);else if(Dt(t)||H(t))t.forEach(n=>{E(n,e,s)});else if(At(t)){for(const n in t)E(t[n],e,s);for(const n of Object.getOwnPropertySymbols(t))Object.prototype.propertyIsEnumerable.call(t,n)&&E(t[n],e,s)}return t}export{le as $,He as A,U as B,R as C,K as D,St as E,gt as F,v as G,ee as H,$e as I,w as J,vs as K,_s as L,E as M,se as N,qe as O,A as P,Wt as Q,Bt as R,ds as S,us as T,ns as U,cs as V,me as W,Fe as X,tt as Y,ke as Z,fs as _,Xt as a,ie as a0,Qe as a1,b as a2,ls as a3,D as a4,ps as a5,be as a6,as as a7,ze as a8,is as b,Pt as c,kt as d,C as e,ts as f,m as g,Xe as h,M as i,es as j,ne as k,Mt as l,Ge as m,It as n,Je as o,rs as p,Ze as q,hs as r,os as s,ve as t,O as u,fe as v,y as w,ss as x,u as y,gs as z};
