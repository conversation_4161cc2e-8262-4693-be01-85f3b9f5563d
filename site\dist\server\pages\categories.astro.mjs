import { e as createComponent, k as renderComponent, r as renderTemplate, m as maybeRenderHead } from '../chunks/astro/server_D7mwM5eH.mjs';
import 'kleur/colors';
import { $ as $$MainLayout } from '../chunks/MainLayout_M77LEN4m.mjs';
import { t as trpcClient } from '../chunks/badge_BlauDB1K.mjs';
import { T as TrpcProvider } from '../chunks/TrpcProvider_CX1isq_a.mjs';
import { C as CategoryCard } from '../chunks/CategoryCard_Cjcin2cY.mjs';
export { renderers } from '../renderers.mjs';

const $$Categories = createComponent(async ($$result, $$props, $$slots) => {
  let categories = [];
  try {
    const categoriesResponse = await trpcClient.crud.partCategory.findMany.query({
      include: {
        image: true,
        children: {
          include: {
            _count: { select: { parts: true } }
          }
        },
        _count: { select: { parts: true } }
      },
      orderBy: [
        { level: "asc" },
        { name: "asc" }
      ]
    });
    categories = categoriesResponse || [];
  } catch (error) {
    console.error("Error loading categories:", error);
  }
  const rootCategories = categories.filter((cat) => cat.level === 0);
  const subcategories = categories.filter((cat) => cat.level > 0);
  return renderTemplate`${renderComponent($$result, "MainLayout", $$MainLayout, { "title": "\u041A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438 \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439", "description": "\u0418\u0435\u0440\u0430\u0440\u0445\u0438\u0447\u0435\u0441\u043A\u0438\u0439 \u043A\u0430\u0442\u0430\u043B\u043E\u0433 \u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0439 \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439" }, { "default": async ($$result2) => renderTemplate` ${renderComponent($$result2, "TrpcProvider", TrpcProvider, { "client:load": true, "client:component-hydration": "load", "client:component-path": "@/components/providers/TrpcProvider", "client:component-export": "TrpcProvider" }, { "default": async ($$result3) => renderTemplate` ${maybeRenderHead()}<div class="container mx-auto px-4 py-8"> <!-- Заголовок --> <div class="mb-8"> <h1 class="text-3xl font-bold tracking-tight mb-2">Категории запчастей</h1> <p class="text-muted-foreground">
Иерархическая структура каталога запчастей
</p> </div> <!-- Основные категории --> ${rootCategories.length > 0 && renderTemplate`<div class="mb-12"> <h2 class="text-2xl font-semibold mb-6">Основные категории</h2> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"> ${rootCategories.map((category) => renderTemplate`${renderComponent($$result3, "CategoryCard", CategoryCard, { "category": category, "client:load": true, "client:component-hydration": "load", "client:component-path": "@/components/catalog/CategoryCard", "client:component-export": "CategoryCard" })}`)} </div> </div>`} <!-- Подкатегории по группам --> ${rootCategories.map((rootCategory) => {
    const childCategories = subcategories.filter(
      (cat) => cat.path.startsWith(rootCategory.path + "/") && cat.level === 1
    );
    if (childCategories.length === 0) return null;
    return renderTemplate`<div class="mb-12"> <h2 class="text-2xl font-semibold mb-6 flex items-center gap-2"> ${rootCategory.icon && renderTemplate`<span class="text-2xl">${rootCategory.icon}</span>`} ${rootCategory.name} </h2> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"> ${childCategories.map((category) => renderTemplate`${renderComponent($$result3, "CategoryCard", CategoryCard, { "category": category, "client:load": true, "client:component-hydration": "load", "client:component-path": "@/components/catalog/CategoryCard", "client:component-export": "CategoryCard" })}`)} </div> </div>`;
  })} <!-- Если нет категорий --> ${categories.length === 0 && renderTemplate`<div class="text-center py-12"> <h3 class="text-lg font-semibold mb-2">Категории не найдены</h3> <p class="text-muted-foreground">
Каталог категорий пуст или недоступен
</p> </div>`} </div> ` })} ` })}`;
}, "D:/Dev/parttec/site/src/pages/categories.astro", void 0);

const $$file = "D:/Dev/parttec/site/src/pages/categories.astro";
const $$url = "/categories";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Categories,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
