import { watch, type Ref, onScopeDispose } from 'vue';
import { useErrorHandler } from '@/composables/useErrorHandler';

// Подключаем централизованный показ ошибок из vue-query
export function useQueryToastErrors(errorRef: Ref<Error | null>) {
  const { handleError } = useErrorHandler();
  const shownErrors = new Set<string>();

  const stopWatcher = watch(errorRef, (err) => {
    if (err) {
      const errorKey = err.message + err.stack;
      if (!shownErrors.has(errorKey)) {
        shownErrors.add(errorKey);
        handleError(err);
      }
    }
  });

  onScopeDispose(() => {
    stopWatcher();
    shownErrors.clear();
  });
}

