import { e as createComponent, m as maybeRenderHead, r as renderTemplate } from '../chunks/astro/server_DbndhTWv.mjs';
import 'kleur/colors';
import 'clsx';
export { r as renderers } from '../chunks/_@astro-renderers_CicWY1rm.mjs';

const $$Index = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${maybeRenderHead()}<div>
welcome
</div>`;
}, "D:/Dev/parttec/cpanel/src/pages/index.astro", void 0);

const $$file = "D:/Dev/parttec/cpanel/src/pages/index.astro";
const $$url = "";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Index,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
