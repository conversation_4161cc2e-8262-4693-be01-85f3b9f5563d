import{A as p,x as f}from"./index.BglzLLgy.js";function a(e){"@babel/helpers - typeof";return a=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(e)}function d(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function b(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,m(n.key),n)}}function h(e,t,r){return t&&b(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function m(e){var t=_(e,"string");return a(t)=="symbol"?t:t+""}function _(e,t){if(a(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(a(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var L=function(){function e(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:function(){};d(this,e),this.element=t,this.listener=r}return h(e,[{key:"bindScrollListener",value:function(){this.scrollableParents=p(this.element);for(var r=0;r<this.scrollableParents.length;r++)this.scrollableParents[r].addEventListener("scroll",this.listener)}},{key:"unbindScrollListener",value:function(){if(this.scrollableParents)for(var r=0;r<this.scrollableParents.length;r++)this.scrollableParents[r].removeEventListener("scroll",this.listener)}},{key:"destroy",value:function(){this.unbindScrollListener(),this.element=null,this.listener=null,this.scrollableParents=null}}])}();function u(e){"@babel/helpers - typeof";return u=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(e)}function g(e){return k(e)||w(e)||P(e)||S()}function S(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function P(e,t){if(e){if(typeof e=="string")return c(e,t);var r={}.toString.call(e).slice(8,-1);return r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set"?Array.from(e):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?c(e,t):void 0}}function w(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function k(e){if(Array.isArray(e))return c(e)}function c(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function A(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function C(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,v(n.key),n)}}function j(e,t,r){return t&&C(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function y(e,t,r){return(t=v(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function v(e){var t=$(e,"string");return u(t)=="symbol"?t:t+""}function $(e,t){if(u(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(u(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var E=function(){function e(t){var r=t.init,n=t.type;A(this,e),y(this,"helpers",void 0),y(this,"type",void 0),this.helpers=new Set(r),this.type=n}return j(e,[{key:"add",value:function(r){this.helpers.add(r)}},{key:"update",value:function(){}},{key:"delete",value:function(r){this.helpers.delete(r)}},{key:"clear",value:function(){this.helpers.clear()}},{key:"get",value:function(r,n){var o=this._get(r,n),i=o?this._recursive(g(this.helpers),o):null;return f(i)?i:null}},{key:"_isMatched",value:function(r,n){var o,i=r?.parent;return(i==null||(o=i.vnode)===null||o===void 0?void 0:o.key)===n||i&&this._isMatched(i,n)||!1}},{key:"_get",value:function(r,n){var o,i;return((o=n||r?.$slots)===null||o===void 0||(i=o.default)===null||i===void 0?void 0:i.call(o))||null}},{key:"_recursive",value:function(){var r=this,n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],i=[];return o.forEach(function(l){l.children instanceof Array?i=i.concat(r._recursive(i,l.children)):l.type.name===r.type?i.push(l):f(l.key)&&(i=i.concat(n.filter(function(s){return r._isMatched(s,l.key)}).map(function(s){return s.vnode})))}),i}}])}();function T(e,t){if(e){var r=e.props;if(r){var n=t.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),o=Object.prototype.hasOwnProperty.call(r,n)?n:t;return e.type.extends.props[t].type===Boolean&&r[o]===""?!0:r[o]}}return null}export{L as C,E as _,T as g};
