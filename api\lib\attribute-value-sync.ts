/**
 * Утилита для синхронизации строковых и числовых значений атрибутов
 * 
 * Обеспечивает надежную двунаправленную синхронизацию между:
 * - value (String) - основное хранилище значений
 * - numericValue (Float) - дублирование для оптимизации запросов
 */

/**
 * Проверяет, является ли строка числом
 * Поддерживает различные форматы чисел, включая:
 * - Целые числа: "123", "-45"
 * - Десятичные: "12.34", "-0.5", ".5", "5."
 * - Экспоненциальная нотация: "1.5e10", "1E-5"
 */
export function isNumericString(value: string): boolean {
    if (!value || typeof value !== 'string') {
        return false;
    }
    
    // Убираем пробелы по краям
    const trimmed = value.trim();
    
    if (trimmed === '') {
        return false;
    }
    
    // Проверяем через преобразование в число
    const num = Number(trimmed);
    
    // Number() возвращает NaN для нечисловых строк
    // и Infinity для слишком больших чисел
    return !isNaN(num) && isFinite(num);
}

/**
 * Безопасно парсит строку в число
 * Возвращает null если строка не является числом
 */
export function parseNumericValue(value: string): number | null {
    if (!isNumericString(value)) {
        return null;
    }
    
    const num = Number(value.trim());
    
    // Дополнительная проверка на валидность
    if (isNaN(num) || !isFinite(num)) {
        return null;
    }
    
    return num;
}

/**
 * Интерфейс для данных атрибута с синхронизацией
 */
export interface AttributeData {
    value: string;
    numericValue?: number | null;
}

/**
 * Синхронизирует value и numericValue
 * 
 * Правила синхронизации:
 * 1. value всегда является источником правды
 * 2. numericValue автоматически вычисляется из value если это возможно
 * 3. Если value не является числом, numericValue = null
 * 
 * @param data - Входные данные атрибута
 * @returns Синхронизированные данные
 */
export function syncAttributeValues(data: Partial<AttributeData>): AttributeData {
    // value является обязательным
    const value = data.value || '';
    
    // Вычисляем numericValue на основе value
    const numericValue = parseNumericValue(value);
    
    return {
        value,
        numericValue
    };
}

/**
 * Хук для Prisma middleware - автоматическая синхронизация при создании/обновлении
 * 
 * Пример использования в Prisma Client Extensions:
 * ```typescript
 * const prisma = new PrismaClient().$extends({
 *   query: {
 *     partAttribute: {
 *       create: createAttributeMiddleware('partAttribute'),
 *       update: updateAttributeMiddleware('partAttribute'),
 *       upsert: upsertAttributeMiddleware('partAttribute'),
 *     },
 *     catalogItemAttribute: {
 *       create: createAttributeMiddleware('catalogItemAttribute'),
 *       update: updateAttributeMiddleware('catalogItemAttribute'),
 *       upsert: upsertAttributeMiddleware('catalogItemAttribute'),
 *     },
 *     // ... и так далее для других моделей атрибутов
 *   },
 * });
 * ```
 */

/**
 * Middleware для операции create
 */
export function createAttributeMiddleware(modelName: string) {
    return async ({ args, query }: any) => {
        // Синхронизируем значения перед созданием
        if (args.data?.value !== undefined) {
            const synced = syncAttributeValues({ value: args.data.value });
            args.data.numericValue = synced.numericValue;
        }
        
        // Обработка createMany
        if (args.data?.createMany?.data) {
            args.data.createMany.data = args.data.createMany.data.map((item: any) => {
                if (item.value !== undefined) {
                    const synced = syncAttributeValues({ value: item.value });
                    return { ...item, numericValue: synced.numericValue };
                }
                return item;
            });
        }
        
        return query(args);
    };
}

/**
 * Middleware для операции update
 */
export function updateAttributeMiddleware(modelName: string) {
    return async ({ args, query }: any) => {
        // Синхронизируем значения при обновлении
        if (args.data?.value !== undefined) {
            const synced = syncAttributeValues({ value: args.data.value });
            args.data.numericValue = synced.numericValue;
        }
        
        // Обработка updateMany
        if (args.data?.value?.set !== undefined) {
            const synced = syncAttributeValues({ value: args.data.value.set });
            args.data.numericValue = { set: synced.numericValue };
        }
        
        return query(args);
    };
}

/**
 * Middleware для операции upsert
 */
export function upsertAttributeMiddleware(modelName: string) {
    return async ({ args, query }: any) => {
        // Синхронизация для create
        if (args.create?.value !== undefined) {
            const synced = syncAttributeValues({ value: args.create.value });
            args.create.numericValue = synced.numericValue;
        }
        
        // Синхронизация для update
        if (args.update?.value !== undefined) {
            const synced = syncAttributeValues({ value: args.update.value });
            args.update.numericValue = synced.numericValue;
        }
        
        return query(args);
    };
}

/**
 * Вспомогательная функция для сравнения числовых значений с учетом допуска
 * 
 * @param value1 - Первое значение
 * @param value2 - Второе значение
 * @param tolerance - Допустимое отклонение (по умолчанию 0)
 * @returns true если значения равны с учетом допуска
 */
export function compareNumericValues(
    value1: number | null | undefined,
    value2: number | null | undefined,
    tolerance: number = 0
): boolean {
    // Если хотя бы одно значение не число - не совпадают
    if (value1 === null || value1 === undefined || 
        value2 === null || value2 === undefined) {
        return false;
    }
    
    // Сравниваем с учетом допуска
    return Math.abs(value1 - value2) <= Math.abs(tolerance);
}

/**
 * Вспомогательная функция для получения числового значения атрибута
 * с fallback на парсинг строкового значения
 * 
 * @param attribute - Атрибут с value и numericValue
 * @returns Числовое значение или null
 */
export function getNumericValue(attribute: { value: string; numericValue?: number | null }): number | null {
    // Приоритет у numericValue если оно есть
    if (attribute.numericValue !== null && attribute.numericValue !== undefined) {
        return attribute.numericValue;
    }
    
    // Fallback на парсинг строкового значения
    return parseNumericValue(attribute.value);
}

/**
 * Утилита для построения Prisma where-условий с поддержкой диапазонов
 * 
 * @param field - Имя поля ('value' или 'numericValue')
 * @param min - Минимальное значение (включительно)
 * @param max - Максимальное значение (включительно)
 * @param dataType - Тип данных атрибута
 */
export function buildRangeCondition(
    field: 'value' | 'numericValue',
    min?: number,
    max?: number,
    dataType?: 'STRING' | 'NUMBER'
): any {
    if (field === 'numericValue') {
        // Для numericValue используем числовое сравнение
        const condition: any = {};
        if (min !== undefined) condition.gte = min;
        if (max !== undefined) condition.lte = max;
        return { numericValue: condition };
    } else {
        // Для value в случае числовых данных возвращаем условие для numericValue
        // Это обеспечивает корректную работу диапазонных запросов
        if (dataType === 'NUMBER') {
            const condition: any = {};
            if (min !== undefined) condition.gte = min;
            if (max !== undefined) condition.lte = max;
            return { numericValue: condition };
        }
        
        // Для строковых данных используем обычное строковое сравнение
        return { value: { contains: field } };
    }
}
