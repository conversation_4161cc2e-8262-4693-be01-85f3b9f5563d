import{j as e}from"./jsx-runtime.D_zvdyIk.js";import"./index.0yr9KlQE.js";import{a as t}from"./auth-client.DHpPh7L9.js";import{B as n}from"./button.BfGcEciz.js";import"./index.BWNh2K4G.js";import"./index.3rXK4OIH.js";import"./utils.CBfrqCZ4.js";function d(){const{data:r,isPending:s}=t.useSession();return s?null:r?e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("a",{href:"/account",className:"text-sm text-foreground/80 hover:underline",children:"Кабинет"}),e.jsx(n,{size:"sm",variant:"outline",onClick:async()=>{await t.signOut({fetchOptions:{onSuccess:()=>window.location.href="/"}})},children:"Выйти"})]}):e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("a",{href:"/login",className:"text-sm text-foreground/80 hover:underline",children:"Вход"}),e.jsx("a",{href:"/register",className:"text-sm text-foreground/80 hover:underline",children:"Регистрация"})]})}export{d as AuthNav};
