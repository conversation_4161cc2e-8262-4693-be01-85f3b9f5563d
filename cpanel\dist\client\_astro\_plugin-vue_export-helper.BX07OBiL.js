import{an as Ke,a3 as $e,U as M,m as ae,a1 as de,a6 as qe,x as Pe,a2 as K,W as Xe,_ as B,$ as q,a0 as U,X as Je,a9 as Qe,ao as Ye}from"./index.BglzLLgy.js";function Ze(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"pc",e=Ke();return"".concat(t).concat(e.replace("v-","").replaceAll("-","_"))}var Te=M.extend({name:"common"});function Y(t){"@babel/helpers - typeof";return Y=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Y(t)}function et(t){return Le(t)||tt(t)||je(t)||Ve()}function tt(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function X(t,e){return Le(t)||rt(t,e)||je(t,e)||Ve()}function Ve(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function je(t,e){if(t){if(typeof t=="string")return Ce(t,e);var r={}.toString.call(t).slice(8,-1);return r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set"?Array.from(t):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Ce(t,e):void 0}}function Ce(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,o=Array(e);r<e;r++)o[r]=t[r];return o}function rt(t,e){var r=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var o,n,s,a,d=[],i=!0,f=!1;try{if(s=(r=r.call(t)).next,e===0){if(Object(r)!==r)return;i=!1}else for(;!(i=(o=s.call(r)).done)&&(d.push(o.value),d.length!==e);i=!0);}catch(u){f=!0,n=u}finally{try{if(!i&&r.return!=null&&(a=r.return(),Object(a)!==a))return}finally{if(f)throw n}}return d}}function Le(t){if(Array.isArray(t))return t}function Oe(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter(function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable})),r.push.apply(r,o)}return r}function g(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Oe(Object(r),!0).forEach(function(o){Q(t,o,r[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Oe(Object(r)).forEach(function(o){Object.defineProperty(t,o,Object.getOwnPropertyDescriptor(r,o))})}return t}function Q(t,e,r){return(e=ot(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ot(t){var e=nt(t,"string");return Y(e)=="symbol"?e:e+""}function nt(t,e){if(Y(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var o=r.call(t,e);if(Y(o)!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var Nt={name:"BaseComponent",props:{pt:{type:Object,default:void 0},ptOptions:{type:Object,default:void 0},unstyled:{type:Boolean,default:void 0},dt:{type:Object,default:void 0}},inject:{$parentInstance:{default:void 0}},watch:{isUnstyled:{immediate:!0,handler:function(e){B.off("theme:change",this._loadCoreStyles),e||(this._loadCoreStyles(),this._themeChangeListener(this._loadCoreStyles))}},dt:{immediate:!0,handler:function(e,r){var o=this;B.off("theme:change",this._themeScopedListener),e?(this._loadScopedThemeStyles(e),this._themeScopedListener=function(){return o._loadScopedThemeStyles(e)},this._themeChangeListener(this._themeScopedListener)):this._unloadScopedThemeStyles()}}},scopedStyleEl:void 0,rootEl:void 0,uid:void 0,$attrSelector:void 0,beforeCreate:function(){var e,r,o,n,s,a,d,i,f,u,b,v=(e=this.pt)===null||e===void 0?void 0:e._usept,y=v?(r=this.pt)===null||r===void 0||(r=r.originalValue)===null||r===void 0?void 0:r[this.$.type.name]:void 0,k=v?(o=this.pt)===null||o===void 0||(o=o.value)===null||o===void 0?void 0:o[this.$.type.name]:this.pt;(n=k||y)===null||n===void 0||(n=n.hooks)===null||n===void 0||(s=n.onBeforeCreate)===null||s===void 0||s.call(n);var x=(a=this.$primevueConfig)===null||a===void 0||(a=a.pt)===null||a===void 0?void 0:a._usept,T=x?(d=this.$primevue)===null||d===void 0||(d=d.config)===null||d===void 0||(d=d.pt)===null||d===void 0?void 0:d.originalValue:void 0,$=x?(i=this.$primevue)===null||i===void 0||(i=i.config)===null||i===void 0||(i=i.pt)===null||i===void 0?void 0:i.value:(f=this.$primevue)===null||f===void 0||(f=f.config)===null||f===void 0?void 0:f.pt;(u=$||T)===null||u===void 0||(u=u[this.$.type.name])===null||u===void 0||(u=u.hooks)===null||u===void 0||(b=u.onBeforeCreate)===null||b===void 0||b.call(u),this.$attrSelector=Ze(),this.uid=this.$attrs.id||this.$attrSelector.replace("pc","pv_id_")},created:function(){this._hook("onCreated")},beforeMount:function(){var e;this.rootEl=Qe(Ye(this.$el)?this.$el:(e=this.$el)===null||e===void 0?void 0:e.parentElement,"[".concat(this.$attrSelector,"]")),this.rootEl&&(this.rootEl.$pc=g({name:this.$.type.name,attrSelector:this.$attrSelector},this.$params)),this._loadStyles(),this._hook("onBeforeMount")},mounted:function(){this._hook("onMounted")},beforeUpdate:function(){this._hook("onBeforeUpdate")},updated:function(){this._hook("onUpdated")},beforeUnmount:function(){this._hook("onBeforeUnmount")},unmounted:function(){this._removeThemeListeners(),this._unloadScopedThemeStyles(),this._hook("onUnmounted")},methods:{_hook:function(e){if(!this.$options.hostName){var r=this._usePT(this._getPT(this.pt,this.$.type.name),this._getOptionValue,"hooks.".concat(e)),o=this._useDefaultPT(this._getOptionValue,"hooks.".concat(e));r?.(),o?.()}},_mergeProps:function(e){for(var r=arguments.length,o=new Array(r>1?r-1:0),n=1;n<r;n++)o[n-1]=arguments[n];return Je(e)?e.apply(void 0,o):ae.apply(void 0,o)},_load:function(){q.isStyleNameLoaded("base")||(M.loadCSS(this.$styleOptions),this._loadGlobalStyles(),q.setLoadedStyleName("base")),this._loadThemeStyles()},_loadStyles:function(){this._load(),this._themeChangeListener(this._load)},_loadCoreStyles:function(){var e,r;!q.isStyleNameLoaded((e=this.$style)===null||e===void 0?void 0:e.name)&&(r=this.$style)!==null&&r!==void 0&&r.name&&(Te.loadCSS(this.$styleOptions),this.$options.style&&this.$style.loadCSS(this.$styleOptions),q.setLoadedStyleName(this.$style.name))},_loadGlobalStyles:function(){var e=this._useGlobalPT(this._getOptionValue,"global.css",this.$params);Pe(e)&&M.load(e,g({name:"global"},this.$styleOptions))},_loadThemeStyles:function(){var e,r;if(!(this.isUnstyled||this.$theme==="none")){if(!U.isStyleNameLoaded("common")){var o,n,s=((o=this.$style)===null||o===void 0||(n=o.getCommonTheme)===null||n===void 0?void 0:n.call(o))||{},a=s.primitive,d=s.semantic,i=s.global,f=s.style;M.load(a?.css,g({name:"primitive-variables"},this.$styleOptions)),M.load(d?.css,g({name:"semantic-variables"},this.$styleOptions)),M.load(i?.css,g({name:"global-variables"},this.$styleOptions)),M.loadStyle(g({name:"global-style"},this.$styleOptions),f),U.setLoadedStyleName("common")}if(!U.isStyleNameLoaded((e=this.$style)===null||e===void 0?void 0:e.name)&&(r=this.$style)!==null&&r!==void 0&&r.name){var u,b,v,y,k=((u=this.$style)===null||u===void 0||(b=u.getComponentTheme)===null||b===void 0?void 0:b.call(u))||{},x=k.css,T=k.style;(v=this.$style)===null||v===void 0||v.load(x,g({name:"".concat(this.$style.name,"-variables")},this.$styleOptions)),(y=this.$style)===null||y===void 0||y.loadStyle(g({name:"".concat(this.$style.name,"-style")},this.$styleOptions),T),U.setLoadedStyleName(this.$style.name)}if(!U.isStyleNameLoaded("layer-order")){var $,A,V=($=this.$style)===null||$===void 0||(A=$.getLayerOrderThemeCSS)===null||A===void 0?void 0:A.call($);M.load(V,g({name:"layer-order",first:!0},this.$styleOptions)),U.setLoadedStyleName("layer-order")}}},_loadScopedThemeStyles:function(e){var r,o,n,s=((r=this.$style)===null||r===void 0||(o=r.getPresetTheme)===null||o===void 0?void 0:o.call(r,e,"[".concat(this.$attrSelector,"]")))||{},a=s.css,d=(n=this.$style)===null||n===void 0?void 0:n.load(a,g({name:"".concat(this.$attrSelector,"-").concat(this.$style.name)},this.$styleOptions));this.scopedStyleEl=d.el},_unloadScopedThemeStyles:function(){var e;(e=this.scopedStyleEl)===null||e===void 0||(e=e.value)===null||e===void 0||e.remove()},_themeChangeListener:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:function(){};q.clearLoadedStyleNames(),B.on("theme:change",e)},_removeThemeListeners:function(){B.off("theme:change",this._loadCoreStyles),B.off("theme:change",this._load),B.off("theme:change",this._themeScopedListener)},_getHostInstance:function(e){return e?this.$options.hostName?e.$.type.name===this.$options.hostName?e:this._getHostInstance(e.$parentInstance):e.$parentInstance:void 0},_getPropValue:function(e){var r;return this[e]||((r=this._getHostInstance(this))===null||r===void 0?void 0:r[e])},_getOptionValue:function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"",o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return Xe(e,r,o)},_getPTValue:function(){var e,r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"",n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,a=/./g.test(o)&&!!n[o.split(".")[0]],d=this._getPropValue("ptOptions")||((e=this.$primevueConfig)===null||e===void 0?void 0:e.ptOptions)||{},i=d.mergeSections,f=i===void 0?!0:i,u=d.mergeProps,b=u===void 0?!1:u,v=s?a?this._useGlobalPT(this._getPTClassValue,o,n):this._useDefaultPT(this._getPTClassValue,o,n):void 0,y=a?void 0:this._getPTSelf(r,this._getPTClassValue,o,g(g({},n),{},{global:v||{}})),k=this._getPTDatasets(o);return f||!f&&y?b?this._mergeProps(b,v,y,k):g(g(g({},v),y),k):g(g({},y),k)},_getPTSelf:function(){for(var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=arguments.length,o=new Array(r>1?r-1:0),n=1;n<r;n++)o[n-1]=arguments[n];return ae(this._usePT.apply(this,[this._getPT(e,this.$name)].concat(o)),this._usePT.apply(this,[this.$_attrsPT].concat(o)))},_getPTDatasets:function(){var e,r,o=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",n="data-pc-",s=o==="root"&&Pe((e=this.pt)===null||e===void 0?void 0:e["data-pc-section"]);return o!=="transition"&&g(g({},o==="root"&&g(g(Q({},"".concat(n,"name"),K(s?(r=this.pt)===null||r===void 0?void 0:r["data-pc-section"]:this.$.type.name)),s&&Q({},"".concat(n,"extend"),K(this.$.type.name))),{},Q({},"".concat(this.$attrSelector),""))),{},Q({},"".concat(n,"section"),K(o)))},_getPTClassValue:function(){var e=this._getOptionValue.apply(this,arguments);return de(e)||qe(e)?{class:e}:e},_getPT:function(e){var r=this,o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"",n=arguments.length>2?arguments[2]:void 0,s=function(d){var i,f=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,u=n?n(d):d,b=K(o),v=K(r.$name);return(i=f?b!==v?u?.[b]:void 0:u?.[b])!==null&&i!==void 0?i:u};return e!=null&&e.hasOwnProperty("_usept")?{_usept:e._usept,originalValue:s(e.originalValue),value:s(e.value)}:s(e,!0)},_usePT:function(e,r,o,n){var s=function(x){return r(x,o,n)};if(e!=null&&e.hasOwnProperty("_usept")){var a,d=e._usept||((a=this.$primevueConfig)===null||a===void 0?void 0:a.ptOptions)||{},i=d.mergeSections,f=i===void 0?!0:i,u=d.mergeProps,b=u===void 0?!1:u,v=s(e.originalValue),y=s(e.value);return v===void 0&&y===void 0?void 0:de(y)?y:de(v)?v:f||!f&&y?b?this._mergeProps(b,v,y):g(g({},v),y):y}return s(e)},_useGlobalPT:function(e,r,o){return this._usePT(this.globalPT,e,r,o)},_useDefaultPT:function(e,r,o){return this._usePT(this.defaultPT,e,r,o)},ptm:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return this._getPTValue(this.pt,e,g(g({},this.$params),r))},ptmi:function(){var e,r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=ae(this.$_attrsWithoutPT,this.ptm(r,o));return n?.hasOwnProperty("id")&&((e=n.id)!==null&&e!==void 0||(n.id=this.$id)),n},ptmo:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"",o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return this._getPTValue(e,r,g({instance:this},o),!1)},cx:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return this.isUnstyled?void 0:this._getOptionValue(this.$style.classes,e,g(g({},this.$params),r))},sx:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};if(r){var n=this._getOptionValue(this.$style.inlineStyles,e,g(g({},this.$params),o)),s=this._getOptionValue(Te.inlineStyles,e,g(g({},this.$params),o));return[s,n]}}},computed:{globalPT:function(){var e,r=this;return this._getPT((e=this.$primevueConfig)===null||e===void 0?void 0:e.pt,void 0,function(o){return $e(o,{instance:r})})},defaultPT:function(){var e,r=this;return this._getPT((e=this.$primevueConfig)===null||e===void 0?void 0:e.pt,void 0,function(o){return r._getOptionValue(o,r.$name,g({},r.$params))||$e(o,g({},r.$params))})},isUnstyled:function(){var e;return this.unstyled!==void 0?this.unstyled:(e=this.$primevueConfig)===null||e===void 0?void 0:e.unstyled},$id:function(){return this.$attrs.id||this.uid},$inProps:function(){var e,r=Object.keys(((e=this.$.vnode)===null||e===void 0?void 0:e.props)||{});return Object.fromEntries(Object.entries(this.$props).filter(function(o){var n=X(o,1),s=n[0];return r?.includes(s)}))},$theme:function(){var e;return(e=this.$primevueConfig)===null||e===void 0?void 0:e.theme},$style:function(){return g(g({classes:void 0,inlineStyles:void 0,load:function(){},loadCSS:function(){},loadStyle:function(){}},(this._getHostInstance(this)||{}).$style),this.$options.style)},$styleOptions:function(){var e;return{nonce:(e=this.$primevueConfig)===null||e===void 0||(e=e.csp)===null||e===void 0?void 0:e.nonce}},$primevueConfig:function(){var e;return(e=this.$primevue)===null||e===void 0?void 0:e.config},$name:function(){return this.$options.hostName||this.$.type.name},$params:function(){var e=this._getHostInstance(this)||this.$parent;return{instance:this,props:this.$props,state:this.$data,attrs:this.$attrs,parent:{instance:e,props:e?.$props,state:e?.$data,attrs:e?.$attrs}}},$_attrsPT:function(){return Object.entries(this.$attrs||{}).filter(function(e){var r=X(e,1),o=r[0];return o?.startsWith("pt:")}).reduce(function(e,r){var o=X(r,2),n=o[0],s=o[1],a=n.split(":"),d=et(a),i=d.slice(1);return i?.reduce(function(f,u,b,v){return!f[u]&&(f[u]=b===v.length-1?s:{}),f[u]},e),e},{})},$_attrsWithoutPT:function(){return Object.entries(this.$attrs||{}).filter(function(e){var r=X(e,1),o=r[0];return!(o!=null&&o.startsWith("pt:"))}).reduce(function(e,r){var o=X(r,2),n=o[0],s=o[1];return e[n]=s,e},{})}}};const ge="-",st=t=>{const e=it(t),{conflictingClassGroups:r,conflictingClassGroupModifiers:o}=t;return{getClassGroupId:a=>{const d=a.split(ge);return d[0]===""&&d.length!==1&&d.shift(),Ge(d,e)||at(a)},getConflictingClassGroupIds:(a,d)=>{const i=r[a]||[];return d&&o[a]?[...i,...o[a]]:i}}},Ge=(t,e)=>{if(t.length===0)return e.classGroupId;const r=t[0],o=e.nextPart.get(r),n=o?Ge(t.slice(1),o):void 0;if(n)return n;if(e.validators.length===0)return;const s=t.join(ge);return e.validators.find(({validator:a})=>a(s))?.classGroupId},ze=/^\[(.+)\]$/,at=t=>{if(ze.test(t)){const e=ze.exec(t)[1],r=e?.substring(0,e.indexOf(":"));if(r)return"arbitrary.."+r}},it=t=>{const{theme:e,classGroups:r}=t,o={nextPart:new Map,validators:[]};for(const n in r)pe(r[n],o,n,e);return o},pe=(t,e,r,o)=>{t.forEach(n=>{if(typeof n=="string"){const s=n===""?e:Ae(e,n);s.classGroupId=r;return}if(typeof n=="function"){if(lt(n)){pe(n(o),e,r,o);return}e.validators.push({validator:n,classGroupId:r});return}Object.entries(n).forEach(([s,a])=>{pe(a,Ae(e,s),r,o)})})},Ae=(t,e)=>{let r=t;return e.split(ge).forEach(o=>{r.nextPart.has(o)||r.nextPart.set(o,{nextPart:new Map,validators:[]}),r=r.nextPart.get(o)}),r},lt=t=>t.isThemeGetter,ct=t=>{if(t<1)return{get:()=>{},set:()=>{}};let e=0,r=new Map,o=new Map;const n=(s,a)=>{r.set(s,a),e++,e>t&&(e=0,o=r,r=new Map)};return{get(s){let a=r.get(s);if(a!==void 0)return a;if((a=o.get(s))!==void 0)return n(s,a),a},set(s,a){r.has(s)?r.set(s,a):n(s,a)}}},fe="!",he=":",dt=he.length,ut=t=>{const{prefix:e,experimentalParseClassName:r}=t;let o=n=>{const s=[];let a=0,d=0,i=0,f;for(let k=0;k<n.length;k++){let x=n[k];if(a===0&&d===0){if(x===he){s.push(n.slice(i,k)),i=k+dt;continue}if(x==="/"){f=k;continue}}x==="["?a++:x==="]"?a--:x==="("?d++:x===")"&&d--}const u=s.length===0?n:n.substring(i),b=mt(u),v=b!==u,y=f&&f>i?f-i:void 0;return{modifiers:s,hasImportantModifier:v,baseClassName:b,maybePostfixModifierPosition:y}};if(e){const n=e+he,s=o;o=a=>a.startsWith(n)?s(a.substring(n.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:a,maybePostfixModifierPosition:void 0}}if(r){const n=o;o=s=>r({className:s,parseClassName:n})}return o},mt=t=>t.endsWith(fe)?t.substring(0,t.length-1):t.startsWith(fe)?t.substring(1):t,pt=t=>{const e=Object.fromEntries(t.orderSensitiveModifiers.map(o=>[o,!0]));return o=>{if(o.length<=1)return o;const n=[];let s=[];return o.forEach(a=>{a[0]==="["||e[a]?(n.push(...s.sort(),a),s=[]):s.push(a)}),n.push(...s.sort()),n}},ft=t=>({cache:ct(t.cacheSize),parseClassName:ut(t),sortModifiers:pt(t),...st(t)}),ht=/\s+/,gt=(t,e)=>{const{parseClassName:r,getClassGroupId:o,getConflictingClassGroupIds:n,sortModifiers:s}=e,a=[],d=t.trim().split(ht);let i="";for(let f=d.length-1;f>=0;f-=1){const u=d[f],{isExternal:b,modifiers:v,hasImportantModifier:y,baseClassName:k,maybePostfixModifierPosition:x}=r(u);if(b){i=u+(i.length>0?" "+i:i);continue}let T=!!x,$=o(T?k.substring(0,x):k);if(!$){if(!T){i=u+(i.length>0?" "+i:i);continue}if($=o(k),!$){i=u+(i.length>0?" "+i:i);continue}T=!1}const A=s(v).join(":"),V=y?A+fe:A,R=V+$;if(a.includes(R))continue;a.push(R);const E=n($,T);for(let j=0;j<E.length;++j){const H=E[j];a.push(V+H)}i=u+(i.length>0?" "+i:i)}return i};function bt(){let t=0,e,r,o="";for(;t<arguments.length;)(e=arguments[t++])&&(r=Re(e))&&(o&&(o+=" "),o+=r);return o}const Re=t=>{if(typeof t=="string")return t;let e,r="";for(let o=0;o<t.length;o++)t[o]&&(e=Re(t[o]))&&(r&&(r+=" "),r+=e);return r};function vt(t,...e){let r,o,n,s=a;function a(i){const f=e.reduce((u,b)=>b(u),t());return r=ft(f),o=r.cache.get,n=r.cache.set,s=d,d(i)}function d(i){const f=o(i);if(f)return f;const u=gt(i,r);return n(i,u),u}return function(){return s(bt.apply(null,arguments))}}const w=t=>{const e=r=>r[t]||[];return e.isThemeGetter=!0,e},Ee=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Ne=/^\((?:(\w[\w-]*):)?(.+)\)$/i,yt=/^\d+\/\d+$/,kt=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,wt=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,xt=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,_t=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,St=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,W=t=>yt.test(t),h=t=>!!t&&!Number.isNaN(Number(t)),I=t=>!!t&&Number.isInteger(Number(t)),ue=t=>t.endsWith("%")&&h(t.slice(0,-1)),z=t=>kt.test(t),$t=()=>!0,Pt=t=>wt.test(t)&&!xt.test(t),Be=()=>!1,Tt=t=>_t.test(t),Ct=t=>St.test(t),Ot=t=>!l(t)&&!c(t),zt=t=>D(t,De,Be),l=t=>Ee.test(t),G=t=>D(t,Fe,Pt),me=t=>D(t,jt,h),Ie=t=>D(t,Ue,Be),At=t=>D(t,We,Ct),ne=t=>D(t,He,Tt),c=t=>Ne.test(t),J=t=>F(t,Fe),It=t=>F(t,Lt),Me=t=>F(t,Ue),Mt=t=>F(t,De),Vt=t=>F(t,We),se=t=>F(t,He,!0),D=(t,e,r)=>{const o=Ee.exec(t);return o?o[1]?e(o[1]):r(o[2]):!1},F=(t,e,r=!1)=>{const o=Ne.exec(t);return o?o[1]?e(o[1]):r:!1},Ue=t=>t==="position"||t==="percentage",We=t=>t==="image"||t==="url",De=t=>t==="length"||t==="size"||t==="bg-size",Fe=t=>t==="length",jt=t=>t==="number",Lt=t=>t==="family-name",He=t=>t==="shadow",Gt=()=>{const t=w("color"),e=w("font"),r=w("text"),o=w("font-weight"),n=w("tracking"),s=w("leading"),a=w("breakpoint"),d=w("container"),i=w("spacing"),f=w("radius"),u=w("shadow"),b=w("inset-shadow"),v=w("text-shadow"),y=w("drop-shadow"),k=w("blur"),x=w("perspective"),T=w("aspect"),$=w("ease"),A=w("animate"),V=()=>["auto","avoid","all","avoid-page","page","left","right","column"],R=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],E=()=>[...R(),c,l],j=()=>["auto","hidden","clip","visible","scroll"],H=()=>["auto","contain","none"],p=()=>[c,l,i],C=()=>[W,"full","auto",...p()],be=()=>[I,"none","subgrid",c,l],ve=()=>["auto",{span:["full",I,c,l]},I,c,l],Z=()=>[I,"auto",c,l],ye=()=>["auto","min","max","fr",c,l],ie=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],N=()=>["start","end","center","stretch","center-safe","end-safe"],O=()=>["auto",...p()],L=()=>[W,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...p()],m=()=>[t,c,l],ke=()=>[...R(),Me,Ie,{position:[c,l]}],we=()=>["no-repeat",{repeat:["","x","y","space","round"]}],xe=()=>["auto","cover","contain",Mt,zt,{size:[c,l]}],le=()=>[ue,J,G],S=()=>["","none","full",f,c,l],P=()=>["",h,J,G],ee=()=>["solid","dashed","dotted","double"],_e=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],_=()=>[h,ue,Me,Ie],Se=()=>["","none",k,c,l],te=()=>["none",h,c,l],re=()=>["none",h,c,l],ce=()=>[h,c,l],oe=()=>[W,"full",...p()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[z],breakpoint:[z],color:[$t],container:[z],"drop-shadow":[z],ease:["in","out","in-out"],font:[Ot],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[z],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[z],shadow:[z],spacing:["px",h],text:[z],"text-shadow":[z],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",W,l,c,T]}],container:["container"],columns:[{columns:[h,l,c,d]}],"break-after":[{"break-after":V()}],"break-before":[{"break-before":V()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:E()}],overflow:[{overflow:j()}],"overflow-x":[{"overflow-x":j()}],"overflow-y":[{"overflow-y":j()}],overscroll:[{overscroll:H()}],"overscroll-x":[{"overscroll-x":H()}],"overscroll-y":[{"overscroll-y":H()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:C()}],"inset-x":[{"inset-x":C()}],"inset-y":[{"inset-y":C()}],start:[{start:C()}],end:[{end:C()}],top:[{top:C()}],right:[{right:C()}],bottom:[{bottom:C()}],left:[{left:C()}],visibility:["visible","invisible","collapse"],z:[{z:[I,"auto",c,l]}],basis:[{basis:[W,"full","auto",d,...p()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[h,W,"auto","initial","none",l]}],grow:[{grow:["",h,c,l]}],shrink:[{shrink:["",h,c,l]}],order:[{order:[I,"first","last","none",c,l]}],"grid-cols":[{"grid-cols":be()}],"col-start-end":[{col:ve()}],"col-start":[{"col-start":Z()}],"col-end":[{"col-end":Z()}],"grid-rows":[{"grid-rows":be()}],"row-start-end":[{row:ve()}],"row-start":[{"row-start":Z()}],"row-end":[{"row-end":Z()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":ye()}],"auto-rows":[{"auto-rows":ye()}],gap:[{gap:p()}],"gap-x":[{"gap-x":p()}],"gap-y":[{"gap-y":p()}],"justify-content":[{justify:[...ie(),"normal"]}],"justify-items":[{"justify-items":[...N(),"normal"]}],"justify-self":[{"justify-self":["auto",...N()]}],"align-content":[{content:["normal",...ie()]}],"align-items":[{items:[...N(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...N(),{baseline:["","last"]}]}],"place-content":[{"place-content":ie()}],"place-items":[{"place-items":[...N(),"baseline"]}],"place-self":[{"place-self":["auto",...N()]}],p:[{p:p()}],px:[{px:p()}],py:[{py:p()}],ps:[{ps:p()}],pe:[{pe:p()}],pt:[{pt:p()}],pr:[{pr:p()}],pb:[{pb:p()}],pl:[{pl:p()}],m:[{m:O()}],mx:[{mx:O()}],my:[{my:O()}],ms:[{ms:O()}],me:[{me:O()}],mt:[{mt:O()}],mr:[{mr:O()}],mb:[{mb:O()}],ml:[{ml:O()}],"space-x":[{"space-x":p()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":p()}],"space-y-reverse":["space-y-reverse"],size:[{size:L()}],w:[{w:[d,"screen",...L()]}],"min-w":[{"min-w":[d,"screen","none",...L()]}],"max-w":[{"max-w":[d,"screen","none","prose",{screen:[a]},...L()]}],h:[{h:["screen","lh",...L()]}],"min-h":[{"min-h":["screen","lh","none",...L()]}],"max-h":[{"max-h":["screen","lh",...L()]}],"font-size":[{text:["base",r,J,G]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[o,c,me]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",ue,l]}],"font-family":[{font:[It,l,e]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[n,c,l]}],"line-clamp":[{"line-clamp":[h,"none",c,me]}],leading:[{leading:[s,...p()]}],"list-image":[{"list-image":["none",c,l]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",c,l]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:m()}],"text-color":[{text:m()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ee(),"wavy"]}],"text-decoration-thickness":[{decoration:[h,"from-font","auto",c,G]}],"text-decoration-color":[{decoration:m()}],"underline-offset":[{"underline-offset":[h,"auto",c,l]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:p()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",c,l]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",c,l]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:ke()}],"bg-repeat":[{bg:we()}],"bg-size":[{bg:xe()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},I,c,l],radial:["",c,l],conic:[I,c,l]},Vt,At]}],"bg-color":[{bg:m()}],"gradient-from-pos":[{from:le()}],"gradient-via-pos":[{via:le()}],"gradient-to-pos":[{to:le()}],"gradient-from":[{from:m()}],"gradient-via":[{via:m()}],"gradient-to":[{to:m()}],rounded:[{rounded:S()}],"rounded-s":[{"rounded-s":S()}],"rounded-e":[{"rounded-e":S()}],"rounded-t":[{"rounded-t":S()}],"rounded-r":[{"rounded-r":S()}],"rounded-b":[{"rounded-b":S()}],"rounded-l":[{"rounded-l":S()}],"rounded-ss":[{"rounded-ss":S()}],"rounded-se":[{"rounded-se":S()}],"rounded-ee":[{"rounded-ee":S()}],"rounded-es":[{"rounded-es":S()}],"rounded-tl":[{"rounded-tl":S()}],"rounded-tr":[{"rounded-tr":S()}],"rounded-br":[{"rounded-br":S()}],"rounded-bl":[{"rounded-bl":S()}],"border-w":[{border:P()}],"border-w-x":[{"border-x":P()}],"border-w-y":[{"border-y":P()}],"border-w-s":[{"border-s":P()}],"border-w-e":[{"border-e":P()}],"border-w-t":[{"border-t":P()}],"border-w-r":[{"border-r":P()}],"border-w-b":[{"border-b":P()}],"border-w-l":[{"border-l":P()}],"divide-x":[{"divide-x":P()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":P()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ee(),"hidden","none"]}],"divide-style":[{divide:[...ee(),"hidden","none"]}],"border-color":[{border:m()}],"border-color-x":[{"border-x":m()}],"border-color-y":[{"border-y":m()}],"border-color-s":[{"border-s":m()}],"border-color-e":[{"border-e":m()}],"border-color-t":[{"border-t":m()}],"border-color-r":[{"border-r":m()}],"border-color-b":[{"border-b":m()}],"border-color-l":[{"border-l":m()}],"divide-color":[{divide:m()}],"outline-style":[{outline:[...ee(),"none","hidden"]}],"outline-offset":[{"outline-offset":[h,c,l]}],"outline-w":[{outline:["",h,J,G]}],"outline-color":[{outline:m()}],shadow:[{shadow:["","none",u,se,ne]}],"shadow-color":[{shadow:m()}],"inset-shadow":[{"inset-shadow":["none",b,se,ne]}],"inset-shadow-color":[{"inset-shadow":m()}],"ring-w":[{ring:P()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:m()}],"ring-offset-w":[{"ring-offset":[h,G]}],"ring-offset-color":[{"ring-offset":m()}],"inset-ring-w":[{"inset-ring":P()}],"inset-ring-color":[{"inset-ring":m()}],"text-shadow":[{"text-shadow":["none",v,se,ne]}],"text-shadow-color":[{"text-shadow":m()}],opacity:[{opacity:[h,c,l]}],"mix-blend":[{"mix-blend":[..._e(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":_e()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[h]}],"mask-image-linear-from-pos":[{"mask-linear-from":_()}],"mask-image-linear-to-pos":[{"mask-linear-to":_()}],"mask-image-linear-from-color":[{"mask-linear-from":m()}],"mask-image-linear-to-color":[{"mask-linear-to":m()}],"mask-image-t-from-pos":[{"mask-t-from":_()}],"mask-image-t-to-pos":[{"mask-t-to":_()}],"mask-image-t-from-color":[{"mask-t-from":m()}],"mask-image-t-to-color":[{"mask-t-to":m()}],"mask-image-r-from-pos":[{"mask-r-from":_()}],"mask-image-r-to-pos":[{"mask-r-to":_()}],"mask-image-r-from-color":[{"mask-r-from":m()}],"mask-image-r-to-color":[{"mask-r-to":m()}],"mask-image-b-from-pos":[{"mask-b-from":_()}],"mask-image-b-to-pos":[{"mask-b-to":_()}],"mask-image-b-from-color":[{"mask-b-from":m()}],"mask-image-b-to-color":[{"mask-b-to":m()}],"mask-image-l-from-pos":[{"mask-l-from":_()}],"mask-image-l-to-pos":[{"mask-l-to":_()}],"mask-image-l-from-color":[{"mask-l-from":m()}],"mask-image-l-to-color":[{"mask-l-to":m()}],"mask-image-x-from-pos":[{"mask-x-from":_()}],"mask-image-x-to-pos":[{"mask-x-to":_()}],"mask-image-x-from-color":[{"mask-x-from":m()}],"mask-image-x-to-color":[{"mask-x-to":m()}],"mask-image-y-from-pos":[{"mask-y-from":_()}],"mask-image-y-to-pos":[{"mask-y-to":_()}],"mask-image-y-from-color":[{"mask-y-from":m()}],"mask-image-y-to-color":[{"mask-y-to":m()}],"mask-image-radial":[{"mask-radial":[c,l]}],"mask-image-radial-from-pos":[{"mask-radial-from":_()}],"mask-image-radial-to-pos":[{"mask-radial-to":_()}],"mask-image-radial-from-color":[{"mask-radial-from":m()}],"mask-image-radial-to-color":[{"mask-radial-to":m()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":R()}],"mask-image-conic-pos":[{"mask-conic":[h]}],"mask-image-conic-from-pos":[{"mask-conic-from":_()}],"mask-image-conic-to-pos":[{"mask-conic-to":_()}],"mask-image-conic-from-color":[{"mask-conic-from":m()}],"mask-image-conic-to-color":[{"mask-conic-to":m()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:ke()}],"mask-repeat":[{mask:we()}],"mask-size":[{mask:xe()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",c,l]}],filter:[{filter:["","none",c,l]}],blur:[{blur:Se()}],brightness:[{brightness:[h,c,l]}],contrast:[{contrast:[h,c,l]}],"drop-shadow":[{"drop-shadow":["","none",y,se,ne]}],"drop-shadow-color":[{"drop-shadow":m()}],grayscale:[{grayscale:["",h,c,l]}],"hue-rotate":[{"hue-rotate":[h,c,l]}],invert:[{invert:["",h,c,l]}],saturate:[{saturate:[h,c,l]}],sepia:[{sepia:["",h,c,l]}],"backdrop-filter":[{"backdrop-filter":["","none",c,l]}],"backdrop-blur":[{"backdrop-blur":Se()}],"backdrop-brightness":[{"backdrop-brightness":[h,c,l]}],"backdrop-contrast":[{"backdrop-contrast":[h,c,l]}],"backdrop-grayscale":[{"backdrop-grayscale":["",h,c,l]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[h,c,l]}],"backdrop-invert":[{"backdrop-invert":["",h,c,l]}],"backdrop-opacity":[{"backdrop-opacity":[h,c,l]}],"backdrop-saturate":[{"backdrop-saturate":[h,c,l]}],"backdrop-sepia":[{"backdrop-sepia":["",h,c,l]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":p()}],"border-spacing-x":[{"border-spacing-x":p()}],"border-spacing-y":[{"border-spacing-y":p()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",c,l]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[h,"initial",c,l]}],ease:[{ease:["linear","initial",$,c,l]}],delay:[{delay:[h,c,l]}],animate:[{animate:["none",A,c,l]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[x,c,l]}],"perspective-origin":[{"perspective-origin":E()}],rotate:[{rotate:te()}],"rotate-x":[{"rotate-x":te()}],"rotate-y":[{"rotate-y":te()}],"rotate-z":[{"rotate-z":te()}],scale:[{scale:re()}],"scale-x":[{"scale-x":re()}],"scale-y":[{"scale-y":re()}],"scale-z":[{"scale-z":re()}],"scale-3d":["scale-3d"],skew:[{skew:ce()}],"skew-x":[{"skew-x":ce()}],"skew-y":[{"skew-y":ce()}],transform:[{transform:[c,l,"","none","gpu","cpu"]}],"transform-origin":[{origin:E()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:oe()}],"translate-x":[{"translate-x":oe()}],"translate-y":[{"translate-y":oe()}],"translate-z":[{"translate-z":oe()}],"translate-none":["translate-none"],accent:[{accent:m()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:m()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",c,l]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":p()}],"scroll-mx":[{"scroll-mx":p()}],"scroll-my":[{"scroll-my":p()}],"scroll-ms":[{"scroll-ms":p()}],"scroll-me":[{"scroll-me":p()}],"scroll-mt":[{"scroll-mt":p()}],"scroll-mr":[{"scroll-mr":p()}],"scroll-mb":[{"scroll-mb":p()}],"scroll-ml":[{"scroll-ml":p()}],"scroll-p":[{"scroll-p":p()}],"scroll-px":[{"scroll-px":p()}],"scroll-py":[{"scroll-py":p()}],"scroll-ps":[{"scroll-ps":p()}],"scroll-pe":[{"scroll-pe":p()}],"scroll-pt":[{"scroll-pt":p()}],"scroll-pr":[{"scroll-pr":p()}],"scroll-pb":[{"scroll-pb":p()}],"scroll-pl":[{"scroll-pl":p()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",c,l]}],fill:[{fill:["none",...m()]}],"stroke-w":[{stroke:[h,J,G,me]}],stroke:[{stroke:["none",...m()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},Rt=vt(Gt),Bt=(t={},e={},r)=>{const{class:o,...n}=t,{class:s,...a}=e;return ae({class:Rt(o,s)},n,a,r)},Ut=(t,e)=>{const r=t.__vccOpts||t;for(const[o,n]of e)r[o]=n;return r};export{Ut as _,Bt as p,Nt as s};
