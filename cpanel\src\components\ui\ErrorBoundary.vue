<template>
  <div>
    <div v-if="hasError" :class="errorContainerClasses">
      <div :class="errorContentClasses">
        <!-- Error Icon -->
        <div :class="iconClasses">
          <AlertTriangle :class="iconSizeClasses" />
        </div>
        
        <!-- Error Content -->
        <div class="flex-1">
          <h3 :class="titleClasses">
            {{ title || 'Произошла ошибка' }}
          </h3>
          
          <p :class="messageClasses">
            {{ message || 'Что-то пошло не так. Пожалуйста, попробуйте обновить страницу.' }}
          </p>
          
          <!-- Error Details (only in development) -->
          <details v-if="showDetails && errorDetails" class="mt-4">
            <summary class="cursor-pointer text-sm text-[--color-muted] hover:text-[--color-foreground]">
              Подробности ошибки
            </summary>
            <pre :class="detailsClasses">{{ errorDetails }}</pre>
          </details>
        </div>
        
        <!-- Actions -->
        <div v-if="showActions" class="flex gap-2 mt-4">
          <button
            @click="retry"
            :class="retryButtonClasses"
          >
            <RotateCcw class="w-4 h-4" />
            Повторить
          </button>
          
          <button
            v-if="showReload"
            @click="reload"
            :class="reloadButtonClasses"
          >
            <RefreshCw class="w-4 h-4" />
            Обновить страницу
          </button>
        </div>
      </div>
    </div>
    
    <!-- Normal content when no error -->
    <slot v-else />
  </div>
</template>

<script setup lang="ts">
import { computed, onErrorCaptured, ref } from 'vue'
import { AlertTriangle, RotateCcw, RefreshCw } from 'lucide-vue-next'

interface Props {
  title?: string
  message?: string
  variant?: 'default' | 'minimal' | 'detailed'
  showActions?: boolean
  showReload?: boolean
  showDetails?: boolean
  onRetry?: () => void
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  showActions: true,
  showReload: true,
  showDetails: import.meta.env.DEV
})

const emit = defineEmits<{
  error: [error: Error]
  retry: []
}>()

const hasError = ref(false)
const errorDetails = ref<string>('')

// Capture errors from child components
onErrorCaptured((error: Error) => {
  hasError.value = true
  errorDetails.value = error.stack || error.message
  emit('error', error)
  
  // Log error in development
  if (import.meta.env.DEV) {
    console.error('ErrorBoundary caught an error:', error)
  }
  
  return false // Prevent error from propagating
})

const retry = () => {
  hasError.value = false
  errorDetails.value = ''
  
  if (props.onRetry) {
    props.onRetry()
  }
  
  emit('retry')
}

const reload = () => {
  window.location.reload()
}

// Computed classes
const errorContainerClasses = computed(() => {
  const base = 'w-full'
  const variants = {
    default: 'p-6 bg-[--color-card] border border-[--color-border] rounded-lg',
    minimal: 'p-4',
    detailed: 'p-8 bg-[--color-card] border border-[--color-border] rounded-xl shadow-sm'
  }
  
  return `${base} ${variants[props.variant]}`
})

const errorContentClasses = computed(() => {
  return props.variant === 'minimal' 
    ? 'flex items-start gap-3'
    : 'flex flex-col items-center text-center'
})

const iconClasses = computed(() => {
  const base = 'flex-shrink-0'
  const variants = {
    default: 'text-[--color-danger] mb-4',
    minimal: 'text-[--color-danger] mt-0.5',
    detailed: 'text-[--color-danger] mb-6 p-3 bg-red-50 dark:bg-red-950/20 rounded-full'
  }
  
  return `${base} ${variants[props.variant]}`
})

const iconSizeClasses = computed(() => {
  const variants = {
    default: 'w-8 h-8',
    minimal: 'w-5 h-5',
    detailed: 'w-12 h-12'
  }
  
  return variants[props.variant]
})

const titleClasses = computed(() => {
  const base = 'font-semibold text-[--color-foreground]'
  const variants = {
    default: 'text-lg mb-2',
    minimal: 'text-base mb-1',
    detailed: 'text-xl mb-3'
  }
  
  return `${base} ${variants[props.variant]}`
})

const messageClasses = computed(() => {
  const base = 'text-[--color-muted]'
  const variants = {
    default: 'text-sm',
    minimal: 'text-sm',
    detailed: 'text-base max-w-md'
  }
  
  return `${base} ${variants[props.variant]}`
})

const detailsClasses = computed(() => {
  return 'mt-2 p-3 bg-[--color-background] border border-[--color-border] rounded text-xs text-[--color-muted] overflow-auto max-h-32'
})

const retryButtonClasses = computed(() => {
  return 'inline-flex items-center gap-2 px-4 py-2 bg-[--color-primary] text-[--color-primary-foreground] rounded-md hover:bg-[--color-primary-hover] transition-colors text-sm font-medium'
})

const reloadButtonClasses = computed(() => {
  return 'inline-flex items-center gap-2 px-4 py-2 bg-[--color-card] text-[--color-foreground] border border-[--color-border] rounded-md hover:bg-[--color-hover] transition-colors text-sm font-medium'
})
</script>