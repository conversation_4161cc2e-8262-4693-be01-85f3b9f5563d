import"./router.DKcY2uv6.js";import{r as x}from"./reactivity.esm-bundler.D5IypM4U.js";import{h as l,w as R,i as C}from"./index.BglzLLgy.js";const H=(a,b={})=>{const{prefix:f="",arrayParams:c=[],numberParams:m=[],booleanParams:w=[],debounceMs:S=300,replaceHistory:F=!0}=b,i=x({...a});let d=null;const P=(t,r)=>{if(!(r==null||r===""))return c.includes(t)?Array.isArray(r)?r.map(e=>String(e)).filter(e=>e!==""):[String(r)].filter(e=>e!==""):String(r)},h=(t,r)=>{if(!r||Array.isArray(r)&&r.length===0)return c.includes(t)?[]:null;if(c.includes(t)){const e=Array.isArray(r)?r:[r];return m.includes(t)?e.map(s=>{const n=Number(s);return isNaN(n)?null:n}).filter(s=>s!==null):w.includes(t)?e.map(s=>s==="true"||s==="1"):e}if(m.includes(t)){const e=Number(Array.isArray(r)?r[0]:r);return isNaN(e)?null:e}if(w.includes(t)){const e=Array.isArray(r)?r[0]:r;return e==="true"||e==="1"}return Array.isArray(r)?r[0]:r},A=()=>typeof window>"u"?new URLSearchParams:new URLSearchParams(window.location.search),p=()=>{const t=A(),r={...a};Object.keys(r).forEach(e=>{const s=f+e,n=t.get(s),o=t.getAll(s);(n!==null||o.length>0)&&(c.includes(e)&&o.length>0?r[e]=h(e,o):n!==null&&(r[e]=h(e,n)))}),i.value=r},y=()=>{typeof window>"u"||(d&&clearTimeout(d),d=setTimeout(()=>{const t=A(),r=[];t.forEach((n,o)=>{o.startsWith(f)&&r.push(o)}),r.forEach(n=>t.delete(n)),Object.entries(i.value).forEach(([n,o])=>{const g=f+n,u=P(n,o);u!==void 0&&(Array.isArray(u)?u.forEach(L=>t.append(g,L)):t.set(g,u))});const e=t.toString(),s=typeof window<"u"&&window.location.search?window.location.search.replace(/^\?/,""):"";if(e!==s){const n=new URL(window.location.href);n.search=e,F?window.history.replaceState(null,"",n):window.history.pushState(null,"",n)}},S))},E=(t,r)=>{i.value[t]=r},N=t=>{Object.assign(i.value,t)},T=()=>{i.value={...a}},O=t=>{i.value[t]=a[t]},U=l(()=>Object.entries(i.value).some(([t,r])=>{const e=a[t];return Array.isArray(r)&&Array.isArray(e)?r.length!==e.length||!r.every((s,n)=>s===e[n]):r!==e})),V=l(()=>Object.entries(i.value).reduce((t,[r,e])=>{const s=a[r];return Array.isArray(e)?t+(e.length>0?1:0):e!==s&&e!==null&&e!==void 0&&e!==""?t+1:t},0)),j=l(()=>JSON.stringify(i.value,null,2));return R(i,y,{deep:!0}),C(()=>{if(p(),typeof window<"u"){const t=()=>{p()};return window.addEventListener("popstate",t),()=>{window.removeEventListener("popstate",t)}}}),{filters:l(()=>i.value),hasActiveFilters:U,activeFiltersCount:V,filtersString:j,updateFilter:E,updateFilters:N,resetFilters:T,resetFilter:O,loadFromUrl:p,saveToUrl:y}};export{H as u};
