import{a5 as he,a6 as pe,a7 as ye}from"./reactivity.esm-bundler.D5IypM4U.js";import{o as me,s as L,d as J,b as ge,e as ve}from"./types.FgRm47Sn.js";var we=Object.defineProperty,Re=Object.defineProperties,be=Object.getOwnPropertyDescriptors,G=Object.getOwnPropertySymbols,_e=Object.prototype.hasOwnProperty,Se=Object.prototype.propertyIsEnumerable,z=(e,t,r)=>t in e?we(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,_=(e,t)=>{for(var r in t||(t={}))_e.call(t,r)&&z(e,r,t[r]);if(G)for(var r of G(t))Se.call(t,r)&&z(e,r,t[r]);return e},S=(e,t)=>Re(e,be(t)),Oe=class extends Error{constructor(e,t,r){super(t||e.toString(),{cause:r}),this.status=e,this.statusText=t,this.error=r}},Te=async(e,t)=>{var r,n,s,a,o,c;let l=t||{};const i={onRequest:[t?.onRequest],onResponse:[t?.onResponse],onSuccess:[t?.onSuccess],onError:[t?.onError],onRetry:[t?.onRetry]};if(!t||!t?.plugins)return{url:e,options:l,hooks:i};for(const u of t?.plugins||[]){if(u.init){const f=await((r=u.init)==null?void 0:r.call(u,e.toString(),t));l=f.options||l,e=f.url}i.onRequest.push((n=u.hooks)==null?void 0:n.onRequest),i.onResponse.push((s=u.hooks)==null?void 0:s.onResponse),i.onSuccess.push((a=u.hooks)==null?void 0:a.onSuccess),i.onError.push((o=u.hooks)==null?void 0:o.onError),i.onRetry.push((c=u.hooks)==null?void 0:c.onRetry)}return{url:e,options:l,hooks:i}},Q=class{constructor(e){this.options=e}shouldAttemptRetry(e,t){return this.options.shouldRetry?Promise.resolve(e<this.options.attempts&&this.options.shouldRetry(t)):Promise.resolve(e<this.options.attempts)}getDelay(){return this.options.delay}},Pe=class{constructor(e){this.options=e}shouldAttemptRetry(e,t){return this.options.shouldRetry?Promise.resolve(e<this.options.attempts&&this.options.shouldRetry(t)):Promise.resolve(e<this.options.attempts)}getDelay(e){return Math.min(this.options.maxDelay,this.options.baseDelay*2**e)}};function Ee(e){if(typeof e=="number")return new Q({type:"linear",attempts:e,delay:1e3});switch(e.type){case"linear":return new Q(e);case"exponential":return new Pe(e);default:throw new Error("Invalid retry strategy")}}var Ae=async e=>{const t={},r=async n=>typeof n=="function"?await n():n;if(e?.auth){if(e.auth.type==="Bearer"){const n=await r(e.auth.token);if(!n)return t;t.authorization=`Bearer ${n}`}else if(e.auth.type==="Basic"){const n=r(e.auth.username),s=r(e.auth.password);if(!n||!s)return t;t.authorization=`Basic ${btoa(`${n}:${s}`)}`}else if(e.auth.type==="Custom"){const n=r(e.auth.value);if(!n)return t;t.authorization=`${r(e.auth.prefix)} ${n}`}}return t},Ue=/^application\/(?:[\w!#$%&*.^`~-]*\+)?json(;.+)?$/i;function Ie(e){const t=e.headers.get("content-type"),r=new Set(["image/svg","application/xml","application/xhtml","application/html"]);if(!t)return"json";const n=t.split(";").shift()||"";return Ue.test(n)?"json":r.has(n)||n.startsWith("text/")?"text":"blob"}function Le(e){try{return JSON.parse(e),!0}catch{return!1}}function ee(e){if(e===void 0)return!1;const t=typeof e;return t==="string"||t==="number"||t==="boolean"||t===null?!0:t!=="object"?!1:Array.isArray(e)?!0:e.buffer?!1:e.constructor&&e.constructor.name==="Object"||typeof e.toJSON=="function"}function X(e){try{return JSON.parse(e)}catch{return e}}function Y(e){return typeof e=="function"}function Ne(e){if(e?.customFetchImpl)return e.customFetchImpl;if(typeof globalThis<"u"&&Y(globalThis.fetch))return globalThis.fetch;if(typeof window<"u"&&Y(window.fetch))return window.fetch;throw new Error("No fetch implementation found")}async function xe(e){const t=new Headers(e?.headers),r=await Ae(e);for(const[n,s]of Object.entries(r||{}))t.set(n,s);if(!t.has("content-type")){const n=qe(e?.body);n&&t.set("content-type",n)}return t}function qe(e){return ee(e)?"application/json":null}function Ce(e){if(!e?.body)return null;const t=new Headers(e?.headers);if(ee(e.body)&&!t.has("content-type")){for(const[r,n]of Object.entries(e?.body))n instanceof Date&&(e.body[r]=n.toISOString());return JSON.stringify(e.body)}return e.body}function je(e,t){var r;if(t?.method)return t.method.toUpperCase();if(e.startsWith("@")){const n=(r=e.split("@")[1])==null?void 0:r.split("/")[0];return re.includes(n)?n.toUpperCase():t?.body?"POST":"GET"}return t?.body?"POST":"GET"}function ke(e,t){let r;return!e?.signal&&e?.timeout&&(r=setTimeout(()=>t?.abort(),e?.timeout)),{abortTimeout:r,clearTimeout:()=>{r&&clearTimeout(r)}}}var $e=class te extends Error{constructor(t,r){super(r||JSON.stringify(t,null,2)),this.issues=t,Object.setPrototypeOf(this,te.prototype)}};async function $(e,t){let r=await e["~standard"].validate(t);if(r.issues)throw new $e(r.issues);return r.value}var re=["get","post","put","patch","delete"],De=e=>({id:"apply-schema",name:"Apply Schema",version:"1.0.0",async init(t,r){var n,s,a,o;const c=((s=(n=e.plugins)==null?void 0:n.find(l=>{var i;return(i=l.schema)!=null&&i.config?t.startsWith(l.schema.config.baseURL||"")||t.startsWith(l.schema.config.prefix||""):!1}))==null?void 0:s.schema)||e.schema;if(c){let l=t;(a=c.config)!=null&&a.prefix&&l.startsWith(c.config.prefix)&&(l=l.replace(c.config.prefix,""),c.config.baseURL&&(t=t.replace(c.config.prefix,c.config.baseURL))),(o=c.config)!=null&&o.baseURL&&l.startsWith(c.config.baseURL)&&(l=l.replace(c.config.baseURL,""));const i=c.schema[l];if(i){let u=S(_({},r),{method:i.method,output:i.output});return r?.disableValidation||(u=S(_({},u),{body:i.input?await $(i.input,r?.body):r?.body,params:i.params?await $(i.params,r?.params):r?.params,query:i.query?await $(i.query,r?.query):r?.query})),{url:t,options:u}}}return{url:t,options:r}}}),Me=e=>{async function t(r,n){const s=S(_(_({},e),n),{plugins:[...e?.plugins||[],De(e||{})]});if(e?.catchAllError)try{return await W(r,s)}catch(a){return{data:null,error:{status:500,statusText:"Fetch Error",message:"Fetch related error. Captured by catchAllError option. See error property for more details.",error:a}}}return await W(r,s)}return t};function Fe(e,t){let{baseURL:r,params:n,query:s}=t||{query:{},params:{},baseURL:""},a=e.startsWith("http")?e.split("/").slice(0,3).join("/"):r||"";if(e.startsWith("@")){const f=e.toString().split("@")[1].split("/")[0];re.includes(f)&&(e=e.replace(`@${f}/`,"/"))}a.endsWith("/")||(a+="/");let[o,c]=e.replace(a,"").split("?");const l=new URLSearchParams(c);for(const[f,h]of Object.entries(s||{}))h!=null&&l.set(f,String(h));if(n)if(Array.isArray(n)){const f=o.split("/").filter(h=>h.startsWith(":"));for(const[h,v]of f.entries()){const m=n[h];o=o.replace(v,m)}}else for(const[f,h]of Object.entries(n))o=o.replace(`:${f}`,String(h));o=o.split("/").map(encodeURIComponent).join("/"),o.startsWith("/")&&(o=o.slice(1));let i=l.toString();return i=i.length>0?`?${i}`.replace(/\+/g,"%20"):"",a.startsWith("http")?new URL(`${o}${i}`,a):`${a}${o}${i}`}var W=async(e,t)=>{var r,n,s,a,o,c,l,i;const{hooks:u,url:f,options:h}=await Te(e,t),v=Ne(h),m=new AbortController,w=(r=h.signal)!=null?r:m.signal,O=Fe(f,h),I=Ce(h),E=await xe(h),d=je(f,h);let p=S(_({},h),{url:O,headers:E,body:I,method:d,signal:w});for(const R of u.onRequest)if(R){const g=await R(p);g instanceof Object&&(p=g)}("pipeTo"in p&&typeof p.pipeTo=="function"||typeof((n=t?.body)==null?void 0:n.pipe)=="function")&&("duplex"in p||(p.duplex="half"));const{clearTimeout:x}=ke(h,m);let y=await v(p.url,p);x();const H={response:y,request:p};for(const R of u.onResponse)if(R){const g=await R(S(_({},H),{response:(s=t?.hookOptions)!=null&&s.cloneResponse?y.clone():y}));g instanceof Response?y=g:g instanceof Object&&(y=g.response)}if(y.ok){if(!(p.method!=="HEAD"))return{data:"",error:null};const g=Ie(y),T={data:"",response:y,request:p};if(g==="json"||g==="text"){const P=await y.text(),de=await((a=p.jsonParser)!=null?a:X)(P);T.data=de}else T.data=await y[g]();p?.output&&p.output&&!p.disableValidation&&(T.data=await $(p.output,T.data));for(const P of u.onSuccess)P&&await P(S(_({},T),{response:(o=t?.hookOptions)!=null&&o.cloneResponse?y.clone():y}));return t?.throw?T.data:{data:T.data,error:null}}const le=(c=t?.jsonParser)!=null?c:X,q=await y.text(),V=Le(q),M=V?await le(q):null,fe={response:y,responseText:q,request:p,error:S(_({},M),{status:y.status,statusText:y.statusText})};for(const R of u.onError)R&&await R(S(_({},fe),{response:(l=t?.hookOptions)!=null&&l.cloneResponse?y.clone():y}));if(t?.retry){const R=Ee(t.retry),g=(i=t.retryAttempt)!=null?i:0;if(await R.shouldAttemptRetry(g,y)){for(const P of u.onRetry)P&&await P(H);const T=R.getDelay(g);return await new Promise(P=>setTimeout(P,T)),await W(e,S(_({},t),{retryAttempt:g+1}))}}if(t?.throw)throw new Oe(y.status,y.statusText,V?M:q);return{data:null,error:S(_({},M),{status:y.status,statusText:y.statusText})}},Be={},We={};const D=Object.create(null),N=e=>Be||globalThis.Deno?.env.toObject()||globalThis.__env__||(e?D:globalThis),U=new Proxy(D,{get(e,t){return N()[t]??D[t]},has(e,t){const r=N();return t in r||t in D},set(e,t,r){const n=N(!0);return n[t]=r,!0},deleteProperty(e,t){if(!t)return!1;const r=N(!0);return delete r[t],!0},ownKeys(){const e=N(!0);return Object.keys(e)}});function He(e){return e?e!=="false":!1}const Ve=typeof process<"u"&&We&&"production"||"";Ve==="test"||He(U.TEST);class ne extends Error{constructor(t,r){super(t),this.name="BetterAuthError",this.message=t,this.cause=r,this.stack=""}}function Je(e){try{return new URL(e).pathname!=="/"}catch{throw new ne(`Invalid base URL: ${e}. Please provide a valid base URL.`)}}function F(e,t="/api/auth"){return Je(e)?e:(t=t.startsWith("/")?t:`/${t}`,`${e.replace(/\/+$/,"")}${t}`)}function Ge(e,t,r){if(e)return F(e,t);const n=U.BETTER_AUTH_URL||U.NEXT_PUBLIC_BETTER_AUTH_URL||U.PUBLIC_BETTER_AUTH_URL||U.NUXT_PUBLIC_BETTER_AUTH_URL||U.NUXT_PUBLIC_AUTH_URL||(U.BASE_URL!=="/"?U.BASE_URL:void 0);if(n)return F(n,t);if(typeof window<"u"&&window.location)return F(window.location.origin,t)}let b=[],A=0;const C=4;let se=e=>{let t=[],r={get(){return r.lc||r.listen(()=>{})(),r.value},lc:0,listen(n){return r.lc=t.push(n),()=>{for(let a=A+C;a<b.length;)b[a]===n?b.splice(a,C):a+=C;let s=t.indexOf(n);~s&&(t.splice(s,1),--r.lc||r.off())}},notify(n,s){let a=!b.length;for(let o of t)b.push(o,r.value,n,s);if(a){for(A=0;A<b.length;A+=C)b[A](b[A+1],b[A+2],b[A+3]);b.length=0}},off(){},set(n){let s=r.value;s!==n&&(r.value=n,r.notify(s))},subscribe(n){let s=r.listen(n);return n(r.value),s},value:e};return r};const ze=5,j=6,k=10;let Qe=(e,t,r,n)=>(e.events=e.events||{},e.events[r+k]||(e.events[r+k]=n(s=>{e.events[r].reduceRight((a,o)=>(o(a),a),{shared:{},...s})})),e.events[r]=e.events[r]||[],e.events[r].push(t),()=>{let s=e.events[r],a=s.indexOf(t);s.splice(a,1),s.length||(delete e.events[r],e.events[r+k](),delete e.events[r+k])}),Xe=1e3,Ye=(e,t)=>Qe(e,n=>{let s=t(n);s&&e.events[j].push(s)},ze,n=>{let s=e.listen;e.listen=(...o)=>(!e.lc&&!e.active&&(e.active=!0,n()),s(...o));let a=e.off;return e.events[j]=[],e.off=()=>{a(),setTimeout(()=>{if(e.active&&!e.lc){e.active=!1;for(let o of e.events[j])o();e.events[j]=[]}},Xe)},()=>{e.listen=s,e.off=a}});const Ze=typeof window>"u",Ke=(e,t,r,n)=>{const s=se({data:null,error:null,isPending:!0,isRefetching:!1,refetch:()=>a()}),a=()=>{const c=typeof n=="function"?n({data:s.get().data,error:s.get().error,isPending:s.get().isPending}):n;return r(t,{...c,async onSuccess(l){s.set({data:l.data,error:null,isPending:!1,isRefetching:!1,refetch:s.value.refetch}),await c?.onSuccess?.(l)},async onError(l){const{request:i}=l,u=typeof i.retry=="number"?i.retry:i.retry?.attempts,f=i.retryAttempt||0;u&&f<u||(s.set({error:l.error,data:null,isPending:!1,isRefetching:!1,refetch:s.value.refetch}),await c?.onError?.(l))},async onRequest(l){const i=s.get();s.set({isPending:i.data===null,data:i.data,error:null,isRefetching:!0,refetch:s.value.refetch}),await c?.onRequest?.(l)}})};e=Array.isArray(e)?e:[e];let o=!1;for(const c of e)c.subscribe(()=>{Ze||(o?a():Ye(s,()=>(setTimeout(()=>{a()},0),o=!0,()=>{s.off(),c.off()})))});return s},et={proto:/"(?:_|\\u0{2}5[Ff]){2}(?:p|\\u0{2}70)(?:r|\\u0{2}72)(?:o|\\u0{2}6[Ff])(?:t|\\u0{2}74)(?:o|\\u0{2}6[Ff])(?:_|\\u0{2}5[Ff]){2}"\s*:/,constructor:/"(?:c|\\u0063)(?:o|\\u006[Ff])(?:n|\\u006[Ee])(?:s|\\u0073)(?:t|\\u0074)(?:r|\\u0072)(?:u|\\u0075)(?:c|\\u0063)(?:t|\\u0074)(?:o|\\u006[Ff])(?:r|\\u0072)"\s*:/,protoShort:/"__proto__"\s*:/,constructorShort:/"constructor"\s*:/},tt=/^\s*["[{]|^\s*-?\d{1,16}(\.\d{1,17})?([Ee][+-]?\d+)?\s*$/,Z={true:!0,false:!1,null:null,undefined:void 0,nan:Number.NaN,infinity:Number.POSITIVE_INFINITY,"-infinity":Number.NEGATIVE_INFINITY},rt=/^(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):(\d{2})(?:\.(\d{1,7}))?(?:Z|([+-])(\d{2}):(\d{2}))$/;function nt(e){return e instanceof Date&&!isNaN(e.getTime())}function st(e){const t=rt.exec(e);if(!t)return null;const[,r,n,s,a,o,c,l,i,u,f]=t;let h=new Date(Date.UTC(parseInt(r,10),parseInt(n,10)-1,parseInt(s,10),parseInt(a,10),parseInt(o,10),parseInt(c,10),l?parseInt(l.padEnd(3,"0"),10):0));if(i){const v=(parseInt(u,10)*60+parseInt(f,10))*(i==="+"?-1:1);h.setUTCMinutes(h.getUTCMinutes()+v)}return nt(h)?h:null}function ot(e,t={}){const{strict:r=!1,warnings:n=!1,reviver:s,parseDates:a=!0}=t;if(typeof e!="string")return e;const o=e.trim();if(o[0]==='"'&&o.endsWith('"')&&!o.slice(1,-1).includes('"'))return o.slice(1,-1);const c=o.toLowerCase();if(c.length<=9&&c in Z)return Z[c];if(!tt.test(o)){if(r)throw new SyntaxError("[better-json] Invalid JSON");return e}if(Object.entries(et).some(([i,u])=>{const f=u.test(o);return f&&n&&console.warn(`[better-json] Detected potential prototype pollution attempt using ${i} pattern`),f})&&r)throw new Error("[better-json] Potential prototype pollution attempt detected");try{return JSON.parse(o,(u,f)=>{if(u==="__proto__"||u==="constructor"&&f&&typeof f=="object"&&"prototype"in f){n&&console.warn(`[better-json] Dropping "${u}" key to prevent prototype pollution`);return}if(a&&typeof f=="string"){const h=st(f);if(h)return h}return s?s(u,f):f})}catch(i){if(r)throw i;return e}}function it(e,t={strict:!0}){return ot(e,t)}const at={id:"redirect",name:"Redirect",hooks:{onSuccess(e){if(e.data?.url&&e.data?.redirect&&typeof window<"u"&&window.location&&window.location)try{window.location.href=e.data.url}catch{}}}};function ut(e){const t=se(!1);return{session:Ke(t,"/get-session",e,{method:"GET"}),$sessionSignal:t}}const ct=e=>{const t="credentials"in Request.prototype,r=Ge(e?.baseURL,e?.basePath),n=e?.plugins?.flatMap(d=>d.fetchPlugins).filter(d=>d!==void 0)||[],s={id:"lifecycle-hooks",name:"lifecycle-hooks",hooks:{onSuccess:e?.fetchOptions?.onSuccess,onError:e?.fetchOptions?.onError,onRequest:e?.fetchOptions?.onRequest,onResponse:e?.fetchOptions?.onResponse}},{onSuccess:a,onError:o,onRequest:c,onResponse:l,...i}=e?.fetchOptions||{},u=Me({baseURL:r,...t?{credentials:"include"}:{},method:"GET",jsonParser(d){return d?it(d,{strict:!1}):null},customFetchImpl:async(d,p)=>{try{return await fetch(d,p)}catch{return Response.error()}},...i,plugins:[s,...i.plugins||[],...e?.disableDefaultFetchPlugins?[]:[at],...n]}),{$sessionSignal:f,session:h}=ut(u),v=e?.plugins||[];let m={},w={$sessionSignal:f,session:h},O={"/sign-out":"POST","/revoke-sessions":"POST","/revoke-other-sessions":"POST","/delete-user":"POST"};const I=[{signal:"$sessionSignal",matcher(d){return d==="/sign-out"||d==="/update-user"||d.startsWith("/sign-in")||d.startsWith("/sign-up")||d==="/delete-user"||d==="/verify-email"}}];for(const d of v)d.getAtoms&&Object.assign(w,d.getAtoms?.(u)),d.pathMethods&&Object.assign(O,d.pathMethods),d.atomListeners&&I.push(...d.atomListeners);const E={notify:d=>{w[d].set(!w[d].get())},listen:(d,p)=>{w[d].subscribe(p)},atoms:w};for(const d of v)d.getActions&&Object.assign(m,d.getActions?.(u,E,e));return{pluginsActions:m,pluginsAtoms:w,pluginPathMethods:O,atomListeners:I,$fetch:u,$store:E}};function lt(e,t,r){const n=t[e],{fetchOptions:s,query:a,...o}=r||{};return n||(s?.method?s.method:o&&Object.keys(o).length>0?"POST":"GET")}function ft(e,t,r,n,s){function a(o=[]){return new Proxy(function(){},{get(c,l){const i=[...o,l];let u=e;for(const f of i)if(u&&typeof u=="object"&&f in u)u=u[f];else{u=void 0;break}return typeof u=="function"?u:a(i)},apply:async(c,l,i)=>{const u="/"+o.map(E=>E.replace(/[A-Z]/g,d=>`-${d.toLowerCase()}`)).join("/"),f=i[0]||{},h=i[1]||{},{query:v,fetchOptions:m,...w}=f,O={...h,...m},I=lt(u,r,f);return await t(u,{...O,body:I==="GET"?void 0:{...w,...O?.body||{}},query:v||O?.query,method:I,async onSuccess(E){await O?.onSuccess?.(E);const d=s?.find(y=>y.matcher(u));if(!d)return;const p=n[d.signal];if(!p)return;const x=p.get();setTimeout(()=>{p.set(!x)},10)}})}})}return a()}function dt(e){return e.charAt(0).toUpperCase()+e.slice(1)}function K(e){let t=he(),r=e.subscribe(n=>{t.value=n});return pe()&&ye(r),t}function ht(e){return`use${dt(e)}`}function pt(e){const{pluginPathMethods:t,pluginsActions:r,pluginsAtoms:n,$fetch:s,$store:a,atomListeners:o}=ct(e);let c={};for(const[f,h]of Object.entries(n))c[ht(f)]=()=>K(h);function l(f){if(f){const h=K(n.$sessionSignal),v=e?.fetchOptions?.baseURL||e?.baseURL;let m=v?new URL(v).pathname:"/api/auth";return m=m==="/"?"/api/auth":m,m=m.endsWith("/")?m.slice(0,-1):m,f(`${m}/get-session`,{ref:h}).then(w=>({data:w.data,isPending:!1,error:w.error}))}return c.useSession()}const i={...r,...c,useSession:l,$fetch:s,$store:a};return ft(i,s,t,n,o)}function yt(e){return{authorize(t,r="AND"){let n=!1;for(const[s,a]of Object.entries(t)){const o=e[s];if(!o)return{success:!1,error:`You are not allowed to access resource: ${s}`};if(Array.isArray(a))n=a.every(c=>o.includes(c));else if(typeof a=="object"){const c=a;c.connector==="OR"?n=c.actions.some(l=>o.includes(l)):n=c.actions.every(l=>o.includes(l))}else throw new ne("Invalid access control request");if(n&&r==="OR")return{success:n};if(!n&&r==="AND")return{success:!1,error:`unauthorized to access resource "${s}"`}}return n?{success:n}:{success:!1,error:"Not authorized"}},statements:e}}function mt(e){return{newRole(t){return yt(t)},statements:e}}const gt={user:["create","list","set-role","ban","impersonate","delete","set-password","update"],session:["list","revoke","delete"]},oe=mt(gt),ie=oe.newRole({user:["create","list","set-role","ban","impersonate","delete","set-password","update"],session:["list","revoke","delete"]}),ae=oe.newRole({user:[],session:[]}),vt={admin:ie,user:ae},wt=e=>{if(e.userId&&e.options?.adminUserIds?.includes(e.userId))return!0;if(!e.permissions&&!e.permission)return!1;const t=(e.role||e.options?.defaultRole||"user").split(","),r=e.options?.roles||vt;for(const n of t)if(r[n]?.authorize(e.permission??e.permissions)?.success)return!0;return!1},Rt=()=>({id:"username",$InferServerPlugin:{}}),bt=()=>({id:"phoneNumber",$InferServerPlugin:{},atomListeners:[{matcher(e){return e==="/phone-number/update"||e==="/phone-number/verify"},signal:"$sessionSignal"}]}),_t=()=>({id:"anonymous",$InferServerPlugin:{},pathMethods:{"/sign-in/anonymous":"POST"}}),St=e=>{const t={admin:ie,user:ae,...e?.roles};return{id:"admin-client",$InferServerPlugin:{},getActions:()=>({admin:{checkRolePermission:r=>wt({role:r.role,options:{ac:e?.ac,roles:t},permissions:r.permissions??r.permission})}}),pathMethods:{"/admin/list-users":"GET","/admin/stop-impersonating":"POST"}}};me({id:L(),email:L(),emailVerified:ge(),name:L(),createdAt:J(),updatedAt:J(),image:L().nullable().optional(),role:L()});ve(["USER","ADMIN","SHOP"]);const B={baseURL:"http://localhost:3000",fetchOptions:{timeout:1e4,retries:3}},ue=pt({baseURL:B.baseURL,plugins:[St(),bt(),Rt(),_t()],fetchOptions:{timeout:B.fetchOptions?.timeout,retry:B.fetchOptions?.retries,onRequest:e=>{},onResponse:e=>{},onError:e=>{console.error("❌ Auth error:",e.error),e.response?.status===401?(console.warn("🔒 Unauthorized - redirecting to login"),typeof window<"u"&&window.location.pathname.includes("/login")):e.response?.status===403?console.warn("🚫 Forbidden - insufficient permissions"):e.response?.status>=500&&console.error("🔥 Server error - check API status")}}}),{signIn:Ut,signUp:It,signOut:Lt,useSession:Nt,getSession:xt,$ERROR_CODES:qt}=ue;ue.admin;const ce=(e,t)=>e?.role===t,Ot=e=>ce(e,"ADMIN"),Tt=e=>ce(e,"SHOP"),Ct=e=>Ot(e)||Tt(e),jt=(e,t="Произошла ошибка")=>typeof e=="string"?e:e?.message?e.message:e?.error?.message?e.error.message:t;export{Tt as a,ue as b,Ct as c,jt as g,ce as h,Ot as i,Nt as u};
