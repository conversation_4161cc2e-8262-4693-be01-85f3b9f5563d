version: '3.8'

services:
  # База данных PostgreSQL
  postgres:
    image: postgres:17-alpine
    container_name: parttec-postgres
    environment:
      POSTGRES_DB: parttec
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - ./data/postgres:/var/lib/postgresql/data
    networks:
      - parttec-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3



  # API сервис (Bun + Hono + tRPC)
  api:
    build:
      context: ./api
      dockerfile: Dockerfile
    container_name: parttec-api
    environment:
      - NODE_ENV=production
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD}@postgres:5432/parttec
      - BETTER_AUTH_SECRET=${BETTER_AUTH_SECRET}
      - BETTER_AUTH_URL=${API_URL}
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - ./data/media:/app/uploads
    networks:
      - parttec-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/auth/session"]
      interval: 30s
      timeout: 10s
      retries: 3

  # CPPanel (Astro + Vue)
  cpanel:
    build:
      context: ./cpanel
      dockerfile: Dockerfile
    container_name: parttec-cpanel
    environment:
      - NODE_ENV=production
      - PUBLIC_API_URL=${API_URL}
    depends_on:
      api:
        condition: service_healthy
    networks:
      - parttec-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4322"]
      interval: 30s
      timeout: 10s
      retries: 3
  # Site (Astro + React)
  site:
    build:
      context: ./site
      dockerfile: Dockerfile
    container_name: parttec-site
    environment:
      - NODE_ENV=production
      - PUBLIC_API_URL=${API_URL}
    depends_on:
      api:
        condition: service_healthy
    networks:
      - parttec-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4323"]
      interval: 30s
      timeout: 10s
      retries: 3
# Volumes больше не нужны - используем bind mounts

networks:
  parttec-network:
    driver: bridge
