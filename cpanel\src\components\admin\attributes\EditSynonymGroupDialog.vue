<template>
  <VDialog v-model:visible="visible" modal :header="dialogTitle" :style="{ width: '34rem' }">
    <div class="space-y-4">
      <div>
        <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">Название *</label>
        <VInputText v-model="form.name" placeholder="Стандартные типы уплотнений" class="w-full" :class="{ 'p-invalid': !!errors.name }" />
        <small v-if="errors.name" class="p-error">{{ errors.name }}</small>
      </div>
      <div>
        <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">Описание</label>
        <VTextarea v-model="form.description" rows="2" class="w-full" />
      </div>
      <div>
        <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">Каноническое значение</label>
        <VInputText v-model="form.canonicalValue" placeholder="Например: TC (стандарт)" class="w-full" :class="{ 'p-invalid': !!errors.canonicalValue }" />
        <small v-if="errors.canonicalValue" class="p-error">{{ errors.canonicalValue }}</small>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">Родительская группа</label>
          <VSelect v-model="form.parentId" :options="parentSelectOptions" option-label="name" option-value="id" class="w-full" placeholder="Не выбрано" />
        </div>
        <div>
          <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">Уровень совместимости</label>
          <VSelect v-model="form.compatibilityLevel" :options="compatibilityOptions" option-label="label" option-value="value" class="w-full" />
        </div>
        <div>
          <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">Заметки</label>
          <VInputText v-model="form.notes" placeholder="Например: для старых спецификаций" class="w-full" />
        </div>
      </div>
    </div>
    <template #footer>
      <VButton label="Отмена" severity="secondary" @click="close" />
      <VButton :label="isEdit ? 'Сохранить' : 'Создать'" :loading="saving" @click="save" />
    </template>
  </VDialog>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import VDialog from '@/volt/Dialog.vue'
import VInputText from '@/volt/InputText.vue'
import VTextarea from '@/volt/Textarea.vue'
import VButton from '@/volt/Button.vue'
import VSelect from '@/volt/Select.vue'
import { useTrpc } from '@/composables/useTrpc'
import { useToast } from '@/composables/useToast'

const props = defineProps<{ visible: boolean; templateId: number; group?: any | null }>()
const emit = defineEmits<{ 'update:visible': [boolean]; saved: [] }>()

const { attributeSynonyms } = useTrpc()
const toast = useToast()

const visible = computed({
  get: () => props.visible,
  set: (v: boolean) => emit('update:visible', v)
})

const isEdit = computed(() => !!props.group?.id)
const dialogTitle = computed(() => isEdit.value ? 'Редактировать группу' : 'Создать группу')

const compatibilityOptions = [
  { label: 'EXACT', value: 'EXACT' },
  { label: 'NEAR', value: 'NEAR' },
  { label: 'LEGACY', value: 'LEGACY' }
]

const form = ref({
  name: '',
  canonicalValue: '' as string,
  parentId: null as number | null,
  description: '' as string | null,
  compatibilityLevel: 'EXACT' as 'EXACT' | 'NEAR' | 'LEGACY',
  notes: '' as string | null,
})

const parentOptions = ref<any[]>([])
const parentSelectOptions = computed(() => {
  const opts = Array.isArray(parentOptions.value) ? parentOptions.value : []
  if (!props.group?.id) return [{ id: null, name: 'Нет родителя' }, ...opts]
  // Построим множество всех потомков текущей группы, чтобы запретить выбирать их как родителя
  const childrenMap = new Map<number, number[]>()
  for (const o of opts) {
    if (typeof o.parentId === 'number') {
      const arr = childrenMap.get(o.parentId) || []
      arr.push(o.id)
      childrenMap.set(o.parentId, arr)
    }
  }
  const blocked = new Set<number>()
  const stack = [props.group.id]
  while (stack.length) {
    const id = stack.pop()!
    const kids = childrenMap.get(id) || []
    for (const kid of kids) if (!blocked.has(kid)) { blocked.add(kid); stack.push(kid) }
  }
  const filtered = opts.filter((o: any) => o && typeof o.id === 'number' ? (o.id !== props.group!.id && !blocked.has(o.id)) : true)
  return [{ id: null, name: 'Нет родителя' }, ...filtered]
})

const errors = ref<Record<string, string>>({})
const saving = ref(false)

watch(() => props.group, (g) => {
  if (g) {
    form.value = {
      name: g.name || '',
      canonicalValue: g.canonicalValue || g.name || '',
      parentId: g.parentId ?? null,
      description: g.description || null,
      compatibilityLevel: g.compatibilityLevel || 'EXACT',
      notes: g.notes || null
    }
  } else {
    form.value = { name: '', canonicalValue: '', parentId: null, description: null, compatibilityLevel: 'EXACT', notes: null }
  }
}, { immediate: true })

watch(() => props.templateId, async (tid) => {
  if (!tid) return
  const { attributeSynonyms } = useTrpc()
  const limit = 100
  let offset = 0
  let all: any[] = []
  let total = 0
  while (true) {
    const res = await attributeSynonyms.groups.findMany({ templateId: tid, limit, offset })
    const batch = (res as any)?.groups ?? []
    total = (res as any)?.total ?? total
    all = all.concat(batch)
    if (batch.length < limit) break
    offset += limit
    if (total && offset >= total) break
  }
  parentOptions.value = all
}, { immediate: true })

const validate = () => {
  errors.value = {}
  if (!form.value.name.trim()) errors.value.name = 'Введите название'
  // Требуем canonicalValue только если группа не имеет родителя (контейнеры могут быть без канонического)
  if (!form.value.parentId && !form.value.canonicalValue.trim()) errors.value.canonicalValue = 'Введите каноническое значение'
  return Object.keys(errors.value).length === 0
}

const close = () => { visible.value = false }

const save = async () => {
  if (!validate()) return
  saving.value = true
  try {
    if (isEdit.value) {
      await attributeSynonyms.groups.update({ id: props.group!.id, ...form.value })
      toast.success('Группа обновлена')
    } else {
      await attributeSynonyms.groups.create({ templateId: props.templateId, name: form.value.name, canonicalValue: form.value.canonicalValue || undefined, parentId: form.value.parentId, description: form.value.description, compatibilityLevel: form.value.compatibilityLevel, notes: form.value.notes })
      toast.success('Группа создана')
    }
    emit('saved')
    close()
  } catch (e: any) {
    toast.error(e?.message || 'Не удалось сохранить группу')
  } finally {
    saving.value = false
  }
}
</script>


