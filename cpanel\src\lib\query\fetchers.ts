import { trpc } from '@/lib/trpc';
import { z } from 'zod';

// Локальные типы для билда (заглушки для tRPC вызовов)
export type PartFindManyArgs = any;
export type PartFindUniqueArgs = any;
export type PartCountArgs = any;
export type BrandFindManyArgs = any;
export type CategoryFindManyArgs = any;
export type SearchPartsArgs = any;

// Обёртки-адаптеры для tRPC вызовов, чтобы в хуках было меньше шума (и полная типизация входа)
export const fetchers = {
  parts: {
    list: async (input?: PartFindManyArgs) => trpc.catalog.listParts.query(input as any),
    detail: async (input: PartFindUniqueArgs) => trpc.crud.part.findUnique.query(input as any),
    count: async (input?: PartCountArgs) => trpc.catalog.countParts.query(input as any),
  },
  brands: {
    list: async (input?: BrandFindManyArgs) => trpc.catalog.listBrands.query(input as any),
  },
  categories: {
    list: async (input?: CategoryFindManyArgs) => trpc.catalog.listCategories.query(input as any),
  },
  search: {
    parts: async (input?: SearchPartsArgs) => trpc.search.searchParts.query(input as any),
  },
  attributeTemplates: {
    list: async (input?: z.infer<typeof AttributeTemplateInputSchema.findMany>) => trpc.crud.attributeTemplate.findMany.query(input as any),
  },
  attributeGroups: {
    list: async (input?: z.infer<typeof AttributeGroupInputSchema.findMany>) => trpc.crud.attributeGroup.findMany.query(input as any),
  },
  partApplicability: {
    list: async (input?: z.infer<typeof PartApplicabilityInputSchema.findMany>) => trpc.crud.partApplicability.findMany.query(input as any),
  },
  stats: {
    parts: async () => trpc.crud.part.count.query(),
    brands: async () => trpc.crud.brand.count.query(),
    categories: async () => trpc.crud.partCategory.count.query(),
  },
};
