import { trpc } from '@/lib/trpc';
import { z } from 'zod';

// Типы входов через Zod-схемы, сгенерированные ZenStack/Prisma — никаких server-only импортов
import { PartInputSchema } from '../../../../api/generated/zod/input/PartInput.schema';
import { BrandInputSchema } from '../../../../api/generated/zod/input/BrandInput.schema';
import { PartCategoryInputSchema } from '../../../../api/generated/zod/input/PartCategoryInput.schema';
import { AttributeTemplateInputSchema } from '../../../../api/generated/zod/input/AttributeTemplateInput.schema';
import { AttributeGroupInputSchema } from '../../../../api/generated/zod/input/AttributeGroupInput.schema';
import { PartApplicabilityInputSchema } from '../../../../api/generated/zod/input/PartApplicabilityInput.schema';
import { SearchPartsInput as SearchPartsInputSchema } from '../../../../api/schemas/search';

export type PartFindManyArgs = z.infer<typeof PartInputSchema.findMany>;
export type PartFindUniqueArgs = z.infer<typeof PartInputSchema.findUnique>;
export type PartCountArgs = z.infer<typeof PartInputSchema.count>;

export type BrandFindManyArgs = z.infer<typeof BrandInputSchema.findMany>;
export type CategoryFindManyArgs = z.infer<typeof PartCategoryInputSchema.findMany>;

export type SearchPartsArgs = z.infer<typeof SearchPartsInputSchema>;

// Обёртки-адаптеры для tRPC вызовов, чтобы в хуках было меньше шума (и полная типизация входа)
export const fetchers = {
  parts: {
    list: async (input?: PartFindManyArgs) => trpc.catalog.listParts.query(input as any),
    detail: async (input: PartFindUniqueArgs) => trpc.crud.part.findUnique.query(input as any),
    count: async (input?: PartCountArgs) => trpc.catalog.countParts.query(input as any),
  },
  brands: {
    list: async (input?: BrandFindManyArgs) => trpc.catalog.listBrands.query(input as any),
  },
  categories: {
    list: async (input?: CategoryFindManyArgs) => trpc.catalog.listCategories.query(input as any),
  },
  search: {
    parts: async (input?: SearchPartsArgs) => trpc.search.searchParts.query(input as any),
  },
  attributeTemplates: {
    list: async (input?: z.infer<typeof AttributeTemplateInputSchema.findMany>) => trpc.crud.attributeTemplate.findMany.query(input as any),
  },
  attributeGroups: {
    list: async (input?: z.infer<typeof AttributeGroupInputSchema.findMany>) => trpc.crud.attributeGroup.findMany.query(input as any),
  },
  partApplicability: {
    list: async (input?: z.infer<typeof PartApplicabilityInputSchema.findMany>) => trpc.crud.partApplicability.findMany.query(input as any),
  },
  stats: {
    parts: async () => trpc.crud.part.count.query(),
    brands: async () => trpc.crud.brand.count.query(),
    categories: async () => trpc.crud.partCategory.count.query(),
  },
};
