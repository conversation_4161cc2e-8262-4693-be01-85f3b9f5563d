const y="data-astro-transition-persist",q=["data-astro-transition","data-astro-transition-fallback"],P=new Set;function O(t){const e=t.src?new URL(t.src,location.href).href:t.textContent;return P.has(e)?!0:(P.add(e),!1)}function B(t){for(const e of t.scripts)!e.hasAttribute("data-astro-rerun")&&O(e)&&(e.dataset.astroExec="")}function W(t){const e=document.documentElement,n=[...e.attributes].filter(({name:o})=>(e.removeAttribute(o),q.includes(o)));[...t.documentElement.attributes,...n].forEach(({name:o,value:r})=>e.setAttribute(o,r))}function j(t){for(const e of Array.from(document.head.children)){const n=K(e,t);n?n.remove():e.remove()}document.head.append(...t.head.children)}function U(t,e){e.replaceWith(t);for(const n of e.querySelectorAll(`[${y}]`)){const o=n.getAttribute(y),r=t.querySelector(`[${y}="${o}"]`);r&&(r.replaceWith(n),r.localName==="astro-island"&&z(n)&&!G(n,r)&&(n.setAttribute("ssr",""),n.setAttribute("props",r.getAttribute("props"))))}}const V=()=>{const t=document.activeElement;if(t?.closest(`[${y}]`)){if(t instanceof HTMLInputElement||t instanceof HTMLTextAreaElement){const e=t.selectionStart,n=t.selectionEnd;return()=>E({activeElement:t,start:e,end:n})}return()=>E({activeElement:t})}else return()=>E({activeElement:null})},E=({activeElement:t,start:e,end:n})=>{t&&(t.focus(),(t instanceof HTMLInputElement||t instanceof HTMLTextAreaElement)&&(typeof e=="number"&&(t.selectionStart=e),typeof n=="number"&&(t.selectionEnd=n)))},K=(t,e)=>{const n=t.getAttribute(y),o=n&&e.head.querySelector(`[${y}="${n}"]`);if(o)return o;if(t.matches("link[rel=stylesheet]")){const r=t.getAttribute("href");return e.head.querySelector(`link[rel=stylesheet][href="${r}"]`)}return null},z=t=>{const e=t.dataset.astroTransitionPersistProps;return e==null||e==="false"},G=(t,e)=>t.getAttribute("props")===e.getAttribute("props"),J=t=>{B(t),W(t),j(t);const e=V();U(t.body,document.body),e()},Q="astro:before-preparation",Z="astro:after-preparation",tt="astro:before-swap",et="astro:after-swap",nt=t=>document.dispatchEvent(new Event(t));class F extends Event{from;to;direction;navigationType;sourceElement;info;newDocument;signal;constructor(e,n,o,r,i,u,a,c,d,l){super(e,n),this.from=o,this.to=r,this.direction=i,this.navigationType=u,this.sourceElement=a,this.info=c,this.newDocument=d,this.signal=l,Object.defineProperties(this,{from:{enumerable:!0},to:{enumerable:!0,writable:!0},direction:{enumerable:!0,writable:!0},navigationType:{enumerable:!0},sourceElement:{enumerable:!0},info:{enumerable:!0},newDocument:{enumerable:!0,writable:!0},signal:{enumerable:!0}})}}class ot extends F{formData;loader;constructor(e,n,o,r,i,u,a,c,d,l){super(Q,{cancelable:!0},e,n,o,r,i,u,a,c),this.formData=d,this.loader=l.bind(this,this),Object.defineProperties(this,{formData:{enumerable:!0},loader:{enumerable:!0,writable:!0}})}}class rt extends F{direction;viewTransition;swap;constructor(e,n){super(tt,void 0,e.from,e.to,e.direction,e.navigationType,e.sourceElement,e.info,e.newDocument,e.signal),this.direction=e.direction,this.viewTransition=n,this.swap=()=>J(this.newDocument),Object.defineProperties(this,{direction:{enumerable:!0},viewTransition:{enumerable:!0},swap:{enumerable:!0,writable:!0}})}}async function st(t,e,n,o,r,i,u,a,c){const d=new ot(t,e,n,o,r,i,window.document,u,a,c);return document.dispatchEvent(d)&&(await d.loader(),d.defaultPrevented||(nt(Z),d.navigationType!=="traverse"&&R({scrollX,scrollY}))),d}async function it(t,e,n){const o=new rt(t,e);return document.dispatchEvent(o),n&&await n(),o.swap(),o}const at=history.pushState.bind(history),g=history.replaceState.bind(history),R=t=>{history.state&&(history.scrollRestoration="manual",g({...history.state,...t},""))},M=!!document.startViewTransition,x=()=>!!document.querySelector('[name="astro-view-transitions-enabled"]'),_=(t,e)=>t.pathname===e.pathname&&t.search===e.search;let m,b,A;const H=t=>document.dispatchEvent(new Event(t)),X=()=>H("astro:page-load"),ct=()=>{let t=document.createElement("div");t.setAttribute("aria-live","assertive"),t.setAttribute("aria-atomic","true"),t.className="astro-route-announcer",document.body.append(t),setTimeout(()=>{let e=document.title||document.querySelector("h1")?.textContent||location.pathname;t.textContent=e},60)},D="data-astro-transition-persist",I="data-astro-transition",S="data-astro-transition-fallback";let k,v=0;history.state?(v=history.state.index,scrollTo({left:history.state.scrollX,top:history.state.scrollY})):x()&&(g({index:v,scrollX,scrollY},""),history.scrollRestoration="manual");async function lt(t,e){try{const n=await fetch(t,e),r=(n.headers.get("content-type")??"").split(";",1)[0].trim();return r!=="text/html"&&r!=="application/xhtml+xml"?null:{html:await n.text(),redirected:n.redirected?n.url:void 0,mediaType:r}}catch{return null}}function Y(){const t=document.querySelector('[name="astro-view-transitions-fallback"]');return t?t.getAttribute("content"):"animate"}function ut(){let t=Promise.resolve(),e=!1;for(const n of document.getElementsByTagName("script"))n.dataset.astroExec===void 0&&n.getAttribute("type")==="module"&&(e=n.getAttribute("src")===null);e&&document.body.insertAdjacentHTML("beforeend",'<script type="module" src="data:application/javascript,"/>');for(const n of document.getElementsByTagName("script")){if(n.dataset.astroExec==="")continue;const o=n.getAttribute("type");if(o&&o!=="module"&&o!=="text/javascript")continue;const r=document.createElement("script");r.innerHTML=n.innerHTML;for(const i of n.attributes){if(i.name==="src"){const u=new Promise(a=>{r.onload=r.onerror=a});t=t.then(()=>u)}r.setAttribute(i.name,i.value)}r.dataset.astroExec="",n.replaceWith(r)}return t}const C=(t,e,n,o,r)=>{const i=_(e,t),u=document.title;document.title=o;let a=!1;if(t.href!==location.href&&!r)if(n.history==="replace"){const c=history.state;g({...n.state,index:c.index,scrollX:c.scrollX,scrollY:c.scrollY},"",t.href)}else at({...n.state,index:++v,scrollX:0,scrollY:0},"",t.href);if(document.title=u,A=t,i||(scrollTo({left:0,top:0,behavior:"instant"}),a=!0),r)scrollTo(r.scrollX,r.scrollY);else{if(t.hash){history.scrollRestoration="auto";const c=history.state;location.href=t.href,history.state||(g(c,""),i&&window.dispatchEvent(new PopStateEvent("popstate")))}else a||scrollTo({left:0,top:0,behavior:"instant"});history.scrollRestoration="manual"}};function dt(t){const e=[];for(const n of t.querySelectorAll("head link[rel=stylesheet]"))if(!document.querySelector(`[${D}="${n.getAttribute(D)}"], link[rel=stylesheet][href="${n.getAttribute("href")}"]`)){const o=document.createElement("link");o.setAttribute("rel","preload"),o.setAttribute("as","style"),o.setAttribute("href",n.getAttribute("href")),e.push(new Promise(r=>{["load","error"].forEach(i=>o.addEventListener(i,r)),document.head.append(o)}))}return e}async function L(t,e,n,o,r){async function i(d){function l(f){const w=f.effect;return!w||!(w instanceof KeyframeEffect)||!w.target?!1:window.getComputedStyle(w.target,w.pseudoElement).animationIterationCount==="infinite"}const s=document.getAnimations();document.documentElement.setAttribute(S,d);const T=document.getAnimations().filter(f=>!s.includes(f)&&!l(f));return Promise.allSettled(T.map(f=>f.finished))}const u=async()=>{if(r==="animate"&&!n.transitionSkipped&&!t.signal.aborted)try{await i("old")}catch{}},a=document.title,c=await it(t,n.viewTransition,u);C(c.to,c.from,e,a,o),H(et),r==="animate"&&(!n.transitionSkipped&&!c.signal.aborted?i("new").finally(()=>n.viewTransitionFinished()):n.viewTransitionFinished())}function ft(){return m?.controller.abort(),m={controller:new AbortController}}async function $(t,e,n,o,r){const i=ft();if(!x()||location.origin!==n.origin){i===m&&(m=void 0),location.href=n.href;return}const u=r?"traverse":o.history==="replace"?"replace":"push";if(u!=="traverse"&&R({scrollX,scrollY}),_(e,n)&&!o.formData&&(t!=="back"&&n.hash||t==="back"&&e.hash)){C(n,e,o,document.title,r),i===m&&(m=void 0);return}const a=await st(e,n,t,u,o.sourceElement,o.info,i.controller.signal,o.formData,c);if(a.defaultPrevented||a.signal.aborted){i===m&&(m=void 0),a.signal.aborted||(location.href=n.href);return}async function c(s){const h=s.to.href,T={signal:s.signal};if(s.formData){T.method="POST";const p=s.sourceElement instanceof HTMLFormElement?s.sourceElement:s.sourceElement instanceof HTMLElement&&"form"in s.sourceElement?s.sourceElement.form:s.sourceElement?.closest("form");T.body=e!==void 0&&Reflect.get(HTMLFormElement.prototype,"attributes",p).getNamedItem("enctype")?.value==="application/x-www-form-urlencoded"?new URLSearchParams(s.formData):s.formData}const f=await lt(h,T);if(f===null){s.preventDefault();return}if(f.redirected){const p=new URL(f.redirected);if(p.origin!==s.to.origin){s.preventDefault();return}s.to=p}if(k??=new DOMParser,s.newDocument=k.parseFromString(f.html,f.mediaType),s.newDocument.querySelectorAll("noscript").forEach(p=>p.remove()),!s.newDocument.querySelector('[name="astro-view-transitions-enabled"]')&&!s.formData){s.preventDefault();return}const w=dt(s.newDocument);w.length&&!s.signal.aborted&&await Promise.all(w)}async function d(){if(b&&b.viewTransition){try{b.viewTransition.skipTransition()}catch{}try{await b.viewTransition.updateCallbackDone}catch{}}return b={transitionSkipped:!1}}const l=await d();if(a.signal.aborted){i===m&&(m=void 0);return}if(document.documentElement.setAttribute(I,a.direction),M)l.viewTransition=document.startViewTransition(async()=>await L(a,o,l,r));else{const s=(async()=>{await Promise.resolve(),await L(a,o,l,r,Y())})();l.viewTransition={updateCallbackDone:s,ready:s,finished:new Promise(h=>l.viewTransitionFinished=h),skipTransition:()=>{l.transitionSkipped=!0,document.documentElement.removeAttribute(S)},types:new Set}}l.viewTransition?.updateCallbackDone.finally(async()=>{await ut(),X(),ct()}),l.viewTransition?.finished.finally(()=>{l.viewTransition=void 0,l===b&&(b=void 0),i===m&&(m=void 0),document.documentElement.removeAttribute(I),document.documentElement.removeAttribute(S)});try{await l.viewTransition?.updateCallbackDone}catch(s){const h=s;console.log("[astro]",h.name,h.message,h.stack)}}async function ht(t,e){await $("forward",A,new URL(t,location.href),e??{})}function mt(t){if(!x()&&t.state){location.reload();return}if(t.state===null)return;const e=history.state,n=e.index,o=n>v?"forward":"back";v=n,$(o,A,new URL(location.href),{},e)}const N=()=>{history.state&&(scrollX!==history.state.scrollX||scrollY!==history.state.scrollY)&&R({scrollX,scrollY})};{if(M||Y()!=="none")if(A=new URL(location.href),addEventListener("popstate",mt),addEventListener("load",X),"onscrollend"in window)addEventListener("scrollend",N);else{let t,e,n,o;const r=()=>{if(o!==history.state?.index){clearInterval(t),t=void 0;return}if(e===scrollY&&n===scrollX){clearInterval(t),t=void 0,N();return}else e=scrollY,n=scrollX};addEventListener("scroll",()=>{t===void 0&&(o=history.state?.index,e=scrollY,n=scrollX,t=window.setInterval(r,50))},{passive:!0})}for(const t of document.getElementsByTagName("script"))O(t),t.dataset.astroExec=""}export{ht as n,M as s};
