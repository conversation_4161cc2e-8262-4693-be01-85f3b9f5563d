import { defineComponent, useSSRContext, mergeProps } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent, ssrInterpolate } from 'vue/server-renderer';
import { _ as _export_sfc } from './ClientRouter_B8Zzhk9G.mjs';
import { LoaderIcon } from 'lucide-vue-next';

const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "MatchingEmptyState",
  setup(__props, { expose: __expose }) {
    __expose();
    const __returned__ = {};
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$1(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "text-center py-10 text-surface-500" }, _attrs))}> \u041A\u0430\u043D\u0434\u0438\u0434\u0430\u0442\u044B \u043D\u0435 \u043D\u0430\u0439\u0434\u0435\u043D\u044B </div>`);
}
const _sfc_setup$1 = _sfc_main$1.setup;
_sfc_main$1.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/catalogitems/MatchingEmptyState.vue");
  return _sfc_setup$1 ? _sfc_setup$1(props, ctx) : void 0;
};
const MatchingEmptyState = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["ssrRender", _sfc_ssrRender$1]]);

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "MatchingLoadingState",
  props: {
    message: { default: "\u0412\u044B\u043F\u043E\u043B\u043D\u044F\u0435\u0442\u0441\u044F \u043F\u043E\u0434\u0431\u043E\u0440..." },
    paddingClass: { default: "py-10" }
  },
  setup(__props, { expose: __expose }) {
    __expose();
    const __returned__ = { get LoaderIcon() {
      return LoaderIcon;
    } };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({
    class: ["text-center flex justify-center items-center gap-3", $props.paddingClass]
  }, _attrs))}>`);
  _push(ssrRenderComponent($setup["LoaderIcon"], { class: "animate-spin" }, null, _parent));
  _push(`<div class="mt-2 text-surface-500">${ssrInterpolate($props.message)}</div></div>`);
}
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/catalogitems/MatchingLoadingState.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const MatchingLoadingState = /* @__PURE__ */ _export_sfc(_sfc_main, [["ssrRender", _sfc_ssrRender]]);

export { MatchingLoadingState as M, MatchingEmptyState as a };
