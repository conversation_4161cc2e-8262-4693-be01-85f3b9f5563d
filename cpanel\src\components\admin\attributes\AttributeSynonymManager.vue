<template>
  <div class="flex justify-center gap-4">
    <!-- Левая колонка: группы -->
    <VCard>
      <template #content>
        <div class="p-4 space-y-3">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-surface-900 dark:text-surface-0">Группы синонимов</h3>
            <VButton size="small" @click="openCreateGroup">
              <PlusCircleIcon class="w-4 h-4" />
            </VButton>
          </div>
          <VTree :value="treeNodes" v-model:expandedKeys="expandedKeys" filter filterPlaceholder="Поиск по названию / описанию...">
            <template #default="{ node }">
              <div class="flex items-center justify-between w-full" @click.stop>
                <div class="flex items-center gap-2 flex-1 cursor-pointer rounded px-2 py-1 transition-colors"
                     :class="{ 'bg-primary text-primary-contrast': selectedGroup?.id === node.data.id, 'hover:bg-surface-100 dark:hover:bg-surface-800': selectedGroup?.id !== node.data.id }"
                     @click="selectGroup(node.data)">
                  <span class="font-medium">{{ node.data.name }}</span>
                  <small class="text-surface-500" v-if="node.data.description">{{ node.data.description }}</small>
                  <VTag :value="compatibilityLabel(node.data.compatibilityLevel)" :severity="compatibilitySeverity(node.data.compatibilityLevel)" />
                  <VTag :value="node.data._count?.synonyms || 0" severity="secondary" />
                </div>
                <div class="flex gap-2">
                  <VButton size="small" severity="secondary" outlined @click.stop="openEditGroup(node.data)">
                    <PencilIcon class="w-4 h-4" />
                  </VButton>
                  <VButton size="small" severity="danger" outlined @click.stop="deleteGroup(node.data)">
                    <TrashIcon class="w-4 h-4" />
                  </VButton>
                </div>
              </div>
            </template>
          </VTree>
        </div>
      </template>
    </VCard>

    <!-- Правая колонка: значения -->
    <VCard>
      <template #content>
        <div class="p-4 space-y-4">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-lg font-semibold text-surface-900 dark:text-surface-0">
                {{ selectedGroup ? selectedGroup.name : 'Выберите группу' }}
              </h3>
              <div v-if="selectedGroup" class="flex items-center gap-2 mt-1">
                <VTag :value="compatibilityLabel(selectedGroup.compatibilityLevel)"
                  :severity="compatibilitySeverity(selectedGroup.compatibilityLevel)" />
                <small v-if="selectedGroup.notes" class="text-surface-500">{{ selectedGroup.notes }}</small>
              </div>
            </div>
            <div class="flex gap-2" v-if="selectedGroup">
              <VButton size="small" severity="secondary" outlined @click="toggleGroupExpansion(selectedGroup)"
                :title="isGroupExpanded(selectedGroup) ? 'Свернуть группу' : 'Развернуть группу'">
                <ChevronDownIcon v-if="isGroupExpanded(selectedGroup)" class="w-4 h-4" />
                <ChevronRightIcon v-else class="w-4 h-4" />
              </VButton>
              <VButton size="small" severity="secondary" outlined @click="openEditGroup(selectedGroup)">
                <PencilIcon class="w-4 h-4" />
              </VButton>
              <DangerButton size="small"  outlined @click="deleteGroup(selectedGroup)">
                <TrashIcon class="w-4 h-4" />
              </DangerButton>
            </div>
          </div>

          <div v-if="!selectedGroup" class="text-surface-500">Слева выберите группу, чтобы редактировать значения.</div>

          <div v-else>
            <SynonymValueEditor :group-id="selectedGroup.id" />
          </div>
        </div>
      </template>
    </VCard>

    <!-- Диалог создания/редактирования группы -->
    <EditSynonymGroupDialog v-model:visible="showGroupDialog" :template-id="template.id" :group="editingGroup"
      @saved="onGroupSaved" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import VCard from '@/volt/Card.vue'
import VButton from '@/volt/Button.vue'

import VTag from '@/volt/Tag.vue'
import VTree from '@/volt/Tree.vue'
import { useToast } from '@/composables/useToast'
import { useTrpc } from '@/composables/useTrpc'
import EditSynonymGroupDialog from './EditSynonymGroupDialog.vue'
import SynonymValueEditor from './SynonymValueEditor.vue'
import { PlusCircleIcon, PencilIcon, TrashIcon, ChevronDownIcon, ChevronRightIcon } from 'lucide-vue-next'
import DangerButton from '@/volt/DangerButton.vue'

interface AttributeTemplate { id: number; dataType: string; title: string; name: string }

const props = defineProps<{ template: AttributeTemplate }>()

const toast = useToast()
const { attributeSynonyms } = useTrpc()

const groups = ref<any[]>([])
const total = ref(0)
const loadingGroups = ref(false)
const selectedGroup = ref<any | null>(null)
const showGroupDialog = ref(false)
const editingGroup = ref<any | null>(null)
const expandedKeys = ref<Record<string, boolean>>({})

const loadGroups = async () => {
  if (!props.template?.id) return
  loadingGroups.value = true
  try {
    const limit = 100
    let offset = 0
    let all: any[] = []
    let totalCount = 0
    while (true) {
      const res = await attributeSynonyms.groups.findMany({ templateId: props.template.id, limit, offset })
      const batch = (res as any)?.groups ?? []
      totalCount = (res as any)?.total ?? totalCount
      all = all.concat(batch)
      if (batch.length < limit) break
      offset += limit
      if (totalCount && offset >= totalCount) break
    }
    groups.value = all
    total.value = totalCount || all.length
    if (selectedGroup.value) {
      const updated = groups.value.find(g => g.id === selectedGroup.value.id)
      if (updated) selectedGroup.value = updated
    }
    updateExpandedForSelection()
  } catch (e: any) {
    toast.error(e?.message || 'Не удалось загрузить группы')
  } finally {
    loadingGroups.value = false
  }
}



const compatibilityLabel = (level: 'EXACT'|'NEAR'|'LEGACY') => {
  switch (level) {
    case 'EXACT': return 'EXACT'
    case 'NEAR': return 'NEAR'
    case 'LEGACY': return 'LEGACY'
    default: return String(level)
  }
}

const compatibilitySeverity = (level: 'EXACT'|'NEAR'|'LEGACY') => {
  return level === 'EXACT' ? 'success' : level === 'NEAR' ? 'warn' : 'secondary'
}

const selectGroup = (group: any) => {
  selectedGroup.value = group
  updateExpandedForSelection()
}

const openCreateGroup = () => {
  editingGroup.value = null
  showGroupDialog.value = true
}

const openEditGroup = (group: any) => {
  editingGroup.value = group
  showGroupDialog.value = true
}

const deleteGroup = async (group: any) => {
  if (!confirm(`Удалить группу "${group.name}"?`)) return
  try {
    await attributeSynonyms.groups.delete({ id: group.id })
    if (selectedGroup.value?.id === group.id) selectedGroup.value = null
    loadGroups()
  } catch (e: any) {
    toast.error(e?.message || 'Не удалось удалить группу')
  }
}

const onGroupSaved = () => {
  showGroupDialog.value = false
  loadGroups()
}

const idMap = computed(() => {
  const m = new Map<number, any>()
  for (const g of groups.value) m.set(g.id, g)
  return m
})

const treeNodes = computed(() => {
  const nodesMap = new Map<number, any>()
  const roots: any[] = []
  for (const g of groups.value) nodesMap.set(g.id, { key: String(g.id), label: g.name, data: g, children: [] as any[] })
  for (const g of groups.value) {
    const node = nodesMap.get(g.id)
    if (g.parentId && nodesMap.has(g.parentId)) nodesMap.get(g.parentId).children.push(node)
    else roots.push(node)
  }
  return roots
})

function updateExpandedForSelection() {
  const keys: Record<string, boolean> = {}
  let current = selectedGroup.value ? idMap.value.get(selectedGroup.value.id) : null
  while (current && current.parentId) {
    keys[String(current.parentId)] = true
    current = idMap.value.get(current.parentId)
  }
  expandedKeys.value = keys
}

const isGroupExpanded = (group: any) => {
  return expandedKeys.value[String(group.id)] === true
}

const toggleGroupExpansion = (group: any) => {
  const key = String(group.id)
  const newKeys = { ...expandedKeys.value }
  if (newKeys[key]) {
    delete newKeys[key]
  } else {
    newKeys[key] = true
  }
  expandedKeys.value = newKeys
}

onMounted(() => {
  loadGroups()
})
</script>


