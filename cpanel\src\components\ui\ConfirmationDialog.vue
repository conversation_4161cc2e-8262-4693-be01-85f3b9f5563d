<template>
  <Teleport to="body">
    <Motion
      v-if="isOpen"
      :initial="{ opacity: 0 }"
      :animate="{ opacity: 1 }"
      :exit="{ opacity: 0 }"
      :transition="{ duration: 0.2 }"
      class="fixed inset-0 z-50 flex items-center justify-center p-4"
      @click="handleBackdropClick"
    >
      <!-- Backdrop -->
      <div class="absolute inset-0 bg-black/50 backdrop-blur-sm" />
      
      <!-- Dialog -->
      <Motion
        :initial="{ opacity: 0, scale: 0.9, y: 20 }"
        :animate="{ opacity: 1, scale: 1, y: 0 }"
        :exit="{ opacity: 0, scale: 0.9, y: 20 }"
        :transition="{ duration: 0.2, type: 'spring', stiffness: 300, damping: 30 }"
        :class="dialogClasses"
        @click.stop
        role="dialog"
        :aria-labelledby="titleId"
        :aria-describedby="descriptionId"
        aria-modal="true"
      >
        <!-- Header -->
        <div class="flex items-start gap-4 p-6 pb-4">
          <!-- Icon -->
          <div :class="iconContainerClasses">
            <component :is="iconComponent" :class="iconClasses" />
          </div>
          
          <!-- Content -->
          <div class="flex-1 min-w-0">
            <h2 :id="titleId" :class="titleClasses">
              {{ title }}
            </h2>
            
            <p v-if="description" :id="descriptionId" :class="descriptionClasses">
              {{ description }}
            </p>
            
            <!-- Custom content slot -->
            <div v-if="$slots.default" class="mt-3">
              <slot />
            </div>
          </div>
          
          <!-- Close button -->
          <button
            v-if="showCloseButton"
            @click="handleCancel"
            class="text-[--color-muted] hover:text-[--color-foreground] transition-colors p-1 rounded-md hover:bg-[--color-hover]"
            :aria-label="closeLabel"
          >
            <X class="w-5 h-5" />
          </button>
        </div>
        
        <!-- Actions -->
        <div class="flex items-center justify-end gap-3 px-6 pb-6">
          <button
            v-if="showCancel"
            @click="handleCancel"
            :class="cancelButtonClasses"
            :disabled="loading"
          >
            <component v-if="cancelIcon" :is="cancelIcon" class="w-4 h-4" />
            {{ cancelLabel }}
          </button>
          
          <button
            @click="handleConfirm"
            :class="confirmButtonClasses"
            :disabled="loading"
            ref="confirmButtonRef"
          >
            <Spinner v-if="loading" size="xs" variant="default" class="text-current" />
            <component v-else-if="confirmIcon" :is="confirmIcon" class="w-4 h-4" />
            {{ confirmLabel }}
          </button>
        </div>
      </Motion>
    </Motion>
  </Teleport>
</template>

<script setup lang="ts">
import { computed, nextTick, onMounted, ref, watch } from 'vue'
import { Motion } from 'motion-v'
import { 
  X, 
  AlertTriangle, 
  Trash2, 
  Check, 
  Info, 
  AlertCircle,
  HelpCircle,
  Save,
  Upload,
  Download,
  Settings,
  Undo2
} from 'lucide-vue-next'
import Spinner from './Spinner.vue'

interface Props {
  isOpen: boolean
  title: string
  description?: string
  variant?: 'default' | 'danger' | 'warning' | 'success' | 'info'
  confirmLabel?: string
  cancelLabel?: string
  confirmIcon?: any
  cancelIcon?: any
  showCancel?: boolean
  showCloseButton?: boolean
  closeOnBackdrop?: boolean
  loading?: boolean
  closeLabel?: string
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  confirmLabel: 'Подтвердить',
  cancelLabel: 'Отмена',
  showCancel: true,
  showCloseButton: true,
  closeOnBackdrop: true,
  loading: false,
  closeLabel: 'Закрыть диалог'
})

const emit = defineEmits<{
  confirm: []
  cancel: []
  close: []
}>()

const confirmButtonRef = ref<HTMLButtonElement>()
const titleId = `confirmation-title-${Math.random().toString(36).substr(2, 9)}`
const descriptionId = `confirmation-description-${Math.random().toString(36).substr(2, 9)}`

// Focus management
onMounted(() => {
  if (props.isOpen) {
    focusConfirmButton()
  }
})

watch(() => props.isOpen, (isOpen) => {
  if (isOpen) {
    nextTick(() => {
      focusConfirmButton()
    })
  }
})

const focusConfirmButton = () => {
  confirmButtonRef.value?.focus()
}

// Event handlers
const handleConfirm = () => {
  emit('confirm')
}

const handleCancel = () => {
  emit('cancel')
  emit('close')
}

const handleBackdropClick = () => {
  if (props.closeOnBackdrop && !props.loading) {
    handleCancel()
  }
}

// Icon mapping based on variant
const iconMap = {
  default: HelpCircle,
  danger: AlertTriangle,
  warning: AlertCircle,
  success: Check,
  info: Info
}

const iconComponent = computed(() => {
  return iconMap[props.variant]
})

// Computed classes
const dialogClasses = computed(() => {
  return 'relative bg-[--color-card] border border-[--color-border] rounded-xl shadow-xl max-w-md w-full max-h-[90vh] overflow-auto'
})

const iconContainerClasses = computed(() => {
  const base = 'flex-shrink-0 p-2 rounded-full'
  const variants = {
    default: 'bg-blue-50 dark:bg-blue-950/20',
    danger: 'bg-red-50 dark:bg-red-950/20',
    warning: 'bg-yellow-50 dark:bg-yellow-950/20',
    success: 'bg-green-50 dark:bg-green-950/20',
    info: 'bg-blue-50 dark:bg-blue-950/20'
  }
  
  return `${base} ${variants[props.variant]}`
})

const iconClasses = computed(() => {
  const base = 'w-6 h-6'
  const variants = {
    default: 'text-blue-600 dark:text-blue-400',
    danger: 'text-red-600 dark:text-red-400',
    warning: 'text-yellow-600 dark:text-yellow-400',
    success: 'text-green-600 dark:text-green-400',
    info: 'text-blue-600 dark:text-blue-400'
  }
  
  return `${base} ${variants[props.variant]}`
})

const titleClasses = computed(() => {
  return 'text-lg font-semibold text-[--color-foreground] leading-tight'
})

const descriptionClasses = computed(() => {
  return 'mt-2 text-sm text-[--color-muted] leading-relaxed'
})

const cancelButtonClasses = computed(() => {
  const base = 'inline-flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium rounded-md transition-colors min-w-[100px]'
  const disabled = props.loading ? 'opacity-50 cursor-not-allowed' : ''
  
  return `${base} bg-[--color-card] text-[--color-foreground] border border-[--color-border] hover:bg-[--color-hover] ${disabled}`.trim()
})

const confirmButtonClasses = computed(() => {
  const base = 'inline-flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium rounded-md transition-colors min-w-[100px]'
  const disabled = props.loading ? 'opacity-50 cursor-not-allowed' : ''
  
  const variants = {
    default: 'bg-[--color-primary] text-[--color-primary-foreground] hover:bg-[--color-primary-hover]',
    danger: 'bg-red-600 text-white hover:bg-red-700',
    warning: 'bg-yellow-600 text-white hover:bg-yellow-700',
    success: 'bg-green-600 text-white hover:bg-green-700',
    info: 'bg-blue-600 text-white hover:bg-blue-700'
  }
  
  return `${base} ${variants[props.variant]} ${disabled}`.trim()
})
</script>