---
import MainLayout from "../../../layouts/MainLayout.astro";
import { trpcClient } from "@/lib/trpc";
import { TrpcProvider } from "@/components/providers/TrpcProvider";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

const { id } = Astro.params;

if (!id || isNaN(Number(id))) {
  return Astro.redirect('/catalog');
}

// Загружаем детальную информацию о группе запчастей
let part: any = null;

try {
  part = await trpcClient.crud.part.findUnique.query({
    where: { id: Number(id) },
    include: {
      image: true,
      partCategory: { include: { image: true } },
      parent: { include: { partCategory: true } },
      children: { include: { partCategory: true } },
      attributes: {
        include: {
          template: {
            include: {
              group: true,
              synonymGroups: { include: { synonyms: true } }
            }
          }
        }
      },
      applicabilities: {
        include: {
          catalogItem: {
            include: {
              brand: true,
              attributes: {
                include: {
                  template: { include: { group: true } }
                }
              }
            }
          }
        }
      },
      equipmentApplicabilities: {
        include: {
          equipmentModel: {
            include: {
              brand: true,
              attributes: {
                include: {
                  template: { include: { group: true } }
                }
              }
            }
          }
        }
      }
    }
  });
} catch (error) {
  console.error('Error loading part details:', error);
}

if (!part) {
  return Astro.redirect('/catalog');
}

// Группируем атрибуты по группам
const attributeGroups = part.attributes.reduce((groups: any, attr: any) => {
  const groupName = attr.template.group?.name || 'Основные характеристики';
  if (!groups[groupName]) {
    groups[groupName] = [];
  }
  groups[groupName].push(attr);
  return groups;
}, {});

// Функция для получения варианта badge по точности
const getAccuracyVariant = (accuracy: string) => {
  switch (accuracy) {
    case 'EXACT_MATCH': return 'success';
    case 'MATCH_WITH_NOTES': return 'warning';
    case 'REQUIRES_MODIFICATION': return 'secondary';
    case 'PARTIAL_MATCH': return 'outline';
    default: return 'default';
  }
};

// Функция для получения текста точности
const getAccuracyText = (accuracy: string) => {
  switch (accuracy) {
    case 'EXACT_MATCH': return 'Точное совпадение';
    case 'MATCH_WITH_NOTES': return 'Совпадение с примечаниями';
    case 'REQUIRES_MODIFICATION': return 'Требует доработки';
    case 'PARTIAL_MATCH': return 'Частичное совпадение';
    default: return accuracy;
  }
};
---

<MainLayout title={part.name || `Запчасть #${part.id}`} description={`Детальная информация о группе взаимозаменяемости ${part.name || part.id}`}>
  <TrpcProvider client:load>
    <div class="container mx-auto px-4 py-8">
      <!-- Хлебные крошки -->
      <nav class="flex mb-8" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
          <li class="inline-flex items-center">
            <a href="/" class="inline-flex items-center text-sm font-medium text-muted-foreground hover:text-foreground">
              Главная
            </a>
          </li>
          <li>
            <div class="flex items-center">
              <span class="mx-2 text-muted-foreground">/</span>
              <a href="/catalog" class="text-sm font-medium text-muted-foreground hover:text-foreground">Каталог</a>
            </div>
          </li>
          <li>
            <div class="flex items-center">
              <span class="mx-2 text-muted-foreground">/</span>
              <a href={`/catalog/categories/${part.partCategory.slug}`} class="text-sm font-medium text-muted-foreground hover:text-foreground">
                {part.partCategory.name}
              </a>
            </div>
          </li>
          <li aria-current="page">
            <div class="flex items-center">
              <span class="mx-2 text-muted-foreground">/</span>
              <span class="text-sm font-medium text-foreground">{part.name || `Запчасть #${part.id}`}</span>
            </div>
          </li>
        </ol>
      </nav>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Основная информация -->
        <div class="lg:col-span-2 space-y-6">
          <!-- Заголовок и основная информация -->
          <div>
            <h1 class="text-3xl font-bold tracking-tight mb-2">
              {part.name || `Запчасть #${part.id}`}
            </h1>
            <p class="text-muted-foreground mb-4">
              Категория: {part.partCategory.name}
            </p>
            
            {/* Иерархия */}
            {part.parent && (
              <div class="mb-4">
                <p class="text-sm text-muted-foreground">
                  Входит в: 
                  <a href={`/catalog/parts/${part.parent.id}`} class="text-primary hover:underline ml-1">
                    {part.parent.name || `Запчасть #${part.parent.id}`}
                  </a>
                </p>
              </div>
            )}
          </div>

          <!-- Атрибуты по группам -->
          {Object.keys(attributeGroups).length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Характеристики</CardTitle>
              </CardHeader>
              <CardContent>
                <div class="space-y-6">
                  {Object.entries(attributeGroups).map(([groupName, attrs]: [string, any]) => (
                    <div>
                      <h4 class="font-semibold mb-3">{groupName}</h4>
                      <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {attrs.map((attr: any) => (
                          <div class="flex justify-between items-center py-2 border-b border-border/50">
                            <span class="text-sm text-muted-foreground">{attr.template.title}:</span>
                            <span class="font-medium">
                              {attr.value}
                              {attr.template.unit && ` ${attr.template.unit}`}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          <!-- Дочерние элементы -->
          {part.children.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Входящие компоненты</CardTitle>
                <CardDescription>Детали, входящие в состав этого узла</CardDescription>
              </CardHeader>
              <CardContent>
                <div class="space-y-2">
                  {part.children.map((child: any) => (
                    <a 
                      href={`/catalog/parts/${child.id}`}
                      class="block p-3 rounded-md border hover:bg-muted/50 transition-colors"
                    >
                      <div class="font-medium">{child.name || `Запчасть #${child.id}`}</div>
                      <div class="text-sm text-muted-foreground">{child.partCategory.name}</div>
                    </a>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        <!-- Боковая панель -->
        <div class="space-y-6">
          <!-- Изображение -->
          {part.image && (
            <Card>
              <CardContent class="p-4">
                <img
                  src={part.image.url}
                  alt={part.name || 'Изображение запчасти'}
                  class="w-full h-auto rounded-md"
                />
              </CardContent>
            </Card>
          )}

          <!-- Взаимозаменяемые артикулы -->
          {part.applicabilities.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Взаимозаменяемые артикулы</CardTitle>
                <CardDescription>Конкретные запчасти от производителей</CardDescription>
              </CardHeader>
              <CardContent>
                <div class="space-y-3">
                  {part.applicabilities.map((applicability: any) => (
                    <div class="p-3 rounded-md border">
                      <div class="flex items-start justify-between mb-2">
                        <div>
                          <div class="font-medium">{applicability.catalogItem.sku}</div>
                          <div class="text-sm text-muted-foreground">{applicability.catalogItem.brand.name}</div>
                        </div>
                        <Badge variant={getAccuracyVariant(applicability.accuracy)}>
                          {getAccuracyText(applicability.accuracy)}
                        </Badge>
                      </div>
                      {applicability.notes && (
                        <p class="text-xs text-muted-foreground mt-2">{applicability.notes}</p>
                      )}
                      {applicability.catalogItem.description && (
                        <p class="text-xs text-muted-foreground mt-1">{applicability.catalogItem.description}</p>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          <!-- Применимость к технике -->
          {part.equipmentApplicabilities.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Применимость к технике</CardTitle>
                <CardDescription>Модели техники, где используется эта запчасть</CardDescription>
              </CardHeader>
              <CardContent>
                <div class="space-y-3">
                  {part.equipmentApplicabilities.map((applicability: any) => (
                    <div class="p-3 rounded-md border">
                      <div class="font-medium">{applicability.equipmentModel.name}</div>
                      {applicability.equipmentModel.brand && (
                        <div class="text-sm text-muted-foreground">{applicability.equipmentModel.brand.name}</div>
                      )}
                      {applicability.notes && (
                        <p class="text-xs text-muted-foreground mt-2">{applicability.notes}</p>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  </TrpcProvider>
</MainLayout>
