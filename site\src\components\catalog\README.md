# Каталог - React Islands Архитектура

## Обзор

Каталог был рефакторен с монолитного компонента `ModernCatalogSearch` на архитектуру React Islands для лучшей производительности и управления состоянием.

## Архитектура

### Глобальное состояние
- **`CatalogStateManager`** - управляет состоянием через localStorage и события
- **`useCatalogGlobalState`** - React хук для работы с глобальным состоянием
- Синхронизация между островками через CustomEvents

### React Islands

#### 1. SearchHeaderIsland
- **Файл**: `islands/SearchHeaderIsland.tsx`
- **Загрузка**: `client:load` (приоритетная)
- **Функции**: поисковая строка, статистика результатов

#### 2. FiltersIsland  
- **Файл**: `islands/FiltersIsland.tsx`
- **Загрузка**: `client:visible` (при видимости)
- **Функции**: фильтры по категориям, брендам, атрибутам

#### 3. ResultsIsland
- **Файл**: `islands/ResultsIsland.tsx` 
- **Загрузка**: `client:load` (приоритетная)
- **Функции**: отображение результатов поиска, пагинация

#### 4. AIAssistantIsland
- **Файл**: `islands/AIAssistantIsland.tsx`
- **Загрузка**: `client:idle` (при простое)
- **Функции**: AI помощник для поиска

#### 5. ItemDetailsIsland
- **Файл**: `islands/ItemDetailsIsland.tsx`
- **Загрузка**: `client:idle` (при простое)
- **Функции**: модальное окно с деталями товара

## Оптимизации

### Кэширование
- **Категории**: 10 минут stale, 1 час в кэше
- **Бренды**: 10 минут stale, 1 час в кэше  
- **Шаблоны атрибутов**: 15 минут stale, 1 час в кэше
- **Результаты поиска**: 30 секунд stale, 5 минут в кэше

### Дебаунсинг
- Поисковые запросы дебаунсятся на 300мс
- Предотвращает избыточные API вызовы

### Загрузка компонентов
- **Критичные** (поиск, результаты): `client:load`
- **Второстепенные** (фильтры): `client:visible`
- **Интерактивные** (AI, детали): `client:idle`

## Использование

### Основная страница
```astro
---
import { TrpcProvider } from '@/components/providers/TrpcProvider'
import SearchHeaderIsland from '@/components/catalog/islands/SearchHeaderIsland'
import FiltersIsland from '@/components/catalog/islands/FiltersIsland'
import ResultsIsland from '@/components/catalog/islands/ResultsIsland'
import AIAssistantIsland from '@/components/catalog/islands/AIAssistantIsland'
import ItemDetailsIsland from '@/components/catalog/islands/ItemDetailsIsland'
---

<TrpcProvider client:load>
  <div class="min-h-screen bg-background">
    <SearchHeaderIsland client:load />

    <div class="flex">
      <FiltersIsland client:visible />
      <ResultsIsland client:load />
    </div>

    <AIAssistantIsland client:idle />
    <ItemDetailsIsland client:idle />
  </div>
</TrpcProvider>
```

### В React компонентах
```tsx
import { useCatalogSearch } from '@/components/catalog/pro/useCatalogSearch'

function MyComponent() {
  const { filters, updateFilters, results, isLoading } = useCatalogSearch()
  
  // Использование данных и методов
}
```

## Миграция

### Старый подход (монолит)
```tsx
<ModernCatalogSearchIsland client:load />
```

### Новый подход (островки)
```astro
<TrpcProvider client:load>
  <SearchHeaderIsland client:load />
  <FiltersIsland client:visible />
  <ResultsIsland client:load />
  <AIAssistantIsland client:idle />
  <ItemDetailsIsland client:idle />
</TrpcProvider>
```

## Преимущества

1. **Производительность**: Компоненты загружаются по мере необходимости
2. **Кэширование**: Агрессивное кэширование редко изменяемых данных
3. **Состояние**: Глобальное состояние синхронизируется между островками
4. **Масштабируемость**: Легко добавлять новые островки
5. **SEO**: Лучшая производительность загрузки страницы

## Файлы

- `islands/` - React Islands компоненты
- `pro/useCatalogSearch.ts` - Основной хук для работы с каталогом
- `lib/catalog-state.ts` - Глобальное управление состоянием
- `hooks/useCatalogData.ts` - Хуки для загрузки данных
- `hooks/useDebounce.ts` - Дебаунсинг утилита
- `pages/catalog.astro` - Пример использования
