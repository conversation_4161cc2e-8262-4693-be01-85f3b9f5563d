import { router, publicProcedure } from './trpc';
import { getAccessCatalog } from './permissions';
import { schemaEditorRouter } from './routers/schema-editor';
import { adminRouter } from './routers/admin';
import { uploadRouter } from './routers/upload';
import { partAttributesRouter } from './routers/part-attributes';
import { attributeTemplatesRouter } from './routers/attribute-templates';
import { attributeSynonymsRouter } from './routers/attribute-synonyms';
import { matchingRouter } from './routers/matching';
import { searchRouter } from './routers/search';
import { catalogRouter } from './routers/catalog';
import { siteRouter } from './routers/site';
import { importExportRouter } from './routers/import-export';
import { importRouter } from './routers/import';
import { createRouter as createCRUDRouter } from './generated/trpc/routers';

// Создаем автоматически сгенерированные CRUD роутеры
const crudRouter = createCRUDRouter();

export const appRouter = router({
  // Site-клиентский слой (изоляция от админки)
  site: siteRouter,

  // Кастомные роутеры (админка / внутренние)
  catalog: catalogRouter,
  schemaEditor: schemaEditorRouter,
  admin: adminRouter,
  upload: uploadRouter,
  partAttributes: partAttributesRouter,
  attributeTemplates: attributeTemplatesRouter,
  attributeSynonyms: attributeSynonymsRouter,
  matching: matchingRouter,
  search: searchRouter,
  importExport: importExportRouter,
  import: importRouter,

  // Автоматически сгенерированные CRUD роутеры для всех моделей
  crud: crudRouter,

  // вспомогательное: каталог ресурсов/действий для UI ACL
  access: router({
    list: publicProcedure.query(() => getAccessCatalog()),
  }),
});

export type AppRouter = typeof appRouter;
