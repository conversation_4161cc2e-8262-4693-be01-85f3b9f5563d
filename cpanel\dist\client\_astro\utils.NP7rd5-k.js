var a={};function i(e){return new Promise((r,s)=>{const t=new FileReader;t.onload=()=>r(String(t.result)),t.onerror=n=>s(n),t.readAsDataURL(e)})}function c(e){if(!e)return"";const r={а:"a",б:"b",в:"v",г:"g",д:"d",е:"e",ё:"e",ж:"zh",з:"z",и:"i",й:"y",к:"k",л:"l",м:"m",н:"n",о:"o",п:"p",р:"r",с:"s",т:"t",у:"u",ф:"f",х:"h",ц:"ts",ч:"ch",ш:"sh",щ:"sch",ъ:"",ы:"y",ь:"",э:"e",ю:"yu",я:"ya"},s=e.trim().toLowerCase();let t="";for(const n of s){const o=n.charCodeAt(0);if(o>=97&&o<=122||o>=48&&o<=57){t+=n;continue}if(r[n]!==void 0){t+=r[n];continue}if(/\s|[_]+/.test(n)){t+="-";continue}}return t.replace(/[^a-z0-9-]/g,"-").replace(/-{2,}/g,"-").replace(/^-+|-+$/g,"")}function f(e){if(!e)return;if(/^https?:\/\//i.test(e))return e;const r=typeof window<"u"?"http://localhost:3000":a.API_URL?String(a.API_URL).replace(/\/$/,""):"http://localhost:3000";return e.startsWith("/")?`${r}${e}`:`${r}/${e}`}export{i as f,f as r,c as s};
