<template>
  <div class="w-full max-w-7xl">
    <div class="grid grid-cols-1 xl:grid-cols-3 gap-6">
      <!-- Экспорт -->
      <VCard class="p-6">
        <template #title>
          <h2 class="text-lg font-semibold mb-4">Экспорт каталога</h2>
        </template>
        <template #content>
          <div class="space-y-4">
            <!-- Основные сущности -->
            <div>
              <h3 class="text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">Основные сущности</h3>
              <div class="grid grid-cols-1 gap-2">
                <div class="flex items-center space-x-2">
                  <VCheckbox v-model="include.Brand" inputId="brand" :binary="true" />
                  <label for="brand" class="text-sm">Бренды</label>
                </div>
                <div class="flex items-center space-x-2">
                  <VCheckbox v-model="include.PartCategory" inputId="partCategory" :binary="true" />
                  <label for="partCategory" class="text-sm">Категории запчастей</label>
                </div>
                <div class="flex items-center space-x-2">
                  <VCheckbox v-model="include.AttributeGroup" inputId="attributeGroup" :binary="true" />
                  <label for="attributeGroup" class="text-sm">Группы атрибутов</label>
                </div>
                <div class="flex items-center space-x-2">
                  <VCheckbox v-model="include.AttributeTemplate" inputId="attributeTemplate" :binary="true" />
                  <label for="attributeTemplate" class="text-sm">Шаблоны атрибутов</label>
                </div>
                <div class="flex items-center space-x-2">
                  <VCheckbox v-model="include.AttributeSynonymGroup" inputId="synonymGroup" :binary="true" />
                  <label for="synonymGroup" class="text-sm">Группы синонимов</label>
                </div>
                <div class="flex items-center space-x-2">
                  <VCheckbox v-model="include.AttributeSynonym" inputId="synonym" :binary="true" />
                  <label for="synonym" class="text-sm">Синонимы атрибутов</label>
                </div>
                <div class="flex items-center space-x-2">
                  <VCheckbox v-model="include.Part" inputId="part" :binary="true" />
                  <label for="part" class="text-sm">Группы взаимозаменяемости</label>
                </div>
                <div class="flex items-center space-x-2">
                  <VCheckbox v-model="include.CatalogItem" inputId="catalogItem" :binary="true" />
                  <label for="catalogItem" class="text-sm">Каталожные позиции</label>
                </div>
                <div class="flex items-center space-x-2">
                  <VCheckbox v-model="include.EquipmentModel" inputId="equipmentModel" :binary="true" />
                  <label for="equipmentModel" class="text-sm">Модели техники</label>
                </div>
              </div>
            </div>

            <!-- Атрибуты и связи -->
            <div>
              <h3 class="text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">Атрибуты и связи</h3>
              <div class="grid grid-cols-1 gap-2">
                <div class="flex items-center space-x-2">
                  <VCheckbox v-model="include.PartAttribute" inputId="partAttribute" :binary="true" />
                  <label for="partAttribute" class="text-sm">Атрибуты запчастей</label>
                </div>
                <div class="flex items-center space-x-2">
                  <VCheckbox v-model="include.CatalogItemAttribute" inputId="catalogItemAttribute" :binary="true" />
                  <label for="catalogItemAttribute" class="text-sm">Атрибуты позиций</label>
                </div>
                <div class="flex items-center space-x-2">
                  <VCheckbox v-model="include.EquipmentModelAttribute" inputId="equipmentModelAttribute" :binary="true" />
                  <label for="equipmentModelAttribute" class="text-sm">Атрибуты техники</label>
                </div>
                <div class="flex items-center space-x-2">
                  <VCheckbox v-model="include.PartApplicability" inputId="partApplicability" :binary="true" />
                  <label for="partApplicability" class="text-sm">Применимость запчастей</label>
                </div>
                <div class="flex items-center space-x-2">
                  <VCheckbox v-model="include.EquipmentApplicability" inputId="equipmentApplicability" :binary="true" />
                  <label for="equipmentApplicability" class="text-sm">Применимость к технике</label>
                </div>
              </div>
            </div>

            <!-- Фильтры -->
            <div>
              <h3 class="text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">Фильтры</h3>
              <VInputText
                v-model="brandSlugsInput"
                placeholder="Бренды через запятую (например: cat,komatsu)"
                class="w-full"
              />
            </div>

            <!-- Кнопки экспорта -->
            <div class="flex flex-col gap-2">
              <VButton
                @click="onExport"
                :disabled="loading"
                :loading="loading"
                class="w-full"
              >
                {{ loading ? 'Экспорт...' : 'Скачать данные' }}
              </VButton>
              <VButton
                @click="onExportTemplate"
                :disabled="loading"
                :loading="loadingTemplate"
                severity="secondary"
                outlined
                class="w-full"
              >
                {{ loadingTemplate ? 'Создание...' : 'Скачать шаблон' }}
              </VButton>
            </div>
          </div>
        </template>
      </VCard>

      <!-- Импорт -->
      <VCard class="p-6">
        <template #title>
          <h2 class="text-lg font-semibold mb-4">Импорт каталога</h2>
        </template>
        <template #content>
          <div class="space-y-4">
            <!-- Загрузка файла -->
            <div>
              <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
                Файл Excel (.xlsx)
              </label>
              <div class="flex items-center gap-2">
                <input
                  type="file"
                  @change="onFileChange"
                  accept=".xlsx"
                  class="flex-1 text-sm text-surface-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-primary-50 file:text-primary-700 hover:file:bg-primary-100"
                />
                <DangerButton
                  v-if="file"
                  @click="clearFile"
                  severity="secondary"
                  outlined
                  size="small"
                >
                  Очистить
                </DangerButton>
              </div>
              <div v-if="file" class="mt-2 text-sm text-surface-600">
                Выбран файл: {{ file.name }} ({{ formatFileSize(file.size) }})
              </div>
            </div>

            <!-- Настройки импорта -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
                  Режим конфликтов
                </label>
                <VSelect
                  v-model="onConflict"
                  :options="conflictOptions"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="Выберите режим"
                  class="w-full"
                />
              </div>
              <div class="flex items-center space-x-2 pt-6">
                <VCheckbox v-model="createMissingRefs" inputId="createMissingRefs" :binary="true" />
                <label for="createMissingRefs" class="text-sm">Создавать отсутствующие ссылки</label>
              </div>
            </div>

            <!-- Кнопки действий -->
            <div class="flex flex-col gap-2">
              <VButton
                @click="onDryRun"
                :disabled="loading || !file"
                :loading="loading && currentAction === 'dryRun'"
                severity="info"
                class="w-full"
              >
                {{ loading && currentAction === 'dryRun' ? 'Проверка...' : 'Проверить файл (Dry Run)' }}
              </VButton>
              <VButton
                @click="onExecute"
                :disabled="loading || !file"
                :loading="loading && currentAction === 'execute'"
                severity="success"
                class="w-full"
              >
                {{ loading && currentAction === 'execute' ? 'Выполняю...' : 'Выполнить импорт' }}
              </VButton>
            </div>

            <!-- Результат -->
            <div v-if="dryRunResult" class="space-y-3">
              <h3 class="text-sm font-medium text-surface-700 dark:text-surface-300">Результат:</h3>

              <!-- Краткая статистика -->
              <div v-if="dryRunResult.perSheet" class="grid grid-cols-2 gap-2">
                <div v-for="(counters, sheetName) in dryRunResult.perSheet" :key="sheetName"
                     class="p-3 bg-surface-50 dark:bg-surface-900 rounded border">
                  <div class="text-xs font-medium text-surface-700 dark:text-surface-300">{{ sheetName }}</div>
                  <div class="text-xs text-surface-600 dark:text-surface-400">
                    Строк: {{ counters.rowsSeen }} | Валидных: {{ counters.rowsValid }}
                    <span v-if="counters.created">| Создано: {{ counters.created }}</span>
                    <span v-if="counters.updated">| Обновлено: {{ counters.updated }}</span>
                    <span v-if="counters.errorsCount">| Ошибок: {{ counters.errorsCount }}</span>
                  </div>
                </div>
              </div>

              <!-- Ошибки -->
              <div v-if="dryRunResult.errors?.length" class="space-y-2">
                <h4 class="text-sm font-medium text-red-700 dark:text-red-400">Ошибки:</h4>
                <div class="max-h-32 overflow-y-auto space-y-1">
                  <div v-for="(error, idx) in dryRunResult.errors.slice(0, 10)" :key="idx"
                       class="text-xs p-2 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 rounded">
                    {{ error.sheet }}:{{ error.rowIndex }} - {{ error.message }}
                  </div>
                  <div v-if="dryRunResult.errors.length > 10" class="text-xs text-surface-500">
                    ... и еще {{ dryRunResult.errors.length - 10 }} ошибок
                  </div>
                </div>
              </div>

              <!-- Кнопка скачивания отчета -->
              <div v-if="dryRunResult.reportBase64">
                <VButton
                  @click="downloadReport(dryRunResult.reportBase64)"
                  severity="secondary"
                  outlined
                  size="small"
                >
                
                  Скачать отчёт
                </VButton>
              </div>
            </div>
          </div>
        </template>
      </VCard>

      <!-- История импортов -->
      <VCard class="p-6">
        <template #title>
          <h2 class="text-lg font-semibold mb-4">История импортов</h2>
        </template>
        <template #content>
          <div class="text-sm text-surface-600 dark:text-surface-400">
            Функционал истории импортов будет добавлен в следующей итерации
          </div>
        </template>
      </VCard>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useTrpc } from '@/composables/useTrpc'
import VCard from '@/volt/Card.vue'
import VCheckbox from '@/volt/Checkbox.vue'
import VInputText from '@/volt/InputText.vue'
import VButton from '@/volt/Button.vue'
import VSelect from '@/volt/Select.vue'
import DangerButton from '@/volt/DangerButton.vue'

// tRPC client is imported directly

// tRPC helpers
const { importExport, excelImport } = useTrpc()


// Reactive state
const include = ref({
  Brand: true,
  PartCategory: true,
  AttributeGroup: false,
  AttributeTemplate: true,
  AttributeSynonymGroup: false,
  AttributeSynonym: false,
  Part: true,
  CatalogItem: true,
  EquipmentModel: true,
  PartAttribute: false,
  CatalogItemAttribute: false,
  EquipmentModelAttribute: false,
  PartApplicability: false,
  EquipmentApplicability: false
})

const brandSlugsInput = ref('')
const onConflict = ref<'upsert' | 'update_only' | 'skip' | 'error'>('upsert')
const createMissingRefs = ref(false)
const file = ref<File | null>(null)
const loading = ref(false)
const loadingTemplate = ref(false)
const currentAction = ref<'dryRun' | 'execute' | null>(null)
const dryRunResult = ref<any>(null)

// Options for conflict mode select
const conflictOptions = [
  { label: 'Создать или обновить (upsert)', value: 'upsert' },
  { label: 'Только обновить (update_only)', value: 'update_only' },
  { label: 'Пропустить (skip)', value: 'skip' },
  { label: 'Ошибка (error)', value: 'error' }
]

// Utility functions
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const clearFile = () => {
  file.value = null
  dryRunResult.value = null
}

// Export functions
const onExport = async () => {
  loading.value = true
  try {
    const brandSlugs = brandSlugsInput.value.split(',').map(s => s.trim()).filter(Boolean)
    const res = await importExport.exportXlsx({
      include: include.value as any,
      filters: { brandSlugs },
      meta: {
        createMissingRefs: createMissingRefs.value,
        onConflict: onConflict.value
      }
    })
    if (!res) return
    const blob = b64toBlob(res.base64, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = res.fileName
    a.click()
    URL.revokeObjectURL(url)
  } catch (error) {
    console.error('Export failed:', error)
  } finally {
    loading.value = false
  }
}

const onExportTemplate = async () => {
  loadingTemplate.value = true
  try {
    const res = await importExport.exportTemplate({
      include: include.value as any
    }) as any
    if (!res) return
    const blob = b64toBlob(res.base64, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = res.fileName
    a.click()
    URL.revokeObjectURL(url)
  } catch (error) {
    console.error('Export template failed:', error)
  } finally {
    loadingTemplate.value = false
  }
}

// File handling
const onFileChange = (e: Event) => {
  const target = e.target as HTMLInputElement
  file.value = target.files?.[0] ?? null
  dryRunResult.value = null // Clear previous results
}

// File processing
function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => resolve(String(reader.result).split(',')[1] || '')
    reader.onerror = reject
    reader.readAsDataURL(file)
  })
}

// Import functions
const onDryRun = async () => {
  if (!file.value) return
  currentAction.value = 'dryRun'
  loading.value = true
  try {
    const base64 = await fileToBase64(file.value)
    const res = await useTrpc().excelImport.dryRun({
      base64,
      overrides: {
        createMissingRefs: createMissingRefs.value,
        onConflict: onConflict.value
      }
    })
    dryRunResult.value = res
  } catch (error) {
    console.error('Dry run failed:', error)
    // TODO: Show error toast
  } finally {
    loading.value = false
    currentAction.value = null
  }
}

const onExecute = async () => {
  if (!file.value) return
  currentAction.value = 'execute'
  loading.value = true
  try {
    const base64 = await fileToBase64(file.value)
    const res = await useTrpc().excelImport.execute({
      base64,
      overrides: {
        createMissingRefs: createMissingRefs.value,
        onConflict: onConflict.value
      }
    }) as any
    if (!res) return
    dryRunResult.value = res
    if (res.reportBase64) downloadReport(res.reportBase64)
  } catch (error) {
    console.error('Execute failed:', error)
    // TODO: Show error toast
  } finally {
    loading.value = false
    currentAction.value = null
  }
}

// Download functions
function downloadReport(base64: string) {
  const blob = b64toBlob(base64, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `import-report-${Date.now()}.xlsx`
  a.click()
  URL.revokeObjectURL(url)
}

function b64toBlob(base64: string, contentType = '', sliceSize = 512) {
  const byteCharacters = atob(base64);
  const byteArrays = [];

  for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
    const slice = byteCharacters.slice(offset, offset + sliceSize);

    const byteNumbers = new Array(slice.length);
    for (let i = 0; i < slice.length; i++) {
      byteNumbers[i] = slice.charCodeAt(i);
    }

    const byteArray = new Uint8Array(byteNumbers);
    byteArrays.push(byteArray);
  }

  const blob = new Blob(byteArrays, { type: contentType });
  return blob;
}
</script>

