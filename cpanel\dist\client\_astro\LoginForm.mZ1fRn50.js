import{w as B}from"./runtime-dom.esm-bundler.C-dfRCGi.js";import{u as C}from"./useAuth.DETRY1gV.js";import D from"./Button.oEwD-lSq.js";import V from"./Card.CAr8QcdG.js";import{I as k}from"./InputText.COaPodMV.js";import{P as S}from"./Password.ZvHvPIrJ.js";import{M as h}from"./Message.BOpJRjRi.js";import{C as A}from"./Checkbox.B9YJ1Jrl.js";import{n as _}from"./router.DKcY2uv6.js";/* empty css                       */import{_ as I}from"./_plugin-vue_export-helper.BX07OBiL.js";import{d as M,c as f,a as r,e as i,j as x,h as P,o as n,g as j,b as c,f as b}from"./index.BglzLLgy.js";import{r as g,a as F,t as p}from"./reactivity.esm-bundler.D5IypM4U.js";import"./auth-client.D7sWEz1h.js";import"./types.FgRm47Sn.js";import"./index.DQmIdHOH.js";import"./index.B_yc9D3m.js";import"./index.CRcBj2l1.js";import"./index.CLkTvMlq.js";import"./index.CLs7nh7g.js";import"./index.Tc5ZRw49.js";import"./index.irlSZ_18.js";import"./index.B0GjtQuk.js";import"./index.D7_1DwEX.js";import"./index.CXDqTVvt.js";const T=M({__name:"LoginForm",setup(w,{expose:e}){e();const{signIn:d,LoginFormSchema:u}=C(),m=g(!1),l=g(""),a=F({email:"",password:"",rememberMe:!1}),s=F({}),y=P(()=>a.email&&a.password&&Object.keys(s).length===0),E={signIn:d,LoginFormSchema:u,isSubmitting:m,generalError:l,formData:a,fieldErrors:s,isFormValid:y,validateField:o=>{try{const v=u.shape[o].safeParse(a[o]);v.success?delete s[o]:s[o]=v.error.issues[0]?.message||"Ошибка валидации"}catch(t){console.error("Validation error for field:",o,t),delete s[o]}},clearFieldError:o=>{s[o]&&delete s[o],l.value&&(l.value="")},handleSubmit:async()=>{l.value="",Object.keys(s).forEach(t=>delete s[t]);const o=u.safeParse(a);if(!o.success){o.error.issues.forEach(t=>{t.path[0]&&(s[t.path[0]]=t.message)});return}m.value=!0;try{const t=await d(a);t.error?t.error.message.includes("Invalid email or password")?l.value="Неверный email или пароль":t.error.message.includes("Too many requests")?l.value="Слишком много попыток входа. Попробуйте позже.":l.value=t.error.message||"Ошибка входа в систему":(console.log("✅ Успешный вход в систему"),setTimeout(()=>{_("/admin")},100))}catch(t){console.error("Login error:",t),l.value="Произошла неожиданная ошибка. Попробуйте еще раз."}finally{m.value=!1}},Button:D,Card:V,InputText:k,Password:S,Message:h,Checkbox:A};return Object.defineProperty(E,"__isScriptSetup",{enumerable:!1,value:!0}),E}}),L={class:"min-h-screen flex items-center justify-center bg-surface-50 py-12 px-4 sm:px-6 lg:px-8"},N={class:"max-w-md w-full space-y-8"},O={key:1,class:"text-red-500"},U={key:2,class:"text-red-500"},q={class:"flex items-center justify-between"},z={class:"flex items-center"};function R(w,e,d,u,m,l){return n(),f("div",L,[r("div",N,[e[12]||(e[12]=r("div",null,[r("div",{class:"mx-auto h-12 w-12 flex items-center justify-center"},[r("img",{class:"h-12 w-12",src:"/favicon.svg",alt:"PartTec"})]),r("h2",{class:"mt-6 text-center text-3xl font-extrabold text-surface-900"}," Вход в админ панель "),r("p",{class:"mt-2 text-center text-sm text-surface-600"}," Система управления каталогом PartTec ")],-1)),i(u.Card,{class:"mt-8"},{content:x(()=>[r("form",{class:"space-y-6",onSubmit:B(u.handleSubmit,["prevent"])},[u.generalError?(n(),j(u.Message,{key:0,severity:"error",closable:!1},{default:x(()=>[b(p(u.generalError),1)]),_:1})):c("",!0),r("div",null,[i(u.InputText,{id:"email",modelValue:u.formData.email,"onUpdate:modelValue":e[0]||(e[0]=a=>u.formData.email=a),type:"email",autocomplete:"email",class:"w-full",invalid:!!u.fieldErrors.email,onBlur:e[1]||(e[1]=a=>u.validateField("email")),onInput:e[2]||(e[2]=a=>u.clearFieldError("email"))},null,8,["modelValue","invalid"]),e[7]||(e[7]=r("label",{for:"email"},"Email адрес",-1))]),u.fieldErrors.email?(n(),f("small",O,p(u.fieldErrors.email),1)):c("",!0),r("div",null,[i(u.Password,{id:"password",modelValue:u.formData.password,"onUpdate:modelValue":e[3]||(e[3]=a=>u.formData.password=a),autocomplete:"current-password",class:"w-full",invalid:!!u.fieldErrors.password,feedback:!1,toggleMask:"",onBlur:e[4]||(e[4]=a=>u.validateField("password")),onInput:e[5]||(e[5]=a=>u.clearFieldError("password"))},null,8,["modelValue","invalid"]),e[8]||(e[8]=r("label",{for:"password"},"Пароль",-1))]),u.fieldErrors.password?(n(),f("small",U,p(u.fieldErrors.password),1)):c("",!0),r("div",q,[r("div",z,[i(u.Checkbox,{id:"remember-me",modelValue:u.formData.rememberMe,"onUpdate:modelValue":e[6]||(e[6]=a=>u.formData.rememberMe=a),binary:!0},null,8,["modelValue"]),e[9]||(e[9]=r("label",{for:"remember-me",class:"ml-2 block text-sm text-surface-700"}," Запомнить меня ",-1))]),e[10]||(e[10]=r("div",{class:"text-sm"},[r("a",{href:"#",class:"font-medium text-primary-600 hover:text-primary-500"}," Забыли пароль? ")],-1))]),r("div",null,[i(u.Button,{type:"submit",disabled:u.isSubmitting||!u.isFormValid,loading:u.isSubmitting,label:"Войти",class:"w-full",size:"large"},null,8,["disabled","loading"])]),e[11]||(e[11]=r("div",{class:"text-center"},[r("p",{class:"text-sm text-surface-600"},[b(" Нет аккаунта? "),r("a",{href:"/admin/register",class:"font-medium text-primary-600 hover:text-primary-500"}," Зарегистрироваться ")])],-1))],32)]),_:1})])])}const b4=I(T,[["render",R],["__scopeId","data-v-e3f15633"]]);export{b4 as default};
