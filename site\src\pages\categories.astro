---
import MainLayout from "../layouts/MainLayout.astro";
import { trpcClient } from "@/lib/trpc";
import { TrpcProvider } from "@/components/providers/TrpcProvider";
import { CategoryCard } from "@/components/catalog/CategoryCard";

// Загружаем все категории с иерархией
let categories: any[] = [];

try {
  const categoriesResponse = await trpcClient.crud.partCategory.findMany.query({
    include: {
      image: true,
      children: {
        include: {
          _count: { select: { parts: true } }
        }
      },
      _count: { select: { parts: true } }
    },
    orderBy: [
      { level: 'asc' },
      { name: 'asc' }
    ]
  });
  categories = categoriesResponse || [];
} catch (error) {
  console.error('Error loading categories:', error);
}

// Группируем категории по уровням
const rootCategories = categories.filter(cat => cat.level === 0);
const subcategories = categories.filter(cat => cat.level > 0);
---

<MainLayout title="Категории запчастей" description="Иерархический каталог категорий запчастей">
  <TrpcProvider client:load>
    <div class="container mx-auto px-4 py-8">
      <!-- Заголовок -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold tracking-tight mb-2">Категории запчастей</h1>
        <p class="text-muted-foreground">
          Иерархическая структура каталога запчастей
        </p>
      </div>

      <!-- Основные категории -->
      {rootCategories.length > 0 && (
        <div class="mb-12">
          <h2 class="text-2xl font-semibold mb-6">Основные категории</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {rootCategories.map((category) => (
              <CategoryCard category={category} client:load />
            ))}
          </div>
        </div>
      )}

      <!-- Подкатегории по группам -->
      {rootCategories.map((rootCategory) => {
        const childCategories = subcategories.filter(cat => 
          cat.path.startsWith(rootCategory.path + '/') && cat.level === 1
        );
        
        if (childCategories.length === 0) return null;
        
        return (
          <div class="mb-12">
            <h2 class="text-2xl font-semibold mb-6 flex items-center gap-2">
              {rootCategory.icon && (
                <span class="text-2xl">{rootCategory.icon}</span>
              )}
              {rootCategory.name}
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {childCategories.map((category) => (
                <CategoryCard category={category} client:load />
              ))}
            </div>
          </div>
        );
      })}

      <!-- Если нет категорий -->
      {categories.length === 0 && (
        <div class="text-center py-12">
          <h3 class="text-lg font-semibold mb-2">Категории не найдены</h3>
          <p class="text-muted-foreground">
            Каталог категорий пуст или недоступен
          </p>
        </div>
      )}
    </div>
  </TrpcProvider>
</MainLayout>
