// Общие include/select для tRPC запросов каталога (DRY)
export const partListInclude = {
  partCategory: true,
  image: true,
  attributes: {
    include: { template: true },
    take: 3,
  },
} as const;

export const rootCategoriesInclude = {
  image: true,
  _count: { select: { parts: true } },
} as const;

export const brandListInclude = {
  _count: { select: { catalogItems: true } },
} as const;

