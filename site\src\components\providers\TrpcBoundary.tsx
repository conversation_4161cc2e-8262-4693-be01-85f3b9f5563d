"use client"

import * as React from 'react'
import { QueryClientProvider } from '@tanstack/react-query'
import { trpc } from '@/lib/trpc'
import { queryClient, trpcClient } from '@/lib/clients'

interface Props {
  children: React.ReactNode
}

// Мини-провайдер без создания новых инстансов
export function TrpcBoundary({ children }: Props) {
  return (
    <trpc.Provider client={trpcClient} queryClient={queryClient}>
      <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
    </trpc.Provider>
  )
}

