/*
  Warnings:

  - A unique constraint covering the columns `[imageId]` on the table `CatalogItem` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "public"."CatalogItem" ADD COLUMN     "imageId" INTEGER;

-- CreateTable
CREATE TABLE "public"."_PartMediaAssets" (
    "A" INTEGER NOT NULL,
    "B" INTEGER NOT NULL,

    CONSTRAINT "_PartMediaAssets_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "public"."_CatalogItemMediaAssets" (
    "A" INTEGER NOT NULL,
    "B" INTEGER NOT NULL,

    CONSTRAINT "_CatalogItemMediaAssets_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE INDEX "_PartMediaAssets_B_index" ON "public"."_PartMediaAssets"("B");

-- CreateIndex
CREATE INDEX "_CatalogItemMediaAssets_B_index" ON "public"."_CatalogItemMediaAssets"("B");

-- CreateIndex
CREATE UNIQUE INDEX "CatalogItem_imageId_key" ON "public"."CatalogItem"("imageId");

-- AddForeignKey
ALTER TABLE "public"."CatalogItem" ADD CONSTRAINT "CatalogItem_imageId_fkey" FOREIGN KEY ("imageId") REFERENCES "public"."MediaAsset"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."_PartMediaAssets" ADD CONSTRAINT "_PartMediaAssets_A_fkey" FOREIGN KEY ("A") REFERENCES "public"."MediaAsset"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."_PartMediaAssets" ADD CONSTRAINT "_PartMediaAssets_B_fkey" FOREIGN KEY ("B") REFERENCES "public"."Part"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."_CatalogItemMediaAssets" ADD CONSTRAINT "_CatalogItemMediaAssets_A_fkey" FOREIGN KEY ("A") REFERENCES "public"."CatalogItem"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."_CatalogItemMediaAssets" ADD CONSTRAINT "_CatalogItemMediaAssets_B_fkey" FOREIGN KEY ("B") REFERENCES "public"."MediaAsset"("id") ON DELETE CASCADE ON UPDATE CASCADE;
