import { ref, computed, watch, onMounted } from 'vue';

const useUrlParams = (initialState, options = {}) => {
  const {
    prefix = "",
    arrayParams = [],
    numberParams = [],
    booleanParams = [],
    debounceMs = 300,
    replaceHistory = true
  } = options;
  const filters = ref({ ...initialState });
  let debounceTimer = null;
  const valueToString = (key, value) => {
    if (value === null || value === void 0 || value === "") {
      return void 0;
    }
    if (arrayParams.includes(key)) {
      if (Array.isArray(value)) {
        return value.map((v) => String(v)).filter((v) => v !== "");
      }
      return [String(value)].filter((v) => v !== "");
    }
    return String(value);
  };
  const stringToValue = (key, value) => {
    if (!value || Array.isArray(value) && value.length === 0) {
      return arrayParams.includes(key) ? [] : null;
    }
    if (arrayParams.includes(key)) {
      const arr = Array.isArray(value) ? value : [value];
      if (numberParams.includes(key)) {
        return arr.map((v) => {
          const num = Number(v);
          return isNaN(num) ? null : num;
        }).filter((v) => v !== null);
      }
      if (booleanParams.includes(key)) {
        return arr.map((v) => v === "true" || v === "1");
      }
      return arr;
    }
    if (numberParams.includes(key)) {
      const num = Number(Array.isArray(value) ? value[0] : value);
      return isNaN(num) ? null : num;
    }
    if (booleanParams.includes(key)) {
      const val = Array.isArray(value) ? value[0] : value;
      return val === "true" || val === "1";
    }
    return Array.isArray(value) ? value[0] : value;
  };
  const getCurrentParams = () => {
    if (typeof window === "undefined") {
      return new URLSearchParams();
    }
    return new URLSearchParams(window.location.search);
  };
  const loadFromUrl = () => {
    const params = getCurrentParams();
    const newFilters = { ...initialState };
    Object.keys(newFilters).forEach((key) => {
      const paramKey = prefix + key;
      const paramValue = params.get(paramKey);
      const paramValues = params.getAll(paramKey);
      if (paramValue !== null || paramValues.length > 0) {
        if (arrayParams.includes(key) && paramValues.length > 0) {
          newFilters[key] = stringToValue(key, paramValues);
        } else if (paramValue !== null) {
          newFilters[key] = stringToValue(key, paramValue);
        }
      }
    });
    filters.value = newFilters;
  };
  const saveToUrl = () => {
    if (typeof window === "undefined") return;
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }
    debounceTimer = setTimeout(() => {
      const params = getCurrentParams();
      const keysToDelete = [];
      params.forEach((_, key) => {
        if (key.startsWith(prefix)) {
          keysToDelete.push(key);
        }
      });
      keysToDelete.forEach((key) => params.delete(key));
      Object.entries(filters.value).forEach(([key, value]) => {
        const paramKey = prefix + key;
        const stringValue = valueToString(key, value);
        if (stringValue !== void 0) {
          if (Array.isArray(stringValue)) {
            stringValue.forEach((v) => params.append(paramKey, v));
          } else {
            params.set(paramKey, stringValue);
          }
        }
      });
      const nextSearch = params.toString();
      const currentSearch = typeof window !== "undefined" && window.location.search ? window.location.search.replace(/^\?/, "") : "";
      if (nextSearch !== currentSearch) {
        const url = new URL(window.location.href);
        url.search = nextSearch;
        if (replaceHistory) {
          window.history.replaceState(null, "", url);
        } else {
          window.history.pushState(null, "", url);
        }
      }
    }, debounceMs);
  };
  const updateFilter = (key, value) => {
    filters.value[key] = value;
  };
  const updateFilters = (updates) => {
    Object.assign(filters.value, updates);
  };
  const resetFilters = () => {
    filters.value = { ...initialState };
  };
  const resetFilter = (key) => {
    filters.value[key] = initialState[key];
  };
  const hasActiveFilters = computed(() => {
    return Object.entries(filters.value).some(([key, value]) => {
      const initialValue = initialState[key];
      if (Array.isArray(value) && Array.isArray(initialValue)) {
        return value.length !== initialValue.length || !value.every((v, i) => v === initialValue[i]);
      }
      return value !== initialValue;
    });
  });
  const activeFiltersCount = computed(() => {
    return Object.entries(filters.value).reduce((count, [key, value]) => {
      const initialValue = initialState[key];
      if (Array.isArray(value)) {
        return count + (value.length > 0 ? 1 : 0);
      }
      if (value !== initialValue && value !== null && value !== void 0 && value !== "") {
        return count + 1;
      }
      return count;
    }, 0);
  });
  const filtersString = computed(() => {
    return JSON.stringify(filters.value, null, 2);
  });
  watch(filters, saveToUrl, { deep: true });
  onMounted(() => {
    loadFromUrl();
    if (typeof window !== "undefined") {
      const handlePopState = () => {
        loadFromUrl();
      };
      window.addEventListener("popstate", handlePopState);
      return () => {
        window.removeEventListener("popstate", handlePopState);
      };
    }
  });
  return {
    // Состояние
    filters: computed(() => filters.value),
    hasActiveFilters,
    activeFiltersCount,
    filtersString,
    // Методы
    updateFilter,
    updateFilters,
    resetFilters,
    resetFilter,
    loadFromUrl,
    saveToUrl
  };
};

export { useUrlParams as u };
