import{s as A,_,p as $}from"./_plugin-vue_export-helper.BX07OBiL.js";import{U as C,c,o as i,a as n,l as g,m,d as w,g as k,k as E,r as j,j as f,n as O,b as M,V as H,e as s,h as v,F as K}from"./index.BglzLLgy.js";import{b as I,r as h,t as b,n as L}from"./reactivity.esm-bundler.D5IypM4U.js";import{S as V}from"./SecondaryButton.DjJyItVx.js";import{u as W}from"./useAuth.DETRY1gV.js";import{f as J}from"./index.B_yc9D3m.js";import{M as Q}from"./Menu.DDvAgNDU.js";import{n as x}from"./router.DKcY2uv6.js";import{I as X}from"./Icon.Ci-mb2Ee.js";/* empty css                                */import{T as Y}from"./ThemeToggle.BngcXAj7.js";import{c as Z}from"./createLucideIcon.3yDVQAYz.js";import"./index.DQmIdHOH.js";import"./auth-client.D7sWEz1h.js";import"./types.FgRm47Sn.js";import"./index.CRcBj2l1.js";import"./index.CLs7nh7g.js";import"./index.Tc5ZRw49.js";import"./index.irlSZ_18.js";import"./index.B0GjtQuk.js";import"./runtime-dom.esm-bundler.C-dfRCGi.js";/* empty css                       */import"./check.BMRaCSED.js";/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ee=Z("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]]);var ae=`
    .p-toolbar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
        padding: dt('toolbar.padding');
        background: dt('toolbar.background');
        border: 1px solid dt('toolbar.border.color');
        color: dt('toolbar.color');
        border-radius: dt('toolbar.border.radius');
        gap: dt('toolbar.gap');
    }

    .p-toolbar-start,
    .p-toolbar-center,
    .p-toolbar-end {
        display: flex;
        align-items: center;
    }
`,te={root:"p-toolbar p-component",start:"p-toolbar-start",center:"p-toolbar-center",end:"p-toolbar-end"},re=C.extend({name:"toolbar",style:ae,classes:te}),ne={name:"BaseToolbar",extends:A,props:{ariaLabelledby:{type:String,default:null}},style:re,provide:function(){return{$pcToolbar:this,$parentInstance:this}}},D={name:"Toolbar",extends:ne,inheritAttrs:!1},oe=["aria-labelledby"];function se(e,t,r,a,p,o){return i(),c("div",m({class:e.cx("root"),role:"toolbar","aria-labelledby":e.ariaLabelledby},e.ptmi("root")),[n("div",m({class:e.cx("start")},e.ptm("start")),[g(e.$slots,"start")],16),n("div",m({class:e.cx("center")},e.ptm("center")),[g(e.$slots,"center")],16),n("div",m({class:e.cx("end")},e.ptm("end")),[g(e.$slots,"end")],16)],16,oe)}D.render=se;const ie=w({__name:"Toolbar",setup(e,{expose:t}){t();const a={theme:h({root:`flex items-center justify-between flex-wrap p-3 gap-2
        bg-surface-0 dark:bg-surface-900
        text-surface-700 dark:text-surface-0
        border border-surface-200 dark:border-surface-700 rounded-md`,start:"flex items-center",center:"flex items-center",end:"flex items-center"}),get Toolbar(){return D},get ptViewMerge(){return $}};return Object.defineProperty(a,"__isScriptSetup",{enumerable:!1,value:!0}),a}});function le(e,t,r,a,p,o){return i(),k(a.Toolbar,{unstyled:"",pt:a.theme,ptOptions:{mergeProps:a.ptViewMerge}},E({_:2},[j(e.$slots,(d,l)=>({name:l,fn:f(u=>[g(e.$slots,l,I(O(u??{})))])}))]),1032,["pt","ptOptions"])}const ue=_(ie,[["render",le]]);var de=`
    .p-avatar {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: dt('avatar.width');
        height: dt('avatar.height');
        font-size: dt('avatar.font.size');
        background: dt('avatar.background');
        color: dt('avatar.color');
        border-radius: dt('avatar.border.radius');
    }

    .p-avatar-image {
        background: transparent;
    }

    .p-avatar-circle {
        border-radius: 50%;
    }

    .p-avatar-circle img {
        border-radius: 50%;
    }

    .p-avatar-icon {
        font-size: dt('avatar.icon.size');
        width: dt('avatar.icon.size');
        height: dt('avatar.icon.size');
    }

    .p-avatar img {
        width: 100%;
        height: 100%;
    }

    .p-avatar-lg {
        width: dt('avatar.lg.width');
        height: dt('avatar.lg.width');
        font-size: dt('avatar.lg.font.size');
    }

    .p-avatar-lg .p-avatar-icon {
        font-size: dt('avatar.lg.icon.size');
        width: dt('avatar.lg.icon.size');
        height: dt('avatar.lg.icon.size');
    }

    .p-avatar-xl {
        width: dt('avatar.xl.width');
        height: dt('avatar.xl.width');
        font-size: dt('avatar.xl.font.size');
    }

    .p-avatar-xl .p-avatar-icon {
        font-size: dt('avatar.xl.icon.size');
        width: dt('avatar.xl.icon.size');
        height: dt('avatar.xl.icon.size');
    }

    .p-avatar-group {
        display: flex;
        align-items: center;
    }

    .p-avatar-group .p-avatar + .p-avatar {
        margin-inline-start: dt('avatar.group.offset');
    }

    .p-avatar-group .p-avatar {
        border: 2px solid dt('avatar.group.border.color');
    }

    .p-avatar-group .p-avatar-lg + .p-avatar-lg {
        margin-inline-start: dt('avatar.lg.group.offset');
    }

    .p-avatar-group .p-avatar-xl + .p-avatar-xl {
        margin-inline-start: dt('avatar.xl.group.offset');
    }
`,ce={root:function(t){var r=t.props;return["p-avatar p-component",{"p-avatar-image":r.image!=null,"p-avatar-circle":r.shape==="circle","p-avatar-lg":r.size==="large","p-avatar-xl":r.size==="xlarge"}]},label:"p-avatar-label",icon:"p-avatar-icon"},pe=C.extend({name:"avatar",style:de,classes:ce}),me={name:"BaseAvatar",extends:A,props:{label:{type:String,default:null},icon:{type:String,default:null},image:{type:String,default:null},size:{type:String,default:"normal"},shape:{type:String,default:"square"},ariaLabelledby:{type:String,default:null},ariaLabel:{type:String,default:null}},style:pe,provide:function(){return{$pcAvatar:this,$parentInstance:this}}};function y(e){"@babel/helpers - typeof";return y=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},y(e)}function P(e,t,r){return(t=fe(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function fe(e){var t=be(e,"string");return y(t)=="symbol"?t:t+""}function be(e,t){if(y(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var a=r.call(e,t);if(y(a)!="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var U={name:"Avatar",extends:me,inheritAttrs:!1,emits:["error"],methods:{onError:function(t){this.$emit("error",t)}},computed:{dataP:function(){return J(P(P({},this.shape,this.shape),this.size,this.size))}}},ge=["aria-labelledby","aria-label","data-p"],ve=["data-p"],he=["data-p"],ye=["src","alt","data-p"];function xe(e,t,r,a,p,o){return i(),c("div",m({class:e.cx("root"),"aria-labelledby":e.ariaLabelledby,"aria-label":e.ariaLabel},e.ptmi("root"),{"data-p":o.dataP}),[g(e.$slots,"default",{},function(){return[e.label?(i(),c("span",m({key:0,class:e.cx("label")},e.ptm("label"),{"data-p":o.dataP}),b(e.label),17,ve)):e.$slots.icon?(i(),k(H(e.$slots.icon),{key:1,class:L(e.cx("icon"))},null,8,["class"])):e.icon?(i(),c("span",m({key:2,class:[e.cx("icon"),e.icon]},e.ptm("icon"),{"data-p":o.dataP}),null,16,he)):e.image?(i(),c("img",m({key:3,src:e.image,alt:e.ariaLabel,onError:t[0]||(t[0]=function(){return o.onError&&o.onError.apply(o,arguments)})},e.ptm("image"),{"data-p":o.dataP}),null,16,ye)):M("",!0)]})],16,ge)}U.render=xe;const _e=w({__name:"Avatar",setup(e,{expose:t}){t();const a={theme:h({root:`inline-flex items-center justify-center
        w-8 h-8 text-base rounded-md
        bg-surface-200 dark:bg-surface-700
        has-[img]:bg-transparent
        p-circle:rounded-full
        p-large:w-12 p-large:h-12 p-large:text-2xl
        p-xlarge:w-16 p-xlarge:h-16 p-xlarge:text-[2rem]`,label:"",icon:"text-base p-large:text-2xl p-xlarge:text-[2rem]",image:"p-circle:rounded-full w-full h-full"}),get Avatar(){return U},get ptViewMerge(){return $}};return Object.defineProperty(a,"__isScriptSetup",{enumerable:!1,value:!0}),a}});function we(e,t,r,a,p,o){return i(),k(a.Avatar,{unstyled:"",pt:a.theme,ptOptions:{mergeProps:a.ptViewMerge}},E({_:2},[j(e.$slots,(d,l)=>({name:l,fn:f(u=>[g(e.$slots,l,I(O(u??{})))])}))]),1032,["pt","ptOptions"])}const Se=_(_e,[["render",we]]),ke=w({__name:"UserMenu",setup(e,{expose:t}){t();const{user:r,displayName:a,userAvatar:p,userRole:o,signOut:d,loadingState:l}=W(),u=h(),F=h(!1),N=v(()=>a.value?a.value.split(" ").map(S=>S.charAt(0)).join("").toUpperCase().slice(0,2):"U"),R=v(()=>{switch(o.value){case"ADMIN":return"Администратор";case"SHOP":return"Владелец магазина";case"USER":return"Пользователь";case"GUEST":return"Гость";default:return"Пользователь"}}),z=v(()=>l.signOut),q=v(()=>[{label:"Профиль",icon:"pi pi-user",command:()=>{x("/admin/profile")}},{label:"Настройки",icon:"pi pi-cog",command:()=>{x("/admin/settings")}},{separator:!0},{label:z.value?"Выход...":"Выйти",icon:"pi pi-sign-out",class:"text-red-600",command:T}]),G=S=>{u.value.toggle(S)},T=async()=>{x("/admin/logout")},B={user:r,displayName:a,userAvatar:p,userRole:o,signOut:d,loadingState:l,menu:u,isMenuOpen:F,userInitials:N,roleLabel:R,isSigningOut:z,menuItems:q,toggleMenu:G,handleSignOut:T,SecondaryButton:V,Avatar:Se,Menu:Q,Icon:X};return Object.defineProperty(B,"__isScriptSetup",{enumerable:!1,value:!0}),B}}),Me={class:"relative"},ze={class:"hidden md:block text-left"},Te={class:"text-sm font-medium text-surface-700"},Be={class:"text-xs text-surface-500"},Pe={class:"px-4 py-3 border-b border-surface-200"},Ae={class:"text-sm font-medium text-surface-900"},$e={class:"text-sm text-surface-500"},Ce={class:"text-xs text-surface-400 mt-1"};function Ee(e,t,r,a,p,o){return i(),c("div",Me,[s(a.SecondaryButton,{onClick:a.toggleMenu,text:"",class:"flex items-center space-x-3"},{default:f(()=>[s(a.Avatar,{image:a.userAvatar,label:a.userInitials,size:"normal",shape:"circle"},null,8,["image","label"]),n("div",ze,[n("p",Te,b(a.displayName),1),n("p",Be,b(a.roleLabel),1)]),s(a.Icon,{name:"pi pi-chevron-down",class:L(["text-surface-400 transition-transform duration-200",{"rotate-180":a.isMenuOpen}])},null,8,["class"])]),_:1}),s(a.Menu,{ref:"menu",model:a.menuItems,popup:!0,class:"w-56"},{start:f(()=>[n("div",Pe,[n("p",Ae,b(a.displayName),1),n("p",$e,b(a.user?.email),1),n("p",Ce,b(a.roleLabel),1)])]),_:1},8,["model"]),a.isMenuOpen?(i(),c("div",{key:0,class:"fixed inset-0 z-40",onClick:t[0]||(t[0]=(...d)=>e.closeMenu&&e.closeMenu(...d))})):M("",!0)])}const je=_(ke,[["render",Ee],["__scopeId","data-v-b7dcda07"]]),Oe=w({__name:"AdminToolbar",setup(e,{expose:t}){t();const r=h(!1),a=v(()=>typeof window<"u"?window.location.pathname:""),l={showMobileMenu:r,currentPath:a,navigateTo:u=>{x(u),r.value=!1},isActive:u=>u==="/admin"?a.value==="/admin":a.value.startsWith(u),toggleMobileMenu:()=>{r.value=!r.value},Toolbar:ue,SecondaryButton:V,UserMenu:je,ThemeToggle:Y,get Menu(){return ee}};return Object.defineProperty(l,"__isScriptSetup",{enumerable:!1,value:!0}),l}}),Ie={class:"flex items-center space-x-4"},Le={key:0,class:"md:hidden bg-surface-0 dark:bg-surface-100 border-b border-surface-200 dark:border-surface-700 px-4 py-2"},Ve={class:"space-y-1"},De={class:"px-2 py-1"};function Ue(e,t,r,a,p,o){return i(),c(K,null,[s(a.Toolbar,{class:"bg-surface-0 dark:bg-surface-100 shadow-sm border-b border-surface-200 dark:border-surface-700 px-4 sm:px-6 lg:px-8"},{start:f(()=>t[4]||(t[4]=[n("div",{class:"flex items-center"},[n("div",{class:"flex-shrink-0"},[n("img",{class:"h-8 w-8",src:"/favicon.svg",alt:"PartTec"})]),n("div",{class:"ml-4"},[n("h1",{class:"text-xl font-semibold text-surface-900 dark:text-surface-50"}," PartTec Admin ")])],-1)])),center:f(()=>t[5]||(t[5]=[n("nav",{class:"hidden md:flex space-x-2"},null,-1)])),end:f(()=>[n("div",Ie,[s(a.ThemeToggle,{mode:"menu",class:"hidden sm:block"}),n("button",{onClick:a.toggleMobileMenu,class:"md:hidden p-2 rounded-md text-surface-700 dark:text-surface-300 hover:bg-surface-100 dark:hover:bg-surface-200 transition-colors"},[s(a.Menu,{size:20})]),s(a.UserMenu)])]),_:1}),a.showMobileMenu?(i(),c("div",Le,[n("nav",Ve,[s(a.SecondaryButton,{label:"Главная",text:"",class:"w-full justify-start",onClick:t[0]||(t[0]=d=>a.navigateTo("/admin"))}),s(a.SecondaryButton,{label:"Каталог",text:"",class:"w-full justify-start",onClick:t[1]||(t[1]=d=>a.navigateTo("/admin/catalog"))}),s(a.SecondaryButton,{label:"Пользователи",text:"",class:"w-full justify-start",onClick:t[2]||(t[2]=d=>a.navigateTo("/admin/users"))}),s(a.SecondaryButton,{label:"Настройки",text:"",class:"w-full justify-start",onClick:t[3]||(t[3]=d=>a.navigateTo("/admin/settings"))}),t[6]||(t[6]=n("div",{class:"border-t border-surface-200 dark:border-surface-700 my-2"},null,-1)),n("div",De,[s(a.ThemeToggle,{mode:"buttons","show-label":"",class:"w-full"})])])])):M("",!0)],64)}const da=_(Oe,[["render",Ue]]);export{da as default};
