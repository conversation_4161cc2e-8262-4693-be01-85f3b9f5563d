import{t as J}from"./trpc.BpyaUO08.js";import re from"./Button.oEwD-lSq.js";import{D as Se,s as Ve}from"./index.xFbNStuK.js";import{D as Ee}from"./Dialog.Dcz8Sg9i.js";import{I as ke}from"./InputText.COaPodMV.js";import{D as Ge}from"./Dropdown.CfwcStHd.js";import{S as Ue}from"./SecondaryButton.DjJyItVx.js";import{_ as oe}from"./_plugin-vue_export-helper.BX07OBiL.js";import{d as de,g as k,o as i,j as d,a,e as n,Q as we,R as Pe,w as ie,i as Ce,c as r,F as q,r as G,b as x,f as h,h as T,ag as ze,k as je}from"./index.BglzLLgy.js";import{r as E,t as b,n as se}from"./reactivity.esm-bundler.D5IypM4U.js";import{T as ye}from"./Tag.DvN1X7lb.js";import Je from"./Card.CAr8QcdG.js";import{V as He}from"./AutoComplete.XhB-0aUS.js";import{A as qe}from"./AttributeValueInput.CSCilM_R.js";import{e as Ae,o as ce,d as z,s as U,n as R,u as Me,b as De,r as We,a as Be,c as Ke}from"./types.FgRm47Sn.js";import{I as Te}from"./Icon.Ci-mb2Ee.js";/* empty css                       */import{c as me}from"./createLucideIcon.3yDVQAYz.js";import{C as Qe}from"./check.BMRaCSED.js";import{T as Ne,P as Fe,a as Oe}from"./trash.BzwTNQJC.js";import{S as Xe}from"./Select.CKfyRLxl.js";import{M as Ye}from"./Message.BOpJRjRi.js";import{u as Re}from"./useTrpc.CAkGIEe7.js";import{u as Ze}from"./useToast.DbdIHNOo.js";import{I as $e}from"./info.NGiRJ8VE.js";import{S as et}from"./search.SsUYlPPO.js";import{T as tt,C as ut}from"./tags.C8rWdFOL.js";import{n as xe}from"./router.DKcY2uv6.js";import"./index.DQmIdHOH.js";import"./index.B_yc9D3m.js";import"./index.CRcBj2l1.js";import"./index.BHZvt6Rq.js";import"./index.Tc5ZRw49.js";import"./index.CJuyVe3p.js";import"./index.CLs7nh7g.js";import"./index.D7_1DwEX.js";import"./index.CLkTvMlq.js";import"./index.irlSZ_18.js";import"./index.B0GjtQuk.js";import"./runtime-dom.esm-bundler.C-dfRCGi.js";import"./index.D6LCJW96.js";import"./index.CXDqTVvt.js";import"./index.CBatl9QV.js";import"./index.BsVcoVHU.js";import"./InputNumber.BtccV88f.js";import"./Checkbox.B9YJ1Jrl.js";import"./Textarea.Bgj6qPNm.js";/* empty css                       */import"./index.CzaMXvxd.js";/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const at=me("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lt=me("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nt=me("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const st=me("table",[["path",{d:"M12 3v18",key:"108xh3"}],["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Le=me("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),it=de({__name:"EditEquipmentDialog",props:we({equipment:{}},{isVisible:{type:Boolean,required:!0},isVisibleModifiers:{}}),emits:we(["save","cancel"],["update:isVisible"]),setup(f,{expose:u,emit:c}){u();const e=E([]),p=Pe(f,"isVisible"),m=f,t=c,l=E({}),o=E("Создать модель техники");ie(p,A=>{A&&(l.value={...m.equipment||{}},o.value=m.equipment?.id?`Редактировать: ${m.equipment.name}`:"Создать модель техники")});function C(){p.value=!1,t("cancel")}function D(){t("save",{...l.value}),p.value=!1}async function w(){const A=await J.crud.brand.findMany.query({select:{id:!0,name:!0},orderBy:{name:"asc"}});e.value=A}Ce(()=>{w()});const B={brandOptions:e,isVisible:p,props:m,emit:t,localEquipment:l,dialogTitle:o,handleCancel:C,handleSave:D,loadBrands:w,Button:re,Dialog:Ee,InputText:ke,Dropdown:Ge,SecondaryButton:Ue};return Object.defineProperty(B,"__isScriptSetup",{enumerable:!1,value:!0}),B}}),rt={class:"flex flex-col gap-4 py-4"},ot={class:"flex flex-col"},dt={class:"flex flex-col"},ct={class:"flex justify-end gap-2"};function mt(f,u,c,e,p,m){return i(),k(e.Dialog,{visible:e.isVisible,"onUpdate:visible":u[2]||(u[2]=t=>e.isVisible=t),modal:"",header:e.dialogTitle,class:"sm:w-100 w-9/10"},{default:d(()=>[a("div",rt,[a("div",ot,[u[3]||(u[3]=a("label",{for:"name"},"Наименование модели",-1)),n(e.InputText,{id:"name",modelValue:e.localEquipment.name,"onUpdate:modelValue":u[0]||(u[0]=t=>e.localEquipment.name=t),placeholder:"Например: Экскаватор CAT 320D"},null,8,["modelValue"])]),a("div",dt,[u[4]||(u[4]=a("label",{for:"brand"},"Бренд",-1)),n(e.Dropdown,{id:"brand",modelValue:e.localEquipment.brandId,"onUpdate:modelValue":u[1]||(u[1]=t=>e.localEquipment.brandId=t),options:e.brandOptions,optionLabel:"name",optionValue:"id",placeholder:"Выберите бренд",showClear:""},null,8,["modelValue","options"])])]),a("div",ct,[n(e.SecondaryButton,{type:"button",label:"Cancel",onClick:e.handleCancel}),n(e.Button,{type:"button",label:"Save",onClick:e.handleSave})])]),_:1},8,["visible","header"])}const ft=oe(it,[["render",mt]]),pt=Ae(["STRING","NUMBER","BOOLEAN","DATE","JSON"]),vt=Ae(["MM","CM","M","KM","G","KG","T","ML","L","PIECE","PERCENT","DEGREE"]);ce({id:R(),name:U(),description:U().nullable(),createdAt:z(),updatedAt:z()});ce({id:R(),name:U(),description:U().nullable(),dataType:pt,unit:vt.nullable(),groupId:R().nullable(),createdAt:z(),updatedAt:z()});const bt=ce({id:R(),equipmentModelId:R(),templateId:R(),value:U(),createdAt:z(),updatedAt:z()}),gt=bt.omit({id:!0,createdAt:!0,updatedAt:!0});gt.partial();const yt=f=>{switch(f.dataType){case"STRING":let u=U().min(1,"Значение не может быть пустым");return f.allowedValues&&f.allowedValues.length>0?Ae(f.allowedValues):u;case"NUMBER":let c=R({required_error:"Значение обязательно",invalid_type_error:"Значение должно быть числом"});return f.minValue!==null&&f.minValue!==void 0&&(c=c.min(f.minValue,`Минимальное значение: ${f.minValue}`)),f.maxValue!==null&&f.maxValue!==void 0&&(c=c.max(f.maxValue,`Максимальное значение: ${f.maxValue}`)),c;case"BOOLEAN":return De();case"DATE":return z({required_error:"Дата обязательна",invalid_type_error:"Неверный формат даты"});case"JSON":return We(Be()).or(Ke(Be()));default:return U()}};ce({templateId:R().min(1,"Выберите шаблон атрибута"),value:Me([U(),R(),De(),z()]),equipmentModelId:U().min(1,"ID модели техники обязателен")});ce({id:R().min(1,"ID атрибута обязателен"),value:Me([U(),R(),De(),z()])});function _e(f){const{value:u,template:c}=f,{dataType:e,unit:p}=c;let m,t;try{switch(e){case"STRING":t=u,m=u;break;case"NUMBER":t=parseFloat(u),m=isNaN(t)?u:t.toLocaleString("ru-RU"),p&&(m+=` ${be(p)}`);break;case"BOOLEAN":t=u.toLowerCase()==="true",m=t?"Да":"Нет";break;case"DATE":t=new Date(u),m=isNaN(t.getTime())?u:t.toLocaleDateString("ru-RU");break;case"JSON":try{t=JSON.parse(u),m=JSON.stringify(t,null,2)}catch{t=u,m=u}break;default:t=u,m=u}}catch{t=u,m=u}return{displayValue:m,rawValue:t,unit:p,dataType:e}}function ve(f){const u={};return f.forEach(c=>{const e=c.template.group?.name||"Общие";u[e]||(u[e]=[]),u[e].push(c)}),Object.keys(u).forEach(c=>{u[c].sort((e,p)=>e.template.title.localeCompare(p.template.title,"ru"))}),u}function ht(f,u,c){const e=new Set(u.map(m=>m.templateId));let p=f.filter(m=>!e.has(m.id));if(c){if(c.excludeTemplateIds){const m=new Set(c.excludeTemplateIds);p=p.filter(t=>!m.has(t.id))}if(c.groupId&&(p=p.filter(m=>m.groupId===c.groupId)),c.dataType&&(p=p.filter(m=>m.dataType===c.dataType)),c.searchQuery){const m=c.searchQuery.toLowerCase();p=p.filter(t=>t.title.toLowerCase().includes(m)||t.name.toLowerCase().includes(m)||t.description&&t.description.toLowerCase().includes(m))}}return p.sort((m,t)=>m.title.localeCompare(t.title,"ru"))}function be(f){return f?{MM:"мм",INCH:"дюйм",FT:"фт",G:"г",KG:"кг",T:"т",LB:"фунт",ML:"мл",L:"л",GAL:"гал",SEC:"сек",MIN:"мин",H:"ч",PCS:"шт",SET:"комплект",PAIR:"пара",BAR:"бар",PSI:"psi",KW:"кВт",HP:"л.с.",NM:"Н⋅м",RPM:"об/мин",C:"°C",F:"°F",PERCENT:"%"}[f]||f:""}function ge(f){return{STRING:"Текст",NUMBER:"Число",BOOLEAN:"Да/Нет",DATE:"Дата",JSON:"JSON"}[f]||f}function Ie(f,u){switch(u){case"NUMBER":const c=parseFloat(f);return isNaN(c)?0:c;case"BOOLEAN":return f.toLowerCase()==="true"||f==="1"||f==="да";case"DATE":const e=new Date(f);return isNaN(e.getTime())?new Date:e;case"JSON":try{return JSON.parse(f)}catch{return f}default:return f}}const xt=de({__name:"EquipmentAttributesList",props:{attributes:{},readonly:{type:Boolean,default:!1},showGroupColumn:{type:Boolean,default:!0},showDataTypeColumn:{type:Boolean,default:!1},compact:{type:Boolean,default:!1},groupByTemplate:{type:Boolean,default:!1}},emits:["edit-attribute","delete-attribute","update-value"],setup(f,{expose:u,emit:c}){u();const e=f,p=c,m=E(null),t=E(null),l=T(()=>e.attributes.map(g=>{const V=_e(g);return{id:g.id,name:g.template.title,value:V.displayValue,rawValue:V.rawValue,unit:V.unit,dataType:g.template.dataType,dataTypeDisplay:ge(g.template.dataType),group:g.template.group?.name||"Общие",description:g.template.description,isRequired:g.template.isRequired,template:g.template,attribute:g}})),o=T(()=>e.groupByTemplate?ve(e.attributes):{});function C(g){p("edit-attribute",g)}function D(g){p("delete-attribute",g)}function w(g){switch(g){case"NUMBER":return"info";case"BOOLEAN":return"success";case"DATE":return"warning";case"JSON":return"secondary";default:return}}function B(g){if(g.dataType==="BOOLEAN")return g.rawValue?"✓":"✗";if(g.dataType==="NUMBER"&&g.unit){const V=be(g.unit);return`${g.rawValue.toLocaleString("ru-RU")} ${V}`}return g.value}function A(g){m.value=g.id,t.value=g.rawValue}function F(){m.value=null,t.value=null}function M(g){t.value!==null&&t.value!==void 0&&p("update-value",g,t.value),F()}function H(g){return{STRING:"pi pi-font",NUMBER:"pi pi-hashtag",BOOLEAN:"pi pi-check-square",DATE:"pi pi-calendar",JSON:"pi pi-code"}[g]||"pi pi-question"}const P={props:e,emit:p,editingAttributeId:m,editingValue:t,tableData:l,groupedAttributes:o,handleEditAttribute:C,handleDeleteAttribute:D,getValueSeverity:w,formatValueForDisplay:B,startEditing:A,cancelEditing:F,saveEditing:M,getDataTypeIcon:H,get PencilIcon(){return Fe},get TrashIcon(){return Ne},get CheckIcon(){return Qe},get XIcon(){return Le},Button:re,Tag:ye,DataTable:Se,get Column(){return Ve},AttributeValueInput:qe,get formatAttributeValue(){return _e},get getUnitDisplayName(){return be},Icon:Te};return Object.defineProperty(P,"__isScriptSetup",{enumerable:!1,value:!0}),P}}),_t={class:"equipment-attributes-list"},Et={key:0,class:"space-y-6"},kt={class:"flex items-center gap-2 mb-3 pb-2 border-b border-surface-200 dark:border-surface-700"},Ct={class:"font-medium text-surface-900 dark:text-surface-0"},At={class:"space-y-3"},Dt={class:"flex-shrink-0 relative"},Tt=["title"],wt={class:"flex-shrink-0 w-48"},Bt={class:"font-medium text-surface-900 dark:text-surface-0 text-sm"},It={key:0,class:"text-xs text-surface-500 dark:text-surface-400 mt-1"},St={class:"flex-1"},Vt={key:0,class:"flex items-center gap-2"},qt={class:"flex gap-1"},Mt={key:1,class:"flex items-center gap-2 group"},Nt={class:"text-surface-900 dark:text-surface-0"},Ft={class:"flex-shrink-0 w-16 text-center"},Ot={key:0,class:"text-sm text-surface-500 font-medium"},Rt={key:0,class:"flex-shrink-0 flex gap-2"},Lt={class:"flex items-start gap-2"},Gt={class:"flex-1 min-w-0"},Ut={class:"flex items-center gap-2 mb-1"},Pt={class:"font-medium text-surface-900 dark:text-surface-0 truncate"},zt=["title"],jt={key:0,class:"flex items-center gap-2"},Jt={class:"flex gap-1"},Ht={key:1,class:"flex items-center gap-2 group"},Wt=["title"],Kt={class:"flex items-center gap-1"};function Qt(f,u,c,e,p,m){return i(),r("div",_t,[c.groupByTemplate&&Object.keys(e.groupedAttributes).length>0?(i(),r("div",Et,[(i(!0),r(q,null,G(e.groupedAttributes,(t,l)=>(i(),r("div",{key:l,class:"attribute-group"},[a("div",kt,[u[2]||(u[2]=a("i",{class:"pi pi-folder text-blue-600"},null,-1)),a("h4",Ct,b(l==="undefined"?"Без группы":l),1),n(e.Tag,{value:`${t.length} атр.`,severity:"secondary",size:"small"},null,8,["value"])]),a("div",At,[(i(!0),r(q,null,G(t,o=>(i(),r("div",{key:o.id,class:"attribute-item"},[a("div",{class:se(["flex items-center gap-3 p-4 border rounded-lg transition-all duration-200 hover:shadow-sm",[o.value&&String(o.value).trim()?"border-green-200 dark:border-green-700 bg-green-50 dark:bg-green-900/10":"border-orange-200 dark:border-orange-700 bg-orange-50 dark:bg-orange-900/10"]])},[a("div",Dt,[n(e.Icon,{name:e.getDataTypeIcon(o.template.dataType),class:"text-lg text-primary w-5 h-5"},null,8,["name"]),a("div",{class:se(["absolute -top-1 -right-1 w-3 h-3 rounded-full border-2 border-white dark:border-surface-900",o.value&&String(o.value).trim()?"bg-green-500":"bg-orange-500"]),title:o.value&&String(o.value).trim()?"Заполнено":"Не заполнено"},null,10,Tt)]),a("div",wt,[a("div",Bt,[h(b(o.template.title)+" ",1),o.template.isRequired?(i(),k(e.Tag,{key:0,severity:"danger",class:"text-xs ml-1"},{default:d(()=>u[3]||(u[3]=[h("*")])),_:1,__:[3]})):x("",!0)]),o.template.description?(i(),r("div",It,b(o.template.description),1)):x("",!0)]),a("div",St,[e.editingAttributeId===o.id?(i(),r("div",Vt,[n(e.AttributeValueInput,{modelValue:e.editingValue,"onUpdate:modelValue":u[0]||(u[0]=C=>e.editingValue=C),template:o.template,class:"flex-1",size:"small"},null,8,["modelValue","template"]),a("div",qt,[n(e.Button,{onClick:C=>e.saveEditing(o.id),size:"small",class:"p-1"},{default:d(()=>[n(e.CheckIcon,{class:"w-3 h-3"})]),_:2},1032,["onClick"]),n(e.Button,{onClick:e.cancelEditing,severity:"secondary",size:"small",class:"p-1"},{default:d(()=>[n(e.XIcon,{class:"w-3 h-3"})]),_:1})])])):(i(),r("div",Mt,[a("span",Nt,b(e.formatAttributeValue(o).displayValue||"Не указано"),1),c.readonly?x("",!0):(i(),k(e.Button,{key:0,onClick:C=>e.startEditing(o),text:"",size:"small",class:"p-1 opacity-0 group-hover:opacity-100 transition-opacity"},{default:d(()=>[n(e.PencilIcon,{class:"w-3 h-3"})]),_:2},1032,["onClick"]))]))]),a("div",Ft,[o.template.unit?(i(),r("span",Ot,b(e.getUnitDisplayName(o.template.unit)),1)):x("",!0)]),c.readonly?x("",!0):(i(),r("div",Rt,[n(e.Button,{onClick:C=>e.handleEditAttribute(o.id),severity:"secondary",size:"small",outlined:"",class:"p-1"},{default:d(()=>[n(e.PencilIcon,{class:"w-3 h-3"})]),_:2},1032,["onClick"]),n(e.Button,{onClick:C=>e.handleDeleteAttribute(o.id),severity:"danger",size:"small",outlined:"",class:"p-1",disabled:o.template.isRequired},{default:d(()=>[n(e.TrashIcon,{class:"w-3 h-3"})]),_:2},1032,["onClick","disabled"])]))],2)]))),128))])]))),128))])):(i(),k(e.DataTable,{key:1,value:e.tableData,paginator:!c.compact&&e.tableData.length>10,rows:c.compact?5:10,rowsPerPageOptions:[5,10,25,50],sortMode:"multiple",removableSort:"",loading:!1,dataKey:"id",size:c.compact?"small":"normal",class:se(["p-datatable-sm",{"compact-table":c.compact}])},{empty:d(()=>u[6]||(u[6]=[a("div",{class:"text-center py-6 text-surface-500 dark:text-surface-400"},[a("i",{class:"pi pi-info-circle text-2xl mb-2 block"}),a("p",{class:"text-sm"},"Нет атрибутов для отображения")],-1)])),default:d(()=>[n(e.Column,{field:"name",header:"Атрибут",sortable:"",style:{minWidth:"200px"}},{body:d(({data:t})=>[a("div",Lt,[a("div",Gt,[a("div",Ut,[a("span",Pt,b(t.name),1),t.isRequired?(i(),k(e.Tag,{key:0,severity:"danger",class:"text-xs flex-shrink-0"},{default:d(()=>u[4]||(u[4]=[h(" Обязательный ")])),_:1,__:[4]})):x("",!0)]),t.description&&!c.compact?(i(),r("div",{key:0,class:"text-xs text-surface-500 dark:text-surface-400 line-clamp-2",title:t.description},b(t.description),9,zt)):x("",!0)])])]),_:1}),n(e.Column,{field:"value",header:"Значение",sortable:"",style:{minWidth:"150px"}},{body:d(({data:t})=>[e.editingAttributeId===t.id?(i(),r("div",jt,[n(e.AttributeValueInput,{modelValue:e.editingValue,"onUpdate:modelValue":u[1]||(u[1]=l=>e.editingValue=l),template:t.template,class:"w-full",size:"small"},null,8,["modelValue","template"]),a("div",Jt,[n(e.Button,{onClick:l=>e.saveEditing(t.id),size:"small",class:"p-1"},{default:d(()=>[n(e.CheckIcon,{class:"w-3 h-3"})]),_:2},1032,["onClick"]),n(e.Button,{onClick:e.cancelEditing,severity:"secondary",size:"small",class:"p-1"},{default:d(()=>[n(e.XIcon,{class:"w-3 h-3"})]),_:1})])])):(i(),r("div",Ht,[n(e.Tag,{severity:e.getValueSeverity(t.dataType),class:se(["font-mono text-sm",{"text-green-700 bg-green-50 dark:text-green-300 dark:bg-green-900/20":t.dataType==="BOOLEAN"&&t.rawValue,"text-red-700 bg-red-50 dark:text-red-300 dark:bg-red-900/20":t.dataType==="BOOLEAN"&&!t.rawValue}])},{default:d(()=>[h(b(e.formatValueForDisplay(t)),1)]),_:2},1032,["severity","class"]),c.readonly?x("",!0):(i(),k(e.Button,{key:0,onClick:l=>e.startEditing(t),text:"",size:"small",class:"p-1 opacity-0 group-hover:opacity-100 transition-opacity"},{default:d(()=>[n(e.PencilIcon,{class:"w-3 h-3"})]),_:2},1032,["onClick"])),c.compact&&!c.showDataTypeColumn?(i(),r("span",{key:1,class:"text-xs text-surface-400 dark:text-surface-500",title:`Тип: ${t.dataTypeDisplay}`},b(t.dataType),9,Wt)):x("",!0)]))]),_:1}),c.showGroupColumn?(i(),k(e.Column,{key:0,field:"group",header:"Группа",sortable:"",style:{minWidth:"120px"}},{body:d(({data:t})=>[n(e.Tag,{severity:"secondary",class:"text-xs"},{default:d(()=>[u[5]||(u[5]=a("i",{class:"pi pi-folder text-xs mr-1"},null,-1)),h(" "+b(t.group),1)]),_:2,__:[5]},1024)]),_:1})):x("",!0),c.showDataTypeColumn?(i(),k(e.Column,{key:1,field:"dataTypeDisplay",header:"Тип",sortable:"",style:{minWidth:"100px"}},{body:d(({data:t})=>[n(e.Tag,{severity:e.getValueSeverity(t.dataType),class:"text-xs"},{default:d(()=>[h(b(t.dataTypeDisplay),1)]),_:2},1032,["severity"])]),_:1})):x("",!0),c.readonly?x("",!0):(i(),k(e.Column,{key:2,field:"actions",header:"Действия",sortable:!1,style:{minWidth:"120px",width:"120px"}},{body:d(({data:t})=>[a("div",Kt,[n(e.Button,{onClick:l=>e.handleEditAttribute(t.id),text:"",size:"small",class:"p-2",title:`Редактировать ${t.name}`},{default:d(()=>[n(e.PencilIcon,{class:"w-4 h-4"})]),_:2},1032,["onClick","title"]),n(e.Button,{onClick:l=>e.handleDeleteAttribute(t.id),text:"",severity:"danger",size:"small",class:"p-2",title:`Удалить ${t.name}`,disabled:t.isRequired},{default:d(()=>[n(e.TrashIcon,{class:"w-4 h-4"})]),_:2},1032,["onClick","title","disabled"])])]),_:1}))]),_:1},8,["value","paginator","rows","size","class"]))])}const Xt=oe(xt,[["render",Qt],["__scopeId","data-v-b449289d"]]),Yt=de({__name:"AddEquipmentAttributeDialog",props:{visible:{type:Boolean},equipmentId:{},existingAttributes:{default:()=>[]}},emits:["update:visible","save","cancel"],setup(f,{expose:u,emit:c}){u();const e=f,p=c,{$trpc:m}=Re(),t=Ze(),l=E({templateId:0,value:"",template:void 0}),o=E(!1),C=E(!1),D=E(""),w=E(null),B=E(null),A=E([]),F=E([]),M=E([]),H=T({get:()=>e.visible,set:v=>p("update:visible",v)}),P=T(()=>{const v={searchQuery:D.value||void 0,groupId:w.value||void 0,dataType:B.value||void 0,excludeTemplateIds:e.existingAttributes.map(s=>s.templateId)};return ht(F.value,e.existingAttributes,v)}),g=T(()=>F.value.find(v=>v.id===l.value.templateId)||null),V=T(()=>l.value.templateId>0&&l.value.value!==null&&l.value.value!==""&&A.value.length===0),L=T(()=>["STRING","NUMBER","BOOLEAN","DATE","JSON"].map(s=>({label:ge(s),value:s}))),ee=T(()=>[{label:"Все группы",value:null},...M.value.map(v=>({label:v.name,value:v.id}))]);async function W(){o.value=!0;try{const v=await m.crud.attributeTemplate.findMany.query({include:{group:!0},orderBy:[{group:{name:"asc"}},{title:"asc"}]});F.value=v}catch(v){t.error("Не удалось загрузить шаблоны атрибутов"),console.error("Failed to load templates:",v)}finally{o.value=!1}}async function K(){try{const v=await m.crud.attributeGroup.findMany.query({orderBy:{name:"asc"}});M.value=v}catch(v){console.error("Failed to load attribute groups:",v)}}function te(v){l.value.templateId=v.id,l.value.template=v,X(),D.value=""}function X(){const v=g.value;if(!v){l.value.value="";return}switch(v.dataType){case"STRING":l.value.value="";break;case"NUMBER":l.value.value=v.minValue||0;break;case"BOOLEAN":l.value.value=!1;break;case"DATE":l.value.value=new Date;break;case"JSON":l.value.value="";break;default:l.value.value=""}j()}function j(){A.value=[];const v=g.value;if(!(!v||l.value.value===null||l.value.value===""))try{const s=yt(v),y=Ie(String(l.value.value),v.dataType);s.parse(y)}catch(s){s.errors?A.value=s.errors.map(y=>y.message):A.value=["Неверное значение атрибута"]}}async function Y(){if(V.value){C.value=!0;try{const v=g.value,s=Ie(String(l.value.value),v.dataType),y={templateId:l.value.templateId,value:s,template:v};p("save",y),Z()}catch(v){t.error("Ошибка при сохранении атрибута"),console.error("Save error:",v)}finally{C.value=!1}}}function Z(){l.value={templateId:0,value:"",template:void 0},D.value="",w.value=null,B.value=null,A.value=[],p("cancel")}function ue(){D.value="",w.value=null,B.value=null}function ae(v){const s=[];return v.group&&s.push(v.group.name),s.push(ge(v.dataType)),v.unit&&s.push(be(v.unit)),v.isRequired&&s.push("Обязательный"),s.join(" • ")}ie(()=>e.visible,v=>{v&&(W(),K())}),ie(()=>l.value.value,()=>{j()},{deep:!0}),ie(()=>l.value.templateId,()=>{j()}),Ce(()=>{e.visible&&(W(),K())});const $={props:e,emit:p,$trpc:m,toast:t,formData:l,loading:o,saving:C,searchQuery:D,selectedGroupId:w,selectedDataType:B,validationErrors:A,allTemplates:F,attributeGroups:M,isVisible:H,availableTemplates:P,selectedTemplate:g,isFormValid:V,dataTypeOptions:L,groupOptions:ee,loadTemplates:W,loadAttributeGroups:K,selectTemplate:te,resetValue:X,validateValue:j,handleSave:Y,handleCancel:Z,clearFilters:ue,getTemplateDisplayInfo:ae,get SearchIcon(){return et},get XIcon(){return Le},get InfoIcon(){return $e},Dialog:Ee,Button:re,InputText:ke,Select:Xe,Tag:ye,Message:Ye,AttributeValueInput:qe};return Object.defineProperty($,"__isScriptSetup",{enumerable:!1,value:!0}),$}}),Zt={class:"space-y-6"},$t={class:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4 p-4 bg-surface-50 dark:bg-surface-800 rounded-lg"},eu={class:"relative"},tu={class:"flex justify-between items-center mb-4"},uu={class:"text-sm text-surface-600 dark:text-surface-400"},au={class:"max-h-64 overflow-y-auto border border-surface-200 dark:border-surface-700 rounded-lg"},lu={key:0,class:"p-4 text-center text-surface-500"},nu={key:1,class:"p-4 text-center text-surface-500"},su={key:2,class:"divide-y divide-surface-200 dark:divide-surface-700"},iu=["onClick"],ru={class:"flex items-start justify-between"},ou={class:"flex-1"},du={class:"flex items-center gap-2 mb-1"},cu={class:"font-medium text-surface-900 dark:text-surface-0"},mu={class:"text-sm text-surface-600 dark:text-surface-400 mb-2"},fu={key:0,class:"text-xs text-surface-500 dark:text-surface-400 line-clamp-2"},pu={key:0,class:"ml-4"},vu={key:0},bu={class:"text-sm font-medium text-surface-900 dark:text-surface-0 mb-3"},gu={class:"p-3 bg-surface-50 dark:bg-surface-800 rounded-lg mb-4"},yu={class:"flex items-center gap-2 text-sm text-surface-600 dark:text-surface-400"},hu={key:0,class:"text-xs text-surface-500 dark:text-surface-400 mt-1"},xu={class:"space-y-4"},_u={key:0,class:"mt-4"},Eu={class:"flex justify-end gap-2"};function ku(f,u,c,e,p,m){const t=ze("Icon");return i(),k(e.Dialog,{visible:e.isVisible,"onUpdate:visible":u[4]||(u[4]=l=>e.isVisible=l),modal:"",closable:!0,draggable:!1,class:"w-full max-w-4xl",header:"Добавить атрибут"},{footer:d(()=>[a("div",Eu,[n(e.Button,{onClick:e.handleCancel,outlined:"",disabled:e.saving},{default:d(()=>u[13]||(u[13]=[h(" Отмена ")])),_:1,__:[13]},8,["disabled"]),n(e.Button,{onClick:e.handleSave,disabled:!e.isFormValid||e.saving,loading:e.saving},{default:d(()=>[h(b(e.saving?"Сохранение...":"Добавить атрибут"),1)]),_:1},8,["disabled","loading"])])]),default:d(()=>[a("div",Zt,[a("div",null,[u[11]||(u[11]=a("h6",{class:"text-sm font-medium text-surface-900 dark:text-surface-0 mb-3"}," Выберите шаблон атрибута ",-1)),a("div",$t,[a("div",eu,[n(e.InputText,{modelValue:e.searchQuery,"onUpdate:modelValue":u[0]||(u[0]=l=>e.searchQuery=l),placeholder:"Поиск по названию...",class:"w-full pl-10"},null,8,["modelValue"]),n(e.SearchIcon,{class:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-surface-400"})]),n(e.Select,{modelValue:e.selectedGroupId,"onUpdate:modelValue":u[1]||(u[1]=l=>e.selectedGroupId=l),options:e.groupOptions,"option-label":"label","option-value":"value",placeholder:"Выберите группу",class:"w-full","show-clear":!0},null,8,["modelValue","options"]),n(e.Select,{modelValue:e.selectedDataType,"onUpdate:modelValue":u[2]||(u[2]=l=>e.selectedDataType=l),options:e.dataTypeOptions,"option-label":"label","option-value":"value",placeholder:"Тип данных",class:"w-full","show-clear":!0},null,8,["modelValue","options"])]),a("div",tu,[a("span",uu," Найдено шаблонов: "+b(e.availableTemplates.length),1),n(e.Button,{onClick:e.clearFilters,text:"",size:"small",class:"text-sm"},{default:d(()=>[n(e.XIcon,{class:"w-4 h-4 mr-1"}),u[5]||(u[5]=h(" Очистить фильтры "))]),_:1,__:[5]})]),a("div",au,[e.loading?(i(),r("div",lu,[n(t,{name:"pi pi-spinner pi-spin",class:"mr-2 inline-block"}),u[6]||(u[6]=h(" Загрузка шаблонов... "))])):e.availableTemplates.length===0?(i(),r("div",nu,[n(e.InfoIcon,{class:"w-5 h-5 mx-auto mb-2"}),u[7]||(u[7]=a("p",{class:"text-sm"},"Нет доступных шаблонов",-1)),u[8]||(u[8]=a("p",{class:"text-xs mt-1"},"Попробуйте изменить фильтры или все шаблоны уже используются",-1))])):(i(),r("div",su,[(i(!0),r(q,null,G(e.availableTemplates,l=>(i(),r("div",{key:l.id,class:se(["p-4 hover:bg-surface-50 dark:hover:bg-surface-800 cursor-pointer transition-colors",{"bg-primary-50 dark:bg-primary-900/20 border-l-4 border-primary":e.formData.templateId===l.id}]),onClick:o=>e.selectTemplate(l)},[a("div",ru,[a("div",ou,[a("div",du,[a("h6",cu,b(l.title),1),l.isRequired?(i(),k(e.Tag,{key:0,severity:"danger",class:"text-xs"},{default:d(()=>u[9]||(u[9]=[h(" Обязательный ")])),_:1,__:[9]})):x("",!0)]),a("p",mu,b(e.getTemplateDisplayInfo(l)),1),l.description?(i(),r("p",fu,b(l.description),1)):x("",!0)]),e.formData.templateId===l.id?(i(),r("div",pu,u[10]||(u[10]=[a("i",{class:"pi pi-check text-primary text-lg"},null,-1)]))):x("",!0)])],10,iu))),128))]))])]),e.selectedTemplate?(i(),r("div",vu,[a("h6",bu,' Введите значение для "'+b(e.selectedTemplate.title)+'" ',1),a("div",gu,[a("div",yu,[n(e.InfoIcon,{class:"w-4 h-4"}),a("span",null,b(e.getTemplateDisplayInfo(e.selectedTemplate)),1)]),e.selectedTemplate.description?(i(),r("p",hu,b(e.selectedTemplate.description),1)):x("",!0)]),a("div",xu,[u[12]||(u[12]=a("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Значение ",-1)),n(e.AttributeValueInput,{modelValue:e.formData.value,"onUpdate:modelValue":u[3]||(u[3]=l=>e.formData.value=l),template:e.selectedTemplate,placeholder:"Введите значение атрибута",class:"w-full"},null,8,["modelValue","template"])]),e.validationErrors.length>0?(i(),r("div",_u,[(i(!0),r(q,null,G(e.validationErrors,l=>(i(),k(e.Message,{key:l,severity:"error",closable:!1,class:"mb-2"},{default:d(()=>[h(b(l),1)]),_:2},1024))),128))])):x("",!0)])):x("",!0)])]),_:1},8,["visible"])}const Cu=oe(Yt,[["render",ku],["__scopeId","data-v-cb27c7ef"]]),ne=6,Au=de({__name:"EquipmentAttributesSection",props:{equipmentId:{},attributes:{},readonly:{type:Boolean,default:!1}},emits:["add-attribute","edit-attribute","delete-attribute"],setup(f,{expose:u,emit:c}){u();const e=f,p=c,{client:m}=Re(),t=E(!1),l=E("grid"),o=E(!1),C=E(!1),D=E(null),w=E(null),B=E([]),A=E([]),F=E([]),M=E(!1),H=T(()=>e.attributes?ve(e.attributes):{}),P=T(()=>e.attributes&&e.attributes.length>0),g=T(()=>e.attributes?.length||0),V=T(()=>g.value>ne),L=T(()=>e.attributes?t.value||g.value<=ne?e.attributes:e.attributes.slice(0,ne):[]),ee=T(()=>L.value.length?ve(L.value):{}),W=T(()=>Math.max(0,g.value-ne)),K=T(()=>e.attributes?.filter(_=>_.value&&String(_.value).trim()).length||0);function te(){o.value=!0}function X(_){p("add-attribute",_),o.value=!1}function j(){o.value=!1}function Y(_){p("edit-attribute",_)}function Z(_){p("delete-attribute",_)}function ue(){t.value=!t.value}function ae(){l.value=l.value==="grid"?"table":"grid"}function $(_){return _e(_)}const v=T(()=>!e.readonly);async function s(_){const I=_.query.toLowerCase();try{const S=await m.crud.attributeGroup.findMany.query({where:{name:{contains:I,mode:"insensitive"}},take:10});B.value=S||[]}catch(S){console.error("Error filtering groups:",S),B.value=[]}}async function y(_){const I=_.query.toLowerCase();try{const S=await m.crud.attributeTemplate.findMany.query({where:{OR:[{title:{contains:I,mode:"insensitive"}},{name:{contains:I,mode:"insensitive"}}]},include:{group:!0},take:10});A.value=S||[]}catch(S){console.error("Error filtering templates:",S),A.value=[]}}async function N(){if(D.value){M.value=!0;try{const _=D.value.id||D.value,I=await m.crud.attributeTemplate.findMany.query({where:{groupId:_},include:{group:!0}});if(I)for(const S of I){const he={templateId:S.id,value:Q(S.dataType),template:S};p("add-attribute",he)}D.value=null}catch(_){console.error("Error loading group templates:",_)}finally{M.value=!1}}}function O(){if(!w.value)return;const _=w.value,I={templateId:_.id,value:Q(_.dataType),template:_};p("add-attribute",I),w.value=null}async function fe(_){M.value=!0;try{const I=await m.crud.attributeTemplate.findMany.query({where:{groupId:_},include:{group:!0}});if(I)for(const S of I){const he={templateId:S.id,value:Q(S.dataType),template:S};p("add-attribute",he)}C.value=!1}catch(I){console.error("Error loading templates by group:",I)}finally{M.value=!1}}function Q(_){switch(_){case"STRING":return"";case"NUMBER":return 0;case"BOOLEAN":return!1;case"DATE":return new Date;case"JSON":return"";default:return""}}function pe(_){return{STRING:"pi pi-font",NUMBER:"pi pi-hashtag",BOOLEAN:"pi pi-check-square",DATE:"pi pi-calendar",JSON:"pi pi-code"}[_]||"pi pi-question"}Ce(async()=>{try{const _=await m.crud.attributeGroup.findMany.query({include:{_count:{select:{templates:!0}}},orderBy:{name:"asc"}});F.value=_||[]}catch(_){console.error("Error loading template groups:",_)}});const le={props:e,emit:p,client:m,showAllAttributes:t,COMPACT_LIMIT:ne,viewMode:l,showAddDialog:o,showGroupDialog:C,selectedTemplateGroup:D,selectedTemplate:w,groupSuggestions:B,templateSuggestions:A,templateGroups:F,loadingTemplates:M,groupedAttributes:H,hasAttributes:P,totalAttributesCount:g,shouldShowExpandButton:V,visibleAttributes:L,visibleGroupedAttributes:ee,hiddenAttributesCount:W,filledAttributesCount:K,handleAddAttribute:te,handleSaveAttribute:X,handleCancelAddAttribute:j,handleEditAttribute:Y,handleDeleteAttribute:Z,toggleShowAll:ue,toggleViewMode:ae,formatAttribute:$,canManageAttributes:v,filterGroups:s,filterTemplates:y,loadSelectedGroupTemplates:N,addSingleTemplate:O,loadTemplatesByGroupId:fe,getDefaultValueForType:Q,getDataTypeIcon:pe,get PlusIcon(){return Oe},get ChevronDownIcon(){return ut},get ChevronUpIcon(){return at},get TableIcon(){return st},get GridIcon(){return nt},get TagsIcon(){return tt},Button:re,Tag:ye,Card:Je,AutoComplete:He,Dialog:Ee,EquipmentAttributesList:Xt,AddEquipmentAttributeDialog:Cu,get groupAttributes(){return ve},get getDataTypeDisplayName(){return ge},Icon:Te};return Object.defineProperty(le,"__isScriptSetup",{enumerable:!1,value:!0}),le}}),Du={class:"equipment-attributes-section"},Tu={class:"flex items-center justify-between mb-4"},wu={class:"flex items-center gap-3"},Bu={class:"font-semibold flex items-center gap-2 text-surface-900 dark:text-surface-0"},Iu={class:"flex items-center gap-2"},Su={class:"p-4"},Vu={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},qu={class:"flex items-center gap-2"},Mu={class:"flex-1"},Nu={class:"font-medium"},Fu={class:"text-sm text-surface-600"},Ou={class:"flex items-end gap-2"},Ru={key:1},Lu={key:0},Gu={key:0,class:"text-center pt-4"},Uu={key:1},Pu={class:"mb-3 pb-2 border-b border-surface-200 dark:border-surface-700"},zu={class:"text-sm font-medium text-surface-700 dark:text-surface-300 flex items-center gap-2"},ju={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3"},Ju={class:"flex items-start justify-between"},Hu={class:"flex-1 min-w-0"},Wu={class:"flex items-center gap-2 mb-1"},Ku={class:"text-sm font-medium text-surface-900 dark:text-surface-0 truncate"},Qu={class:"text-sm text-surface-600 dark:text-surface-400 mb-2 break-words"},Xu=["title"],Yu={key:0,class:"ml-2 flex flex-col gap-1 flex-shrink-0"},Zu={key:0,class:"text-center pt-2"},$u={key:2,class:"text-center py-6 text-surface-500 dark:text-surface-400"},ea={class:"space-y-4"},ta={class:"flex items-center justify-between"},ua={class:"font-medium text-surface-900 dark:text-surface-0"},aa={class:"text-sm text-surface-600 dark:text-surface-400"},la={class:"text-surface-500"};function na(f,u,c,e,p,m){return i(),r("div",Du,[a("div",Tu,[a("div",wu,[a("h5",Bu,[n(e.Icon,{name:"pi pi-list",class:"text-green-600 w-4 h-4"}),u[5]||(u[5]=h(" Атрибуты модели "))]),e.totalAttributesCount>0?(i(),k(e.Tag,{key:0,value:`${e.filledAttributesCount}/${e.totalAttributesCount} заполнено`,severity:e.filledAttributesCount===e.totalAttributesCount?"success":"warn",size:"small"},null,8,["value","severity"])):x("",!0)]),a("div",Iu,[e.hasAttributes?(i(),k(e.Button,{key:0,onClick:e.toggleViewMode,text:"",size:"small",title:e.viewMode==="grid"?"Переключить на табличный вид":"Переключить на сеточный вид"},{default:d(()=>[e.viewMode==="grid"?(i(),k(e.TableIcon,{key:0,class:"w-4 h-4"})):(i(),k(e.GridIcon,{key:1,class:"w-4 h-4"}))]),_:1},8,["title"])):x("",!0),e.canManageAttributes?(i(),k(e.Button,{key:1,onClick:u[0]||(u[0]=t=>e.showGroupDialog=!0),outlined:"",severity:"secondary",size:"small",class:"text-sm"},{default:d(()=>[n(e.TagsIcon,{class:"w-4 h-4 mr-1"}),u[6]||(u[6]=h(" Добавить группу "))]),_:1,__:[6]})):x("",!0),e.canManageAttributes?(i(),k(e.Button,{key:2,onClick:e.handleAddAttribute,size:"small",class:"text-sm"},{default:d(()=>[n(e.PlusIcon,{class:"w-4 h-4 mr-1"}),u[7]||(u[7]=h(" Добавить атрибут "))]),_:1,__:[7]})):x("",!0)])]),e.canManageAttributes?(i(),k(e.Card,{key:0,class:"mb-4"},{content:d(()=>[a("div",Su,[a("div",Vu,[a("div",null,[u[8]||(u[8]=a("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Группа шаблонов ",-1)),n(e.AutoComplete,{modelValue:e.selectedTemplateGroup,"onUpdate:modelValue":u[1]||(u[1]=t=>e.selectedTemplateGroup=t),suggestions:e.groupSuggestions,onComplete:e.filterGroups,"option-label":"name",placeholder:"Поиск группы...",class:"w-full",dropdown:""},null,8,["modelValue","suggestions"])]),a("div",null,[u[9]||(u[9]=a("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Или отдельный шаблон ",-1)),n(e.AutoComplete,{modelValue:e.selectedTemplate,"onUpdate:modelValue":u[2]||(u[2]=t=>e.selectedTemplate=t),suggestions:e.templateSuggestions,onComplete:e.filterTemplates,"option-label":"title",placeholder:"Поиск шаблона...",class:"w-full",dropdown:""},{option:d(({option:t})=>[a("div",qu,[n(e.Icon,{name:e.getDataTypeIcon(t.dataType),class:"text-primary w-4 h-4"},null,8,["name"]),a("div",Mu,[a("div",Nu,b(t.title),1),a("div",Fu,b(t.group?.name)+" • "+b(e.getDataTypeDisplayName(t.dataType)),1)])])]),_:1},8,["modelValue","suggestions"])]),a("div",Ou,[n(e.Button,{onClick:e.loadSelectedGroupTemplates,size:"small",outlined:"",disabled:!e.selectedTemplateGroup||e.loadingTemplates,loading:e.loadingTemplates,label:"Добавить группу",class:"flex-1"},null,8,["disabled","loading"]),n(e.Button,{onClick:e.addSingleTemplate,size:"small",outlined:"",disabled:!e.selectedTemplate,label:"Добавить",class:"flex-1"},null,8,["disabled"])])])])]),_:1})):x("",!0),e.hasAttributes?(i(),r("div",Ru,[e.viewMode==="table"?(i(),r("div",Lu,[n(e.EquipmentAttributesList,{attributes:e.visibleAttributes,readonly:c.readonly,compact:!e.showAllAttributes&&e.shouldShowExpandButton,onEditAttribute:e.handleEditAttribute,onDeleteAttribute:e.handleDeleteAttribute},null,8,["attributes","readonly","compact"]),e.shouldShowExpandButton?(i(),r("div",Gu,[n(e.Button,{onClick:e.toggleShowAll,text:"",size:"small",class:"text-sm text-surface-600 dark:text-surface-400 hover:text-surface-900 dark:hover:text-surface-0"},{default:d(()=>[e.showAllAttributes?(i(),r(q,{key:1},[n(e.ChevronUpIcon,{class:"w-4 h-4 mr-1"}),u[10]||(u[10]=h(" Свернуть "))],64)):(i(),r(q,{key:0},[n(e.ChevronDownIcon,{class:"w-4 h-4 mr-1"}),h(" Показать все (еще "+b(e.hiddenAttributesCount)+") ",1)],64))]),_:1})])):x("",!0)])):(i(),r("div",Uu,[(i(!0),r(q,null,G(e.visibleGroupedAttributes,(t,l)=>(i(),r("div",{key:l,class:"attribute-group mb-4"},[a("div",Pu,[a("h6",zu,[n(e.Icon,{name:"pi pi-folder",class:"w-3 h-3"}),h(" "+b(l)+" ",1),n(e.Tag,{severity:"info",value:t.length.toString(),class:"text-xs"},null,8,["value"])])]),a("div",ju,[(i(!0),r(q,null,G(t,o=>(i(),r("div",{key:o.id,class:"attribute-card p-3 bg-surface-0 dark:bg-surface-900 rounded-lg border border-surface-200 dark:border-surface-700 hover:shadow-sm transition-shadow"},[a("div",Ju,[a("div",Hu,[a("div",Wu,[a("span",Ku,b(o.template.title),1),o.template.isRequired?(i(),k(e.Tag,{key:0,severity:"danger",class:"text-xs flex-shrink-0"},{default:d(()=>u[11]||(u[11]=[h(" Обязательный ")])),_:1,__:[11]})):x("",!0)]),a("div",Qu,b(e.formatAttribute(o).displayValue),1),o.template.description?(i(),r("div",{key:0,class:"text-xs text-surface-500 dark:text-surface-400 line-clamp-2",title:o.template.description},b(o.template.description),9,Xu)):x("",!0)]),e.canManageAttributes?(i(),r("div",Yu,[n(e.Button,{onClick:C=>e.handleEditAttribute(o.id),text:"",size:"small",class:"p-1 text-xs",title:`Редактировать ${o.template.title}`},{default:d(()=>[n(e.Icon,{name:"pi pi-pencil",class:"w-3 h-3"})]),_:2},1032,["onClick","title"]),n(e.Button,{onClick:C=>e.handleDeleteAttribute(o.id),text:"",severity:"danger",size:"small",class:"p-1 text-xs",title:`Удалить ${o.template.title}`,disabled:o.template.isRequired},{default:d(()=>[n(e.Icon,{name:"pi pi-trash",class:"w-3 h-3"})]),_:2},1032,["onClick","title","disabled"])])):x("",!0)])]))),128))])]))),128)),e.shouldShowExpandButton?(i(),r("div",Zu,[n(e.Button,{onClick:e.toggleShowAll,text:"",size:"small",class:"text-sm text-surface-600 dark:text-surface-400 hover:text-surface-900 dark:hover:text-surface-0"},{default:d(()=>[e.showAllAttributes?(i(),r(q,{key:1},[n(e.ChevronUpIcon,{class:"w-4 h-4 mr-1"}),u[12]||(u[12]=h(" Свернуть "))],64)):(i(),r(q,{key:0},[n(e.ChevronDownIcon,{class:"w-4 h-4 mr-1"}),h(" Показать все (еще "+b(e.hiddenAttributesCount)+") ",1)],64))]),_:1})])):x("",!0)]))])):(i(),r("div",$u,[n(e.Icon,{name:"pi pi-info-circle",class:"text-2xl mb-2 inline-block"}),u[14]||(u[14]=a("p",{class:"text-sm mb-3"},"У данной модели техники нет атрибутов",-1)),e.canManageAttributes?(i(),k(e.Button,{key:0,onClick:e.handleAddAttribute,outlined:"",size:"small"},{default:d(()=>[n(e.PlusIcon,{class:"w-4 h-4 mr-1"}),u[13]||(u[13]=h(" Добавить первый атрибут "))]),_:1,__:[13]})):x("",!0)])),n(e.AddEquipmentAttributeDialog,{visible:e.showAddDialog,"onUpdate:visible":u[3]||(u[3]=t=>e.showAddDialog=t),"equipment-id":c.equipmentId,"existing-attributes":c.attributes||[],onSave:e.handleSaveAttribute,onCancel:e.handleCancelAddAttribute},null,8,["visible","equipment-id","existing-attributes"]),n(e.Dialog,{visible:e.showGroupDialog,"onUpdate:visible":u[4]||(u[4]=t=>e.showGroupDialog=t),modal:"",header:"Выбор группы шаблонов",style:{width:"40rem"}},{default:d(()=>[a("div",ea,[(i(!0),r(q,null,G(e.templateGroups,t=>(i(),r("div",{key:t.id,class:"border border-surface-200 dark:border-surface-700 rounded-lg p-4"},[a("div",ta,[a("div",null,[a("h4",ua,b(t.name),1),a("p",aa,b(t.description),1),a("small",la,b(t._count?.templates||0)+" шаблонов",1)]),n(e.Button,{onClick:l=>e.loadTemplatesByGroupId(t.id),size:"small",loading:e.loadingTemplates},{default:d(()=>u[15]||(u[15]=[h(" Добавить все ")])),_:2,__:[15]},1032,["onClick","loading"])])]))),128))])]),_:1},8,["visible"])])}const sa=oe(Au,[["render",na],["__scopeId","data-v-2cbb67a9"]]),ia=de({__name:"EquipmentList",props:{initialData:{}},setup(f,{expose:u}){u();const c=E(""),e=E(!1),p=E(null),m=E([]),t=E({}),l=E({}),o=f,C=E(o.initialData),D={id:"ID",name:"Наименование",brandId:"Бренд ID",createdAt:"Создано",updatedAt:"Обновлено"},w=["id","name","createdAt"];function B(){p.value={},e.value=!0}function A(s){p.value={...s},e.value=!0}async function F(s){if(s)try{if(s.id){const{id:y,...N}=s;await J.crud.equipmentModel.update.mutate({where:{id:y},data:N})}else if(s.name)await J.crud.equipmentModel.create.mutate({data:{name:s.name,brandId:s.brandId||null}});else{console.error("Name is required to create an equipment model.");return}xe(window.location.href)}catch(y){console.error("Failed to save equipment model:",y)}finally{e.value=!1}}function M(){e.value=!1,p.value=null}async function H(s){e.value=!1,await J.crud.equipmentModel.delete.mutate({where:{id:s.id}}),xe(window.location.href)}ie(c,s=>{P(s)});async function P(s=""){console.log("value",s),C.value=await J.crud.equipmentModel.findMany.query({where:{OR:[{name:{contains:s}},{brand:{name:{contains:s}}}]},include:{brand:{select:{name:!0}},_count:{select:{partApplicabilities:!0,attributes:!0}}}})}function g(s){return new Date(s).toLocaleDateString("ru-RU",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})}async function V(s){if(t.value[s])return t.value[s];try{const y=await J.crud.equipmentApplicability.findMany.query({where:{equipmentModelId:s},include:{part:{include:{partCategory:!0,applicabilities:{include:{catalogItem:{include:{brand:!0}}}},_count:{select:{attributes:!0,applicabilities:!0}}}}},orderBy:{part:{name:"asc"}}});return t.value[s]=y||[],y||[]}catch(y){return console.error("Failed to load equipment parts:",y),[]}}async function L(s){if(l.value[s])return l.value[s];try{const N=(await J.crud.equipmentModelAttribute.findMany.query({where:{equipmentModelId:s},include:{template:{include:{group:!0}}},orderBy:[{template:{group:{name:"asc"}}},{template:{title:"asc"}}]})).map(O=>({...O,template:{...O.template,group:O.template.group}}));return l.value[s]=N,N}catch(y){return console.error("Failed to load equipment attributes:",y),[]}}async function ee(s){s.data._count.partApplicabilities>0&&await V(s.data.id),s.data._count.attributes>0&&await L(s.data.id)}function W(s){return{EXACT_MATCH:"Точное совпадение",MATCH_WITH_NOTES:"С примечаниями",REQUIRES_MODIFICATION:"Требует доработки",PARTIAL_MATCH:"Частичное совпадение"}[s]||s}function K(s){return{EXACT_MATCH:"success",MATCH_WITH_NOTES:"info",REQUIRES_MODIFICATION:"warning",PARTIAL_MATCH:"secondary"}[s]||"secondary"}function te(s){console.log("Edit part:",s)}function X(s){const y={};return s.forEach(N=>{const O=N.template.group?.name||"Общие";y[O]||(y[O]=[]),y[O].push(N)}),y}function j(s){const{value:y,template:N}=s,{dataType:O,unit:fe}=N;switch(O){case"STRING":return y;case"NUMBER":const Q=parseFloat(y),pe=isNaN(Q)?y:Q.toLocaleString("ru-RU");return fe?`${pe} ${Y(fe)}`:pe;case"BOOLEAN":return y.toLowerCase()==="true"?"Да":"Нет";case"DATE":const le=new Date(y);return isNaN(le.getTime())?y:le.toLocaleDateString("ru-RU");case"JSON":try{return JSON.stringify(JSON.parse(y),null,2)}catch{return y}default:return y}}function Y(s){return s?{MM:"мм",INCH:"дюйм",FT:"фт",G:"г",KG:"кг",T:"т",LB:"фунт",ML:"мл",L:"л",GAL:"гал",SEC:"сек",MIN:"мин",H:"ч",PCS:"шт",SET:"комплект",PAIR:"пара",BAR:"бар",PSI:"psi",KW:"кВт",HP:"л.с.",NM:"Н⋅м",RPM:"об/мин",C:"°C",F:"°F",PERCENT:"%"}[s]||s:""}async function Z(s){m.value.some(N=>N.id===s.id)||(m.value.push(s),s._count.attributes>0&&!l.value[s.id]&&await L(s.id))}async function ue(s){try{await J.crud.equipmentModelAttribute.create.mutate({data:{equipmentModelId:s.equipmentId||s.equipmentModelId,templateId:s.templateId,value:String(s.value)}}),s.equipmentId&&(delete l.value[s.equipmentId],await L(s.equipmentId)),console.log("Attribute added successfully")}catch(y){console.error("Failed to add attribute:",y)}}function ae(s){console.log("Edit attribute:",s)}function $(s){console.log("Delete attribute:",s)}const v={searchValue:c,dialogVisible:e,editingEquipment:p,expandedRows:m,equipmentPartsCache:t,equipmentAttributesCache:l,props:o,items:C,keyMapping:D,columnKeys:w,createEquipment:B,editEquipment:A,handleSave:F,handleCancel:M,deleteEquipment:H,debouncedSearch:P,formatDate:g,loadEquipmentParts:V,loadEquipmentAttributes:L,onRowExpand:ee,getAccuracyLabel:W,getAccuracySeverity:K,editPart:te,groupAttributesByGroup:X,formatAttributeValue:j,getUnitDisplayName:Y,expandRowForAttributes:Z,handleAddAttribute:ue,handleEditAttribute:ae,handleDeleteAttribute:$,Button:re,DataTable:Se,get PencilIcon(){return Fe},get TrashIcon(){return Ne},get PlusIcon(){return Oe},get ExternalLinkIcon(){return lt},get Column(){return Ve},EditEquipmentDialog:ft,EquipmentAttributesSection:sa,get navigate(){return xe},InputText:ke,Tag:ye,Icon:Te};return Object.defineProperty(v,"__isScriptSetup",{enumerable:!1,value:!0}),v}}),ra={class:"flex justify-between items-center mb-4"},oa={class:"flex justify-end"},da={class:"font-mono text-sm"},ca={key:1,class:"text-surface-500"},ma={key:1,class:"text-surface-500"},fa={class:"flex gap-2"},pa={class:"p-4 bg-surface-50 dark:bg-surface-800"},va={key:0,class:"mb-6"},ba={key:1},ga={class:"mb-3 font-semibold flex items-center gap-2"},ya={key:0},ha={class:"grid gap-3"},xa={class:"flex items-start justify-between"},_a={class:"flex-1"},Ea={class:"flex items-center gap-2 mb-2"},ka={class:"font-medium text-surface-900 dark:text-surface-0"},Ca={class:"flex items-center gap-4 text-sm text-surface-600 dark:text-surface-400 mb-2"},Aa={class:"flex items-center gap-1"},Da={class:"flex items-center gap-1"},Ta={key:0,class:"mt-2 p-2 bg-surface-100 dark:bg-surface-800 rounded text-sm text-surface-600 dark:text-surface-400"},wa={key:1,class:"mt-3"},Ba={class:"flex flex-wrap gap-2"},Ia={class:"font-medium"},Sa={class:"text-surface-500"},Va={key:0,class:"px-2 py-1 bg-surface-200 dark:bg-surface-700 rounded text-xs text-surface-600 dark:text-surface-400"},qa={class:"ml-4 flex flex-col gap-2"},Ma={key:1,class:"text-center py-6 text-surface-500 dark:text-surface-400"},Na={key:2,class:"text-center py-6 text-surface-500 dark:text-surface-400"};function Fa(f,u,c,e,p,m){return i(),r("div",null,[a("div",ra,[u[4]||(u[4]=a("h1",{class:"text-2xl font-bold"},"Модели техники",-1)),n(e.Button,{onClick:e.createEquipment},{default:d(()=>[n(e.PlusIcon,{class:"w-5 h-5 mr-2"}),u[3]||(u[3]=h(" Создать модель "))]),_:1,__:[3]})]),n(e.DataTable,{"show-headers":"",value:e.items,expandedRows:e.expandedRows,"onUpdate:expandedRows":u[1]||(u[1]=t=>e.expandedRows=t),onRowExpand:e.onRowExpand,rowHover:!0},{header:d(()=>[a("div",oa,[n(e.InputText,{modelValue:e.searchValue,"onUpdate:modelValue":u[0]||(u[0]=t=>e.searchValue=t),placeholder:"Поиск"},null,8,["modelValue"])])]),expansion:d(({data:t})=>[a("div",pa,[(t._count.attributes>0,i(),r("div",va,[n(e.EquipmentAttributesSection,{"equipment-id":t.id,attributes:e.equipmentAttributesCache[t.id],onAddAttribute:l=>e.handleAddAttribute({...l,equipmentId:t.id}),onEditAttribute:e.handleEditAttribute,onDeleteAttribute:e.handleDeleteAttribute},null,8,["equipment-id","attributes","onAddAttribute"])])),t._count.partApplicabilities>0?(i(),r("div",ba,[a("h5",ga,[n(e.Icon,{name:"pi pi-wrench",class:"text-blue-600 w-4 h-4"}),h(" Запчасти для: "+b(t.name),1)]),e.equipmentPartsCache[t.id]&&e.equipmentPartsCache[t.id].length>0?(i(),r("div",ya,[a("div",ha,[(i(!0),r(q,null,G(e.equipmentPartsCache[t.id],l=>(i(),r("div",{key:l.id,class:"p-4 bg-surface-0 dark:bg-surface-900 rounded-lg border border-surface-200 dark:border-surface-700 hover:shadow-sm transition-shadow"},[a("div",xa,[a("div",_a,[a("div",Ea,[a("h6",ka,b(l.part?.name||"Без названия"),1),l.part?.partCategory?(i(),k(e.Tag,{key:0,severity:"info",class:"text-xs"},{default:d(()=>[h(b(l.part.partCategory.name),1)]),_:2},1024)):x("",!0)]),a("div",Ca,[a("span",Aa,[n(e.Icon,{name:"pi pi-list",class:"w-3 h-3"}),h(" "+b(l.part?._count?.attributes||0)+" атрибутов ",1)]),a("span",Da,[n(e.Icon,{name:"pi pi-box",class:"w-3 h-3"}),h(" "+b(l.part?._count?.applicabilities||0)+" каталожных позиций ",1)])]),l.notes?(i(),r("div",Ta,[n(e.Icon,{name:"pi pi-info-circle",class:"mr-1 w-4 h-4 inline-block"}),h(" "+b(l.notes),1)])):x("",!0),l.part?.applicabilities?.length>0?(i(),r("div",wa,[u[5]||(u[5]=a("div",{class:"text-xs font-medium text-surface-700 dark:text-surface-300 mb-2"}," Каталожные позиции: ",-1)),a("div",Ba,[(i(!0),r(q,null,G(l.part.applicabilities.slice(0,3),o=>(i(),r("div",{key:o.id,class:"flex items-center gap-1 px-2 py-1 bg-surface-100 dark:bg-surface-800 rounded text-xs"},[a("span",Ia,b(o.catalogItem?.sku),1),a("span",Sa,b(o.catalogItem?.brand?.name),1),n(e.Tag,{severity:e.getAccuracySeverity(o.accuracy),class:"text-xs"},{default:d(()=>[h(b(e.getAccuracyLabel(o.accuracy)),1)]),_:2},1032,["severity"])]))),128)),l.part.applicabilities.length>3?(i(),r("div",Va," +"+b(l.part.applicabilities.length-3)+" еще ",1)):x("",!0)])])):x("",!0)]),a("div",qa,[n(e.Button,{onClick:o=>e.editPart(l.part.id),outlined:"",size:"small",class:"text-xs"},{default:d(()=>[n(e.PencilIcon,{class:"w-3 h-3 mr-1"}),u[6]||(u[6]=h(" Редактировать "))]),_:2,__:[6]},1032,["onClick"]),n(e.Button,{onClick:()=>e.navigate(`/admin/parts/${l.part.id}`),outlined:"",severity:"secondary",size:"small",class:"text-xs"},{default:d(()=>[n(e.ExternalLinkIcon,{class:"w-3 h-3 mr-1"}),u[7]||(u[7]=h(" Подробнее "))]),_:2,__:[7]},1032,["onClick"])])])]))),128))])])):(i(),r("div",Ma,[n(e.Icon,{name:"pi pi-info-circle",class:"text-2xl mb-2 inline-block"}),u[8]||(u[8]=h(" Запчасти для данной модели техники не найдены "))]))])):x("",!0),t._count.attributes===0&&t._count.partApplicabilities===0?(i(),r("div",Na,[n(e.Icon,{name:"pi pi-info-circle",class:"text-2xl mb-2 inline-block"}),u[9]||(u[9]=h(" Для данной модели техники нет дополнительной информации "))])):x("",!0)])]),default:d(()=>[n(e.Column,{expander:!0,headerStyle:"width: 3rem"}),(i(),r(q,null,G(e.columnKeys,t=>n(e.Column,{key:t,field:t,header:e.keyMapping[t]||t},je({_:2},[t==="createdAt"?{name:"body",fn:d(({data:l})=>[h(b(e.formatDate(l[t])),1)]),key:"0"}:t==="id"?{name:"body",fn:d(({data:l})=>[a("div",da,b(l[t].substring(0,8))+"... ",1)]),key:"1"}:void 0]),1032,["field","header"])),64)),n(e.Column,{field:"brand.name",header:"Бренд"},{body:d(({data:t})=>[h(b(t.brand?.name||"-"),1)]),_:1}),n(e.Column,{field:"_count.partApplicabilities",header:"Применимость деталей"},{body:d(({data:t})=>[t._count.partApplicabilities>0?(i(),k(e.Tag,{key:0,severity:"info",value:t._count.partApplicabilities.toString()},null,8,["value"])):(i(),r("span",ca,"0"))]),_:1}),n(e.Column,{field:"_count.attributes",header:"Атрибуты"},{body:d(({data:t})=>[t._count.attributes>0?(i(),k(e.Tag,{key:0,severity:"secondary",value:t._count.attributes.toString(),class:"cursor-pointer hover:bg-surface-200 dark:hover:bg-surface-700 transition-colors",onClick:l=>e.expandRowForAttributes(t)},null,8,["value","onClick"])):(i(),r("span",ma,"0"))]),_:1}),n(e.Column,{header:"Действия"},{body:d(({data:t})=>[a("div",fa,[n(e.Button,{onClick:l=>e.editEquipment(t),outlined:"",size:"small"},{default:d(()=>[n(e.PencilIcon,{class:"w-5 h-5"})]),_:2},1032,["onClick"]),n(e.Button,{onClick:l=>e.deleteEquipment(t),outlined:"",severity:"danger",size:"small"},{default:d(()=>[n(e.TrashIcon,{class:"w-5 h-5"})]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["value","expandedRows"]),n(e.EditEquipmentDialog,{isVisible:e.dialogVisible,"onUpdate:isVisible":u[2]||(u[2]=t=>e.dialogVisible=t),equipment:e.editingEquipment,onSave:e.handleSave,onCancel:e.handleCancel},null,8,["isVisible","equipment"])])}const V0=oe(ia,[["render",Fa]]);export{V0 as default};
