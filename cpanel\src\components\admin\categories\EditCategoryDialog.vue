<template>
  <Dialog v-model:visible="isVisible" modal :header="dialogTitle" class="sm:w-100 w-9/10">
    <!-- Форма для редактирования данных -->
    <div class="flex flex-col gap-4 py-4">
      <div class="flex flex-col">
        <label for="name">Наименование</label>
        <InputText id="name" v-model="localCategory.name" />
      </div>
      <div class="flex flex-col">
        <label for="slug">URL слаг</label>
        <InputText id="slug" v-model="localCategory.slug" @input="onSlugInput" />
        <small class="text-surface-500">Автогенерация из названия, можно отредактировать вручную</small>
      </div>
      <div class="flex flex-col">
        <label for="description">Описание</label>
        <Textarea id="description" v-model="localCategory.description" rows="3" />
      </div>
      <div class="flex flex-col">
        <label for="parent">Родительская категория</label>
        <Dropdown 
          id="parent" 
          v-model="localCategory.parentId" 
          :options="parentOptions" 
          optionLabel="name" 
          optionValue="id" 
          placeholder="Выберите родительскую категорию"
          showClear
        />
      </div>
      <div class="flex flex-col">
        <label for="icon">Иконка</label>
        <InputText id="icon" v-model="localCategory.icon" placeholder="Например: engine, filter, etc." />
      </div>

      <!-- Изображение категории -->
      <div class="flex flex-col">
        <label>Изображение категории</label>
        <div class="flex items-center gap-3">
          <div class="w-24 h-24 border rounded bg-surface-50 dark:bg-surface-900 flex items-center justify-center overflow-hidden">
            <img v-if="categoryImageUrl" :src="resolveMediaUrl(categoryImageUrl)" class="object-cover w-full h-full" />
            <span v-else class="text-surface-500 text-xs">Нет</span>
          </div>
          <div class="flex flex-col gap-2">
            <input type="file" accept="image/*" @change="onSelectImage" />
            <div class="flex gap-2">
              <Button size="small" :disabled="!selectedImage || uploading" @click="uploadImage">Загрузить</Button>
              <Button size="small" severity="danger" outlined :disabled="!categoryImageUrl || uploading" @click="deleteImage">Удалить</Button>
            </div>
            <small v-if="uploading" class="text-surface-500">Загрузка...</small>
          </div>
        </div>
      </div>
    </div>
    <div class="flex justify-end gap-2">
      <SecondaryButton type="button" label="Cancel" @click="handleCancel" />
      <Button type="button" label="Save" @click="handleSave" />
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import Button from '@/volt/Button.vue'
import Dialog from '@/volt/Dialog.vue'
import InputText from '@/volt/InputText.vue'
import Textarea from '@/volt/Textarea.vue'
import Dropdown from '@/volt/Dropdown.vue'
import SecondaryButton from '@/volt/SecondaryButton.vue'
import type { CategoryFormData } from '@/types/category'
import { trpc } from '@/lib/trpc'
import { slugify, fileToBase64, resolveMediaUrl } from '@/lib/utils'
import { useTrpc } from '@/composables/useTrpc'

const parentOptions = ref<Array<{ id: number; name: string }>>([])

// v-model для управления видимостью диалога
const isVisible = defineModel<boolean>('isVisible', { required: true })

// Входящие данные категории для редактирования
const props = defineProps<{
  category: CategoryFormData | null
}>()

// Определяем события, которые компонент может отправлять родителю
const emit = defineEmits<{
  (e: 'save', categoryData: CategoryFormData): void
  (e: 'cancel'): void
}>()

// Локальное состояние для данных формы.
// Инициализируется пустым объектом или данными входящей категории.
const localCategory = ref<CategoryFormData>({})
const userEditedSlug = ref(false)

// Заголовок диалогового окна
const dialogTitle = ref('Создать категорию')

// Отслеживаем изменения isVisible.
// При открытии диалога (новое значение true), инициализируем локальные данные
watch(isVisible, (newValue) => {
  if (newValue) {
    // Копируем данные из props в локальное состояние, чтобы избежать прямого изменения props.
    // Если props.category не предоставлен (создание новой), localCategory будет пустым объектом.
    localCategory.value = { ...(props.category || {}) }
    dialogTitle.value = props.category?.id ? `Редактировать: ${props.category.name}` : 'Создать категорию'
    userEditedSlug.value = false
  }
})

// Живая генерация slug из name, пока пользователь не редактировал slug вручную
watch(
  () => localCategory.value.name,
  (name) => {
    const currentSlug = String(localCategory.value.slug ?? '')
    if (!userEditedSlug.value || currentSlug.length === 0) {
      localCategory.value.slug = slugify(String(name ?? ''))
    }
  }
)

function onSlugInput() {
  userEditedSlug.value = true
  // Санитизируем пользовательский ввод по тем же правилам
  localCategory.value.slug = slugify(String(localCategory.value.slug ?? ''))
}

function handleCancel() {
  isVisible.value = false
  emit('cancel')
}

function handleSave() {
  // Автогенерация slug: если не задан пользователем — строим из name
  if ((!localCategory.value.slug || String(localCategory.value.slug).trim() === '') && localCategory.value.name) {
    localCategory.value.slug = slugify(String(localCategory.value.name))
  }
  // Отправляем копию локальных данных, чтобы избежать
  // случайных изменений после отправки события.
  emit('save', { ...localCategory.value })
  isVisible.value = false
}

async function loadParentCategories() {
  const categories = await trpc.crud.partCategory.findMany.query({
    select: {
      id: true,
      name: true
    },
    orderBy: {
      name: 'asc'
    }
  })

  parentOptions.value = categories
}

onMounted(() => {
  loadParentCategories()
})

// Работа с изображением категории
const { media } = useTrpc()
const selectedImage = ref<File | null>(null)
const uploading = ref(false)
const categoryImageUrl = computed<string | null>(() => (props.category as any)?.image?.url || (localCategory.value as any)?.image?.url || null)

function onSelectImage(e: Event) {
  const input = e.target as HTMLInputElement
  if (input.files && input.files[0]) selectedImage.value = input.files[0]
}

async function uploadImage() {
  if (!localCategory.value?.id || !selectedImage.value) return
  uploading.value = true
  try {
    const dataUrl = await fileToBase64(selectedImage.value)
    await media.uploadPartCategoryImage({
      partCategoryId: localCategory.value.id as number,
      fileName: selectedImage.value.name,
      fileData: dataUrl,
      mimeType: selectedImage.value.type || 'image/png',
    })
    // Обновим изображение в локальном состоянии (перечитаем из API при следующей загрузке)
    const updated = await trpc.crud.partCategory.findUnique.query({ where: { id: localCategory.value.id as number }, include: { image: true } as any })
    if (updated) (localCategory.value as any).image = (updated as any).image
    selectedImage.value = null
  } finally {
    uploading.value = false
  }
}

async function deleteImage() {
  if (!localCategory.value?.id) return
  uploading.value = true
  try {
    await media.deletePartCategoryImage({ partCategoryId: localCategory.value.id as number })
    const updated = await trpc.crud.partCategory.findUnique.query({ where: { id: localCategory.value.id as number }, include: { image: true } as any })
    if (updated) (localCategory.value as any).image = (updated as any).image
  } finally {
    uploading.value = false
  }
}
</script>