import { jsxs, jsx } from 'react/jsx-runtime';
import { C as Card, b as <PERSON><PERSON><PERSON><PERSON>, c as <PERSON><PERSON><PERSON><PERSON>, d as CardDes<PERSON>, a as CardContent } from './card_ZM99lQrJ.mjs';
import { n as navigate } from './router_vN4ZPF0m.mjs';

function PartCard({ part, onClick }) {
  const handleClick = () => {
    if (onClick) {
      onClick();
    } else {
      navigate(`/catalog/parts/${part.id}`);
    }
  };
  return /* @__PURE__ */ jsxs(
    Card,
    {
      className: "cursor-pointer transition-all hover:shadow-md hover:scale-[1.02]",
      onClick: handleClick,
      children: [
        /* @__PURE__ */ jsx(CardHeader, { className: "pb-3", children: /* @__PURE__ */ jsxs("div", { className: "flex items-start justify-between", children: [
          /* @__PURE__ */ jsxs("div", { className: "flex-1", children: [
            /* @__PURE__ */ jsx(CardTitle, { className: "text-lg line-clamp-2", children: part.name || `Запчасть #${part.id}` }),
            /* @__PURE__ */ jsx(CardDescription, { className: "mt-1", children: part.partCategory.name })
          ] }),
          part.image && /* @__PURE__ */ jsx(
            "img",
            {
              src: part.image.url,
              alt: part.name || "Изображение запчасти",
              className: "w-16 h-16 object-cover rounded-md ml-3"
            }
          )
        ] }) }),
        /* @__PURE__ */ jsxs(CardContent, { className: "pt-0", children: [
          part.attributes.length > 0 && /* @__PURE__ */ jsxs("div", { className: "space-y-2", children: [
            /* @__PURE__ */ jsx("h4", { className: "text-sm font-medium text-muted-foreground", children: "Характеристики:" }),
            /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 gap-1 text-sm", children: [
              part.attributes.slice(0, 3).map((attr) => /* @__PURE__ */ jsxs("div", { className: "flex justify-between", children: [
                /* @__PURE__ */ jsxs("span", { className: "text-muted-foreground", children: [
                  attr.template.title,
                  ":"
                ] }),
                /* @__PURE__ */ jsxs("span", { className: "font-medium", children: [
                  attr.value,
                  attr.template.unit && ` ${attr.template.unit}`
                ] })
              ] }, attr.id)),
              part.attributes.length > 3 && /* @__PURE__ */ jsxs("div", { className: "text-xs text-muted-foreground", children: [
                "+",
                part.attributes.length - 3,
                " характеристик"
              ] })
            ] })
          ] }),
          /* @__PURE__ */ jsx("div", { className: "mt-3 pt-3 border-t", children: /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-between text-xs text-muted-foreground", children: [
            /* @__PURE__ */ jsx("span", { children: "Обновлено:" }),
            /* @__PURE__ */ jsx("span", { children: new Date(part.updatedAt).toLocaleDateString("ru-RU") })
          ] }) })
        ] })
      ]
    }
  );
}

export { PartCard as P };
