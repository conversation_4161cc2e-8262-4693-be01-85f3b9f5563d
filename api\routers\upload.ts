import { z } from 'zod';
import { router, authProcedure, expertProcedure } from '../trpc';
import type { Context } from '../trpc';
import { UploadService } from '../services/upload.service';

export const uploadRouter = router({
  // Загрузка изображения схемы
  uploadSchemaImage: authProcedure
    .input(z.object({
      fileName: z.string().min(1),
      fileData: z.string(), // base64 encoded file data
      mimeType: z.string().min(1)
    }))
    .mutation(async ({ input }: { input: { fileName: string; fileData: string; mimeType: string } }) => UploadService.uploadSchemaImage(input))
  ,

  // Загрузка изображения для Part. Возвращает MediaAsset
  uploadPartImage: expertProcedure
    .input(z.object({
      partId: z.number().int().positive(),
      fileName: z.string().min(1),
      fileData: z.string(), // base64
      mimeType: z.string().min(1)
    }))
    .mutation(async ({ input, ctx }: { input: { partId: number; fileName: string; fileData: string; mimeType: string }; ctx: Context }) => UploadService.uploadPartImage(ctx, input))
  ,

  // Удаление изображения Part
  deletePartImage: expertProcedure
    .input(z.object({ partId: z.number().int().positive() }))
    .mutation(async ({ input, ctx }: { input: { partId: number }; ctx: Context }) => UploadService.deletePartImage(ctx, input))
  ,

  // Загрузка изображения для PartCategory
  uploadPartCategoryImage: expertProcedure
    .input(z.object({
      partCategoryId: z.number().int().positive(),
      fileName: z.string().min(1),
      fileData: z.string(),
      mimeType: z.string().min(1)
    }))
    .mutation(async ({ input, ctx }: { input: { partCategoryId: number; fileName: string; fileData: string; mimeType: string }; ctx: Context }) => UploadService.uploadPartCategoryImage(ctx, input))
  ,

  deletePartCategoryImage: expertProcedure
    .input(z.object({ partCategoryId: z.number().int().positive() }))
    .mutation(async ({ input, ctx }: { input: { partCategoryId: number }; ctx: Context }) => UploadService.deletePartCategoryImage(ctx, input))
  ,

  // ===== Новые эндпоинты: Part media gallery =====
  uploadPartMedia: expertProcedure
    .input(z.object({
      partId: z.number().int().positive(),
      fileName: z.string().min(1),
      fileData: z.string(),
      mimeType: z.string().min(1),
    }))
    .mutation(async ({ input, ctx }: { input: { partId: number; fileName: string; fileData: string; mimeType: string }; ctx: Context }) => UploadService.uploadPartMedia(ctx, input))
  ,

  removePartMedia: expertProcedure
    .input(z.object({
      partId: z.number().int().positive(),
      mediaId: z.number().int().positive(),
    }))
    .mutation(async ({ input, ctx }: { input: { partId: number; mediaId: number }; ctx: Context }) => UploadService.removePartMedia(ctx, input))
  ,

  // ===== Новые эндпоинты: CatalogItem main image =====
  uploadCatalogItemImage: expertProcedure
    .input(z.object({
      catalogItemId: z.number().int().positive(),
      fileName: z.string().min(1),
      fileData: z.string(),
      mimeType: z.string().min(1),
    }))
    .mutation(async ({ input, ctx }: { input: { catalogItemId: number; fileName: string; fileData: string; mimeType: string }; ctx: Context }) => UploadService.uploadCatalogItemImage(ctx, input))
  ,

  deleteCatalogItemImage: expertProcedure
    .input(z.object({ catalogItemId: z.number().int().positive() }))
    .mutation(async ({ input, ctx }: { input: { catalogItemId: number }; ctx: Context }) => UploadService.deleteCatalogItemImage(ctx, input))
  ,

  // ===== Новые эндпоинты: CatalogItem media gallery =====
  uploadCatalogItemMedia: expertProcedure
    .input(z.object({
      catalogItemId: z.number().int().positive(),
      fileName: z.string().min(1),
      fileData: z.string(),
      mimeType: z.string().min(1),
    }))
    .mutation(async ({ input, ctx }: { input: { catalogItemId: number; fileName: string; fileData: string; mimeType: string }; ctx: Context }) => UploadService.uploadCatalogItemMedia(ctx, input))
  ,

  removeCatalogItemMedia: expertProcedure
    .input(z.object({
      catalogItemId: z.number().int().positive(),
      mediaId: z.number().int().positive(),
    }))
    .mutation(async ({ input, ctx }: { input: { catalogItemId: number; mediaId: number }; ctx: Context }) => UploadService.removeCatalogItemMedia(ctx, input))
});
