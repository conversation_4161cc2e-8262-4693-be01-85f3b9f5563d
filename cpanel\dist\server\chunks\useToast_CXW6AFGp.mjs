import { a as useToast$1 } from './_@astro-renderers_CicWY1rm.mjs';

const useToast = () => {
  let toast = null;
  try {
    toast = useToast$1();
  } catch (e) {
    toast = null;
  }
  const emitGlobal = (payload) => {
    if (typeof window !== "undefined") {
      window.dispatchEvent(new CustomEvent("app:toast", { detail: payload }));
    }
  };
  const show = (message) => {
    const payload = {
      severity: message.severity || "info",
      summary: message.summary,
      detail: message.detail,
      life: message.life || 5e3,
      closable: message.closable !== false,
      group: message.group
    };
    if (toast && typeof toast.add === "function") {
      toast.add(payload);
    } else {
      emitGlobal(payload);
    }
  };
  const success = (summary, detail, life) => {
    show({
      severity: "success",
      summary,
      detail,
      life
    });
  };
  const info = (summary, detail, life) => {
    show({
      severity: "info",
      summary,
      detail,
      life
    });
  };
  const warn = (summary, detail, life) => {
    show({
      severity: "warn",
      summary,
      detail,
      life
    });
  };
  const error = (summary, detail, life) => {
    show({
      severity: "error",
      summary,
      detail,
      life: life || 8e3
      // Ошибки показываем дольше
    });
  };
  const clear = (group) => {
    toast.removeAllGroups();
  };
  const remove = (message) => {
    toast.remove(message);
  };
  const showSaveSuccess = (entityName = "Запись") => {
    success("Сохранено", `${entityName} успешно сохранена`);
  };
  const showDeleteSuccess = (entityName = "Запись") => {
    success("Удалено", `${entityName} успешно удалена`);
  };
  const showSaveError = (entityName = "Запись", errorMessage) => {
    error(
      "Ошибка сохранения",
      errorMessage || `Не удалось сохранить ${entityName.toLowerCase()}`
    );
  };
  const showDeleteError = (entityName = "Запись", errorMessage) => {
    error(
      "Ошибка удаления",
      errorMessage || `Не удалось удалить ${entityName.toLowerCase()}`
    );
  };
  const showLoadError = (entityName = "Данные", errorMessage) => {
    error(
      "Ошибка загрузки",
      errorMessage || `Не удалось загрузить ${entityName.toLowerCase()}`
    );
  };
  const showValidationError = (message = "Проверьте правильность заполнения полей") => {
    warn("Ошибка валидации", message);
  };
  const showNetworkError = () => {
    error(
      "Ошибка сети",
      "Проверьте подключение к интернету и попробуйте снова"
    );
  };
  const showUnauthorizedError = () => {
    error(
      "Нет доступа",
      "У вас недостаточно прав для выполнения этого действия"
    );
  };
  return {
    // Базовые методы
    show,
    success,
    info,
    warn,
    error,
    clear,
    remove,
    // Удобные методы
    showSaveSuccess,
    showDeleteSuccess,
    showSaveError,
    showDeleteError,
    showLoadError,
    showValidationError,
    showNetworkError,
    showUnauthorizedError
  };
};

export { useToast as u };
