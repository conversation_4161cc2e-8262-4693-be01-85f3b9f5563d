import React from "react";
import { authClient } from "@/lib/auth-client";
import { Button } from "@/components/ui/button";

export function AuthNav() {
  const { data: session, isPending } = authClient.useSession();

  if (isPending) return null;

  if (!session) {
    return (
      <div className="flex items-center gap-2">
        <a href="/login" className="text-sm text-foreground/80 hover:underline">Вход</a>
        <a href="/register" className="text-sm text-foreground/80 hover:underline">Регистрация</a>
      </div>
    );
  }

  return (
    <div className="flex items-center gap-2">
      <a href="/account" className="text-sm text-foreground/80 hover:underline">Кабинет</a>
      <Button
        size="sm"
        variant="outline"
        onClick={async () => {
          await authClient.signOut({
            fetchOptions: { onSuccess: () => (window.location.href = "/") },
          });
        }}
      >
        Выйти
      </Button>
    </div>
  );
}

