# Архитектура: Атрибуты и Поиск взаимозаменяемости

Этот документ описывает финальную, утвержденную архитектуру для хранения атрибутов и реализации механизма поиска взаимозаменяемых запчастей.

## 1. Фундаментальная концепция: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs. Реализация

Чтобы обеспечить гибкость и ясность системы, мы вводим четкое разделение ответственности между ключевыми моделями:

-   **`Part` и `PartAttribute` (Эталон):**
    -   `Part` представляет собой "идеальную" или "абстрактную" деталь.
    -   `PartAttribute` хранит *эталонные, нормализованные* характеристики этой детали. Это наш "золотой стандарт".
    -   **Пример:** Для группы сальников 20x30x5, атрибуты `Part` будут: `inner_diameter = 20.0`, `outer_diameter = 30.0`, `width = 5.0`. Значение атрибута "Тип" может быть `TC/SC_GENERIC` — внутренний эталонный идентификатор.
    -   **Цель:** Описать, каким требованиям должна соответствовать деталь, чтобы принадлежать к этой группе.

-   **`CatalogItem` и `CatalogItemAttribute` (Реализация):**
    -   `CatalogItem` — это конкретный товар (артикул) от конкретного производителя.
    -   `CatalogItemAttribute` хранит *фактические* характеристики этого товара, **как их указал производитель**.
    -   **Пример:** `CatalogItem` от SKF может иметь атрибуты: `outer_diameter = 30.01`, `type = "SC"`, `material = "NBR"`.
    -   **Цель:** Зафиксировать реальные данные производителя и описать отличия от эталона `Part`, если они есть.

## 2. Предлагаемые изменения схемы данных

Основываясь на этой концепции, мы расширим схему `api/catalog_schema.zmodel`, чтобы поддерживать гибкое сравнение атрибутов.

```zmodel
// Файл: api/catalog_schema.zmodel

// ... (существующая схема)

// НОВАЯ МОДЕЛЬ: Группа синонимов для значений атрибутов
// Позволяет объединять разные строковые значения в одну логическую группу.
// Применяется при сравнении CatalogItemAttribute.value с эталонным PartAttribute.value
// Например, для атрибута "Тип сальника" можно создать группу "Стандартные типы",
// в которую войдут значения "TC", "SC", "BABSL".
model AttributeSynonymGroup {
    id          Int      @id @default(autoincrement())
    name        String   @unique // Название группы, например "Стандартные типы уплотнений TC/SC"
    description String?

    // Связь с шаблоном атрибута, к которому применяется эта группа
    template    AttributeTemplate @relation(fields: [templateId], references: [id], onDelete: Cascade)
    templateId  Int

    // Список всех синонимов в этой группе
    synonyms    AttributeSynonym[]

    @@allow('all', auth() != null && auth().role == 'ADMIN')
    @@index([templateId])
}

// НОВАЯ МОДЕЛЬ: Конкретный синоним в группе
// Хранит одно из эквивалентных значений.
model AttributeSynonym {
    id          Int      @id @default(autoincrement())
    value       String   // Значение синонима, например, "TC"

    // Связь с родительской группой
    group       AttributeSynonymGroup @relation(fields: [groupId], references: [id], onDelete: Cascade)
    groupId     Int

    @@unique([groupId, value]) // Значение может входить в группу только один раз
    @@index([value])           // Индекс для быстрого поиска группы по значению
    @@allow('all', auth() != null && auth().role == 'ADMIN')
}

// ДОПОЛНЕНИЯ В СУЩЕСТВУЮЩУЮ МОДЕЛЬ
model AttributeTemplate {
    // ... (существующие поля)
    
    // Новое поле для числовых атрибутов: допустимое отклонение (толеранс)
    // Позволит считать значение из CatalogItemAttribute (например, 30.01) соответствующим
    // эталонному значению из PartAttribute (например, 30.0), если |30.01 - 30.0| <= tolerance.
    tolerance   Float?            @default(0)

    // Обратная связь с группами синонимов
    synonymGroups AttributeSynonymGroup[]
}
```

## 3. Архитектура и рабочие процессы

### Процесс 1: Поиск подходящей группы `Part` для нового `CatalogItem`

Этот процесс является центральным для каталогизации.

1.  **Триггер:** Инженер хочет добавить новый `CatalogItem` в существующую группу взаимозаменяемости.
2.  **UI:** На странице `CatalogItem` есть кнопка "Найти группу взаимозаменяемости".
3.  **API эндпоинт:** `findMatchingParts(catalogItemId: Int)`
4.  **Алгоритм работы бэкенда:**
    a.  Получает `CatalogItem` и все его `CatalogItemAttribute`.
    b.  Формирует "профиль поиска" на основе этих атрибутов.
    c.  Итерирует по всем существующим `Part` в базе данных.
    d.  Для каждого `Part` выполняет проверку на соответствие:
        -   Берет все эталонные `PartAttribute` текущей `Part`.
        -   Для каждого эталонного атрибута ищет соответствующий ему атрибут в "профиле поиска" `CatalogItem`.
        -   **Выполняет сравнение:**
            -   **Числа:** `abs(catalogAttr.value - partAttr.value) <= template.tolerance`
            -   **Строки:** `catalogAttr.value` и `partAttr.value` принадлежат одной `AttributeSynonymGroup`. Если для `partAttr.value` нет группы синонимов, то требуется точное совпадение.
        -   Если **все** ключевые эталонные атрибуты `Part` соответствуют атрибутам `CatalogItem` (с учетом допусков и синонимов), то эта `Part` считается кандидатом.
    e.  **Результат:** API возвращает список `Part`-кандидатов, отсортированный по степени "точности" совпадения.

### Процесс 2: Создание новой группы `Part` из нескольких `CatalogItem`

1.  **Триггер:** Инженер определил, что несколько `CatalogItem` являются аналогами, но для них еще нет группы.
2.  **UI:** Инженер выбирает 2+ `CatalogItem` и нажимает "Создать группу взаимозаменяемости".
3.  **Логика:**
    a.  Система анализирует атрибуты выбранных `CatalogItem`.
    b.  Открывается форма создания `Part`, где поля для `PartAttribute` уже предзаполнены:
        -   **Числа:** Предлагается среднее значение или значение из первого элемента.
        -   **Строки:** Если значения разные (напр., "TC" и "SC"), система может предложить создать новую `AttributeSynonymGroup`.
    c.  Инженер редактирует и утверждает эталонные атрибуты для новой `Part`.
    d.  После создания `Part`, выбранные `CatalogItem`'ы автоматически привязываются к ней.

## 4. План реализации

1.  **Бэкенд:**
    -   [ ] Обновить файл `api/catalog_schema.zmodel`, добавив новые модели и поля.
    -   [ ] Выполнить команду `bunx zenstack generate`, чтобы обновить Prisma-клиент и Zod-схемы.
    -   [ ] Реализовать API-эндпоинт `findMatchingParts`.

2.  **Фронтенд:**
    -   [ ] **Панель администратора:** Создать интерфейс для управления `AttributeTemplate.tolerance` и `AttributeSynonymGroup`.
    -   [ ] **Страница `CatalogItem`:** Реализовать UI для запуска поиска `findMatchingParts` и отображения результатов.
    -   [ ] **Страница сравнения:** Создать UI для процесса №2 — создания `Part` из нескольких `CatalogItem`.
