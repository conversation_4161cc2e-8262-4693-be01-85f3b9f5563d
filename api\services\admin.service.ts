import { TRPCError } from '@trpc/server'
import { $prisma } from '../db'
import { auth } from '../auth'
import { AdminAuditLogger, AdminActionType, validateAdminPermissions, AdminRateLimiter } from '../lib/admin-audit'

export class AdminService {
  static async getDashboard(input: { rangeDays: number }) {
    const now = new Date()
    const from = new Date(now)
    from.setDate(now.getDate() - input.rangeDays)
    const startOfDay = (d: Date) => { const nd = new Date(d); nd.setHours(0,0,0,0); return nd }
    const formatDayKey = (d: Date) => d.toISOString().slice(0, 10)
    const buildEmptyDailySeries = (days: number) => { const series: Array<{ date: string; count: number }> = []; const cur = startOfDay(from); for (let i=0;i<=days;i++){ const dd = new Date(cur); dd.setDate(cur.getDate()+i); series.push({ date: formatDayKey(dd), count: 0 }) } return series }

    const [usersTotal, shopsTotal, brandsTotal, oemBrandsTotal, partsTotal, partCategoriesTotal, catalogItemsTotal, equipmentModelsTotal, partAttributesTotal, catalogItemAttributesTotal, equipmentModelAttributesTotal, synonymGroupsTotal, synonymsTotal, attributeTemplatesTotal, numericTemplatesTotal, numericTemplatesWithToleranceTotal, sessionsActiveTotal, proposalsPendingTotal, proposalsApprovedTotal, proposalsRejectedTotal] = await Promise.all([
      $prisma.user.count(),
      $prisma.user.count({ where: { role: 'SHOP' as any } }),
      $prisma.brand.count(),
      $prisma.brand.count({ where: { isOem: true } }),
      $prisma.part.count(),
      $prisma.partCategory.count(),
      $prisma.catalogItem.count(),
      $prisma.equipmentModel.count(),
      $prisma.partAttribute.count(),
      $prisma.catalogItemAttribute.count(),
      $prisma.equipmentModelAttribute.count(),
      $prisma.attributeSynonymGroup.count(),
      $prisma.attributeSynonym.count(),
      $prisma.attributeTemplate.count(),
      $prisma.attributeTemplate.count({ where: { dataType: 'NUMBER' as any } }),
      $prisma.attributeTemplate.count({ where: { dataType: 'NUMBER' as any, tolerance: { gt: 0 } } }),
      $prisma.session.count({ where: { expiresAt: { gt: new Date() } } }),
      $prisma.matchingProposal.count({ where: { status: 'PENDING' as any } }),
      $prisma.matchingProposal.count({ where: { status: 'APPROVED' as any } }),
      $prisma.matchingProposal.count({ where: { status: 'REJECTED' as any } }),
    ])

    const [usersCreated, partsCreated, proposalsCreated] = await Promise.all([
      $prisma.user.findMany({ where: { createdAt: { gte: from } }, select: { createdAt: true } }),
      $prisma.part.findMany({ where: { createdAt: { gte: from } }, select: { createdAt: true } as any }),
      $prisma.matchingProposal.findMany({ where: { createdAt: { gte: from } }, select: { createdAt: true } }),
    ])
    const aggregateDaily = (dates: Array<{ createdAt: Date }>) => { const series = buildEmptyDailySeries(input.rangeDays); const index = new Map(series.map((p, i) => [p.date, i] as const)); for (const r of dates) { const key = formatDayKey(startOfDay(r.createdAt)); const i = index.get(key); if (i !== undefined) series[i].count += 1 } return series }

    const [topCategoriesRaw, topBrandsRaw] = await Promise.all([
      $prisma.partCategory.findMany({ take: 5, orderBy: { parts: { _count: 'desc' } }, include: { _count: { select: { parts: true } } } }),
      $prisma.brand.findMany({ take: 5, orderBy: { catalogItems: { _count: 'desc' } }, include: { _count: { select: { catalogItems: true } } } }),
    ])

    let recentAudit: any[] = []
    try {
      const maybeAudit = ($prisma as any).adminAuditLog
      if (maybeAudit && typeof maybeAudit.findMany === 'function') {
        recentAudit = await maybeAudit.findMany({ take: 5, orderBy: { createdAt: 'desc' }, select: { id: true, action: true, createdAt: true, adminEmail: true, targetUserEmail: true } })
      }
    } catch {}

    const recentPendingProposals = await $prisma.matchingProposal.findMany({ where: { status: 'PENDING' as any }, take: 5, orderBy: { createdAt: 'desc' }, include: { catalogItem: { include: { brand: true } }, part: true } })

    return {
      totals: {
        users: usersTotal, shops: shopsTotal, brands: brandsTotal, oemBrands: oemBrandsTotal,
        parts: partsTotal, partCategories: partCategoriesTotal, catalogItems: catalogItemsTotal, equipmentModels: equipmentModelsTotal,
        attributes: { partAttributes: partAttributesTotal, catalogItemAttributes: catalogItemAttributesTotal, equipmentModelAttributes: equipmentModelAttributesTotal },
        synonyms: { groups: synonymGroupsTotal, values: synonymsTotal },
        templates: { total: attributeTemplatesTotal, numeric: numericTemplatesTotal, numericWithTolerance: numericTemplatesWithToleranceTotal },
        sessionsActive: sessionsActiveTotal,
        proposals: { pending: proposalsPendingTotal, approved: proposalsApprovedTotal, rejected: proposalsRejectedTotal },
      },
      trends: { usersDaily: aggregateDaily(usersCreated), partsDaily: aggregateDaily(partsCreated), proposalsDaily: aggregateDaily(proposalsCreated) },
      top: { categories: topCategoriesRaw.map((c: any) => ({ id: c.id, name: c.name, count: c._count.parts })), brands: topBrandsRaw.map((b: any) => ({ id: b.id, name: b.name, count: b._count.catalogItems })) },
      recent: { audit: recentAudit, pendingProposals: recentPendingProposals.map((p: any) => ({ id: p.id, createdAt: p.createdAt, catalogItem: p.catalogItem ? { id: p.catalogItem.id, sku: p.catalogItem.sku, brand: p.catalogItem.brand ? { id: p.catalogItem.brand.id, name: p.catalogItem.brand.name } : null } : null, part: p.part ? { id: p.part.id, name: p.part.name } : null, accuracySuggestion: p.accuracySuggestion, notesSuggestion: p.notesSuggestion })) },
    }
  }

  // Users
  static async createUser(ctx: any, input: any) {
    if (!validateAdminPermissions(ctx.user, 'createUser')) throw new TRPCError({ code: 'FORBIDDEN', message: 'Недостаточно прав для создания пользователей' })
    if (!AdminRateLimiter.checkRateLimit(ctx.user!.id, 'createUser', 10, 60000)) throw new TRPCError({ code: 'TOO_MANY_REQUESTS', message: 'Слишком много запросов на создание пользователей. Попробуйте позже.' })
    try {
      const result = await auth.api.createUser({ headers: ctx.req.headers, body: { name: input.name, email: input.email, password: input.password, role: input.role, data: { emailVerified: input.emailVerified, ...input.data } } })
      await AdminAuditLogger.logAction({ admin: ctx.user!, action: AdminActionType.USER_CREATED, targetUserEmail: input.email, details: { name: input.name, role: input.role, emailVerified: input.emailVerified } })
      return result
    } catch (error: any) {
      await AdminAuditLogger.logAction({ admin: ctx.user!, action: AdminActionType.USER_CREATED, targetUserEmail: input.email, details: { error: error.message, success: false } })
      throw new TRPCError({ code: 'BAD_REQUEST', message: error.message || 'Не удалось создать пользователя' })
    }
  }

  static async listUsers(_ctx: any, input: any) {
    try {
      const sortableFields = new Set(['createdAt', 'updatedAt', 'email', 'name', 'role'])
      const sortBy = sortableFields.has(input.sortBy) ? input.sortBy : 'createdAt'
      const where: any = {}
      if (input.search) {
        const mode = 'insensitive' as const
        const field = input.searchField
        const op = input.searchOperator
        const value = input.search
        const stringFilter = op === 'starts_with' ? { startsWith: value, mode } : op === 'ends_with' ? { endsWith: value, mode } : { contains: value, mode }
        where[field] = stringFilter
      }
      if (input.filterField === 'role' && input.filterValue) where.role = input.filterValue as any
      const total = await $prisma.user.count({ where })
      const users = await $prisma.user.findMany({ where, orderBy: { [sortBy]: input.sortDirection }, skip: input.offset, take: input.limit, select: { id: true, email: true, name: true, role: true, emailVerified: true, image: true, banned: true, banReason: true, banExpires: true, createdAt: true, updatedAt: true } })
      return { users, total }
    } catch (error: any) {
      throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: error.message || 'Не удалось получить список пользователей' })
    }
  }

  static async setUserRole(ctx: any, input: any) {
    try {
      await $prisma.user.update({ where: { id: input.userId }, data: { role: input.role as any } })
      const adminRole = input.role === 'ADMIN' ? 'admin' : 'user'
      await auth.api.setRole({ headers: ctx.req.headers, body: { userId: input.userId, role: adminRole as any } })
      return { success: true }
    } catch (error: any) {
      throw new TRPCError({ code: 'BAD_REQUEST', message: error.message || 'Не удалось изменить роль пользователя' })
    }
  }

  static async resetUserPassword(ctx: any, input: any) {
    if (!validateAdminPermissions(ctx.user, 'resetUserPassword')) throw new TRPCError({ code: 'FORBIDDEN', message: 'Недостаточно прав для сброса паролей пользователей' })
    if (!AdminRateLimiter.checkRateLimit(ctx.user!.id, 'resetUserPassword', 10, 60000)) throw new TRPCError({ code: 'TOO_MANY_REQUESTS', message: 'Слишком много запросов на сброс паролей. Попробуйте позже.' })
    try {
      const user = await $prisma.user.findUnique({ where: { id: input.userId }, select: { id: true, email: true, name: true } })
      if (!user) throw new Error('Пользователь не найден')
      await auth.api.setPassword({ headers: ctx.req.headers, body: { userId: input.userId, newPassword: input.newPassword } })
      if (input.sendByEmail) {
        // TODO: реальная отправка письма; пока логируем в dev
        console.log('📧 ОТПРАВКА НОВОГО ПАРОЛЯ:', user.email, input.newPassword)
      }
      await AdminAuditLogger.logAction({ admin: ctx.user!, action: AdminActionType.USER_UPDATED, targetUserId: input.userId, targetUserEmail: user.email, details: { action: 'password_reset', sentByEmail: input.sendByEmail } })
      return { success: true }
    } catch (error: any) {
      await AdminAuditLogger.logAction({ admin: ctx.user!, action: AdminActionType.USER_UPDATED, targetUserId: input.userId, details: { error: error.message, action: 'password_reset' }, success: false })
      throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: `Ошибка сброса пароля: ${error.message}` })
    }
  }

  static async removeUser(ctx: any, input: any) {
    if (!validateAdminPermissions(ctx.user, 'removeUser')) throw new TRPCError({ code: 'FORBIDDEN', message: 'Недостаточно прав для удаления пользователей' })
    if (!AdminRateLimiter.checkRateLimit(ctx.user!.id, 'removeUser', 5, 60000)) throw new TRPCError({ code: 'TOO_MANY_REQUESTS', message: 'Слишком много запросов на удаление. Попробуйте позже.' })
    if (input.userId === ctx.user!.id) throw new TRPCError({ code: 'BAD_REQUEST', message: 'Нельзя удалить самого себя' })
    try {
      const result = await auth.api.removeUser({ headers: ctx.req.headers, body: { userId: input.userId } })
      await AdminAuditLogger.logAction({ admin: ctx.user!, action: AdminActionType.USER_DELETED, targetUserId: input.userId })
      return result
    } catch (error: any) {
      await AdminAuditLogger.logAction({ admin: ctx.user!, action: AdminActionType.USER_DELETED, targetUserId: input.userId, details: { error: error.message, success: false } })
      throw new TRPCError({ code: 'BAD_REQUEST', message: error.message || 'Не удалось удалить пользователя' })
    }
  }

  static async banUser(ctx: any, input: any) {
    if (!validateAdminPermissions(ctx.user, 'banUser')) throw new TRPCError({ code: 'FORBIDDEN', message: 'Недостаточно прав для блокировки пользователей' })
    if (!AdminRateLimiter.checkRateLimit(ctx.user!.id, 'banUser', 20, 60000)) throw new TRPCError({ code: 'TOO_MANY_REQUESTS', message: 'Слишком много запросов на блокировку. Попробуйте позже.' })
    if (input.userId === ctx.user!.id) throw new TRPCError({ code: 'BAD_REQUEST', message: 'Нельзя заблокировать самого себя' })
    try {
      await $prisma.user.update({ where: { id: input.userId }, data: { banned: true, banReason: input.banReason, banExpires: input.banExpiresIn ? new Date(Date.now() + input.banExpiresIn * 1000) : null } })
      const result = await auth.api.banUser({ headers: ctx.req.headers, body: { userId: input.userId, banReason: input.banReason, banExpiresIn: input.banExpiresIn } })
      await AdminAuditLogger.logAction({ admin: ctx.user!, action: AdminActionType.USER_BANNED, targetUserId: input.userId, details: { banReason: input.banReason, banExpiresIn: input.banExpiresIn } })
      return result
    } catch (error: any) {
      await AdminAuditLogger.logAction({ admin: ctx.user!, action: AdminActionType.USER_BANNED, targetUserId: input.userId, details: { error: error.message, success: false } })
      throw new TRPCError({ code: 'BAD_REQUEST', message: error.message || 'Не удалось заблокировать пользователя' })
    }
  }

  static async unbanUser(ctx: any, input: any) {
    if (!validateAdminPermissions(ctx.user, 'unbanUser')) throw new TRPCError({ code: 'FORBIDDEN', message: 'Недостаточно прав для разблокировки пользователей' })
    if (!AdminRateLimiter.checkRateLimit(ctx.user!.id, 'unbanUser', 20, 60000)) throw new TRPCError({ code: 'TOO_MANY_REQUESTS', message: 'Слишком много запросов на разблокировку. Попробуйте позже.' })
    try {
      await $prisma.user.update({ where: { id: input.userId }, data: { banned: false, banReason: null, banExpires: null } })
      const result = await auth.api.unbanUser({ headers: ctx.req.headers, body: { userId: input.userId } })
      await AdminAuditLogger.logAction({ admin: ctx.user!, action: AdminActionType.USER_UNBANNED, targetUserId: input.userId })
      return result
    } catch (error: any) {
      await AdminAuditLogger.logAction({ admin: ctx.user!, action: AdminActionType.USER_UNBANNED, targetUserId: input.userId, details: { error: error.message, success: false } })
      throw new TRPCError({ code: 'BAD_REQUEST', message: error.message || 'Не удалось разблокировать пользователя' })
    }
  }

  static async listUserSessions(ctx: any, input: any) {
    if (!validateAdminPermissions(ctx.user, 'listUserSessions')) throw new TRPCError({ code: 'FORBIDDEN', message: 'Недостаточно прав' })
    try {
      const sessions = await $prisma.session.findMany({ where: { userId: input.userId }, orderBy: { createdAt: 'desc' }, select: { token: true, createdAt: true, expiresAt: true, ipAddress: true, userAgent: true } })
      return { sessions }
    } catch (error: any) {
      throw new TRPCError({ code: 'BAD_REQUEST', message: error.message || 'Не удалось получить список сессий' })
    }
  }

  static async revokeUserSession(ctx: any, input: any) {
    try {
      const result = await auth.api.revokeUserSession({ headers: ctx.req.headers, body: { sessionToken: input.sessionToken } })
      return result
    } catch (error: any) {
      throw new TRPCError({ code: 'BAD_REQUEST', message: error.message || 'Не удалось отозвать сессию' })
    }
  }

  static async revokeAllUserSessions(ctx: any, input: any) {
    try {
      const result = await auth.api.revokeUserSessions({ headers: ctx.req.headers, body: { userId: input.userId } })
      return result
    } catch (error: any) {
      throw new TRPCError({ code: 'BAD_REQUEST', message: error.message || 'Не удалось отозвать сессии пользователя' })
    }
  }

  static async impersonateUser(ctx: any, input: any) {
    if (!validateAdminPermissions(ctx.user, 'impersonateUser')) throw new TRPCError({ code: 'FORBIDDEN', message: 'Недостаточно прав для входа от имени пользователей' })
    if (!AdminRateLimiter.checkRateLimit(ctx.user!.id, 'impersonateUser', 5, 60000)) throw new TRPCError({ code: 'TOO_MANY_REQUESTS', message: 'Слишком много попыток входа от имени пользователей. Попробуйте позже.' })
    if (input.userId === ctx.user!.id) throw new TRPCError({ code: 'BAD_REQUEST', message: 'Нельзя войти от своего имени' })
    try {
      const result = await auth.api.impersonateUser({ headers: ctx.req.headers, body: { userId: input.userId } })
      await AdminAuditLogger.logAction({ admin: ctx.user!, action: AdminActionType.USER_IMPERSONATED, targetUserId: input.userId, details: { timestamp: new Date().toISOString() } })
      return result
    } catch (error: any) {
      await AdminAuditLogger.logAction({ admin: ctx.user!, action: AdminActionType.USER_IMPERSONATED, targetUserId: input.userId, details: { error: error.message, success: false } })
      throw new TRPCError({ code: 'BAD_REQUEST', message: error.message || 'Не удалось войти от имени пользователя' })
    }
  }

  static async stopImpersonating(ctx: any) {
    try {
      const result = await auth.api.stopImpersonating({ headers: ctx.req.headers })
      return result
    } catch (error: any) {
      throw new TRPCError({ code: 'BAD_REQUEST', message: error.message || 'Не удалось выйти из режима имперсонации' })
    }
  }

  static async hasPermission(ctx: any, input: any) {
    try {
      const result = await auth.api.hasPermission({ headers: ctx.req.headers, body: { permissions: (input.permission || input.permissions) as Record<string, string[]>, userId: input.userId, role: input.role } })
      return result
    } catch (error: any) {
      throw new TRPCError({ code: 'BAD_REQUEST', message: error.message || 'Не удалось проверить права доступа' })
    }
  }

  static async getAuditLogs(ctx: any, input: any) {
    if (!validateAdminPermissions(ctx.user, 'getAuditLogs')) throw new TRPCError({ code: 'FORBIDDEN', message: 'Недостаточно прав для просмотра журнала аудита' })
    try {
      const result = await AdminAuditLogger.getAdminLogs({
        adminId: input.adminId,
        action: input.action as AdminActionType,
        targetUserId: input.targetUserId,
        targetUserEmail: input.targetUserEmail,
        search: input.search,
        searchField: input.searchField,
        success: input.success,
        dateFrom: input.dateFrom,
        dateTo: input.dateTo,
        limit: input.limit,
        offset: input.offset,
        sortBy: input.sortBy,
        sortDirection: input.sortDirection,
      })
      return result
    } catch (error: any) {
      throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: error.message || 'Не удалось получить журнал аудита' })
    }
  }

  static async getAuditStats(ctx: any, input: any) {
    if (!validateAdminPermissions(ctx.user, 'getAuditStats')) throw new TRPCError({ code: 'FORBIDDEN', message: 'Недостаточно прав для просмотра статистики аудита' })
    try {
      const result = await AdminAuditLogger.getAdminStats(input.adminId)
      return result
    } catch (error: any) {
      throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: error.message || 'Не удалось получить статистику аудита' })
    }
  }
}


