import{u as O}from"./useToast.DbdIHNOo.js";import{u as V,C as L}from"./ConfirmDialog.B2pd8YuJ.js";import U from"./Button.oEwD-lSq.js";import{S as P}from"./SecondaryButton.DjJyItVx.js";import{D as M,s as j}from"./index.xFbNStuK.js";import{D as z}from"./Dialog.Dcz8Sg9i.js";import R from"./Toast.SYHAzbvX.js";import{c as N}from"./index.D6LCJW96.js";import{s as B,_ as F,p as W}from"./_plugin-vue_export-helper.BX07OBiL.js";import{U as q,m as l,c as m,b as f,o as s,g as C,V as v,ag as y,a as n,F as x,r as k,e as o,l as w,d as _,k as G,j as p,n as H}from"./index.BglzLLgy.js";import{n as A,t as I,b as J,r as h}from"./reactivity.esm-bundler.D5IypM4U.js";import{T as K}from"./ThemeToggle.BngcXAj7.js";import"./index.CzaMXvxd.js";import"./index.CKgBWlpe.js";import"./index.DQmIdHOH.js";import"./index.B_yc9D3m.js";import"./index.CRcBj2l1.js";import"./DangerButton.PrtrqpTW.js";import"./createLucideIcon.3yDVQAYz.js";import"./index.BHZvt6Rq.js";import"./index.Tc5ZRw49.js";import"./index.CJuyVe3p.js";import"./index.CLs7nh7g.js";import"./index.D7_1DwEX.js";import"./index.CLkTvMlq.js";import"./index.irlSZ_18.js";import"./index.B0GjtQuk.js";import"./runtime-dom.esm-bundler.C-dfRCGi.js";import"./index.CXDqTVvt.js";import"./index.BksvaNwr.js";import"./index.BsVcoVHU.js";/* empty css                                */import"./check.BMRaCSED.js";var Q=`
    .p-breadcrumb {
        background: dt('breadcrumb.background');
        padding: dt('breadcrumb.padding');
        overflow-x: auto;
    }

    .p-breadcrumb-list {
        margin: 0;
        padding: 0;
        list-style-type: none;
        display: flex;
        align-items: center;
        flex-wrap: nowrap;
        gap: dt('breadcrumb.gap');
    }

    .p-breadcrumb-separator {
        display: flex;
        align-items: center;
        color: dt('breadcrumb.separator.color');
    }

    .p-breadcrumb-separator-icon:dir(rtl) {
        transform: rotate(180deg);
    }

    .p-breadcrumb::-webkit-scrollbar {
        display: none;
    }

    .p-breadcrumb-item-link {
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: dt('breadcrumb.item.gap');
        transition:
            background dt('breadcrumb.transition.duration'),
            color dt('breadcrumb.transition.duration'),
            outline-color dt('breadcrumb.transition.duration'),
            box-shadow dt('breadcrumb.transition.duration');
        border-radius: dt('breadcrumb.item.border.radius');
        outline-color: transparent;
        color: dt('breadcrumb.item.color');
    }

    .p-breadcrumb-item-link:focus-visible {
        box-shadow: dt('breadcrumb.item.focus.ring.shadow');
        outline: dt('breadcrumb.item.focus.ring.width') dt('breadcrumb.item.focus.ring.style') dt('breadcrumb.item.focus.ring.color');
        outline-offset: dt('breadcrumb.item.focus.ring.offset');
    }

    .p-breadcrumb-item-link:hover .p-breadcrumb-item-label {
        color: dt('breadcrumb.item.hover.color');
    }

    .p-breadcrumb-item-label {
        transition: inherit;
    }

    .p-breadcrumb-item-icon {
        color: dt('breadcrumb.item.icon.color');
        transition: inherit;
    }

    .p-breadcrumb-item-link:hover .p-breadcrumb-item-icon {
        color: dt('breadcrumb.item.icon.hover.color');
    }
`,X={root:"p-breadcrumb p-component",list:"p-breadcrumb-list",homeItem:"p-breadcrumb-home-item",separator:"p-breadcrumb-separator",separatorIcon:"p-breadcrumb-separator-icon",item:function(t){var r=t.instance;return["p-breadcrumb-item",{"p-disabled":r.disabled()}]},itemLink:"p-breadcrumb-item-link",itemIcon:"p-breadcrumb-item-icon",itemLabel:"p-breadcrumb-item-label"},Y=q.extend({name:"breadcrumb",style:Q,classes:X}),Z={name:"BaseBreadcrumb",extends:B,props:{model:{type:Array,default:null},home:{type:null,default:null}},style:Y,provide:function(){return{$pcBreadcrumb:this,$parentInstance:this}}},S={name:"BreadcrumbItem",hostName:"Breadcrumb",extends:B,props:{item:null,templates:null,index:null},methods:{onClick:function(t){this.item.command&&this.item.command({originalEvent:t,item:this.item})},visible:function(){return typeof this.item.visible=="function"?this.item.visible():this.item.visible!==!1},disabled:function(){return typeof this.item.disabled=="function"?this.item.disabled():this.item.disabled},label:function(){return typeof this.item.label=="function"?this.item.label():this.item.label},isCurrentUrl:function(){var t=this.item,r=t.to,u=t.url,c=typeof window<"u"?window.location.pathname:"";return r===c||u===c?"page":void 0}},computed:{ptmOptions:function(){return{context:{item:this.item,index:this.index}}},getMenuItemProps:function(){var t=this;return{action:l({class:this.cx("itemLink"),"aria-current":this.isCurrentUrl(),onClick:function(u){return t.onClick(u)}},this.ptm("itemLink",this.ptmOptions)),icon:l({class:[this.cx("icon"),this.item.icon]},this.ptm("icon",this.ptmOptions)),label:l({class:this.cx("label")},this.ptm("label",this.ptmOptions))}}}},$=["href","target","aria-current"];function u4(e,t,r,u,c,a){return a.visible()?(s(),m("li",l({key:0,class:[e.cx("item"),r.item.class]},e.ptm("item",a.ptmOptions)),[r.templates.item?(s(),C(v(r.templates.item),{key:1,item:r.item,label:a.label(),props:a.getMenuItemProps},null,8,["item","label","props"])):(s(),m("a",l({key:0,href:r.item.url||"#",class:e.cx("itemLink"),target:r.item.target,"aria-current":a.isCurrentUrl(),onClick:t[0]||(t[0]=function(){return a.onClick&&a.onClick.apply(a,arguments)})},e.ptm("itemLink",a.ptmOptions)),[r.templates&&r.templates.itemicon?(s(),C(v(r.templates.itemicon),{key:0,item:r.item,class:A(e.cx("itemIcon",a.ptmOptions))},null,8,["item","class"])):r.item.icon?(s(),m("span",l({key:1,class:[e.cx("itemIcon"),r.item.icon]},e.ptm("itemIcon",a.ptmOptions)),null,16)):f("",!0),r.item.label?(s(),m("span",l({key:2,class:e.cx("itemLabel")},e.ptm("itemLabel",a.ptmOptions)),I(a.label()),17)):f("",!0)],16,$))],16)):f("",!0)}S.render=u4;var T={name:"Breadcrumb",extends:Z,inheritAttrs:!1,components:{BreadcrumbItem:S,ChevronRightIcon:N}};function e4(e,t,r,u,c,a){var i=y("BreadcrumbItem"),d=y("ChevronRightIcon");return s(),m("nav",l({class:e.cx("root")},e.ptmi("root")),[n("ol",l({class:e.cx("list")},e.ptm("list")),[e.home?(s(),C(i,l({key:0,item:e.home,class:e.cx("homeItem"),templates:e.$slots,pt:e.pt,unstyled:e.unstyled},e.ptm("homeItem")),null,16,["item","class","templates","pt","unstyled"])):f("",!0),(s(!0),m(x,null,k(e.model,function(b,g){return s(),m(x,{key:b.label+"_"+g},[e.home||g!==0?(s(),m("li",l({key:0,class:e.cx("separator")},{ref_for:!0},e.ptm("separator")),[w(e.$slots,"separator",{},function(){return[o(d,l({"aria-hidden":"true",class:e.cx("separatorIcon")},{ref_for:!0},e.ptm("separatorIcon")),null,16,["class"])]})],16)):f("",!0),o(i,{item:b,index:g,templates:e.$slots,pt:e.pt,unstyled:e.unstyled},null,8,["item","index","templates","pt","unstyled"])],64)}),128))],16)],16)}T.render=e4;const t4=_({__name:"Breadcrumb",setup(e,{expose:t}){t();const u={theme:h({root:"bg-surface-0 dark:bg-surface-900 p-4 overflow-x-auto",list:"m-0 p-0 list-none flex items-center flex-nowrap gap-2",item:"",itemLink:`no-underline flex items-center gap-2 transition-colors duration-200 rounded-md
        text-surface-500 dark:text-surface-400 hover:text-surface-700 dark:hover:text-surface-0
        focus-visible:outline focus-visible:outline-1 focus-visible:outline-offset-2 focus-visible:outline-primary`,itemIcon:"",itemLabel:"",separator:"flex items-center text-surface-400 dark:text-surface-500",separatorIcon:""}),get Breadcrumb(){return T},get ptViewMerge(){return W}};return Object.defineProperty(u,"__isScriptSetup",{enumerable:!1,value:!0}),u}});function r4(e,t,r,u,c,a){return s(),C(u.Breadcrumb,{unstyled:"",pt:u.theme,ptOptions:{mergeProps:u.ptViewMerge}},G({_:2},[k(e.$slots,(i,d)=>({name:d,fn:p(b=>[w(e.$slots,d,J(H(b??{})))])}))]),1032,["pt","ptOptions"])}const o4=F(t4,[["render",r4]]),n4=_({__name:"UIDemo",setup(e,{expose:t}){t();const r=O(),u=V(),c=h(!1),a=h([{label:"Админ панель",url:"/admin"},{label:"UI Компоненты"}]),i=h([{id:1,name:"Сальник коленвала",category:"Двигатель",status:"Активен"},{id:2,name:"Фильтр масляный",category:"Система смазки",status:"Активен"},{id:3,name:"Прокладка ГБЦ",category:"Двигатель",status:"Неактивен"},{id:4,name:"Тормозные колодки",category:"Тормозная система",status:"Ожидание"},{id:5,name:"Амортизатор передний",category:"Подвеска",status:"Активен"},{id:6,name:"Свеча зажигания",category:"Система зажигания",status:"Активен"},{id:7,name:"Ремень ГРМ",category:"Двигатель",status:"Неактивен"},{id:8,name:"Радиатор охлаждения",category:"Система охлаждения",status:"Активен"}]),E={toast:r,confirm:u,dialogVisible:c,breadcrumbItems:a,tableData:i,showSuccessToast:()=>{r.success("Успешно!","Операция выполнена успешно")},showInfoToast:()=>{r.info("Информация","Это информационное сообщение")},showWarnToast:()=>{r.warn("Внимание!","Это предупреждение")},showErrorToast:()=>{r.error("Ошибка!","Произошла ошибка при выполнении операции")},showDialog:()=>{c.value=!0},showDeleteConfirm:()=>{u.confirmDelete("запись",()=>{r.success("Удалено","Запись успешно удалена")})},showSaveConfirm:()=>{u.confirmSave("Сохранить изменения?",()=>{r.success("Сохранено","Изменения успешно сохранены")})},editItem:D=>{r.info("Редактирование",`Редактирование записи: ${D.name}`)},deleteItem:D=>{u.confirmDelete(`запись "${D.name}"`,()=>{r.success("Удалено",`Запись "${D.name}" успешно удалена`)})},Button:U,SecondaryButton:P,DataTable:M,get Column(){return j},Dialog:z,Toast:R,ConfirmDialog:L,Breadcrumb:o4,ThemeToggle:K};return Object.defineProperty(E,"__isScriptSetup",{enumerable:!1,value:!0}),E}}),a4={class:"space-y-8"},i4={class:"bg-[--color-card] rounded-[--radius-lg] p-6 border border-[--color-border]"},s4={class:"bg-[--color-card] rounded-[--radius-lg] p-6 border border-[--color-border]"},l4={class:"flex flex-wrap gap-4 items-center"},c4={class:"bg-[--color-card] rounded-[--radius-lg] p-6 border border-[--color-border]"},m4={class:"flex flex-wrap gap-4"},d4={class:"bg-[--color-card] rounded-[--radius-lg] p-6 border border-[--color-border]"},b4={class:"flex flex-wrap gap-4"},p4={class:"bg-[--color-card] rounded-[--radius-lg] p-6 border border-[--color-border]"},f4={class:"flex gap-2"},g4={class:"flex justify-end gap-2"};function D4(e,t,r,u,c,a){return s(),m("div",a4,[t[9]||(t[9]=n("div",{class:"mb-8"},[n("h1",{class:"text-3xl font-bold text-[--color-foreground] mb-2"},"UI Компоненты"),n("p",{class:"text-[--color-muted]"}," Демонстрация всех доступных UI компонентов с поддержкой тем ")],-1)),n("div",i4,[t[3]||(t[3]=n("h2",{class:"text-xl font-semibold text-[--color-foreground] mb-4"},"Breadcrumb",-1)),o(u.Breadcrumb,{model:u.breadcrumbItems},null,8,["model"])]),n("div",s4,[t[4]||(t[4]=n("h2",{class:"text-xl font-semibold text-[--color-foreground] mb-4"},"Кнопки и переключатель тем",-1)),n("div",l4,[o(u.Button,{label:"Основная кнопка"}),o(u.SecondaryButton,{label:"Вторичная кнопка"}),o(u.SecondaryButton,{label:"С иконкой",icon:"pi pi-plus"}),o(u.ThemeToggle,{mode:"buttons","show-label":""})])]),n("div",c4,[t[5]||(t[5]=n("h2",{class:"text-xl font-semibold text-[--color-foreground] mb-4"},"Toast уведомления",-1)),n("div",m4,[o(u.SecondaryButton,{label:"Успех",onClick:u.showSuccessToast,class:"bg-[--p-success-color] text-[--p-success-contrast-color] hover:bg-[color-mix(in_srgb,var(--p-success-color),black_10%)]"}),o(u.SecondaryButton,{label:"Информация",onClick:u.showInfoToast,class:"bg-[--p-primary-color] text-[--p-primary-contrast-color] hover:bg-[color-mix(in_srgb,var(--p-primary-color),black_10%)]"}),o(u.SecondaryButton,{label:"Предупреждение",onClick:u.showWarnToast,class:"bg-[--p-warning-color] text-[--p-warning-contrast-color] hover:bg-[color-mix(in_srgb,var(--p-warning-color),black_10%)]"}),o(u.SecondaryButton,{label:"Ошибка",onClick:u.showErrorToast,class:"bg-[--p-danger-color] text-[--p-danger-contrast-color] hover:bg-[color-mix(in_srgb,var(--p-danger-color),black_10%)]"})])]),n("div",d4,[t[6]||(t[6]=n("h2",{class:"text-xl font-semibold text-[--color-foreground] mb-4"},"Диалоги",-1)),n("div",b4,[o(u.SecondaryButton,{label:"Обычный диалог",onClick:u.showDialog}),o(u.SecondaryButton,{label:"Подтверждение удаления",onClick:u.showDeleteConfirm,class:"bg-[--p-danger-color] text-[--p-danger-contrast-color] hover:bg-[color-mix(in_srgb,var(--p-danger-color),black_10%)]"}),o(u.SecondaryButton,{label:"Подтверждение сохранения",onClick:u.showSaveConfirm,class:"bg-[--p-success-color] text-[--p-success-contrast-color] hover:bg-[color-mix(in_srgb,var(--p-success-color),black_10%)]"})])]),n("div",p4,[t[7]||(t[7]=n("h2",{class:"text-xl font-semibold text-[--color-foreground] mb-4"},"DataTable",-1)),o(u.DataTable,{value:u.tableData,paginator:"",rows:5,rowsPerPageOptions:[5,10,20],tableStyle:"min-width: 50rem"},{default:p(()=>[o(u.Column,{field:"id",header:"ID",sortable:"",style:{width:"10%"}}),o(u.Column,{field:"name",header:"Название",sortable:"",style:{width:"30%"}}),o(u.Column,{field:"category",header:"Категория",sortable:"",style:{width:"25%"}}),o(u.Column,{field:"status",header:"Статус",style:{width:"15%"}},{body:p(i=>[n("span",{class:A({"px-2 py-1 rounded-full text-xs bg-[color-mix(in_srgb,var(--p-success-color),transparent_85%)] text-[--p-success-color]":i.data.status==="Активен","px-2 py-1 rounded-full text-xs bg-[color-mix(in_srgb,var(--p-danger-color),transparent_85%)] text-[--p-danger-color]":i.data.status==="Неактивен","px-2 py-1 rounded-full text-xs bg-[color-mix(in_srgb,var(--p-warning-color),transparent_85%)] text-[--p-warning-color]":i.data.status==="Ожидание"})},I(i.data.status),3)]),_:1}),o(u.Column,{header:"Действия",style:{width:"20%"}},{body:p(i=>[n("div",f4,[o(u.SecondaryButton,{icon:"pi pi-pencil",text:"",size:"small",onClick:d=>u.editItem(i.data)},null,8,["onClick"]),o(u.SecondaryButton,{icon:"pi pi-trash",text:"",size:"small",class:"text-red-500 hover:text-red-700",onClick:d=>u.deleteItem(i.data)},null,8,["onClick"])])]),_:1})]),_:1},8,["value"])]),o(u.Dialog,{visible:u.dialogVisible,"onUpdate:visible":t[2]||(t[2]=i=>u.dialogVisible=i),modal:"",header:"Пример диалога",style:{width:"25rem"}},{default:p(()=>[t[8]||(t[8]=n("p",{class:"text-[--color-muted] mb-4"}," Это пример диалогового окна с поддержкой тем. ",-1)),n("div",g4,[o(u.SecondaryButton,{label:"Отмена",onClick:t[0]||(t[0]=i=>u.dialogVisible=!1)}),o(u.Button,{label:"Сохранить",onClick:t[1]||(t[1]=i=>u.dialogVisible=!1)})])]),_:1,__:[8]},8,["visible"]),o(u.Toast),o(u.ConfirmDialog)])}const ru=F(n4,[["render",D4]]);export{ru as default};
