import { ref, computed } from 'vue';
import { t as trpc } from './trpc_B_6Rt2Mk.mjs';
import { u as useToast } from './useToast_CXW6AFGp.mjs';

const globalErrors = ref([]);
const maxErrors = 50;
const useErrorHandler = () => {
  const toast = useToast();
  const generateErrorId = () => {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  };
  const createError = (code, message, details, field) => {
    return {
      id: generateErrorId(),
      code,
      message,
      details,
      field,
      timestamp: /* @__PURE__ */ new Date()
    };
  };
  const addToGlobalErrors = (error) => {
    globalErrors.value.unshift(error);
    if (globalErrors.value.length > maxErrors) {
      globalErrors.value = globalErrors.value.slice(0, maxErrors);
    }
  };
  const handleTRPCError = (err, showToast = true) => {
    console.error("tRPC Error:", err);
    const code = err?.data?.code || err?.shape?.code || err?.name || "UNKNOWN_ERROR";
    const zodErrors = err?.data?.zodError?.fieldErrors || err?.zodError?.fieldErrors;
    let message = err?.message;
    if (!message) {
      switch (code) {
        case "UNAUTHORIZED":
          message = "Требуется авторизация";
          break;
        case "FORBIDDEN":
          message = "Недостаточно прав для выполнения операции";
          break;
        case "NOT_FOUND":
          message = "Ресурс не найден";
          break;
        case "BAD_REQUEST":
          message = "Некорректный запрос";
          break;
        case "CONFLICT":
          message = "Конфликт данных";
          break;
        case "PRECONDITION_FAILED":
          message = "Нарушены условия выполнения операции";
          break;
        case "INTERNAL_SERVER_ERROR":
          message = "Внутренняя ошибка сервера";
          break;
        case "TIMEOUT":
          message = "Превышено время ожидания";
          break;
        default:
          message = "Произошла ошибка при выполнении запроса";
      }
    }
    if (zodErrors && typeof zodErrors === "object") {
      const validationDetails = Object.entries(zodErrors).flatMap(
        ([field, issues]) => (Array.isArray(issues) ? issues : [issues]).filter(Boolean).map((issue) => `${field}: ${issue}`)
      ).slice(0, 5).join("\n");
      if (validationDetails) {
        message = `Ошибка валидации:
${validationDetails}`;
      }
    }
    const error = {
      ...createError(code, message, err),
      trpcCode: code,
      zodErrors
    };
    addToGlobalErrors(error);
    if (showToast) {
      toast.error("Ошибка", message);
    }
    return error;
  };
  const handleNetworkError = (err, url, showToast = true) => {
    console.error("Network Error:", err);
    const status = err?.status || err?.response?.status || 0;
    const message = err?.message || "Ошибка сети";
    const retryable = status >= 500 || status === 0 || status === 408 || status === 429;
    const error = {
      ...createError("NETWORK_ERROR", message, err),
      status,
      retryable,
      url
    };
    addToGlobalErrors(error);
    if (showToast) {
      if (status === 0) {
        toast.error("Ошибка сети", "Проверьте подключение к интернету");
      } else if (status >= 500) {
        toast.error("Ошибка сервера", "Попробуйте повторить запрос позже");
      } else {
        toast.error("Ошибка сети", message);
      }
    }
    return error;
  };
  const handleValidationError = (field, value, constraint, showToast = true) => {
    const message = `Поле "${field}": ${constraint}`;
    const error = {
      ...createError("VALIDATION_ERROR", message, { value, constraint }, field),
      field,
      value,
      constraint
    };
    addToGlobalErrors(error);
    if (showToast) {
      toast.warn("Ошибка валидации", message);
    }
    return error;
  };
  const handleGenericError = (err, context, showToast = true) => {
    console.error("Generic Error:", err);
    const message = err?.message || "Произошла неизвестная ошибка";
    const code = err?.code || err?.name || "GENERIC_ERROR";
    const error = createError(
      code,
      context ? `${context}: ${message}` : message,
      err
    );
    addToGlobalErrors(error);
    if (showToast) {
      toast.error("Ошибка", error.message);
    }
    return error;
  };
  const handleError = (err, options = {}) => {
    const { context, showToast = true, url } = options;
    if (err?.data?.code || err?.shape?.code) {
      return handleTRPCError(err, showToast);
    }
    if (err?.status || err?.response?.status) {
      return handleNetworkError(err, url, showToast);
    }
    return handleGenericError(err, context, showToast);
  };
  const clearErrors = () => {
    globalErrors.value = [];
  };
  const removeError = (errorId) => {
    const index = globalErrors.value.findIndex((err) => err.id === errorId);
    if (index !== -1) {
      globalErrors.value.splice(index, 1);
    }
  };
  const getErrorsByType = (code) => {
    return globalErrors.value.filter((err) => err.code === code);
  };
  const getRecentErrors = (count = 10) => {
    return globalErrors.value.slice(0, count);
  };
  const hasCriticalErrors = computed(() => {
    const criticalCodes = ["INTERNAL_SERVER_ERROR", "UNAUTHORIZED", "FORBIDDEN"];
    return globalErrors.value.some((err) => criticalCodes.includes(err.code));
  });
  const errorStats = computed(() => {
    const stats = {};
    globalErrors.value.forEach((err) => {
      stats[err.code] = (stats[err.code] || 0) + 1;
    });
    return stats;
  });
  const showSaveError = (entityName = "Запись", error) => {
    if (error) {
      handleError(error, { context: `Сохранение ${entityName.toLowerCase()}` });
    } else {
      toast.error("Ошибка сохранения", `Не удалось сохранить ${entityName.toLowerCase()}`);
    }
  };
  const showDeleteError = (entityName = "Запись", error) => {
    if (error) {
      handleError(error, { context: `Удаление ${entityName.toLowerCase()}` });
    } else {
      toast.error("Ошибка удаления", `Не удалось удалить ${entityName.toLowerCase()}`);
    }
  };
  const showLoadError = (entityName = "Данные", error) => {
    if (error) {
      handleError(error, { context: `Загрузка ${entityName.toLowerCase()}` });
    } else {
      toast.error("Ошибка загрузки", `Не удалось загрузить ${entityName.toLowerCase()}`);
    }
  };
  return {
    // Состояние
    errors: computed(() => globalErrors.value),
    hasCriticalErrors,
    errorStats,
    // Основные методы обработки
    handleError,
    handleTRPCError,
    handleNetworkError,
    handleValidationError,
    handleGenericError,
    // Управление ошибками
    clearErrors,
    removeError,
    getErrorsByType,
    getRecentErrors,
    // Удобные методы
    showSaveError,
    showDeleteError,
    showLoadError
  };
};

function useTrpc() {
  const loading = ref(false);
  const error = ref(null);
  const errorHandler = useErrorHandler();
  const emitToast = (payload) => {
    if (typeof window !== "undefined") {
      window.dispatchEvent(new CustomEvent("app:toast", { detail: payload }));
    }
  };
  const handleError = (err) => {
    const trpcError = errorHandler.handleTRPCError(err, false);
    error.value = trpcError.message;
    emitToast({ severity: "error", summary: "Ошибка", detail: trpcError.message });
  };
  const clearError = () => {
    error.value = null;
  };
  const execute = async (operation, opts) => {
    try {
      loading.value = true;
      clearError();
      const result = await operation();
      if (opts?.success) {
        emitToast({ severity: "success", summary: opts.success.title ?? "Успешно", detail: opts.success.message });
      }
      return result;
    } catch (err) {
      handleError(err);
      return null;
    } finally {
      loading.value = false;
    }
  };
  return {
    // Состояние
    loading: computed(() => loading.value),
    error: computed(() => error.value),
    // Методы
    clearError,
    execute,
    // tRPC клиент
    client: trpc,
    // Админ — вызывать напрямую через trpc в компонентах (см. AdminDashboard)
    // Удобные методы для работы с основными сущностями
    parts: {
      // Получить все части
      findMany: (input) => execute(() => trpc.crud.part.findMany.query(input)),
      // Получить часть по ID
      findUnique: (input) => execute(() => trpc.crud.part.findUnique.query(input)),
      // Создать новую часть
      create: (input) => execute(() => trpc.crud.part.create.mutate(input), { success: { title: "Сохранено", message: "Запчасть создана" } }),
      // Обновить часть
      update: (input) => execute(() => trpc.crud.part.update.mutate(input), { success: { title: "Сохранено", message: "Запчасть обновлена" } }),
      // Удалить часть
      delete: (input) => execute(() => trpc.crud.part.delete.mutate(input), { success: { title: "Удалено", message: "Запчасть удалена" } })
    },
    catalogItems: {
      // Получить все каталожные позиции
      findMany: (input) => execute(() => trpc.crud.catalogItem.findMany.query(input)),
      // Получить каталожную позицию по ID
      findUnique: (input) => execute(() => trpc.crud.catalogItem.findUnique.query(input)),
      // Создать новую каталожную позицию
      create: (input) => execute(() => trpc.crud.catalogItem.create.mutate(input), { success: { title: "Сохранено", message: "Позиция создана" } }),
      // Обновить каталожную позицию
      update: (input) => execute(() => trpc.crud.catalogItem.update.mutate(input), { success: { title: "Сохранено", message: "Позиция обновлена" } }),
      // Удалить каталожную позицию
      delete: (input) => execute(() => trpc.crud.catalogItem.delete.mutate(input), { success: { title: "Удалено", message: "Позиция удалена" } })
    },
    matching: {
      findMatchingParts: (input) => execute(() => trpc.matching.findMatchingParts.query(input)),
      findMatchingCatalogItems: (input) => execute(() => trpc.matching.findMatchingCatalogItems.query(input)),
      proposeLink: (input) => execute(() => trpc.matching.proposeLink.mutate(input)),
      listProposals: (input) => execute(() => trpc.matching.listProposals.query(input)),
      approveProposal: (input) => execute(() => trpc.matching.approveProposal.mutate(input)),
      rejectProposal: (input) => execute(() => trpc.matching.rejectProposal.mutate(input)),
      generateProposals: (input) => execute(() => trpc.matching.generateProposals.mutate(input)),
      createPartFromItems: (input) => execute(() => trpc.matching.createPartFromItems.mutate(input))
    },
    brands: {
      // Получить все бренды
      findMany: (input) => execute(() => trpc.crud.brand.findMany.query(input)),
      // Создать новый бренд
      create: (input) => execute(() => trpc.crud.brand.create.mutate(input), { success: { title: "Сохранено", message: "Бренд создан" } }),
      // Обновить бренд
      update: (input) => execute(() => trpc.crud.brand.update.mutate(input), { success: { title: "Сохранено", message: "Бренд обновлён" } }),
      // Удалить бренд
      delete: (input) => execute(() => trpc.crud.brand.delete.mutate(input), { success: { title: "Удалено", message: "Бренд удалён" } })
    },
    partCategories: {
      // Получить все категории
      findMany: (input) => execute(() => trpc.crud.partCategory.findMany.query(input)),
      // Создать новую категорию
      create: (input) => execute(() => trpc.crud.partCategory.create.mutate(input), { success: { title: "Сохранено", message: "Категория создана" } }),
      // Обновить категорию
      update: (input) => execute(() => trpc.crud.partCategory.update.mutate(input), { success: { title: "Сохранено", message: "Категория обновлена" } }),
      // Удалить категорию
      delete: (input) => execute(() => trpc.crud.partCategory.delete.mutate(input), { success: { title: "Удалено", message: "Категория удалена" } })
    },
    media: {
      // Part: основное изображение
      uploadPartImage: (input) => execute(() => trpc.upload.uploadPartImage.mutate(input), { success: { title: "Загружено", message: "Изображение запчасти обновлено" } }),
      deletePartImage: (input) => execute(() => trpc.upload.deletePartImage.mutate(input), { success: { title: "Удалено", message: "Изображение запчасти удалено" } }),
      // Part: галерея (изображения + PDF)
      uploadPartMedia: (input) => execute(() => trpc.upload.uploadPartMedia.mutate(input), { success: { title: "Загружено", message: "Медиа добавлено в галерею запчасти" } }),
      removePartMedia: (input) => execute(() => trpc.upload.removePartMedia.mutate(input), { success: { title: "Удалено", message: "Медиа удалено из галереи запчасти" } }),
      // PartCategory: основное изображение
      uploadPartCategoryImage: (input) => execute(() => trpc.upload.uploadPartCategoryImage.mutate(input), { success: { title: "Загружено", message: "Изображение категории обновлено" } }),
      deletePartCategoryImage: (input) => execute(() => trpc.upload.deletePartCategoryImage.mutate(input), { success: { title: "Удалено", message: "Изображение категории удалено" } }),
      // CatalogItem: основное изображение
      uploadCatalogItemImage: (input) => execute(() => trpc.upload.uploadCatalogItemImage.mutate(input), { success: { title: "Загружено", message: "Изображение позиции обновлено" } }),
      deleteCatalogItemImage: (input) => execute(() => trpc.upload.deleteCatalogItemImage.mutate(input), { success: { title: "Удалено", message: "Изображение позиции удалено" } }),
      // CatalogItem: галерея (изображения + PDF)
      uploadCatalogItemMedia: (input) => execute(() => trpc.upload.uploadCatalogItemMedia.mutate(input), { success: { title: "Загружено", message: "Медиа добавлено в галерею позиции" } }),
      removeCatalogItemMedia: (input) => execute(() => trpc.upload.removeCatalogItemMedia.mutate(input), { success: { title: "Удалено", message: "Медиа удалено из галереи позиции" } })
    },
    equipmentModels: {
      // Получить все модели техники
      findMany: (input) => execute(() => trpc.crud.equipmentModel.findMany.query(input)),
      // Получить модель техники по ID
      findUnique: (input) => execute(() => trpc.crud.equipmentModel.findUnique.query(input)),
      // Создать новую модель техники
      create: (input) => execute(() => trpc.crud.equipmentModel.create.mutate(input)),
      // Обновить модель техники
      update: (input) => execute(() => trpc.crud.equipmentModel.update.mutate(input)),
      // Удалить модель техники
      delete: (input) => execute(() => trpc.crud.equipmentModel.delete.mutate(input))
    },
    // Новые методы для работы с атрибутами на основе шаблонов
    partAttributes: {
      // Получить атрибуты запчасти
      findByPartId: (input) => execute(() => trpc.partAttributes.findByPartId.query(input)),
      // Создать атрибут запчасти
      create: (input) => execute(() => trpc.partAttributes.create.mutate(input), { success: { title: "Сохранено", message: "Атрибут добавлен" } }),
      // Обновить атрибут запчасти
      update: (input) => execute(() => trpc.partAttributes.update.mutate(input), { success: { title: "Сохранено", message: "Атрибут обновлён" } }),
      // Удалить атрибут запчасти
      delete: (input) => execute(() => trpc.partAttributes.delete.mutate(input), { success: { title: "Удалено", message: "Атрибут удалён" } }),
      // Массовое создание атрибутов
      bulkCreate: (input) => execute(() => trpc.partAttributes.bulkCreate.mutate(input))
    },
    // Методы для работы с шаблонами атрибутов
    attributeTemplates: {
      // Получить все шаблоны
      findMany: (input) => execute(() => trpc.attributeTemplates.findMany.query(input)),
      // Получить шаблон по ID
      findById: (input) => execute(() => trpc.attributeTemplates.findById.query(input)),
      // Создать шаблон
      create: (input) => execute(() => trpc.attributeTemplates.create.mutate(input), { success: { title: "Сохранено", message: "Шаблон создан" } }),
      // Обновить шаблон
      update: (input) => execute(() => trpc.attributeTemplates.update.mutate(input), { success: { title: "Сохранено", message: "Шаблон обновлён" } }),
      // Удалить шаблон
      delete: (input) => execute(() => trpc.attributeTemplates.delete.mutate(input), { success: { title: "Удалено", message: "Шаблон удалён" } }),
      // Получить все группы
      findAllGroups: () => execute(() => trpc.attributeTemplates.findAllGroups.query({})),
      // Получить иерархию групп
      findGroupsHierarchy: () => execute(() => trpc.attributeTemplates.findGroupsHierarchy.query({})),
      // Создать группу
      createGroup: (input) => execute(() => trpc.attributeTemplates.createGroup.mutate(input), { success: { title: "Сохранено", message: "Группа создана" } }),
      // Обновить группу
      updateGroup: (input) => execute(() => trpc.attributeTemplates.updateGroup.mutate(input), { success: { title: "Сохранено", message: "Группа обновлена" } }),
      // Удалить группу
      deleteGroup: (input) => execute(() => trpc.attributeTemplates.deleteGroup.mutate(input), { success: { title: "Удалено", message: "Группа удалена" } })
    },
    // Методы для работы с группами синонимов атрибутов
    attributeSynonyms: {
      groups: {
        findMany: (input) => execute(() => trpc.attributeSynonyms.groups.findMany.query(input)),
        create: (input) => execute(() => trpc.attributeSynonyms.groups.create.mutate(input), { success: { title: "Сохранено", message: "Группа синонимов создана" } }),
        update: (input) => execute(() => trpc.attributeSynonyms.groups.update.mutate(input), { success: { title: "Сохранено", message: "Группа синонимов обновлена" } }),
        delete: (input) => execute(() => trpc.attributeSynonyms.groups.delete.mutate(input), { success: { title: "Удалено", message: "Группа синонимов удалена" } })
      },
      synonyms: {
        findMany: (input) => execute(() => trpc.attributeSynonyms.synonyms.findMany.query(input)),
        create: (input) => execute(() => trpc.attributeSynonyms.synonyms.create.mutate(input), { success: { title: "Сохранено", message: "Синоним добавлен" } }),
        update: (input) => execute(() => trpc.attributeSynonyms.synonyms.update.mutate(input), { success: { title: "Сохранено", message: "Синоним обновлён" } }),
        delete: (input) => execute(() => trpc.attributeSynonyms.synonyms.delete.mutate(input), { success: { title: "Удалено", message: "Синоним удалён" } })
      },
      utils: {
        findGroupByValue: (input) => execute(() => trpc.attributeSynonyms.utils.findGroupByValue.query(input)),
        findGroupSynonymsByValue: (input) => execute(() => trpc.attributeSynonyms.utils.findGroupSynonymsByValue.query(input))
      }
    },
    // NOTE: removed duplicate short matching section; full API is provided above
    partApplicability: {
      upsert: (input) => execute(() => trpc.crud.partApplicability.upsert.mutate(input), { success: { title: "Сохранено", message: "Применимость обновлена" } }),
      findMany: (input) => execute(() => trpc.crud.partApplicability.findMany.query(input)),
      create: (input) => execute(() => trpc.crud.partApplicability.create.mutate(input)),
      update: (input) => execute(() => trpc.crud.partApplicability.update.mutate(input)),
      findFirst: (input) => execute(() => trpc.crud.partApplicability.findFirst.query(input)),
      delete: (input) => execute(() => trpc.crud.partApplicability.delete.mutate(input), { success: { title: "Удалено", message: "Связь с группой удалена" } })
    },
    equipmentApplicability: {
      upsert: (input) => execute(() => trpc.crud.equipmentApplicability.upsert.mutate(input)),
      findMany: (input) => execute(() => trpc.crud.equipmentApplicability.findMany.query(input)),
      create: (input) => execute(() => trpc.crud.equipmentApplicability.create.mutate(input), { success: { title: "Сохранено", message: "Применимость техники добавлена" } }),
      update: (input) => execute(() => trpc.crud.equipmentApplicability.update.mutate(input), { success: { title: "Сохранено", message: "Применимость техники обновлена" } }),
      findFirst: (input) => execute(() => trpc.crud.equipmentApplicability.findFirst.query(input))
    },
    // Импорт/Экспорт Excel
    importExport: {
      exportXlsx: (input) => execute(() => trpc.importExport.exportXlsx.mutate(input)),
      exportTemplate: (input) => execute(() => trpc.importExport.exportTemplate.mutate(input))
    },
    excelImport: {
      dryRun: (input) => execute(() => trpc.import.dryRun.mutate(input)),
      execute: (input) => execute(() => trpc.import.execute.mutate(input))
    }
  };
}

export { useTrpc as u };
