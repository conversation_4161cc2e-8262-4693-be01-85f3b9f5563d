/**
 * Composable для работы с ErrorBoundary компонентами
 * Предоставляет утилиты для обработки ошибок и восстановления состояния
 */

import { ref, type Ref } from 'vue'

export interface ErrorBoundaryState {
  hasError: boolean
  error: Error | null
  errorInfo: string | null
  retryCount: number
}

export const useErrorBoundary = () => {
  const state: Ref<ErrorBoundaryState> = ref({
    hasError: false,
    error: null,
    errorInfo: null,
    retryCount: 0
  })

  const captureError = (error: Error, errorInfo?: string) => {
    state.value = {
      hasError: true,
      error,
      errorInfo: errorInfo || error.stack || error.message,
      retryCount: state.value.retryCount
    }

    // Log error in development
    if (import.meta.env.DEV) {
      console.error('ErrorBoundary captured error:', error)
      if (errorInfo) {
        console.error('Error info:', errorInfo)
      }
    }

    // Report error to monitoring service in production
    if (import.meta.env.PROD) {
      // TODO: Integrate with error monitoring service (e.g., Sentry)
      // reportError(error, errorInfo)
    }
  }

  const retry = () => {
    state.value = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: state.value.retryCount + 1
    }
  }

  const reset = () => {
    state.value = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0
    }
  }

  const shouldShowRetry = () => {
    // Allow up to 3 retries
    return state.value.retryCount < 3
  }

  const getErrorMessage = (error: Error): string => {
    // Provide user-friendly error messages
    const errorMessages: Record<string, string> = {
      'NetworkError': 'Проблема с сетевым соединением. Проверьте подключение к интернету.',
      'TypeError': 'Произошла техническая ошибка. Попробуйте обновить страницу.',
      'ReferenceError': 'Произошла техническая ошибка. Попробуйте обновить страницу.',
      'SyntaxError': 'Произошла техническая ошибка. Попробуйте обновить страницу.',
      'ValidationError': 'Ошибка валидации данных. Проверьте введенную информацию.',
      'AuthenticationError': 'Ошибка аутентификации. Войдите в систему заново.',
      'AuthorizationError': 'У вас нет прав для выполнения этого действия.',
      'NotFoundError': 'Запрашиваемый ресурс не найден.',
      'ServerError': 'Ошибка сервера. Попробуйте позже.'
    }

    const errorType = error.constructor.name
    return errorMessages[errorType] || error.message || 'Произошла неизвестная ошибка'
  }

  const getErrorTitle = (error: Error): string => {
    const errorTitles: Record<string, string> = {
      'NetworkError': 'Проблема с соединением',
      'TypeError': 'Техническая ошибка',
      'ReferenceError': 'Техническая ошибка',
      'SyntaxError': 'Техническая ошибка',
      'ValidationError': 'Ошибка валидации',
      'AuthenticationError': 'Ошибка аутентификации',
      'AuthorizationError': 'Нет доступа',
      'NotFoundError': 'Не найдено',
      'ServerError': 'Ошибка сервера'
    }

    const errorType = error.constructor.name
    return errorTitles[errorType] || 'Произошла ошибка'
  }

  return {
    state, // возвращаем Ref для реактивности
    captureError,
    retry,
    reset,
    shouldShowRetry,
    getErrorMessage,
    getErrorTitle
  }
}

// Global error handler for unhandled promise rejections
if (typeof window !== 'undefined') {
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason)
    
    // Prevent the default browser behavior
    event.preventDefault()
    
    // You could emit a global event here to show an error boundary
    // or integrate with a global error handling system
  })
}

export default useErrorBoundary