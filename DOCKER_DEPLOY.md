# Docker Deployment для Parttec Монорепо

## Структура Docker файлов

```
parttec/
├── docker-compose.yml          # Основная оркестрация
├── .env.example               # Пример переменных окружения
├── api/
│   ├── Dockerfile            # Bun + Hono + tRPC API
│   └── .dockerignore
├── cpanel/
│   ├── Dockerfile            # Astro + Vue админ панель
│   └── .dockerignore
└── site/
    ├── Dockerfile            # Astro + React клиентский сайт
    └── .dockerignore
```

## Быстрый старт

### 1. Настройка переменных окружения

```bash
cp .env.example .env
```

Отредактируйте `.env` файл:
- `POSTGRES_PASSWORD` - пароль для PostgreSQL
- `BETTER_AUTH_SECRET` - секретный ключ для аутентификации (минимум 32 символа)
- URL сервисов для production

### 2. Запуск с помощью скрипта (рекомендуется)

**Linux/macOS:**
```bash
# Development режим
./deploy.sh dev

# Production режим
./deploy.sh prod

# Остановка
./deploy.sh stop

# Просмотр логов
./deploy.sh logs
./deploy.sh logs api  # логи конкретного сервиса
```

**Windows:**
```cmd
# Development режим
deploy.bat dev

# Production режим
deploy.bat prod

# Остановка
deploy.bat stop

# Просмотр логов
deploy.bat logs
deploy.bat logs api
```

### 3. Ручной запуск (альтернативный способ)

**Development:**
```bash
docker-compose up -d --build
```

**Production:**
```bash
docker-compose -f docker-compose.prod.yml up -d --build
```

### 4. Инициализация базы данных

Миграции выполняются автоматически при использовании скриптов деплоя.
Для ручного выполнения:

```bash
# Войдите в контейнер API
docker exec -it parttec-api bash

# Выполните миграции Prisma
bunx prisma migrate deploy

# Опционально: заполните тестовыми данными
bun run seed-test-data.ts
```

## Сервисы и порты

- **API**: http://localhost:3000 (Bun + Hono + tRPC)
- **CPPanel**: http://localhost:4322 (Astro + Vue админ панель)
- **Site**: http://localhost:4323 (Astro + React клиентский сайт)
- **PostgreSQL**: localhost:5432

## Деплой в Dokploy

### 1. Подготовка репозитория

Убедитесь, что все Docker файлы закоммичены в ваш Git репозиторий:

```bash
git add .
git commit -m "Add Docker configuration for deployment"
git push origin main
```

### 2. Создание проекта в Dokploy

1. Войдите в Dokploy панель
2. Создайте новый проект "Parttec"
3. Выберите тип деплоя: "Docker Compose"

### 3. Настройка в Dokploy

**Git Repository:**
- Repository URL: `https://github.com/xferqr/parttec3.git`
- Branch: `main` (или ваша основная ветка)
- Build Path: `/` (корень репозитория)
- Docker Compose File: `docker-compose.prod.yml` (для production)

**Environment Variables:**
```
POSTGRES_PASSWORD=your_secure_password_here
BETTER_AUTH_SECRET=your_very_long_random_secret_key_minimum_32_chars

# Домены (замените на ваши)
API_DOMAIN=api.yourdomain.com
CPANEL_DOMAIN=admin.yourdomain.com
SITE_DOMAIN=yourdomain.com

# URL для приложений
API_URL=https://api.yourdomain.com
CPANEL_URL=https://admin.yourdomain.com
SITE_URL=https://yourdomain.com
```

### 4. Настройка доменов и SSL

Dokploy автоматически настроит:
- Traefik reverse proxy
- Let's Encrypt SSL сертификаты
- Автоматическое обновление сертификатов

Домены будут автоматически привязаны к сервисам через labels в `docker-compose.prod.yml`.

### 5. Мониторинг и Health Checks

Production конфигурация включает:
- Health checks для всех сервисов
- Автоматический перезапуск при сбоях
- Зависимости между сервисами
- Мониторинг состояния через Dokploy панель

## Мониторинг и логи

### Просмотр логов

```bash
# Все сервисы
docker-compose logs -f

# Конкретный сервис
docker-compose logs -f api
docker-compose logs -f cpanel
docker-compose logs -f site
```

### Проверка статуса

```bash
docker-compose ps
```

## Обновление

### Локальное обновление

```bash
# Остановить сервисы
docker-compose down

# Обновить код (git pull)
git pull origin main
git submodule update --remote

# Пересобрать и запустить
docker-compose up -d --build
```

### Обновление в Dokploy

1. Закоммитьте изменения в Git
2. В Dokploy нажмите "Redeploy"
3. Dokploy автоматически подтянет изменения и пересоберет контейнеры

## 💾 Персистентность данных

### Структура данных

Все критические данные сохраняются в директории `./data/`:

```
data/
├── postgres/                  # База данных PostgreSQL
├── media/                     # MediaAsset файлы
│   ├── schema-images/         # Изображения схем
│   ├── part-images/           # Основные изображения деталей
│   ├── category-images/       # Изображения категорий
│   ├── part-media/            # Галерея деталей (изображения + PDF)
│   ├── catalogitem-images/    # Основные изображения позиций каталога
│   └── catalogitem-media/     # Галерея позиций каталога
└── .gitkeep                   # Сохраняет структуру в Git
```

### Что сохраняется между деплоями

✅ **Сохраняется:**
- База данных PostgreSQL
- MediaAsset файлы (изображения, PDF)
- Конфигурация (.env)

❌ **НЕ сохраняется:**
- Docker образы (пересобираются)
- Код приложений (обновляется из Git)

### Резервное копирование

```bash
# Создание бэкапа БД
docker exec parttec-postgres pg_dump -U postgres parttec > backup_$(date +%Y%m%d).sql

# Бэкап всех данных
tar -czf parttec-backup-$(date +%Y%m%d).tar.gz data/ .env

# Восстановление
tar -xzf parttec-backup-YYYYMMDD.tar.gz
```

📖 **Подробнее:** См. [DATA_PERSISTENCE.md](DATA_PERSISTENCE.md)

## Troubleshooting

### Проблемы с подключением к базе данных

1. Проверьте, что PostgreSQL контейнер запущен:
   ```bash
   docker-compose ps postgres
   ```

2. Проверьте переменные окружения в `.env`

3. Проверьте логи PostgreSQL:
   ```bash
   docker-compose logs postgres
   ```

### Проблемы с Prisma

```bash
# Пересоздать Prisma клиент
docker exec parttec-api bunx prisma generate

# Сбросить базу данных (ОСТОРОЖНО!)
docker exec parttec-api bunx prisma migrate reset
```

### Проблемы с сборкой

```bash
# Очистить Docker кэш
docker system prune -a

# Пересобрать без кэша
docker-compose build --no-cache
```
