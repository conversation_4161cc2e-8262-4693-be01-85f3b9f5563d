<template>
  <VDialog v-model:visible="visible" modal header="Синонимы атрибута" class="w-auto">
    <div v-if="data" class="p-2 space-y-3">
      <div class="flex items-center justify-between">
        <div>
          <div class="text-sm text-surface-500">Группа</div>
          <div class="text-base font-medium">{{ data.group.name }}</div>
        </div>
        <VTag :value="data.group.compatibilityLevel" :severity="getSeverity(data.group.compatibilityLevel)" />
      </div>

      <div v-if="data.group.description" class="text-xs text-surface-500">{{ data.group.description }}</div>

      <div class="grid grid-cols-1 gap-1 max-h-64 overflow-auto border rounded p-2">
        <div 
          v-for="s in data.synonyms" 
          :key="s.id"
          class="flex items-center justify-between text-sm p-1 rounded"
          :class="s.id === data.currentSynonymId ? 'bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300' : ''"
        >
          <div class="flex items-center gap-2">
            <span class="font-mono">{{ s.value }}</span>
            <VTag v-if="s.compatibilityLevel && s.compatibilityLevel !== data.group.compatibilityLevel" size="small" :value="s.compatibilityLevel" :severity="getSeverity(s.compatibilityLevel)" />
          </div>
          <div class="text-xs text-surface-500" v-if="s.brand">{{ s.brand.name }}</div>
        </div>
      </div>

      <div v-if="data.group.canonicalValue" class="text-xs">
        <span class="text-surface-500">Каноническое:</span>
        <span class="font-mono">{{ data.group.canonicalValue }}</span>
      </div>
      <div v-if="data.group.notes" class="text-xs text-surface-600">{{ data.group.notes }}</div>
    </div>
  </VDialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import VDialog from '@/volt/Dialog.vue'
import VTag from '@/volt/Tag.vue'

const props = defineProps<{ modelValue: boolean; data: any | null }>()
const emit = defineEmits<{ (e: 'update:modelValue', v: boolean): void }>()

const visible = ref(false)

watch(() => props.modelValue, (v) => { visible.value = v })
watch(visible, (v) => emit('update:modelValue', v))

const getSeverity = (level: string) => level === 'EXACT' ? 'success' : (level === 'NEAR' ? 'info' : 'warning')
</script>

