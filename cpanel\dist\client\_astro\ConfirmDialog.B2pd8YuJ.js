import{u as P,C as v}from"./index.CKgBWlpe.js";import{s as z}from"./index.DQmIdHOH.js";import{s as T}from"./Dialog.Dcz8Sg9i.js";import{s as O,_ as V,p as R}from"./_plugin-vue_export-helper.BX07OBiL.js";import{U as E,ag as S,g as j,o as m,k as C,j as f,l as y,e as u,m as h,a as d,c as L,b as w,F as x,V as D,d as M,f as B}from"./index.BglzLLgy.js";import{n as I,t as g,r as U}from"./reactivity.esm-bundler.D5IypM4U.js";import{S as H}from"./SecondaryButton.DjJyItVx.js";import{D as N}from"./DangerButton.PrtrqpTW.js";import{c as k}from"./createLucideIcon.3yDVQAYz.js";/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const q=k("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const X=k("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _=k("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const G=k("undo-2",[["path",{d:"M9 14 4 9l5-5",key:"102s5s"}],["path",{d:"M4 9h10.5a5.5 5.5 0 0 1 5.5 5.5a5.5 5.5 0 0 1-5.5 5.5H11",key:"f3b9sd"}]]),me=()=>{const e=P(),t=n=>{e.require({message:n.message||"Вы уверены?",header:n.header||"Подтверждение",icon:n.icon||"pi pi-exclamation-triangle",acceptLabel:n.acceptLabel||"Да",rejectLabel:n.rejectLabel||"Отмена",acceptClass:n.acceptClass,rejectClass:n.rejectClass,accept:n.accept,reject:n.reject})};return{show:t,confirmDelete:(n="запись",a,s)=>{t({message:`Вы действительно хотите удалить эту ${n}? Это действие нельзя отменить.`,header:"Подтверждение удаления",icon:"pi pi-trash",acceptLabel:"Удалить",rejectLabel:"Отмена",acceptClass:"bg-red-500 hover:bg-red-600",accept:a,reject:s})},confirmSave:(n="Сохранить изменения?",a,s)=>{t({message:n,header:"Сохранение",icon:"pi pi-save",acceptLabel:"Сохранить",rejectLabel:"Отмена",acceptClass:"bg-primary-500 hover:bg-primary-600",accept:a,reject:s})},confirmCancel:(n="У вас есть несохраненные изменения. Вы действительно хотите выйти без сохранения?",a,s)=>{t({message:n,header:"Несохраненные изменения",icon:"pi pi-exclamation-triangle",acceptLabel:"Выйти без сохранения",rejectLabel:"Остаться",acceptClass:"bg-orange-500 hover:bg-orange-600",accept:a,reject:s})},confirmPublish:(n="запись",a,s)=>{t({message:`Опубликовать эту ${n}? После публикации она станет доступна всем пользователям.`,header:"Подтверждение публикации",icon:"pi pi-globe",acceptLabel:"Опубликовать",rejectLabel:"Отмена",acceptClass:"bg-green-500 hover:bg-green-600",accept:a,reject:s})},confirmArchive:(n="запись",a,s)=>{t({message:`Архивировать эту ${n}? Архивированные записи не отображаются в основном списке.`,header:"Подтверждение архивирования",icon:"pi pi-archive",acceptLabel:"Архивировать",rejectLabel:"Отмена",acceptClass:"bg-gray-500 hover:bg-gray-600",accept:a,reject:s})},confirmRestore:(n="запись",a,s)=>{t({message:`Восстановить эту ${n}? Она снова станет доступна в основном списке.`,header:"Подтверждение восстановления",icon:"pi pi-refresh",acceptLabel:"Восстановить",rejectLabel:"Отмена",acceptClass:"bg-blue-500 hover:bg-blue-600",accept:a,reject:s})},confirmBulkAction:(n,a,s,F)=>{t({message:`Выполнить действие "${n}" для ${a} записей?`,header:"Массовое действие",icon:"pi pi-list",acceptLabel:"Выполнить",rejectLabel:"Отмена",acceptClass:"bg-primary-500 hover:bg-primary-600",accept:s,reject:F})}}};var J=`
    .p-confirmdialog .p-dialog-content {
        display: flex;
        align-items: center;
        gap: dt('confirmdialog.content.gap');
    }

    .p-confirmdialog-icon {
        color: dt('confirmdialog.icon.color');
        font-size: dt('confirmdialog.icon.size');
        width: dt('confirmdialog.icon.size');
        height: dt('confirmdialog.icon.size');
    }
`,K={root:"p-confirmdialog",icon:"p-confirmdialog-icon",message:"p-confirmdialog-message",pcRejectButton:"p-confirmdialog-reject-button",pcAcceptButton:"p-confirmdialog-accept-button"},Q=E.extend({name:"confirmdialog",style:J,classes:K}),W={name:"BaseConfirmDialog",extends:O,props:{group:String,breakpoints:{type:Object,default:null},draggable:{type:Boolean,default:!0}},style:Q,provide:function(){return{$pcConfirmDialog:this,$parentInstance:this}}},A={name:"ConfirmDialog",extends:W,confirmListener:null,closeListener:null,data:function(){return{visible:!1,confirmation:null}},mounted:function(){var t=this;this.confirmListener=function(i){i&&i.group===t.group&&(t.confirmation=i,t.confirmation.onShow&&t.confirmation.onShow(),t.visible=!0)},this.closeListener=function(){t.visible=!1,t.confirmation=null},v.on("confirm",this.confirmListener),v.on("close",this.closeListener)},beforeUnmount:function(){v.off("confirm",this.confirmListener),v.off("close",this.closeListener)},methods:{accept:function(){this.confirmation.accept&&this.confirmation.accept(),this.visible=!1},reject:function(){this.confirmation.reject&&this.confirmation.reject(),this.visible=!1},onHide:function(){this.confirmation.onHide&&this.confirmation.onHide(),this.visible=!1}},computed:{appendTo:function(){return this.confirmation?this.confirmation.appendTo:"body"},target:function(){return this.confirmation?this.confirmation.target:null},modal:function(){return this.confirmation?this.confirmation.modal==null?!0:this.confirmation.modal:!0},header:function(){return this.confirmation?this.confirmation.header:null},message:function(){return this.confirmation?this.confirmation.message:null},blockScroll:function(){return this.confirmation?this.confirmation.blockScroll:!0},position:function(){return this.confirmation?this.confirmation.position:null},acceptLabel:function(){if(this.confirmation){var t,i=this.confirmation;return i.acceptLabel||((t=i.acceptProps)===null||t===void 0?void 0:t.label)||this.$primevue.config.locale.accept}return this.$primevue.config.locale.accept},rejectLabel:function(){if(this.confirmation){var t,i=this.confirmation;return i.rejectLabel||((t=i.rejectProps)===null||t===void 0?void 0:t.label)||this.$primevue.config.locale.reject}return this.$primevue.config.locale.reject},acceptIcon:function(){var t;return this.confirmation?this.confirmation.acceptIcon:(t=this.confirmation)!==null&&t!==void 0&&t.acceptProps?this.confirmation.acceptProps.icon:null},rejectIcon:function(){var t;return this.confirmation?this.confirmation.rejectIcon:(t=this.confirmation)!==null&&t!==void 0&&t.rejectProps?this.confirmation.rejectProps.icon:null},autoFocusAccept:function(){return this.confirmation.defaultFocus===void 0||this.confirmation.defaultFocus==="accept"},autoFocusReject:function(){return this.confirmation.defaultFocus==="reject"},closeOnEscape:function(){return this.confirmation?this.confirmation.closeOnEscape:!0}},components:{Dialog:T,Button:z}};function Y(e,t,i,o,r,c){var p=S("Button"),b=S("Dialog");return m(),j(b,{visible:r.visible,"onUpdate:visible":[t[2]||(t[2]=function(l){return r.visible=l}),c.onHide],role:"alertdialog",class:I(e.cx("root")),modal:c.modal,header:c.header,blockScroll:c.blockScroll,appendTo:c.appendTo,position:c.position,breakpoints:e.breakpoints,closeOnEscape:c.closeOnEscape,draggable:e.draggable,pt:e.pt,unstyled:e.unstyled},C({default:f(function(){return[e.$slots.container?w("",!0):(m(),L(x,{key:0},[e.$slots.message?(m(),j(D(e.$slots.message),{key:1,message:r.confirmation},null,8,["message"])):(m(),L(x,{key:0},[y(e.$slots,"icon",{},function(){return[e.$slots.icon?(m(),j(D(e.$slots.icon),{key:0,class:I(e.cx("icon"))},null,8,["class"])):r.confirmation.icon?(m(),L("span",h({key:1,class:[r.confirmation.icon,e.cx("icon")]},e.ptm("icon")),null,16)):w("",!0)]}),d("span",h({class:e.cx("message")},e.ptm("message")),g(c.message),17)],64))],64))]}),_:2},[e.$slots.container?{name:"container",fn:f(function(l){return[y(e.$slots,"container",{message:r.confirmation,closeCallback:l.onclose,acceptCallback:c.accept,rejectCallback:c.reject})]}),key:"0"}:void 0,e.$slots.container?void 0:{name:"footer",fn:f(function(){var l;return[u(p,h({class:[e.cx("pcRejectButton"),r.confirmation.rejectClass],autofocus:c.autoFocusReject,unstyled:e.unstyled,text:((l=r.confirmation.rejectProps)===null||l===void 0?void 0:l.text)||!1,onClick:t[0]||(t[0]=function(n){return c.reject()})},r.confirmation.rejectProps,{label:c.rejectLabel,pt:e.ptm("pcRejectButton")}),C({_:2},[c.rejectIcon||e.$slots.rejecticon?{name:"icon",fn:f(function(n){return[y(e.$slots,"rejecticon",{},function(){return[d("span",h({class:[c.rejectIcon,n.class]},e.ptm("pcRejectButton").icon,{"data-pc-section":"rejectbuttonicon"}),null,16)]})]}),key:"0"}:void 0]),1040,["class","autofocus","unstyled","text","label","pt"]),u(p,h({label:c.acceptLabel,class:[e.cx("pcAcceptButton"),r.confirmation.acceptClass],autofocus:c.autoFocusAccept,unstyled:e.unstyled,onClick:t[1]||(t[1]=function(n){return c.accept()})},r.confirmation.acceptProps,{pt:e.ptm("pcAcceptButton")}),C({_:2},[c.acceptIcon||e.$slots.accepticon?{name:"icon",fn:f(function(n){return[y(e.$slots,"accepticon",{},function(){return[d("span",h({class:[c.acceptIcon,n.class]},e.ptm("pcAcceptButton").icon,{"data-pc-section":"acceptbuttonicon"}),null,16)]})]}),key:"0"}:void 0]),1040,["label","class","autofocus","unstyled","pt"])]}),key:"1"}]),1032,["visible","class","modal","header","blockScroll","appendTo","position","breakpoints","closeOnEscape","draggable","onUpdate:visible","pt","unstyled"])}A.render=Y;const Z=M({__name:"ConfirmDialog",setup(e,{expose:t}){t();const o={theme:U({root:`max-h-[90%] max-w-screen rounded-xl
        border border-surface-200 dark:border-surface-700
        bg-surface-0 dark:bg-surface-900
        text-surface-700 dark:text-surface-0 shadow-lg`,mask:"bg-black/50 fixed top-0 start-0 w-full h-full",transition:{enterFromClass:"opacity-0 scale-75",enterActiveClass:"transition-all duration-150 ease-[cubic-bezier(0,0,0.2,1)]",leaveActiveClass:"transition-all duration-150 ease-[cubic-bezier(0.4,0,0.2,1)]",leaveToClass:"opacity-0 scale-75"}}),get ConfirmDialog(){return A},SecondaryButton:H,get ptViewMerge(){return R},get AlertCircle(){return q},get XCircleIcon(){return X},DangerButton:N,get Trash2Icon(){return _},get Undo2Icon(){return G}};return Object.defineProperty(o,"__isScriptSetup",{enumerable:!1,value:!0}),o}}),$={class:"flex items-center justify-between shrink-0 p-5"},ee={class:"font-semibold text-xl"},te={class:"overflow-y-auto pt-0 px-5 pb-5 flex items-center gap-4"},ne={class:"pt-0 px-5 pb-5 flex justify-end gap-2"};function ce(e,t,i,o,r,c){return m(),j(o.ConfirmDialog,{unstyled:"",pt:o.theme,ptOptions:{mergeProps:o.ptViewMerge}},{container:f(({message:p,acceptCallback:b,rejectCallback:l})=>[d("div",$,[d("span",ee,g(p.header),1),u(o.SecondaryButton,{variant:"text",rounded:"",onClick:l,autofocus:""},{default:f(()=>[u(o.XCircleIcon,{class:"w-4 h-4"})]),_:2},1032,["onClick"])]),d("div",te,[u(o.AlertCircle,{class:"w-4 h-4"}),B(" "+g(p.message),1)]),d("div",ne,[u(o.SecondaryButton,{onClick:l,size:"small"},{default:f(()=>[u(o.Undo2Icon,{class:"w-4 h-4"}),B(" "+g(p.rejectProps?.label),1)]),_:2},1032,["onClick"]),u(o.DangerButton,{onClick:b,size:"small"},{default:f(()=>[u(o.Trash2Icon,{class:"w-4 h-4"}),B(" "+g(p.acceptProps?.label),1)]),_:2},1032,["onClick"])])]),_:1},8,["pt","ptOptions"])}const de=V(Z,[["render",ce]]);export{de as C,me as u};
