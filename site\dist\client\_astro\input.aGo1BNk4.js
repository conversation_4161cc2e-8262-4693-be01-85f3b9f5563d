import{j as i}from"./jsx-runtime.D_zvdyIk.js";import{r as s}from"./index.0yr9KlQE.js";import{c as n}from"./utils.CBfrqCZ4.js";const a=s.forwardRef(({className:e,type:r,...t},o)=>i.jsx("input",{type:r,className:n("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-xs transition-all file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",e),ref:o,...t}));a.displayName="Input";export{a as I};
