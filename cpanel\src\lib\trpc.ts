import { createTRPCClient, httpBatchLink, loggerLink } from '@trpc/client';
import superjson from 'superjson';

// Типы для tRPC роутера (заглушка для билда)
// В рантайме типы будут получены от API сервера
type AppRouter = any;

export const getBaseUrl = () => {
  if (typeof window !== 'undefined') return 'http://localhost';
  return process.env.API_URL || 'http://localhost';
};

export const trpc = createTRPCClient<AppRouter>({
  links: [
    loggerLink({ enabled: () => import.meta.env.DEV }),
    httpBatchLink({
      url: `${getBaseUrl()}:3000/trpc`,
      transformer: superjson,
      fetch: (url, options) => fetch(url, { ...(options as RequestInit), credentials: 'include' }),
    }),
  ],
});
