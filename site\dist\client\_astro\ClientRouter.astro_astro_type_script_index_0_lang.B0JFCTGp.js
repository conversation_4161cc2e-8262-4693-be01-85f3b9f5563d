import{s as A,n as p}from"./router.WLLD8StG.js";const y=new Set,f=new WeakSet;let u,b,v=!1;function k(e){v||(v=!0,u??=e?.prefetchAll,b??=e?.defaultStrategy??"hover",H(),M(),R(),U())}function H(){for(const e of["touchstart","mousedown"])document.body.addEventListener(e,t=>{i(t.target,"tap")&&l(t.target.href,{ignoreSlowConnection:!0})},{passive:!0})}function M(){let e;document.body.addEventListener("focusin",o=>{i(o.target,"hover")&&t(o)},{passive:!0}),document.body.addEventListener("focusout",n,{passive:!0}),d(()=>{for(const o of document.getElementsByTagName("a"))f.has(o)||i(o,"hover")&&(f.add(o),o.addEventListener("mouseenter",t,{passive:!0}),o.addEventListener("mouseleave",n,{passive:!0}))});function t(o){const r=o.target.href;e&&clearTimeout(e),e=setTimeout(()=>{l(r)},80)}function n(){e&&(clearTimeout(e),e=0)}}function R(){let e;d(()=>{for(const t of document.getElementsByTagName("a"))f.has(t)||i(t,"viewport")&&(f.add(t),e??=P(),e.observe(t))})}function P(){const e=new WeakMap;return new IntersectionObserver((t,n)=>{for(const o of t){const r=o.target,a=e.get(r);o.isIntersecting?(a&&clearTimeout(a),e.set(r,setTimeout(()=>{n.unobserve(r),e.delete(r),l(r.href)},300))):a&&(clearTimeout(a),e.delete(r))}})}function U(){d(()=>{for(const e of document.getElementsByTagName("a"))i(e,"load")&&l(e.href)})}function l(e,t){e=e.replace(/#.*/,"");const n=t?.ignoreSlowConnection??!1;if(V(e,n))if(y.add(e),document.createElement("link").relList?.supports?.("prefetch")&&t?.with!=="fetch"){const o=document.createElement("link");o.rel="prefetch",o.setAttribute("href",e),document.head.append(o)}else fetch(e,{priority:"low"})}function V(e,t){if(!navigator.onLine||!t&&E())return!1;try{const n=new URL(e,location.href);return location.origin===n.origin&&(location.pathname!==n.pathname||location.search!==n.search)&&!y.has(e)}catch{}return!1}function i(e,t){if(e?.tagName!=="A")return!1;const n=e.dataset.astroPrefetch;return n==="false"?!1:t==="tap"&&(n!=null||u)&&E()?!0:n==null&&u||n===""?t===b:n===t}function E(){if("connection"in navigator){const e=navigator.connection;return e.saveData||/2g/.test(e.effectiveType)}return!1}function d(e){e();let t=!1;document.addEventListener("astro:page-load",()=>{if(!t){t=!0;return}e()})}let s=null;function D(){const e=document.querySelector('[name="astro-view-transitions-fallback"]');return e?e.getAttribute("content"):"animate"}function w(e){return e.dataset.astroReload!==void 0}const C=e=>e.button&&e.button!==0||e.metaKey||e.ctrlKey||e.altKey||e.shiftKey;(A||D()!=="none")&&(document.addEventListener("click",e=>{let t=e.target;if(s=C(e)?t:null,e.composed&&(t=e.composedPath()[0]),t instanceof Element&&(t=t.closest("a, area")),!(t instanceof HTMLAnchorElement)&&!(t instanceof SVGAElement)&&!(t instanceof HTMLAreaElement))return;const n=t instanceof HTMLElement?t.target:t.target.baseVal,o=t instanceof HTMLElement?t.href:t.href.baseVal,r=new URL(o,location.href).origin;w(t)||t.hasAttribute("download")||!t.href||n&&n!=="_self"||r!==location.origin||s||e.defaultPrevented||(e.preventDefault(),p(o,{history:t.dataset.astroHistory==="replace"?"replace":"auto",sourceElement:t}))}),document.addEventListener("submit",e=>{let t=e.target;const n=e.submitter,o=n&&n===s;if(s=null,t.tagName!=="FORM"||e.defaultPrevented||w(t)||o)return;const r=t,a=new FormData(r,n),L=typeof r.action=="string"?r.action:r.getAttribute("action"),S=typeof r.method=="string"?r.method:r.getAttribute("method");let c=n?.getAttribute("formaction")??L??location.pathname;const m=n?.getAttribute("formmethod")??S??"get";if(m==="dialog"||location.origin!==new URL(c,location.href).origin)return;const h={sourceElement:n??r};if(m==="get"){const T=new URLSearchParams(a),g=new URL(c);g.search=T.toString(),c=g.toString()}else h.formData=a;e.preventDefault(),p(c,h)}),k({prefetchAll:!0}));
