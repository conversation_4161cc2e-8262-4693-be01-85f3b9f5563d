import type { Context } from '../../trpc'
import type { PrismaClient } from '@prisma/client'
import ExcelJS from 'exceljs'

// Конфигурация включаемых листов и фильтров
export type ExportConflictMode = 'upsert' | 'update_only' | 'skip' | 'error'

export interface ExportScope {
  brandSlug?: string
  categorySlug?: string
}

export interface ExportOptions {
  include: {
    Brand?: boolean
    PartCategory?: boolean
    AttributeGroup?: boolean
    AttributeTemplate?: boolean
    AttributeSynonymGroup?: boolean
    AttributeSynonym?: boolean
    Part?: boolean
    CatalogItem?: boolean
    EquipmentModel?: boolean
    PartAttribute?: boolean
    CatalogItemAttribute?: boolean
    EquipmentModelAttribute?: boolean
    PartApplicability?: boolean
    EquipmentApplicability?: boolean
  }
  filters?: {
    brandSlugs?: string[]
    partCategorySlugs?: string[]
  }
  meta?: {
    createMissingRefs?: boolean
    onConflict?: ExportConflictMode
    scope?: ExportScope
  }
}

export class ExcelExportService {
  constructor(private db: Context['db'] | PrismaClient) {}

  async buildWorkbook(opts: ExportOptions) {
    const wb = new ExcelJS.Workbook()

    // META лист
    const meta = wb.addWorksheet('META')
    const metaObj = {
      version: 1,
      exportedAt: new Date().toISOString(),
      exporterVersion: 'v1',
      schemaHash: 'catalog_schema_v1',
      includedSheets: Object.entries(opts.include)
        .filter(([, v]) => !!v)
        .map(([k]) => k),
      modes: {
        createMissingRefs: opts.meta?.createMissingRefs ?? false,
        onConflict: opts.meta?.onConflict ?? 'upsert',
      },
      scope: opts.meta?.scope ?? {},
      filters: opts.filters ?? {},
    }
    meta.columns = [
      { header: 'key', key: 'key', width: 32 },
      { header: 'value', key: 'value', width: 120 },
    ]
    for (const [k, v] of Object.entries(metaObj)) {
      meta.addRow({ key: k, value: JSON.stringify(v) })
    }

    // Экспорт по листам
    if (opts.include.Brand) {
      await this.addBrandsSheet(wb, opts)
    }
    if (opts.include.PartCategory) {
      await this.addPartCategoriesSheet(wb, opts)
    }
    if (opts.include.AttributeGroup) {
      await this.addAttributeGroupsSheet(wb, opts)
    }
    if (opts.include.AttributeTemplate) {
      await this.addAttributeTemplatesSheet(wb, opts)
    }
    if (opts.include.AttributeSynonymGroup) {
      await this.addAttributeSynonymGroupsSheet(wb, opts)
    }
    if (opts.include.AttributeSynonym) {
      await this.addAttributeSynonymsSheet(wb, opts)
    }
    if (opts.include.Part) {
      await this.addPartsSheet(wb, opts)
    }
    if (opts.include.CatalogItem) {
      await this.addCatalogItemsSheet(wb, opts)
    }
    if (opts.include.EquipmentModel) {
      await this.addEquipmentModelsSheet(wb, opts)
    }

    if (opts.include.PartAttribute) {
      await this.addPartAttributesSheet(wb, opts)
    }
    if (opts.include.CatalogItemAttribute) {
      await this.addCatalogItemAttributesSheet(wb, opts)
    }
    if (opts.include.EquipmentModelAttribute) {
      await this.addEquipmentModelAttributesSheet(wb, opts)
    }
    if (opts.include.PartApplicability) {
      await this.addPartApplicabilitiesSheet(wb, opts)
    }
    if (opts.include.EquipmentApplicability) {
      await this.addEquipmentApplicabilitiesSheet(wb, opts)
    }

    const buffer = await wb.xlsx.writeBuffer()
    const fileName = `catalog-export-${Date.now()}.xlsx`
    return { buffer: Buffer.from(buffer), fileName }
  }

  private async addBrandsSheet(wb: ExcelJS.Workbook, opts: ExportOptions) {
    const ws = wb.addWorksheet('Brand')
    ws.columns = [
      { header: 'id', key: 'id', width: 10 },
      { header: '__key', key: '__key', width: 16 },
      { header: 'name', key: 'name', width: 32 },
      { header: 'slug', key: 'slug', width: 32 },
      { header: 'country', key: 'country', width: 24 },
      { header: 'isOem', key: 'isOem', width: 10 },
    ]

    const rows = await this.db.brand.findMany({
      where: opts.filters?.brandSlugs?.length
        ? { slug: { in: opts.filters!.brandSlugs } }
        : undefined,
      select: { id: true, name: true, slug: true, country: true, isOem: true },
      orderBy: { slug: 'asc' },
    } as any)

    for (const r of rows) ws.addRow({ ...r })
  }

  private async addPartCategoriesSheet(wb: ExcelJS.Workbook, _opts: ExportOptions) {
    const ws = wb.addWorksheet('PartCategory')
    ws.columns = [
      { header: 'id', key: 'id', width: 10 },
      { header: '__key', key: '__key', width: 16 },
      { header: 'name', key: 'name', width: 32 },
      { header: 'slug', key: 'slug', width: 32 },
      { header: 'description', key: 'description', width: 50 },
      { header: 'parent.slug', key: 'parentSlug', width: 32 },
    ]

    const rows = await this.db.partCategory.findMany({
      select: { id: true, name: true, slug: true, description: true, parent: { select: { slug: true } } },
      orderBy: { slug: 'asc' },
    } as any)

    for (const r of rows) ws.addRow({
      id: r.id,
      name: r.name,
      slug: r.slug,
      description: r.description ?? null,
      parentSlug: r.parent?.slug ?? null,
    })
  }

  private async addAttributeTemplatesSheet(wb: ExcelJS.Workbook, _opts: ExportOptions) {
    const ws = wb.addWorksheet('AttributeTemplate')
    ws.columns = [
      { header: 'id', key: 'id', width: 10 },
      { header: '__key', key: '__key', width: 16 },
      { header: 'name', key: 'name', width: 32 },
      { header: 'title', key: 'title', width: 32 },
      { header: 'dataType', key: 'dataType', width: 16 },
      { header: 'unit', key: 'unit', width: 10 },
      { header: 'isRequired', key: 'isRequired', width: 10 },
      { header: 'minValue', key: 'minValue', width: 12 },
      { header: 'maxValue', key: 'maxValue', width: 12 },
      { header: 'allowedValues', key: 'allowedValues', width: 50 },
      { header: 'tolerance', key: 'tolerance', width: 12 },
      { header: 'group.name', key: 'groupName', width: 32 },
    ]

    const rows = await this.db.attributeTemplate.findMany({
      select: {
        id: true,
        name: true,
        title: true,
        dataType: true,
        unit: true,
        isRequired: true,
        minValue: true,
        maxValue: true,
        allowedValues: true,
        tolerance: true,
        group: { select: { name: true } },
      },
      orderBy: { name: 'asc' },
    } as any)

    for (const r of rows) ws.addRow({
      id: r.id,
      name: r.name,
      title: r.title,
      dataType: r.dataType,
      unit: r.unit ?? null,
      isRequired: r.isRequired,
      minValue: r.minValue ?? null,
      maxValue: r.maxValue ?? null,
      allowedValues: r.allowedValues?.length ? JSON.stringify(r.allowedValues) : null,
      tolerance: r.tolerance ?? null,
      groupName: r.group?.name ?? null,
    })
  }

  private async addPartsSheet(wb: ExcelJS.Workbook, _opts: ExportOptions) {
    const ws = wb.addWorksheet('Part')
    ws.columns = [
      { header: 'id', key: 'id', width: 10 },
      { header: '__key', key: '__key', width: 16 },
      { header: 'name', key: 'name', width: 40 },
      { header: 'partCategory.slug', key: 'partCategorySlug', width: 32 },
      { header: 'path', key: 'path', width: 40 },
      { header: 'parent.path', key: 'parentPath', width: 40 },
      { header: 'level', key: 'level', width: 8 },
    ]

    const rows = await this.db.part.findMany({
      select: {
        id: true,
        name: true,
        path: true,
        level: true,
        partCategory: { select: { slug: true } },
        parent: { select: { path: true } },
      },
      orderBy: [{ partCategory: { slug: 'asc' } }, { path: 'asc' }],
    } as any)

    for (const r of rows) ws.addRow({
      id: r.id,
      name: r.name ?? null,
      partCategorySlug: r.partCategory.slug,
      path: r.path,
      parentPath: r.parent?.path ?? null,
      level: r.level,
    })
  }

  private async addCatalogItemsSheet(wb: ExcelJS.Workbook, opts: ExportOptions) {
    const ws = wb.addWorksheet('CatalogItem')
    ws.columns = [
      { header: 'id', key: 'id', width: 10 },
      { header: '__key', key: '__key', width: 16 },
      { header: 'sku', key: 'sku', width: 32 },
      { header: 'brand.slug', key: 'brandSlug', width: 32 },
      { header: 'description', key: 'description', width: 50 },
      { header: 'source', key: 'source', width: 24 },
      { header: 'isPublic', key: 'isPublic', width: 10 },
    ]

    const rows = await this.db.catalogItem.findMany({
      where: opts.filters?.brandSlugs?.length
        ? { brand: { slug: { in: opts.filters!.brandSlugs } } }
        : undefined,
      select: {
        id: true,
        sku: true,
        description: true,
        source: true,
        isPublic: true,
        brand: { select: { slug: true } },
      },
      orderBy: [{ brand: { slug: 'asc' } }, { sku: 'asc' }],
    } as any)

    for (const r of rows) ws.addRow({
      id: r.id,
      sku: r.sku,
      brandSlug: r.brand.slug,
      description: r.description ?? null,
      source: r.source ?? null,
      isPublic: r.isPublic,
    })
  }

  private async addEquipmentModelsSheet(wb: ExcelJS.Workbook, _opts: ExportOptions) {
    const ws = wb.addWorksheet('EquipmentModel')
    ws.columns = [
      { header: 'id', key: 'id', width: 36 },
      { header: '__key', key: '__key', width: 16 },
      { header: 'name', key: 'name', width: 40 },
      { header: 'brand.slug', key: 'brandSlug', width: 32 },
      { header: 'createdAt', key: 'createdAt', width: 24 },
      { header: 'updatedAt', key: 'updatedAt', width: 24 },
    ]

    const rows = await this.db.equipmentModel.findMany({
      select: {
        id: true,
        name: true,
        createdAt: true,
        updatedAt: true,
        brand: { select: { slug: true } },
      },
      orderBy: [{ brand: { slug: 'asc' } }, { name: 'asc' }],
    } as any)

    for (const r of rows) ws.addRow({
      id: r.id,
      name: r.name,
      brandSlug: r.brand?.slug ?? null,
      createdAt: r.createdAt.toISOString(),
      updatedAt: r.updatedAt.toISOString(),
    })
  }

  private async addPartAttributesSheet(wb: ExcelJS.Workbook, _opts: ExportOptions) {
    const ws = wb.addWorksheet('PartAttribute')
    ws.columns = [
      { header: 'id', key: 'id', width: 10 },
      { header: '__key', key: '__key', width: 16 },
      { header: 'part.path', key: 'partPath', width: 40 },
      { header: 'partCategory.slug', key: 'partCategorySlug', width: 32 },
      { header: 'template.name', key: 'templateName', width: 32 },
      { header: 'value', key: 'value', width: 24 },
      { header: 'numericValue', key: 'numericValue', width: 16 },
    ]

    const rows = await this.db.partAttribute.findMany({
      select: {
        id: true,
        value: true,
        numericValue: true,
        part: { select: { path: true, partCategory: { select: { slug: true } } } },
        template: { select: { name: true } },
      },
      orderBy: [{ part: { partCategory: { slug: 'asc' } } }, { part: { path: 'asc' } }],
    } as any)

    for (const r of rows) ws.addRow({
      id: r.id,
      partPath: r.part.path,
      partCategorySlug: r.part.partCategory.slug,
      templateName: r.template.name,
      value: r.value,
      numericValue: r.numericValue ?? null,
    })
  }

  private async addCatalogItemAttributesSheet(wb: ExcelJS.Workbook, opts: ExportOptions) {
    const ws = wb.addWorksheet('CatalogItemAttribute')
    ws.columns = [
      { header: 'id', key: 'id', width: 10 },
      { header: '__key', key: '__key', width: 16 },
      { header: 'catalogItem.sku', key: 'sku', width: 32 },
      { header: 'brand.slug', key: 'brandSlug', width: 32 },
      { header: 'template.name', key: 'templateName', width: 32 },
      { header: 'value', key: 'value', width: 24 },
      { header: 'numericValue', key: 'numericValue', width: 16 },
    ]

    const rows = await this.db.catalogItemAttribute.findMany({
      where: opts.filters?.brandSlugs?.length
        ? { catalogItem: { brand: { slug: { in: opts.filters!.brandSlugs } } } }
        : undefined,
      select: {
        id: true,
        value: true,
        numericValue: true,
        catalogItem: { select: { sku: true, brand: { select: { slug: true } } } },
        template: { select: { name: true } },
      },
      orderBy: [{ catalogItem: { brand: { slug: 'asc' } } }, { catalogItem: { sku: 'asc' } }],
    } as any)

    for (const r of rows) ws.addRow({
      id: r.id,
      sku: r.catalogItem.sku,
      brandSlug: r.catalogItem.brand.slug,
      templateName: r.template.name,
      value: r.value,
      numericValue: r.numericValue ?? null,
    })
  }

  private async addEquipmentModelAttributesSheet(wb: ExcelJS.Workbook, _opts: ExportOptions) {
    const ws = wb.addWorksheet('EquipmentModelAttribute')
    ws.columns = [
      { header: 'id', key: 'id', width: 10 },
      { header: '__key', key: '__key', width: 16 },
      { header: 'equipmentModel.id', key: 'equipmentModelId', width: 36 },
      { header: 'template.name', key: 'templateName', width: 32 },
      { header: 'value', key: 'value', width: 24 },
      { header: 'numericValue', key: 'numericValue', width: 16 },
    ]

    const rows = await this.db.equipmentModelAttribute.findMany({
      select: {
        id: true,
        value: true,
        numericValue: true,
        equipmentModel: { select: { id: true } },
        template: { select: { name: true } },
      },
      orderBy: [{ equipmentModel: { id: 'asc' } }],
    } as any)

    for (const r of rows) ws.addRow({
      id: r.id,
      equipmentModelId: r.equipmentModel.id,
      templateName: r.template.name,
      value: r.value,
      numericValue: r.numericValue ?? null,
    })
  }

  private async addPartApplicabilitiesSheet(wb: ExcelJS.Workbook, _opts: ExportOptions) {
    const ws = wb.addWorksheet('PartApplicability')
    ws.columns = [
      { header: 'id', key: 'id', width: 10 },
      { header: '__key', key: '__key', width: 16 },
      { header: 'partCategory.slug', key: 'partCategorySlug', width: 32 },
      { header: 'part.path', key: 'partPath', width: 40 },
      { header: 'catalogItem.sku', key: 'sku', width: 32 },
      { header: 'brand.slug', key: 'brandSlug', width: 32 },
      { header: 'accuracy', key: 'accuracy', width: 20 },
      { header: 'notes', key: 'notes', width: 50 },
    ]

    const rows = await this.db.partApplicability.findMany({
      select: {
        id: true,
        accuracy: true,
        notes: true,
        part: { select: { path: true, partCategory: { select: { slug: true } } } },
        catalogItem: { select: { sku: true, brand: { select: { slug: true } } } },
      },
      orderBy: [{ part: { partCategory: { slug: 'asc' } } }, { part: { path: 'asc' } }],
    } as any)

    for (const r of rows) ws.addRow({
      id: r.id,
      partCategorySlug: r.part.partCategory.slug,
      partPath: r.part.path,
      sku: r.catalogItem.sku,
      brandSlug: r.catalogItem.brand.slug,
      accuracy: r.accuracy,
      notes: r.notes ?? null,
    })
  }

  private async addEquipmentApplicabilitiesSheet(wb: ExcelJS.Workbook, _opts: ExportOptions) {
    const ws = wb.addWorksheet('EquipmentApplicability')
    ws.columns = [
      { header: 'id', key: 'id', width: 10 },
      { header: '__key', key: '__key', width: 16 },
      { header: 'partCategory.slug', key: 'partCategorySlug', width: 32 },
      { header: 'part.path', key: 'partPath', width: 40 },
      { header: 'equipmentModel.id', key: 'equipmentModelId', width: 36 },
      { header: 'notes', key: 'notes', width: 50 },
    ]

    const rows = await this.db.equipmentApplicability.findMany({
      select: {
        id: true,
        notes: true,
        part: { select: { path: true, partCategory: { select: { slug: true } } } },
        equipmentModel: { select: { id: true } },
      },
      orderBy: [{ part: { partCategory: { slug: 'asc' } } }, { part: { path: 'asc' } }],
    } as any)

    for (const r of rows) ws.addRow({
      id: r.id,
      partCategorySlug: r.part.partCategory.slug,
      partPath: r.part.path,
      equipmentModelId: r.equipmentModel.id,
      notes: r.notes ?? null,
    })
  }

  private async addAttributeGroupsSheet(wb: ExcelJS.Workbook, _opts: ExportOptions) {
    const ws = wb.addWorksheet('AttributeGroup')
    ws.columns = [
      { header: 'id', key: 'id', width: 10 },
      { header: '__key', key: '__key', width: 16 },
      { header: 'name', key: 'name', width: 32 },
      { header: 'description', key: 'description', width: 50 },
      { header: 'parent.name', key: 'parentName', width: 32 },
    ]

    const rows = await this.db.attributeGroup.findMany({
      select: {
        id: true,
        name: true,
        description: true,
        parent: { select: { name: true } },
      },
      orderBy: { name: 'asc' },
    } as any)

    for (const r of rows) ws.addRow({
      id: r.id,
      name: r.name,
      description: r.description ?? null,
      parentName: r.parent?.name ?? null,
    })
  }

  private async addAttributeSynonymGroupsSheet(wb: ExcelJS.Workbook, _opts: ExportOptions) {
    const ws = wb.addWorksheet('AttributeSynonymGroup')
    ws.columns = [
      { header: 'id', key: 'id', width: 10 },
      { header: '__key', key: '__key', width: 16 },
      { header: 'template.name', key: 'templateName', width: 32 },
      { header: 'name', key: 'name', width: 32 },
      { header: 'description', key: 'description', width: 50 },
      { header: 'parent.name', key: 'parentName', width: 32 },
      { header: 'canonicalValue', key: 'canonicalValue', width: 24 },
      { header: 'compatibilityLevel', key: 'compatibilityLevel', width: 16 },
      { header: 'notes', key: 'notes', width: 50 },
    ]

    const rows = await this.db.attributeSynonymGroup.findMany({
      select: {
        id: true,
        name: true,
        description: true,
        canonicalValue: true,
        compatibilityLevel: true,
        notes: true,
        template: { select: { name: true } },
        parent: { select: { name: true } },
      },
      orderBy: [{ template: { name: 'asc' } }, { name: 'asc' }],
    } as any)

    for (const r of rows) ws.addRow({
      id: r.id,
      templateName: r.template.name,
      name: r.name,
      description: r.description ?? null,
      parentName: r.parent?.name ?? null,
      canonicalValue: r.canonicalValue ?? null,
      compatibilityLevel: r.compatibilityLevel,
      notes: r.notes ?? null,
    })
  }

  private async addAttributeSynonymsSheet(wb: ExcelJS.Workbook, _opts: ExportOptions) {
    const ws = wb.addWorksheet('AttributeSynonym')
    ws.columns = [
      { header: 'id', key: 'id', width: 10 },
      { header: '__key', key: '__key', width: 16 },
      { header: 'group.name', key: 'groupName', width: 32 },
      { header: 'template.name', key: 'templateName', width: 32 },
      { header: 'value', key: 'value', width: 24 },
      { header: 'notes', key: 'notes', width: 50 },
      { header: 'brand.slug', key: 'brandSlug', width: 24 },
      { header: 'compatibilityLevel', key: 'compatibilityLevel', width: 16 },
    ]

    const rows = await this.db.attributeSynonym.findMany({
      select: {
        id: true,
        value: true,
        notes: true,
        compatibilityLevel: true,
        group: {
          select: {
            name: true,
            template: { select: { name: true } }
          }
        },
        brand: { select: { slug: true } },
      },
      orderBy: [
        { group: { template: { name: 'asc' } } },
        { group: { name: 'asc' } },
        { value: 'asc' }
      ],
    } as any)

    for (const r of rows) ws.addRow({
      id: r.id,
      groupName: r.group.name,
      templateName: r.group.template.name,
      value: r.value,
      notes: r.notes ?? null,
      brandSlug: r.brand?.slug ?? null,
      compatibilityLevel: r.compatibilityLevel ?? null,
    })
  }

  // Метод для создания пустых шаблонов
  async buildTemplateWorkbook(include: ExportOptions['include']) {
    const wb = new ExcelJS.Workbook()

    // META лист с базовой информацией
    const meta = wb.addWorksheet('META')
    const metaObj = {
      version: 1,
      exportedAt: new Date().toISOString(),
      exporterVersion: 'template_v1',
      schemaHash: 'catalog_schema_v1',
      includedSheets: Object.entries(include)
        .filter(([, v]) => !!v)
        .map(([k]) => k),
      modes: {
        createMissingRefs: false,
        onConflict: 'upsert',
      },
    }
    meta.columns = [
      { header: 'key', key: 'key', width: 32 },
      { header: 'value', key: 'value', width: 120 },
    ]
    for (const [k, v] of Object.entries(metaObj)) {
      meta.addRow({ key: k, value: JSON.stringify(v) })
    }

    // Добавляем пустые листы с заголовками и примерными строками
    if (include.Brand) {
      this.addEmptyBrandSheet(wb)
    }
    if (include.PartCategory) {
      this.addEmptyPartCategorySheet(wb)
    }
    if (include.AttributeGroup) {
      this.addEmptyAttributeGroupSheet(wb)
    }
    if (include.AttributeTemplate) {
      this.addEmptyAttributeTemplateSheet(wb)
    }
    if (include.AttributeSynonymGroup) {
      this.addEmptyAttributeSynonymGroupSheet(wb)
    }
    if (include.AttributeSynonym) {
      this.addEmptyAttributeSynonymSheet(wb)
    }
    if (include.Part) {
      this.addEmptyPartSheet(wb)
    }
    if (include.CatalogItem) {
      this.addEmptyCatalogItemSheet(wb)
    }
    if (include.EquipmentModel) {
      this.addEmptyEquipmentModelSheet(wb)
    }
    if (include.PartAttribute) {
      this.addEmptyPartAttributeSheet(wb)
    }
    if (include.CatalogItemAttribute) {
      this.addEmptyCatalogItemAttributeSheet(wb)
    }
    if (include.EquipmentModelAttribute) {
      this.addEmptyEquipmentModelAttributeSheet(wb)
    }
    if (include.PartApplicability) {
      this.addEmptyPartApplicabilitySheet(wb)
    }
    if (include.EquipmentApplicability) {
      this.addEmptyEquipmentApplicabilitySheet(wb)
    }

    const buffer = await wb.xlsx.writeBuffer()
    const fileName = `catalog-template-${Date.now()}.xlsx`
    return { buffer: Buffer.from(buffer), fileName }
  }

  // Методы для создания пустых листов (только заголовки + примерная строка)
  private addEmptyBrandSheet(wb: ExcelJS.Workbook) {
    const ws = wb.addWorksheet('Brand')
    ws.columns = [
      { header: 'id', key: 'id', width: 10 },
      { header: '__key', key: '__key', width: 16 },
      { header: 'name', key: 'name', width: 32 },
      { header: 'slug', key: 'slug', width: 32 },
      { header: 'country', key: 'country', width: 24 },
      { header: 'isOem', key: 'isOem', width: 10 },
    ]
    // Примерная строка
    ws.addRow({
      id: '(auto)',
      __key: 'caterpillar',
      name: 'Caterpillar',
      slug: 'caterpillar',
      country: 'USA',
      isOem: 'true'
    })
  }

  private addEmptyPartCategorySheet(wb: ExcelJS.Workbook) {
    const ws = wb.addWorksheet('PartCategory')
    ws.columns = [
      { header: 'id', key: 'id', width: 10 },
      { header: '__key', key: '__key', width: 16 },
      { header: 'name', key: 'name', width: 32 },
      { header: 'slug', key: 'slug', width: 32 },
      { header: 'description', key: 'description', width: 50 },
      { header: 'parent.slug', key: 'parentSlug', width: 32 },
    ]
    ws.addRow({
      id: '(auto)',
      __key: 'engine',
      name: 'Двигатель',
      slug: 'engine',
      description: 'Компоненты двигателя',
      parentSlug: null
    })
  }

  private addEmptyAttributeGroupSheet(wb: ExcelJS.Workbook) {
    const ws = wb.addWorksheet('AttributeGroup')
    ws.columns = [
      { header: 'id', key: 'id', width: 10 },
      { header: '__key', key: '__key', width: 16 },
      { header: 'name', key: 'name', width: 32 },
      { header: 'description', key: 'description', width: 50 },
      { header: 'parent.name', key: 'parentName', width: 32 },
    ]
    ws.addRow({
      id: '(auto)',
      __key: 'dimensions',
      name: 'Размеры',
      description: 'Габаритные размеры',
      parentName: null
    })
  }

  private addEmptyAttributeTemplateSheet(wb: ExcelJS.Workbook) {
    const ws = wb.addWorksheet('AttributeTemplate')
    ws.columns = [
      { header: 'id', key: 'id', width: 10 },
      { header: '__key', key: '__key', width: 16 },
      { header: 'name', key: 'name', width: 32 },
      { header: 'title', key: 'title', width: 32 },
      { header: 'dataType', key: 'dataType', width: 12 },
      { header: 'unit', key: 'unit', width: 12 },
      { header: 'isRequired', key: 'isRequired', width: 12 },
      { header: 'minValue', key: 'minValue', width: 12 },
      { header: 'maxValue', key: 'maxValue', width: 12 },
      { header: 'allowedValues', key: 'allowedValues', width: 30 },
      { header: 'tolerance', key: 'tolerance', width: 12 },
      { header: 'group.name', key: 'groupName', width: 32 },
    ]
    ws.addRow({
      id: '(auto)',
      __key: 'inner_diameter',
      name: 'inner_diameter',
      title: 'Внутренний диаметр',
      dataType: 'NUMBER',
      unit: 'MM',
      isRequired: 'false',
      minValue: '0',
      maxValue: '1000',
      allowedValues: null,
      tolerance: '0.1',
      groupName: 'Размеры'
    })
  }

  private addEmptyAttributeSynonymGroupSheet(wb: ExcelJS.Workbook) {
    const ws = wb.addWorksheet('AttributeSynonymGroup')
    ws.columns = [
      { header: 'id', key: 'id', width: 10 },
      { header: '__key', key: '__key', width: 16 },
      { header: 'template.name', key: 'templateName', width: 32 },
      { header: 'name', key: 'name', width: 32 },
      { header: 'description', key: 'description', width: 50 },
      { header: 'parent.name', key: 'parentName', width: 32 },
      { header: 'canonicalValue', key: 'canonicalValue', width: 24 },
      { header: 'compatibilityLevel', key: 'compatibilityLevel', width: 16 },
      { header: 'notes', key: 'notes', width: 50 },
    ]
    ws.addRow({
      id: '(auto)',
      __key: 'seal_types',
      templateName: 'seal_type',
      name: 'Типы уплотнений',
      description: 'Стандартные типы уплотнений',
      parentName: null,
      canonicalValue: 'TC',
      compatibilityLevel: 'EXACT',
      notes: null
    })
  }

  private addEmptyAttributeSynonymSheet(wb: ExcelJS.Workbook) {
    const ws = wb.addWorksheet('AttributeSynonym')
    ws.columns = [
      { header: 'id', key: 'id', width: 10 },
      { header: '__key', key: '__key', width: 16 },
      { header: 'group.name', key: 'groupName', width: 32 },
      { header: 'template.name', key: 'templateName', width: 32 },
      { header: 'value', key: 'value', width: 24 },
      { header: 'notes', key: 'notes', width: 50 },
      { header: 'brand.slug', key: 'brandSlug', width: 24 },
      { header: 'compatibilityLevel', key: 'compatibilityLevel', width: 16 },
    ]
    ws.addRow({
      id: '(auto)',
      __key: 'tc_seal',
      groupName: 'Типы уплотнений',
      templateName: 'seal_type',
      value: 'TC',
      notes: null,
      brandSlug: null,
      compatibilityLevel: null
    })
  }

  private addEmptyPartSheet(wb: ExcelJS.Workbook) {
    const ws = wb.addWorksheet('Part')
    ws.columns = [
      { header: 'id', key: 'id', width: 10 },
      { header: '__key', key: '__key', width: 16 },
      { header: 'partCategory.slug', key: 'partCategorySlug', width: 32 },
      { header: 'path', key: 'path', width: 40 },
      { header: 'name', key: 'name', width: 32 },
      { header: 'parent.path', key: 'parentPath', width: 40 },
    ]
    ws.addRow({
      id: '(auto)',
      __key: 'engine/seal/front',
      partCategorySlug: 'engine',
      path: 'engine/seal/front',
      name: 'Сальник коленвала передний',
      parentPath: null
    })
  }

  private addEmptyCatalogItemSheet(wb: ExcelJS.Workbook) {
    const ws = wb.addWorksheet('CatalogItem')
    ws.columns = [
      { header: 'id', key: 'id', width: 10 },
      { header: '__key', key: '__key', width: 16 },
      { header: 'sku', key: 'sku', width: 24 },
      { header: 'brand.slug', key: 'brandSlug', width: 24 },
      { header: 'description', key: 'description', width: 50 },
      { header: 'source', key: 'source', width: 24 },
      { header: 'isPublic', key: 'isPublic', width: 10 },
    ]
    ws.addRow({
      id: '(auto)',
      __key: 'CAT123456',
      sku: 'CAT123456',
      brandSlug: 'caterpillar',
      description: 'Сальник коленвала',
      source: 'Официальный каталог',
      isPublic: 'true'
    })
  }


  private addEmptyPartAttributeSheet(wb: ExcelJS.Workbook) {
    const ws = wb.addWorksheet('PartAttribute')
    ws.columns = [
      { header: 'id', key: 'id', width: 10 },
      { header: '__key', key: '__key', width: 16 },
      { header: 'part.path', key: 'partPath', width: 40 },
      { header: 'partCategory.slug', key: 'partCategorySlug', width: 32 },
      { header: 'template.name', key: 'templateName', width: 32 },
      { header: 'value', key: 'value', width: 24 },
      { header: 'numericValue', key: 'numericValue', width: 16 },
    ]
    ws.addRow({
      id: '(auto)',
      __key: 'engine/seal/front::inner_diameter',
      partPath: 'engine/seal/front',
      partCategorySlug: 'engine',
      templateName: 'inner_diameter',
      value: '25',
      numericValue: '25'
    })
  }

  private addEmptyCatalogItemAttributeSheet(wb: ExcelJS.Workbook) {
    const ws = wb.addWorksheet('CatalogItemAttribute')
    ws.columns = [
      { header: 'id', key: 'id', width: 10 },
      { header: '__key', key: '__key', width: 16 },
      { header: 'catalogItem.sku', key: 'sku', width: 32 },
      { header: 'brand.slug', key: 'brandSlug', width: 32 },
      { header: 'template.name', key: 'templateName', width: 32 },
      { header: 'value', key: 'value', width: 24 },
      { header: 'numericValue', key: 'numericValue', width: 16 },
    ]
    ws.addRow({
      id: '(auto)',
      __key: 'CAT123456::inner_diameter',
      sku: 'CAT123456',
      brandSlug: 'caterpillar',
      templateName: 'inner_diameter',
      value: '25',
      numericValue: '25'
    })
  }

  private addEmptyEquipmentModelAttributeSheet(wb: ExcelJS.Workbook) {
    const ws = wb.addWorksheet('EquipmentModelAttribute')
    ws.columns = [
      { header: 'id', key: 'id', width: 10 },
      { header: '__key', key: '__key', width: 16 },
      { header: 'equipmentModel.id', key: 'equipmentModelId', width: 36 },
      { header: 'template.name', key: 'templateName', width: 32 },
      { header: 'value', key: 'value', width: 24 },
      { header: 'numericValue', key: 'numericValue', width: 16 },
    ]
    ws.addRow({
      id: '(auto)',
      __key: 'cat-320d::inner_diameter',
      equipmentModelId: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
      templateName: 'inner_diameter',
      value: '25',
      numericValue: '25'
    })
  }

  private addEmptyPartApplicabilitySheet(wb: ExcelJS.Workbook) {
    const ws = wb.addWorksheet('PartApplicability')
    ws.columns = [
      { header: 'id', key: 'id', width: 10 },
      { header: '__key', key: '__key', width: 16 },
      { header: 'partCategory.slug', key: 'partCategorySlug', width: 32 },
      { header: 'part.path', key: 'partPath', width: 40 },
      { header: 'catalogItem.sku', key: 'sku', width: 32 },
      { header: 'brand.slug', key: 'brandSlug', width: 32 },
      { header: 'accuracy', key: 'accuracy', width: 20 },
      { header: 'notes', key: 'notes', width: 50 },
    ]
    ws.addRow({
      id: '(auto)',
      __key: 'engine/seal/front::CAT123456',
      partCategorySlug: 'engine',
      partPath: 'engine/seal/front',
      sku: 'CAT123456',
      brandSlug: 'caterpillar',
      accuracy: 'EXACT_MATCH',
      notes: null
    })
  }

  private addEmptyEquipmentApplicabilitySheet(wb: ExcelJS.Workbook) {
    const ws = wb.addWorksheet('EquipmentApplicability')
    ws.columns = [
      { header: 'id', key: 'id', width: 10 },
      { header: '__key', key: '__key', width: 16 },
      { header: 'partCategory.slug', key: 'partCategorySlug', width: 32 },
      { header: 'part.path', key: 'partPath', width: 40 },
      { header: 'equipmentModel.id', key: 'equipmentModelId', width: 36 },
      { header: 'notes', key: 'notes', width: 50 },
    ]
    ws.addRow({
      id: '(auto)',
      __key: 'engine/seal/front::cat-320d',
      partCategorySlug: 'engine',
      partPath: 'engine/seal/front',
      equipmentModelId: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
      notes: null
    })
  }

  private addEmptyEquipmentModelSheet(wb: ExcelJS.Workbook) {
    const ws = wb.addWorksheet('EquipmentModel')
    ws.columns = [
      { header: 'id', key: 'id', width: 36 },
      { header: '__key', key: '__key', width: 16 },
      { header: 'name', key: 'name', width: 32 },
      { header: 'brand.slug', key: 'brandSlug', width: 24 },
    ]
    ws.addRow({
      id: '(auto)',
      __key: 'cat-320d',
      name: 'Экскаватор CAT 320D',
      brandSlug: 'caterpillar'
    })
  }
}

