import{w as K}from"./runtime-dom.esm-bundler.C-dfRCGi.js";import{r as S,n as p,t as w}from"./reactivity.esm-bundler.D5IypM4U.js";import{h as c,i as A,w as D,d as Q,c as l,o as r,b as i,g as h,V as _,a as g,F as E,r as F,I as R}from"./index.BglzLLgy.js";import{_ as U}from"./_plugin-vue_export-helper.BX07OBiL.js";/* empty css                                */import{c as I}from"./createLucideIcon.3yDVQAYz.js";import{C as Y}from"./check.BMRaCSED.js";/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const N=I("monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const M=I("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const L=I("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]),P="parttec-theme",u=S("system"),v=S(!1),C=c(()=>u.value==="system"?v.value?"dark":"light":u.value),Z=o=>{if(typeof document<"u"){const s=document.documentElement;o==="dark"?(s.setAttribute("data-theme","dark"),s.classList.add("dark")):(s.removeAttribute("data-theme"),s.classList.remove("dark"))}},H=()=>{typeof window<"u"&&window.matchMedia&&(v.value=window.matchMedia("(prefers-color-scheme: dark)").matches)},J=()=>{if(typeof window<"u"){const o=localStorage.getItem(P);if(o&&["light","dark","system"].includes(o))return o}return"system"},W=o=>{typeof window<"u"&&localStorage.setItem(P,o)},X=()=>{A(()=>{if(u.value=J(),H(),typeof window<"u"&&window.matchMedia){const a=window.matchMedia("(prefers-color-scheme: dark)"),f=b=>{v.value=b.matches};return a.addEventListener("change",f),()=>{a.removeEventListener("change",f)}}}),D(C,a=>{Z(a)},{immediate:!0}),D(u,a=>{W(a)});const o=a=>{u.value=a},s=()=>{if(u.value==="system"){const a=v.value?"light":"dark";o(a)}else u.value==="light"?o("dark"):o("light")},n=()=>{o("system")},e=c(()=>C.value==="dark"),k=c(()=>C.value==="light"),y=c(()=>u.value==="system"),t=c(()=>{switch(u.value){case"light":return"Sun";case"dark":return"Moon";case"system":return"Monitor";default:return"Monitor"}}),d=c(()=>{switch(u.value){case"light":return"Светлая";case"dark":return"Темная";case"system":return"Системная";default:return"Системная"}});return{currentTheme:c(()=>u.value),activeTheme:C,systemPrefersDark:c(()=>v.value),isDark:e,isLight:k,isSystem:y,themeIcon:t,themeName:d,setTheme:o,toggleTheme:s,resetToSystem:n}},$=Q({__name:"ThemeToggle",props:{mode:{default:"toggle"},showLabel:{type:Boolean,default:!1},buttonClass:{default:""}},setup(o,{expose:s}){s();const n=o,{currentTheme:e,activeTheme:k,isDark:y,isLight:t,isSystem:d,themeIcon:a,themeName:f,systemPrefersDark:b,setTheme:z,toggleTheme:j}=X(),m=S(!1),O=c(()=>{switch(e.value){case"light":return M;case"dark":return L;case"system":return b.value?L:M;default:return N}}),V=[{value:"light",label:"Светлая",icon:L},{value:"dark",label:"Темная",icon:M},{value:"system",label:"Системная",icon:N}],q=()=>{m.value=!m.value},G=x=>{z(x),m.value=!1},T=x=>{x.target.closest(".theme-toggle")||(m.value=!1)};A(()=>{document.addEventListener("click",T)}),R(()=>{document.removeEventListener("click",T)});const B={props:n,currentTheme:e,activeTheme:k,isDark:y,isLight:t,isSystem:d,themeIcon:a,themeName:f,systemPrefersDark:b,setTheme:z,toggleTheme:j,showMenu:m,themeIconComponent:O,themes:V,toggleMenu:q,selectTheme:G,handleClickOutside:T,get Check(){return Y}};return Object.defineProperty(B,"__isScriptSetup",{enumerable:!1,value:!0}),B}}),ee={class:"theme-toggle"},te=["title"],oe={key:0,class:"ml-2"},re={key:1,class:"relative"},se=["title"],ne={key:0,class:"ml-2"},ae={class:"py-1"},ue=["onClick"],le={key:2,class:"flex rounded-[--radius-md] border border-[--color-border] overflow-hidden"},ce=["onClick","title"],ie={key:0,class:"ml-2"};function de(o,s,n,e,k,y){return r(),l("div",ee,[n.mode==="toggle"?(r(),l("button",{key:0,onClick:s[0]||(s[0]=(...t)=>e.toggleTheme&&e.toggleTheme(...t)),class:p([n.buttonClass,"p-2 rounded-[--radius-md] text-[--color-foreground] hover:bg-[--p-content-hover-background] transition-colors"]),title:`Переключить тему (текущая: ${e.themeName})`},[(r(),h(_(e.themeIconComponent),{size:20})),n.showLabel?(r(),l("span",oe,w(e.themeName),1)):i("",!0)],10,te)):n.mode==="menu"?(r(),l("div",re,[g("button",{onClick:e.toggleMenu,class:p([n.buttonClass,"p-2 rounded-[--radius-md] text-[--color-foreground] hover:bg-[--p-content-hover-background] transition-colors flex items-center"]),title:`Выбрать тему (текущая: ${e.themeName})`},[(r(),h(_(e.themeIconComponent),{size:20})),n.showLabel?(r(),l("span",ne,w(e.themeName),1)):i("",!0)],10,se),e.showMenu?(r(),l("div",{key:0,class:"absolute right-0 mt-2 w-48 bg-[--color-card] rounded-[--radius-md] [box-shadow:var(--shadow-lg)] border border-[--color-border] z-50",onClick:s[1]||(s[1]=K(()=>{},["stop"]))},[g("div",ae,[(r(),l(E,null,F(e.themes,t=>g("button",{key:t.value,onClick:d=>e.selectTheme(t.value),class:p(["flex items-center w-full px-4 py-2 text-sm text-[--color-foreground] hover:bg-[--p-content-hover-background] transition-colors",{"bg-[--p-highlight-background] text-[--p-primary-color]":e.currentTheme===t.value}])},[(r(),h(_(t.icon),{size:16,class:"mr-3"})),g("span",null,w(t.label),1),e.currentTheme===t.value?(r(),h(e.Check,{key:0,size:16,class:"ml-auto text-[--p-primary-color]"})):i("",!0)],10,ue)),64))])])):i("",!0)])):n.mode==="buttons"?(r(),l("div",le,[(r(),l(E,null,F(e.themes,t=>g("button",{key:t.value,onClick:d=>e.selectTheme(t.value),class:p(["flex items-center px-3 py-2 text-sm transition-colors border-r border-[--color-border] last:border-r-0",{"bg-[--color-primary] text-[--color-primary-foreground]":e.currentTheme===t.value,"bg-[--color-card] text-[--color-foreground] hover:bg-[--p-content-hover-background]":e.currentTheme!==t.value}]),title:`Выбрать ${t.label.toLowerCase()} тему`},[(r(),h(_(t.icon),{size:16})),n.showLabel?(r(),l("span",ie,w(t.label),1)):i("",!0)],10,ce)),64))])):i("",!0)])}const pe=U($,[["render",de],["__scopeId","data-v-94d58d16"]]);export{pe as T};
