/**
 * Composable-адаптер для фильтров поиска (URL <-> внутренние типы)
 * Источник истины по типам фильтров — Zod-схемы из сервера и utils/filters
 */

import { computed } from 'vue'
import { useUrlParams } from './useUrlParams'
import type { CatalogFilters } from '@/utils/filters'

// Zod-схемы Zenstack/серверные для типобезопасного ввода на сервер
import { z } from 'zod'
import { SearchPartsInput as SearchPartsInputSchema } from '../../../api/schemas/search'

export type SearchPartsInput = z.infer<typeof SearchPartsInputSchema>

/**
 * URL-синхронизация для поисковых фильтров.
 * Держим URL-параметры плоскими и простыми, а наружу отдаем удобные адаптеры.
 */
export function useSearchFilters(initial?: Partial<{
  query: string
  categoryIds: number[]
  brandIds: number[]
  equipmentModelIds: string[]
  page: number
  limit: number
}>) {
  // 1) Плоское состояние, синхронизируемое с URL
  const urlSync = useUrlParams({
    query: initial?.query ?? '',
    categoryIds: initial?.categoryIds ?? [],
    brandIds: initial?.brandIds ?? [],
    equipmentModelIds: initial?.equipmentModelIds ?? [],
    page: initial?.page ?? 1,
    limit: initial?.limit ?? 20,
  }, {
    prefix: 'search_',
    arrayParams: ['categoryIds', 'brandIds', 'equipmentModelIds'],
    numberParams: ['page', 'limit'],
    debounceMs: 300,
  })

  // 2) Внутреннее представление для UI, совместимое с CatalogFilters из utils/filters
  const catalogFilters = computed<CatalogFilters>(() => {
    const f: any = urlSync.filters.value
    return {
      search: f.query || undefined,
      categoryIds: Array.isArray(f.categoryIds) && f.categoryIds.length ? f.categoryIds.map(Number) : undefined,
      brandIds: Array.isArray(f.brandIds) && f.brandIds.length ? f.brandIds.map(Number) : undefined,
      equipmentModelIds: Array.isArray(f.equipmentModelIds) && f.equipmentModelIds.length ? f.equipmentModelIds : undefined,
      // Цен нет в поиске по умолчанию; можно добавить при необходимости
    }
  })

  // 3) Готовый к серверу input по Zod-схеме (SearchPartsInput)
  const serverInput = computed<SearchPartsInput>(() => {
    const f: any = urlSync.filters.value
    const limit = Number(f.limit) || 20
    const page = Number(f.page) || 1

    const candidate: Partial<SearchPartsInput> = {
      name: (f.query && String(f.query).trim()) || undefined,
      categoryIds: Array.isArray(f.categoryIds) && f.categoryIds.length ? f.categoryIds.map(Number) : undefined,
      brandIds: Array.isArray(f.brandIds) && f.brandIds.length ? f.brandIds.map(Number) : undefined,
      // attributeFilters добавляются на уровне компонента и могут быть объединены через mergeServerInput
      limit,
      offset: (page - 1) * limit,
      orderBy: 'name',
      orderDir: 'asc',
    }

    // Валидация и нормализация по Zod, чтобы не отправлять мусор
    const parsed = SearchPartsInputSchema.safeParse(candidate)
    if (parsed.success) return parsed.data

    // Если валидация не прошла, возвращаем минимально валидный input
    return {
      name: candidate.name,
      limit: limit,
      offset: (page - 1) * limit,
      orderBy: 'name',
      orderDir: 'asc',
    }
  })

  // 4) Удобные апдейтеры (работают с URL-синком)
  const setQuery = (q: string) => urlSync.updateFilter('query' as any, q || '')
  const setCategoryIds = (ids: number[]) => urlSync.updateFilter('categoryIds' as any, ids || [])
  const setBrandIds = (ids: number[]) => urlSync.updateFilter('brandIds' as any, ids || [])
  const setEquipmentModelIds = (ids: string[]) => urlSync.updateFilter('equipmentModelIds' as any, ids || [])
  const setPage = (p: number) => urlSync.updateFilter('page' as any, Number(p) || 1)
  const setLimit = (n: number) => urlSync.updateFilter('limit' as any, Number(n) || 20)

  /**
   * Объединение serverInput с дополнительными фильтрами компонента (например, attributeFilters)
   */
  const mergeServerInput = (extra: Partial<SearchPartsInput>): SearchPartsInput => {
    const base = serverInput.value
    const merged = { ...base, ...extra }
    const parsed = SearchPartsInputSchema.safeParse(merged)
    return parsed.success ? parsed.data : base
  }

  return {
    // Back-compat: filters — это плоское URL-состояние как и раньше
    filters: urlSync.filters,

    // Внутренние фильтры каталога (единые типы)
    catalogFilters,

    // Состояние активности
    hasActiveFilters: urlSync.hasActiveFilters,

    // Для запросов на сервер
    serverInput,
    mergeServerInput,

    // Методы
    setQuery,
    setCategoryIds,
    setBrandIds,
    setEquipmentModelIds,
    setPage,
    setLimit,

    // Проксируем базовые методы при необходимости
    updateFilter: urlSync.updateFilter,
    updateFilters: urlSync.updateFilters,
    resetFilters: urlSync.resetFilters,
    loadFromUrl: urlSync.loadFromUrl,
    saveToUrl: urlSync.saveToUrl,
  }
}

