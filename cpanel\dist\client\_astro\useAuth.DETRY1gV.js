import{u as T,i as C,a as D,c as F,b as u,h as N,g as i}from"./auth-client.D7sWEz1h.js";import{o as p,e as j,s as o,b as v}from"./types.FgRm47Sn.js";import{a as x,d as L}from"./reactivity.esm-bundler.D5IypM4U.js";import{h as a}from"./index.BglzLLgy.js";const k=p({email:o().email(),password:o().min(6),name:o().min(1),role:j(["USER","ADMIN","SHOP"]).optional().default("USER")}),g=p({email:o().email("Введите корректный email"),password:o().min(6,"Пароль должен содержать минимум 6 символов"),rememberMe:v().optional().default(!1)}),d=k.extend({password:o().min(6,"Пароль должен содержать минимум 6 символов"),confirmPassword:o(),acceptTerms:v().refine(t=>t===!0,"Необходимо принять условия использования")}).refine(t=>t.password===t.confirmPassword,{message:"Пароли не совпадают",path:["confirmPassword"]}),n=x({signIn:!1,signUp:!1,signOut:!1,session:!1});function G(){const t=T(),c=a(()=>t.value?.data||null),s=a(()=>c.value?.user),w=a(()=>!!c.value?.user),h=a(()=>Object.values(n).some(e=>e)),m=async()=>{try{const e=await u.getSession();if(e.data)return e.data}catch(e){console.error("Error refetching session:",e)}},y=a(()=>s.value?.role||null),S=a(()=>s.value?C(s.value):!1),R=a(()=>s.value?D(s.value):!1),U=a(()=>s.value?.role==="USER"),A=a(()=>s.value?F(s.value):!1),O=async e=>{const l=g.safeParse(e);if(!l.success)return{error:{message:"Ошибка валидации",details:l.error.issues}};n.signIn=!0;try{const r=await u.signIn.email({email:e.email,password:e.password,rememberMe:e.rememberMe});return r.error?{error:{message:i(r.error)}}:(await m(),{data:r.data})}catch(r){return{error:{message:i(r,"Произошла ошибка при входе")}}}finally{n.signIn=!1}},b=async e=>{const l=d.safeParse(e);if(!l.success)return{error:{message:"Ошибка валидации",details:l.error.issues}};n.signUp=!0;try{const r=await u.signUp.email({email:e.email,password:e.password,name:e.name||""});if(r.error)return{error:{message:i(r.error)}};if(e.role&&e.role!=="USER")try{const f=e.role==="ADMIN"?"admin":"user";await u.admin.setRole({userId:r.data?.user?.id,role:f})}catch(f){console.warn("Failed to set user role:",f)}return{data:r.data}}catch(r){return{error:{message:i(r,"Произошла ошибка при регистрации")}}}finally{n.signUp=!1}},I=async()=>{n.signOut=!0;try{const e=await u.signOut();return e.error?{error:{message:i(e.error)}}:(await m(),{data:e.data})}catch(e){return{error:{message:i(e,"Произошла ошибка при выходе")}}}finally{n.signOut=!1}},E=e=>s.value?N(s.value,e):!1,M=a(()=>s.value?s.value.name||s.value.email:null),P=a(()=>s.value?.image||null);return{user:s,session:c,isAuthenticated:w,isLoading:h,userRole:y,isUserAdmin:S,isUserShopOwner:R,isUserRegular:U,userCanAccessAdmin:A,displayName:M,userAvatar:P,signIn:O,signUp:b,signOut:I,checkRole:E,refetchSession:m,loadingState:L(n),LoginFormSchema:g,RegisterFormSchema:d}}export{G as u};
