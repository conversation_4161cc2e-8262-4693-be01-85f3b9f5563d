import { z } from 'zod'
import { router, procedure } from '../trpc'
import { PartAttributesService } from '../services/part-attributes.service'

// Новые схемы для работы с шаблонами атрибутов
const CreatePartAttributeInputSchema = z.object({
  partId: z.number(),
  templateId: z.number(), // ID шаблона из AttributeTemplate
  value: z.string().min(1) // Только значение, все остальное берется из шаблона
});

const UpdatePartAttributeInputSchema = z.object({
  id: z.number(),
  value: z.string().min(1)
});

const BulkCreatePartAttributesInputSchema = z.object({
  partId: z.number(),
  attributes: z.array(z.object({
    templateId: z.number(),
    value: z.string().min(1)
  }))
});

/**
 * Роутер для работы с атрибутами запчастей на основе шаблонов
 * Новая упрощенная архитектура: PartAttribute содержит только partId, templateId и value
 */
export const partAttributesRouter = router({

  /**
   * Создание атрибута запчасти на основе шаблона
   */
  create: procedure
    .input(CreatePartAttributeInputSchema)
    .mutation(async ({ input }: { input: z.infer<typeof CreatePartAttributeInputSchema> }) => PartAttributesService.create(input)),

  /**
   * Получение атрибутов запчасти с информацией о шаблонах
   */
  findByPartId: procedure
    .input(z.object({
      partId: z.number()
    }))
    .query(async ({ input }: { input: { partId: number } }) => PartAttributesService.findByPartId(input)),

  /**
   * Обновление значения атрибута запчасти
   */
  update: procedure
    .input(UpdatePartAttributeInputSchema)
    .mutation(async ({ input }: { input: z.infer<typeof UpdatePartAttributeInputSchema> }) => PartAttributesService.update(input)),

  /**
   * Удаление атрибута запчасти
   */
  delete: procedure
    .input(z.object({
      id: z.number()
    }))
    .mutation(async ({ input }: { input: { id: number } }) => PartAttributesService.delete(input)),

  /**
   * Массовое создание атрибутов для запчасти
   */
  bulkCreate: procedure
    .input(BulkCreatePartAttributesInputSchema)
    .mutation(async ({ input }: { input: z.infer<typeof BulkCreatePartAttributesInputSchema> }) => PartAttributesService.bulkCreate(input))
});
