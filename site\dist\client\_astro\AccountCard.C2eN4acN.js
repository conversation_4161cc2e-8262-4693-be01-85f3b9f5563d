import{j as s}from"./jsx-runtime.D_zvdyIk.js";import"./index.0yr9KlQE.js";import{a}from"./auth-client.DHpPh7L9.js";import{C as r,a as n}from"./card.AN6mqHU1.js";import{B as o}from"./button.BfGcEciz.js";import"./utils.CBfrqCZ4.js";import"./index.BWNh2K4G.js";import"./index.3rXK4OIH.js";function j({initialUser:i}){const{data:t}=a.useSession(),e=t?.user??i;return s.jsx(r,{children:s.jsxs(n,{className:"p-6 space-y-4",children:[s.jsxs("div",{className:"space-y-1",children:[s.jsx("div",{className:"text-sm text-muted-foreground",children:"Email"}),s.jsx("div",{className:"font-medium",children:e?.email})]}),e?.name&&s.jsxs("div",{className:"space-y-1",children:[s.jsx("div",{className:"text-sm text-muted-foreground",children:"Имя"}),s.jsx("div",{className:"font-medium",children:e?.name})]}),e?.role&&s.jsxs("div",{className:"space-y-1",children:[s.jsx("div",{className:"text-sm text-muted-foreground",children:"Роль"}),s.jsx("div",{className:"font-medium",children:e?.role})]}),s.jsx("div",{children:s.jsx(o,{variant:"secondary",onClick:async()=>{await a.signOut({fetchOptions:{onSuccess:()=>window.location.href="/"}})},children:"Выйти"})})]})})}export{j as default};
