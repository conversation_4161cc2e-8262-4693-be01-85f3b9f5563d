{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "bundler", "strict": true, "jsx": "react-jsx", "jsxImportSource": "hono/jsx", "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "declaration": true, "outDir": "dist", "rootDir": ".", "resolveJsonModule": true}, "include": ["**/*.ts", "**/*.tsx"], "exclude": ["node_modules", "dist", "prisma"]}