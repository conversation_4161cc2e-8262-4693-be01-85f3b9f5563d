import { e as createComponent, k as renderComponent, r as renderTemplate } from '../../chunks/astro/server_DbndhTWv.mjs';
import 'kleur/colors';
import { I as Icon, $ as $$AdminLayout } from '../../chunks/AdminLayout_b20tykPC.mjs';
import { resolveComponent, resolveDirective, createElementBlock, openBlock, mergeProps, createElementVNode, createCommentVNode, withDirectives, createBlock, Fragment, resolveDynamicComponent, normalizeClass, withCtx, withModifiers, createTextVNode, toDisplayString, renderList, renderSlot, createVNode, defineComponent, useSSRContext, ref, createSlots, computed, nextTick, watch, onMounted, onUnmounted } from 'vue';
import { E as ErrorBoundary } from '../../chunks/ErrorBoundary_CGDiV8up.mjs';
import { u as useTrpc } from '../../chunks/useTrpc_B-zNiBto.mjs';
import { u as useUrlParams } from '../../chunks/useUrlParams_B8aK7qAz.mjs';
import { V as VCard } from '../../chunks/Card_BWxK5e93.mjs';
import { V as VButton } from '../../chunks/Button_CuwpNmer.mjs';
import { I as InputText } from '../../chunks/InputText_CtReD0EA.mjs';
import { V as VTextarea } from '../../chunks/Textarea_DrL1j52W.mjs';
import { S as Select } from '../../chunks/Select_Bk34xTKM.mjs';
import { D as DataTable, s as script$7, a as script$8 } from '../../chunks/index_BK2zpZlm.mjs';
import { T as Tag } from '../../chunks/Tag_DnSNoHBb.mjs';
import { D as Dialog } from '../../chunks/Dialog_g9jHgXMZ.mjs';
import ChevronDownIcon from '@primevue/icons/chevrondown';
import ChevronRightIcon from '@primevue/icons/chevronright';
import SearchIcon from '@primevue/icons/search';
import { cn } from '@primeuix/utils';
import { isFunction, resolveFieldData } from '@primeuix/utils/object';
import SpinnerIcon from '@primevue/icons/spinner';
import { s as script$3, a as script$4 } from '../../chunks/index_DUZyrleV.mjs';
import { s as script$5 } from '../../chunks/index_Cf2gUSb2.mjs';
import BaseComponent from '@primevue/core/basecomponent';
import { style } from '@primeuix/styles/tree';
import BaseStyle from '@primevue/core/base/style';
import { getAttribute, findSingle, find } from '@primeuix/utils/dom';
import CheckIcon from '@primevue/icons/check';
import MinusIcon from '@primevue/icons/minus';
import { s as script$6 } from '../../chunks/index_DI-L7n6v.mjs';
import { R as Ripple, p as ptViewMerge, _ as _export_sfc, u as useAuth } from '../../chunks/ClientRouter_B8Zzhk9G.mjs';
import { ssrRenderComponent, ssrRenderSlot, ssrRenderAttrs, ssrRenderList, ssrRenderClass, ssrInterpolate, ssrRenderAttr, ssrRenderStyle } from 'vue/server-renderer';
import { u as useToast } from '../../chunks/useToast_CXW6AFGp.mjs';
import { V as VInputNumber } from '../../chunks/InputNumber_BuB59rTf.mjs';
import TimesIcon from '@primevue/icons/times';
import { C as Checkbox } from '../../chunks/Checkbox_aVHeO0Hn.mjs';
import { V as VAutoComplete } from '../../chunks/AutoComplete_CPZ3fUb-.mjs';
import { TrashIcon, PencilIcon, PlusIcon, ChevronRightIcon as ChevronRightIcon$1, ChevronDownIcon as ChevronDownIcon$1, PlusCircleIcon, FileIcon, FolderIcon, TagsIcon, LoaderCircleIcon, PencilLineIcon, TagIcon, RefreshCwIcon } from 'lucide-vue-next';
import { D as DangerButton } from '../../chunks/DangerButton_CkG9WtM3.mjs';
/* empty css                                         */
export { r as renderers } from '../../chunks/_@astro-renderers_CicWY1rm.mjs';

var classes = {
  root: function root(_ref) {
    var props = _ref.props;
    return ['p-tree p-component', {
      'p-tree-selectable': props.selectionMode != null,
      'p-tree-loading': props.loading,
      'p-tree-flex-scrollable': props.scrollHeight === 'flex'
    }];
  },
  mask: 'p-tree-mask p-overlay-mask',
  loadingIcon: 'p-tree-loading-icon',
  pcFilterContainer: 'p-tree-filter',
  pcFilterInput: 'p-tree-filter-input',
  wrapper: 'p-tree-root',
  //TODO: discuss
  rootChildren: 'p-tree-root-children',
  node: function node(_ref2) {
    var instance = _ref2.instance;
    return ['p-tree-node', {
      'p-tree-node-leaf': instance.leaf
    }];
  },
  nodeContent: function nodeContent(_ref3) {
    var instance = _ref3.instance;
    return ['p-tree-node-content', instance.node.styleClass, {
      'p-tree-node-selectable': instance.selectable,
      'p-tree-node-selected': instance.checkboxMode && instance.$parentInstance.highlightOnSelect ? instance.checked : instance.selected
    }];
  },
  nodeToggleButton: 'p-tree-node-toggle-button',
  nodeToggleIcon: 'p-tree-node-toggle-icon',
  nodeCheckbox: 'p-tree-node-checkbox',
  nodeIcon: 'p-tree-node-icon',
  nodeLabel: 'p-tree-node-label',
  nodeChildren: 'p-tree-node-children'
};
var TreeStyle = BaseStyle.extend({
  name: 'tree',
  style: style,
  classes: classes
});

var script$2 = {
  name: 'BaseTree',
  "extends": BaseComponent,
  props: {
    value: {
      type: null,
      "default": null
    },
    expandedKeys: {
      type: null,
      "default": null
    },
    selectionKeys: {
      type: null,
      "default": null
    },
    selectionMode: {
      type: String,
      "default": null
    },
    metaKeySelection: {
      type: Boolean,
      "default": false
    },
    loading: {
      type: Boolean,
      "default": false
    },
    loadingIcon: {
      type: String,
      "default": undefined
    },
    loadingMode: {
      type: String,
      "default": 'mask'
    },
    filter: {
      type: Boolean,
      "default": false
    },
    filterBy: {
      type: [String, Function],
      "default": 'label'
    },
    filterMode: {
      type: String,
      "default": 'lenient'
    },
    filterPlaceholder: {
      type: String,
      "default": null
    },
    filterLocale: {
      type: String,
      "default": undefined
    },
    highlightOnSelect: {
      type: Boolean,
      "default": false
    },
    scrollHeight: {
      type: String,
      "default": null
    },
    level: {
      type: Number,
      "default": 0
    },
    ariaLabelledby: {
      type: String,
      "default": null
    },
    ariaLabel: {
      type: String,
      "default": null
    }
  },
  style: TreeStyle,
  provide: function provide() {
    return {
      $pcTree: this,
      $parentInstance: this
    };
  }
};

function _typeof$2(o) { "@babel/helpers - typeof"; return _typeof$2 = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof$2(o); }
function _createForOfIteratorHelper$1(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray$1(r)) || e) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: true } : { done: false, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = true, u = false; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = true, o = r; }, f: function f() { try { a || null == t["return"] || t["return"](); } finally { if (u) throw o; } } }; }
function ownKeys$2(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread$2(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys$2(Object(t), true).forEach(function (r) { _defineProperty$2(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$2(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty$2(e, r, t) { return (r = _toPropertyKey$2(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: true, configurable: true, writable: true }) : e[r] = t, e; }
function _toPropertyKey$2(t) { var i = _toPrimitive$2(t, "string"); return "symbol" == _typeof$2(i) ? i : i + ""; }
function _toPrimitive$2(t, r) { if ("object" != _typeof$2(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r); if ("object" != _typeof$2(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
function _toConsumableArray$1(r) { return _arrayWithoutHoles$1(r) || _iterableToArray$1(r) || _unsupportedIterableToArray$1(r) || _nonIterableSpread$1(); }
function _nonIterableSpread$1() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray$1(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray$1(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray$1(r, a) : void 0; } }
function _iterableToArray$1(r) { if ("undefined" != typeof Symbol && null != r[Symbol.iterator] || null != r["@@iterator"]) return Array.from(r); }
function _arrayWithoutHoles$1(r) { if (Array.isArray(r)) return _arrayLikeToArray$1(r); }
function _arrayLikeToArray$1(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
var script$1 = {
  name: 'TreeNode',
  hostName: 'Tree',
  "extends": BaseComponent,
  emits: ['node-toggle', 'node-click', 'checkbox-change'],
  props: {
    node: {
      type: null,
      "default": null
    },
    expandedKeys: {
      type: null,
      "default": null
    },
    loadingMode: {
      type: String,
      "default": 'mask'
    },
    selectionKeys: {
      type: null,
      "default": null
    },
    selectionMode: {
      type: String,
      "default": null
    },
    templates: {
      type: null,
      "default": null
    },
    level: {
      type: Number,
      "default": null
    },
    index: null
  },
  nodeTouched: false,
  toggleClicked: false,
  mounted: function mounted() {
    this.setAllNodesTabIndexes();
  },
  methods: {
    toggle: function toggle() {
      this.$emit('node-toggle', this.node);
      this.toggleClicked = true;
    },
    label: function label(node) {
      return typeof node.label === 'function' ? node.label() : node.label;
    },
    onChildNodeToggle: function onChildNodeToggle(node) {
      this.$emit('node-toggle', node);
    },
    getPTOptions: function getPTOptions(key) {
      return this.ptm(key, {
        context: {
          node: this.node,
          index: this.index,
          expanded: this.expanded,
          selected: this.selected,
          checked: this.checked,
          partialChecked: this.partialChecked,
          leaf: this.leaf
        }
      });
    },
    onClick: function onClick(event) {
      if (this.toggleClicked || getAttribute(event.target, '[data-pc-section="nodetogglebutton"]') || getAttribute(event.target.parentElement, '[data-pc-section="nodetogglebutton"]')) {
        this.toggleClicked = false;
        return;
      }
      if (this.isCheckboxSelectionMode()) {
        if (this.node.selectable != false) {
          this.toggleCheckbox();
        }
      } else {
        this.$emit('node-click', {
          originalEvent: event,
          nodeTouched: this.nodeTouched,
          node: this.node
        });
      }
      this.nodeTouched = false;
    },
    onChildNodeClick: function onChildNodeClick(event) {
      this.$emit('node-click', event);
    },
    onTouchEnd: function onTouchEnd() {
      this.nodeTouched = true;
    },
    onKeyDown: function onKeyDown(event) {
      if (!this.isSameNode(event)) return;
      switch (event.code) {
        case 'Tab':
          this.onTabKey(event);
          break;
        case 'ArrowDown':
          this.onArrowDown(event);
          break;
        case 'ArrowUp':
          this.onArrowUp(event);
          break;
        case 'ArrowRight':
          this.onArrowRight(event);
          break;
        case 'ArrowLeft':
          this.onArrowLeft(event);
          break;
        case 'Enter':
        case 'NumpadEnter':
        case 'Space':
          this.onEnterKey(event);
          break;
      }
    },
    onArrowDown: function onArrowDown(event) {
      var nodeElement = event.target.getAttribute('data-pc-section') === 'nodetogglebutton' ? event.target.closest('[role="treeitem"]') : event.target;
      var listElement = nodeElement.children[1];
      if (listElement) {
        this.focusRowChange(nodeElement, listElement.children[0]);
      } else {
        if (nodeElement.nextElementSibling) {
          this.focusRowChange(nodeElement, nodeElement.nextElementSibling);
        } else {
          var nextSiblingAncestor = this.findNextSiblingOfAncestor(nodeElement);
          if (nextSiblingAncestor) {
            this.focusRowChange(nodeElement, nextSiblingAncestor);
          }
        }
      }
      event.preventDefault();
    },
    onArrowUp: function onArrowUp(event) {
      var nodeElement = event.target;
      if (nodeElement.previousElementSibling) {
        this.focusRowChange(nodeElement, nodeElement.previousElementSibling, this.findLastVisibleDescendant(nodeElement.previousElementSibling));
      } else {
        var parentNodeElement = this.getParentNodeElement(nodeElement);
        if (parentNodeElement) {
          this.focusRowChange(nodeElement, parentNodeElement);
        }
      }
      event.preventDefault();
    },
    onArrowRight: function onArrowRight(event) {
      var _this = this;
      if (this.leaf || this.expanded) return;
      event.currentTarget.tabIndex = -1;
      this.$emit('node-toggle', this.node);
      this.$nextTick(function () {
        _this.onArrowDown(event);
      });
    },
    onArrowLeft: function onArrowLeft(event) {
      var togglerElement = findSingle(event.currentTarget, '[data-pc-section="nodetogglebutton"]');
      if (this.level === 0 && !this.expanded) {
        return false;
      }
      if (this.expanded && !this.leaf) {
        togglerElement.click();
        return false;
      }
      var target = this.findBeforeClickableNode(event.currentTarget);
      if (target) {
        this.focusRowChange(event.currentTarget, target);
      }
    },
    onEnterKey: function onEnterKey(event) {
      this.setTabIndexForSelectionMode(event, this.nodeTouched);
      this.onClick(event);
      event.preventDefault();
    },
    onTabKey: function onTabKey() {
      this.setAllNodesTabIndexes();
    },
    setAllNodesTabIndexes: function setAllNodesTabIndexes() {
      var nodes = find(this.$refs.currentNode.closest('[data-pc-section="rootchildren"]'), '[role="treeitem"]');
      var hasSelectedNode = _toConsumableArray$1(nodes).some(function (node) {
        return node.getAttribute('aria-selected') === 'true' || node.getAttribute('aria-checked') === 'true';
      });
      _toConsumableArray$1(nodes).forEach(function (node) {
        node.tabIndex = -1;
      });
      if (hasSelectedNode) {
        var selectedNodes = _toConsumableArray$1(nodes).filter(function (node) {
          return node.getAttribute('aria-selected') === 'true' || node.getAttribute('aria-checked') === 'true';
        });
        selectedNodes[0].tabIndex = 0;
        return;
      }
      _toConsumableArray$1(nodes)[0].tabIndex = 0;
    },
    setTabIndexForSelectionMode: function setTabIndexForSelectionMode(event, nodeTouched) {
      if (this.selectionMode !== null) {
        var elements = _toConsumableArray$1(find(this.$refs.currentNode.parentElement, '[role="treeitem"]'));
        event.currentTarget.tabIndex = nodeTouched === false ? -1 : 0;
        if (elements.every(function (element) {
          return element.tabIndex === -1;
        })) {
          elements[0].tabIndex = 0;
        }
      }
    },
    focusRowChange: function focusRowChange(firstFocusableRow, currentFocusedRow, lastVisibleDescendant) {
      firstFocusableRow.tabIndex = '-1';
      currentFocusedRow.tabIndex = '0';
      this.focusNode(lastVisibleDescendant || currentFocusedRow);
    },
    findBeforeClickableNode: function findBeforeClickableNode(node) {
      var parentListElement = node.closest('ul').closest('li');
      if (parentListElement) {
        var prevNodeButton = findSingle(parentListElement, 'button');
        if (prevNodeButton && prevNodeButton.style.visibility !== 'hidden') {
          return parentListElement;
        }
        return this.findBeforeClickableNode(node.previousElementSibling);
      }
      return null;
    },
    toggleCheckbox: function toggleCheckbox() {
      var _selectionKeys = this.selectionKeys ? _objectSpread$2({}, this.selectionKeys) : {};
      var _check = !this.checked;
      this.propagateDown(this.node, _check, _selectionKeys);
      this.$emit('checkbox-change', {
        node: this.node,
        check: _check,
        selectionKeys: _selectionKeys
      });
    },
    propagateDown: function propagateDown(node, check, selectionKeys) {
      if (check && node.selectable != false) selectionKeys[node.key] = {
        checked: true,
        partialChecked: false
      };else delete selectionKeys[node.key];
      if (node.children && node.children.length) {
        var _iterator = _createForOfIteratorHelper$1(node.children),
          _step;
        try {
          for (_iterator.s(); !(_step = _iterator.n()).done;) {
            var child = _step.value;
            this.propagateDown(child, check, selectionKeys);
          }
        } catch (err) {
          _iterator.e(err);
        } finally {
          _iterator.f();
        }
      }
    },
    propagateUp: function propagateUp(event) {
      var check = event.check;
      var _selectionKeys = _objectSpread$2({}, event.selectionKeys);
      var checkedChildCount = 0;
      var childPartialSelected = false;
      var _iterator2 = _createForOfIteratorHelper$1(this.node.children),
        _step2;
      try {
        for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {
          var child = _step2.value;
          if (_selectionKeys[child.key] && _selectionKeys[child.key].checked) checkedChildCount++;else if (_selectionKeys[child.key] && _selectionKeys[child.key].partialChecked) childPartialSelected = true;
        }
      } catch (err) {
        _iterator2.e(err);
      } finally {
        _iterator2.f();
      }
      if (check && checkedChildCount === this.node.children.length) {
        _selectionKeys[this.node.key] = {
          checked: true,
          partialChecked: false
        };
      } else {
        if (!check) {
          delete _selectionKeys[this.node.key];
        }
        if (childPartialSelected || checkedChildCount > 0 && checkedChildCount !== this.node.children.length) _selectionKeys[this.node.key] = {
          checked: false,
          partialChecked: true
        };else delete _selectionKeys[this.node.key];
      }
      this.$emit('checkbox-change', {
        node: event.node,
        check: event.check,
        selectionKeys: _selectionKeys
      });
    },
    onChildCheckboxChange: function onChildCheckboxChange(event) {
      this.$emit('checkbox-change', event);
    },
    findNextSiblingOfAncestor: function findNextSiblingOfAncestor(nodeElement) {
      var parentNodeElement = this.getParentNodeElement(nodeElement);
      if (parentNodeElement) {
        if (parentNodeElement.nextElementSibling) return parentNodeElement.nextElementSibling;else return this.findNextSiblingOfAncestor(parentNodeElement);
      } else {
        return null;
      }
    },
    findLastVisibleDescendant: function findLastVisibleDescendant(nodeElement) {
      var childrenListElement = nodeElement.children[1];
      if (childrenListElement) {
        var lastChildElement = childrenListElement.children[childrenListElement.children.length - 1];
        return this.findLastVisibleDescendant(lastChildElement);
      } else {
        return nodeElement;
      }
    },
    getParentNodeElement: function getParentNodeElement(nodeElement) {
      var parentNodeElement = nodeElement.parentElement.parentElement;
      return getAttribute(parentNodeElement, 'role') === 'treeitem' ? parentNodeElement : null;
    },
    focusNode: function focusNode(element) {
      element.focus();
    },
    isCheckboxSelectionMode: function isCheckboxSelectionMode() {
      return this.selectionMode === 'checkbox';
    },
    isSameNode: function isSameNode(event) {
      return event.currentTarget && (event.currentTarget.isSameNode(event.target) || event.currentTarget.isSameNode(event.target.closest('[role="treeitem"]')));
    }
  },
  computed: {
    hasChildren: function hasChildren() {
      return this.node.children && this.node.children.length > 0;
    },
    expanded: function expanded() {
      return this.expandedKeys && this.expandedKeys[this.node.key] === true;
    },
    leaf: function leaf() {
      return this.node.leaf === false ? false : !(this.node.children && this.node.children.length);
    },
    selectable: function selectable() {
      return this.node.selectable === false ? false : this.selectionMode != null;
    },
    selected: function selected() {
      return this.selectionMode && this.selectionKeys ? this.selectionKeys[this.node.key] === true : false;
    },
    checkboxMode: function checkboxMode() {
      return this.selectionMode === 'checkbox' && this.node.selectable !== false;
    },
    checked: function checked() {
      return this.selectionKeys ? this.selectionKeys[this.node.key] && this.selectionKeys[this.node.key].checked : false;
    },
    partialChecked: function partialChecked() {
      return this.selectionKeys ? this.selectionKeys[this.node.key] && this.selectionKeys[this.node.key].partialChecked : false;
    },
    ariaChecked: function ariaChecked() {
      return this.selectionMode === 'single' || this.selectionMode === 'multiple' ? this.selected : undefined;
    },
    ariaSelected: function ariaSelected() {
      return this.checkboxMode ? this.checked : undefined;
    }
  },
  components: {
    Checkbox: script$6,
    ChevronDownIcon: ChevronDownIcon,
    ChevronRightIcon: ChevronRightIcon,
    CheckIcon: CheckIcon,
    MinusIcon: MinusIcon,
    SpinnerIcon: SpinnerIcon
  },
  directives: {
    ripple: Ripple
  }
};

var _hoisted_1$1 = ["aria-label", "aria-selected", "aria-expanded", "aria-setsize", "aria-posinset", "aria-level", "aria-checked", "tabindex"];
var _hoisted_2$1 = ["data-p-selected", "data-p-selectable"];
var _hoisted_3$1 = ["data-p-leaf"];
function render$1(_ctx, _cache, $props, $setup, $data, $options) {
  var _component_SpinnerIcon = resolveComponent("SpinnerIcon");
  var _component_Checkbox = resolveComponent("Checkbox");
  var _component_TreeNode = resolveComponent("TreeNode", true);
  var _directive_ripple = resolveDirective("ripple");
  return openBlock(), createElementBlock("li", mergeProps({
    ref: "currentNode",
    "class": _ctx.cx('node'),
    role: "treeitem",
    "aria-label": $options.label($props.node),
    "aria-selected": $options.ariaSelected,
    "aria-expanded": $options.expanded,
    "aria-setsize": $props.node.children ? $props.node.children.length : 0,
    "aria-posinset": $props.index + 1,
    "aria-level": $props.level,
    "aria-checked": $options.ariaChecked,
    tabindex: $props.index === 0 ? 0 : -1,
    onKeydown: _cache[4] || (_cache[4] = function () {
      return $options.onKeyDown && $options.onKeyDown.apply($options, arguments);
    })
  }, $options.getPTOptions('node')), [createElementVNode("div", mergeProps({
    "class": _ctx.cx('nodeContent'),
    onClick: _cache[2] || (_cache[2] = function () {
      return $options.onClick && $options.onClick.apply($options, arguments);
    }),
    onTouchend: _cache[3] || (_cache[3] = function () {
      return $options.onTouchEnd && $options.onTouchEnd.apply($options, arguments);
    }),
    style: $props.node.style
  }, $options.getPTOptions('nodeContent'), {
    "data-p-selected": $options.checkboxMode ? $options.checked : $options.selected,
    "data-p-selectable": $options.selectable
  }), [withDirectives((openBlock(), createElementBlock("button", mergeProps({
    type: "button",
    "class": _ctx.cx('nodeToggleButton'),
    onClick: _cache[0] || (_cache[0] = function () {
      return $options.toggle && $options.toggle.apply($options, arguments);
    }),
    tabindex: "-1",
    "data-p-leaf": $options.leaf
  }, $options.getPTOptions('nodeToggleButton')), [$props.node.loading && $props.loadingMode === 'icon' ? (openBlock(), createElementBlock(Fragment, {
    key: 0
  }, [$props.templates['nodetoggleicon'] || $props.templates['nodetogglericon'] ? (openBlock(), createBlock(resolveDynamicComponent($props.templates['nodetoggleicon'] || $props.templates['nodetogglericon']), {
    key: 0,
    node: $props.node,
    expanded: $options.expanded,
    "class": normalizeClass(_ctx.cx('nodeToggleIcon'))
  }, null, 8, ["node", "expanded", "class"])) : (openBlock(), createBlock(_component_SpinnerIcon, mergeProps({
    key: 1,
    spin: "",
    "class": _ctx.cx('nodeToggleIcon')
  }, $options.getPTOptions('nodeToggleIcon')), null, 16, ["class"]))], 64)) : (openBlock(), createElementBlock(Fragment, {
    key: 1
  }, [$props.templates['nodetoggleicon'] || $props.templates['togglericon'] ? (openBlock(), createBlock(resolveDynamicComponent($props.templates['nodetoggleicon'] || $props.templates['togglericon']), {
    key: 0,
    node: $props.node,
    expanded: $options.expanded,
    "class": normalizeClass(_ctx.cx('nodeToggleIcon'))
  }, null, 8, ["node", "expanded", "class"])) : $options.expanded ? (openBlock(), createBlock(resolveDynamicComponent($props.node.expandedIcon ? 'span' : 'ChevronDownIcon'), mergeProps({
    key: 1,
    "class": _ctx.cx('nodeToggleIcon')
  }, $options.getPTOptions('nodeToggleIcon')), null, 16, ["class"])) : (openBlock(), createBlock(resolveDynamicComponent($props.node.collapsedIcon ? 'span' : 'ChevronRightIcon'), mergeProps({
    key: 2,
    "class": _ctx.cx('nodeToggleIcon')
  }, $options.getPTOptions('nodeToggleIcon')), null, 16, ["class"]))], 64))], 16, _hoisted_3$1)), [[_directive_ripple]]), $options.checkboxMode ? (openBlock(), createBlock(_component_Checkbox, {
    key: 0,
    defaultValue: $options.checked,
    binary: true,
    indeterminate: $options.partialChecked,
    "class": normalizeClass(_ctx.cx('nodeCheckbox')),
    tabindex: -1,
    unstyled: _ctx.unstyled,
    pt: $options.getPTOptions('pcNodeCheckbox'),
    "data-p-partialchecked": $options.partialChecked
  }, {
    icon: withCtx(function (slotProps) {
      return [$props.templates['checkboxicon'] ? (openBlock(), createBlock(resolveDynamicComponent($props.templates['checkboxicon']), {
        key: 0,
        checked: slotProps.checked,
        partialChecked: $options.partialChecked,
        "class": normalizeClass(slotProps["class"])
      }, null, 8, ["checked", "partialChecked", "class"])) : createCommentVNode("", true)];
    }),
    _: 1
  }, 8, ["defaultValue", "indeterminate", "class", "unstyled", "pt", "data-p-partialchecked"])) : createCommentVNode("", true), $props.templates['nodeicon'] ? (openBlock(), createBlock(resolveDynamicComponent($props.templates['nodeicon']), mergeProps({
    key: 1,
    node: $props.node,
    "class": [_ctx.cx('nodeIcon')]
  }, $options.getPTOptions('nodeIcon')), null, 16, ["node", "class"])) : (openBlock(), createElementBlock("span", mergeProps({
    key: 2,
    "class": [_ctx.cx('nodeIcon'), $props.node.icon]
  }, $options.getPTOptions('nodeIcon')), null, 16)), createElementVNode("span", mergeProps({
    "class": _ctx.cx('nodeLabel')
  }, $options.getPTOptions('nodeLabel'), {
    onKeydown: _cache[1] || (_cache[1] = withModifiers(function () {}, ["stop"]))
  }), [$props.templates[$props.node.type] || $props.templates['default'] ? (openBlock(), createBlock(resolveDynamicComponent($props.templates[$props.node.type] || $props.templates['default']), {
    key: 0,
    node: $props.node,
    expanded: $options.expanded,
    selected: $options.checkboxMode ? $options.checked : $options.selected
  }, null, 8, ["node", "expanded", "selected"])) : (openBlock(), createElementBlock(Fragment, {
    key: 1
  }, [createTextVNode(toDisplayString($options.label($props.node)), 1)], 64))], 16)], 16, _hoisted_2$1), $options.hasChildren && $options.expanded ? (openBlock(), createElementBlock("ul", mergeProps({
    key: 0,
    "class": _ctx.cx('nodeChildren'),
    role: "group"
  }, _ctx.ptm('nodeChildren')), [(openBlock(true), createElementBlock(Fragment, null, renderList($props.node.children, function (childNode) {
    return openBlock(), createBlock(_component_TreeNode, {
      key: childNode.key,
      node: childNode,
      templates: $props.templates,
      level: $props.level + 1,
      loadingMode: $props.loadingMode,
      expandedKeys: $props.expandedKeys,
      onNodeToggle: $options.onChildNodeToggle,
      onNodeClick: $options.onChildNodeClick,
      selectionMode: $props.selectionMode,
      selectionKeys: $props.selectionKeys,
      onCheckboxChange: $options.propagateUp,
      unstyled: _ctx.unstyled,
      pt: _ctx.pt
    }, null, 8, ["node", "templates", "level", "loadingMode", "expandedKeys", "onNodeToggle", "onNodeClick", "selectionMode", "selectionKeys", "onCheckboxChange", "unstyled", "pt"]);
  }), 128))], 16)) : createCommentVNode("", true)], 16, _hoisted_1$1);
}

script$1.render = render$1;

function _typeof$1(o) { "@babel/helpers - typeof"; return _typeof$1 = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof$1(o); }
function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: true } : { done: false, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = true, u = false; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = true, o = r; }, f: function f() { try { a || null == t["return"] || t["return"](); } finally { if (u) throw o; } } }; }
function _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }
function _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _iterableToArray(r) { if ("undefined" != typeof Symbol && null != r[Symbol.iterator] || null != r["@@iterator"]) return Array.from(r); }
function _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
function ownKeys$1(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread$1(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys$1(Object(t), true).forEach(function (r) { _defineProperty$1(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$1(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty$1(e, r, t) { return (r = _toPropertyKey$1(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: true, configurable: true, writable: true }) : e[r] = t, e; }
function _toPropertyKey$1(t) { var i = _toPrimitive$1(t, "string"); return "symbol" == _typeof$1(i) ? i : i + ""; }
function _toPrimitive$1(t, r) { if ("object" != _typeof$1(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r); if ("object" != _typeof$1(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
var script = {
  name: 'Tree',
  "extends": script$2,
  inheritAttrs: false,
  emits: ['node-expand', 'node-collapse', 'update:expandedKeys', 'update:selectionKeys', 'node-select', 'node-unselect', 'filter'],
  data: function data() {
    return {
      d_expandedKeys: this.expandedKeys || {},
      filterValue: null
    };
  },
  watch: {
    expandedKeys: function expandedKeys(newValue) {
      this.d_expandedKeys = newValue;
    }
  },
  methods: {
    onNodeToggle: function onNodeToggle(node) {
      var key = node.key;
      if (this.d_expandedKeys[key]) {
        delete this.d_expandedKeys[key];
        this.$emit('node-collapse', node);
      } else {
        this.d_expandedKeys[key] = true;
        this.$emit('node-expand', node);
      }
      this.d_expandedKeys = _objectSpread$1({}, this.d_expandedKeys);
      this.$emit('update:expandedKeys', this.d_expandedKeys);
    },
    onNodeClick: function onNodeClick(event) {
      if (this.selectionMode != null && event.node.selectable !== false) {
        var metaSelection = event.nodeTouched ? false : this.metaKeySelection;
        var _selectionKeys = metaSelection ? this.handleSelectionWithMetaKey(event) : this.handleSelectionWithoutMetaKey(event);
        this.$emit('update:selectionKeys', _selectionKeys);
      }
    },
    onCheckboxChange: function onCheckboxChange(event) {
      this.$emit('update:selectionKeys', event.selectionKeys);
      if (event.check) this.$emit('node-select', event.node);else this.$emit('node-unselect', event.node);
    },
    handleSelectionWithMetaKey: function handleSelectionWithMetaKey(event) {
      var originalEvent = event.originalEvent;
      var node = event.node;
      var metaKey = originalEvent.metaKey || originalEvent.ctrlKey;
      var selected = this.isNodeSelected(node);
      var _selectionKeys;
      if (selected && metaKey) {
        if (this.isSingleSelectionMode()) {
          _selectionKeys = {};
        } else {
          _selectionKeys = _objectSpread$1({}, this.selectionKeys);
          delete _selectionKeys[node.key];
        }
        this.$emit('node-unselect', node);
      } else {
        if (this.isSingleSelectionMode()) {
          _selectionKeys = {};
        } else if (this.isMultipleSelectionMode()) {
          _selectionKeys = !metaKey ? {} : this.selectionKeys ? _objectSpread$1({}, this.selectionKeys) : {};
        }
        _selectionKeys[node.key] = true;
        this.$emit('node-select', node);
      }
      return _selectionKeys;
    },
    handleSelectionWithoutMetaKey: function handleSelectionWithoutMetaKey(event) {
      var node = event.node;
      var selected = this.isNodeSelected(node);
      var _selectionKeys;
      if (this.isSingleSelectionMode()) {
        if (selected) {
          _selectionKeys = {};
          this.$emit('node-unselect', node);
        } else {
          _selectionKeys = {};
          _selectionKeys[node.key] = true;
          this.$emit('node-select', node);
        }
      } else {
        if (selected) {
          _selectionKeys = _objectSpread$1({}, this.selectionKeys);
          delete _selectionKeys[node.key];
          this.$emit('node-unselect', node);
        } else {
          _selectionKeys = this.selectionKeys ? _objectSpread$1({}, this.selectionKeys) : {};
          _selectionKeys[node.key] = true;
          this.$emit('node-select', node);
        }
      }
      return _selectionKeys;
    },
    isSingleSelectionMode: function isSingleSelectionMode() {
      return this.selectionMode === 'single';
    },
    isMultipleSelectionMode: function isMultipleSelectionMode() {
      return this.selectionMode === 'multiple';
    },
    isNodeSelected: function isNodeSelected(node) {
      return this.selectionMode && this.selectionKeys ? this.selectionKeys[node.key] === true : false;
    },
    isChecked: function isChecked(node) {
      return this.selectionKeys ? this.selectionKeys[node.key] && this.selectionKeys[node.key].checked : false;
    },
    isNodeLeaf: function isNodeLeaf(node) {
      return node.leaf === false ? false : !(node.children && node.children.length);
    },
    onFilterKeyup: function onFilterKeyup(event) {
      if (event.code === 'Enter' || event.code === 'NumpadEnter') {
        event.preventDefault();
      }
      this.$emit('filter', {
        originalEvent: event,
        value: event.target.value
      });
    },
    findFilteredNodes: function findFilteredNodes(node, paramsWithoutNode) {
      if (node) {
        var matched = false;
        if (node.children) {
          var childNodes = _toConsumableArray(node.children);
          node.children = [];
          var _iterator = _createForOfIteratorHelper(childNodes),
            _step;
          try {
            for (_iterator.s(); !(_step = _iterator.n()).done;) {
              var childNode = _step.value;
              var copyChildNode = _objectSpread$1({}, childNode);
              if (this.isFilterMatched(copyChildNode, paramsWithoutNode)) {
                matched = true;
                node.children.push(copyChildNode);
              }
            }
          } catch (err) {
            _iterator.e(err);
          } finally {
            _iterator.f();
          }
        }
        if (matched) {
          return true;
        }
      }
    },
    isFilterMatched: function isFilterMatched(node, _ref) {
      var searchFields = _ref.searchFields,
        filterText = _ref.filterText,
        strict = _ref.strict;
      var matched = false;
      var _iterator2 = _createForOfIteratorHelper(searchFields),
        _step2;
      try {
        for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {
          var field = _step2.value;
          var fieldValue = String(resolveFieldData(node, field)).toLocaleLowerCase(this.filterLocale);
          if (fieldValue.indexOf(filterText) > -1) {
            matched = true;
          }
        }
      } catch (err) {
        _iterator2.e(err);
      } finally {
        _iterator2.f();
      }
      if (!matched || strict && !this.isNodeLeaf(node)) {
        matched = this.findFilteredNodes(node, {
          searchFields: searchFields,
          filterText: filterText,
          strict: strict
        }) || matched;
      }
      return matched;
    }
  },
  computed: {
    filteredValue: function filteredValue() {
      var filteredNodes = [];
      var searchFields = isFunction(this.filterBy) ? [this.filterBy] : this.filterBy.split(',');
      var filterText = this.filterValue.trim().toLocaleLowerCase(this.filterLocale);
      var strict = this.filterMode === 'strict';
      var _iterator3 = _createForOfIteratorHelper(this.value),
        _step3;
      try {
        for (_iterator3.s(); !(_step3 = _iterator3.n()).done;) {
          var node = _step3.value;
          var _node = _objectSpread$1({}, node);
          var paramsWithoutNode = {
            searchFields: searchFields,
            filterText: filterText,
            strict: strict
          };
          if (strict && (this.findFilteredNodes(_node, paramsWithoutNode) || this.isFilterMatched(_node, paramsWithoutNode)) || !strict && (this.isFilterMatched(_node, paramsWithoutNode) || this.findFilteredNodes(_node, paramsWithoutNode))) {
            filteredNodes.push(_node);
          }
        }
      } catch (err) {
        _iterator3.e(err);
      } finally {
        _iterator3.f();
      }
      return filteredNodes;
    },
    valueToRender: function valueToRender() {
      if (this.filterValue && this.filterValue.trim().length > 0) return this.filteredValue;else return this.value;
    },
    containerDataP: function containerDataP() {
      return cn({
        loading: this.loading,
        scrollable: this.scrollHeight === 'flex'
      });
    },
    wrapperDataP: function wrapperDataP() {
      return cn({
        scrollable: this.scrollHeight === 'flex'
      });
    }
  },
  components: {
    TreeNode: script$1,
    InputText: script$5,
    InputIcon: script$4,
    IconField: script$3,
    SearchIcon: SearchIcon,
    SpinnerIcon: SpinnerIcon
  }
};

function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), true).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: true, configurable: true, writable: true }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
var _hoisted_1 = ["data-p"];
var _hoisted_2 = ["data-p"];
var _hoisted_3 = ["aria-labelledby", "aria-label"];
function render(_ctx, _cache, $props, $setup, $data, $options) {
  var _component_SpinnerIcon = resolveComponent("SpinnerIcon");
  var _component_InputText = resolveComponent("InputText");
  var _component_SearchIcon = resolveComponent("SearchIcon");
  var _component_InputIcon = resolveComponent("InputIcon");
  var _component_IconField = resolveComponent("IconField");
  var _component_TreeNode = resolveComponent("TreeNode");
  return openBlock(), createElementBlock("div", mergeProps({
    "class": _ctx.cx('root'),
    "data-p": $options.containerDataP
  }, _ctx.ptmi('root')), [_ctx.loading && _ctx.loadingMode === 'mask' ? (openBlock(), createElementBlock("div", mergeProps({
    key: 0,
    "class": _ctx.cx('mask')
  }, _ctx.ptm('mask')), [renderSlot(_ctx.$slots, "loadingicon", {
    "class": normalizeClass(_ctx.cx('loadingIcon'))
  }, function () {
    return [_ctx.loadingIcon ? (openBlock(), createElementBlock("i", mergeProps({
      key: 0,
      "class": [_ctx.cx('loadingIcon'), 'pi-spin', _ctx.loadingIcon]
    }, _ctx.ptm('loadingIcon')), null, 16)) : (openBlock(), createBlock(_component_SpinnerIcon, mergeProps({
      key: 1,
      spin: "",
      "class": _ctx.cx('loadingIcon')
    }, _ctx.ptm('loadingIcon')), null, 16, ["class"]))];
  })], 16)) : createCommentVNode("", true), _ctx.filter ? (openBlock(), createBlock(_component_IconField, {
    key: 1,
    unstyled: _ctx.unstyled,
    pt: _objectSpread(_objectSpread({}, _ctx.ptm('pcFilter')), _ctx.ptm('pcFilterContainer')),
    "class": normalizeClass(_ctx.cx('pcFilterContainer'))
  }, {
    "default": withCtx(function () {
      return [createVNode(_component_InputText, {
        modelValue: $data.filterValue,
        "onUpdate:modelValue": _cache[0] || (_cache[0] = function ($event) {
          return $data.filterValue = $event;
        }),
        autocomplete: "off",
        "class": normalizeClass(_ctx.cx('pcFilterInput')),
        placeholder: _ctx.filterPlaceholder,
        unstyled: _ctx.unstyled,
        onKeyup: $options.onFilterKeyup,
        pt: _ctx.ptm('pcFilterInput')
      }, null, 8, ["modelValue", "class", "placeholder", "unstyled", "onKeyup", "pt"]), createVNode(_component_InputIcon, {
        unstyled: _ctx.unstyled,
        pt: _ctx.ptm('pcFilterIconContainer')
      }, {
        "default": withCtx(function () {
          return [renderSlot(_ctx.$slots, _ctx.$slots.filtericon ? 'filtericon' : 'searchicon', {
            "class": normalizeClass(_ctx.cx('filterIcon'))
          }, function () {
            return [createVNode(_component_SearchIcon, mergeProps({
              "class": _ctx.cx('filterIcon')
            }, _ctx.ptm('filterIcon')), null, 16, ["class"])];
          })];
        }),
        _: 3
      }, 8, ["unstyled", "pt"])];
    }),
    _: 3
  }, 8, ["unstyled", "pt", "class"])) : createCommentVNode("", true), createElementVNode("div", mergeProps({
    "class": _ctx.cx('wrapper'),
    style: {
      maxHeight: _ctx.scrollHeight
    },
    "data-p": $options.wrapperDataP
  }, _ctx.ptm('wrapper')), [renderSlot(_ctx.$slots, "header", {
    value: _ctx.value,
    expandedKeys: _ctx.expandedKeys,
    selectionKeys: _ctx.selectionKeys
  }), createElementVNode("ul", mergeProps({
    "class": _ctx.cx('rootChildren'),
    role: "tree",
    "aria-labelledby": _ctx.ariaLabelledby,
    "aria-label": _ctx.ariaLabel
  }, _ctx.ptm('rootChildren')), [(openBlock(true), createElementBlock(Fragment, null, renderList($options.valueToRender, function (node, index) {
    return openBlock(), createBlock(_component_TreeNode, {
      key: node.key,
      node: node,
      templates: _ctx.$slots,
      level: _ctx.level + 1,
      index: index,
      expandedKeys: $data.d_expandedKeys,
      onNodeToggle: $options.onNodeToggle,
      onNodeClick: $options.onNodeClick,
      selectionMode: _ctx.selectionMode,
      selectionKeys: _ctx.selectionKeys,
      onCheckboxChange: $options.onCheckboxChange,
      loadingMode: _ctx.loadingMode,
      unstyled: _ctx.unstyled,
      pt: _ctx.pt
    }, null, 8, ["node", "templates", "level", "index", "expandedKeys", "onNodeToggle", "onNodeClick", "selectionMode", "selectionKeys", "onCheckboxChange", "loadingMode", "unstyled", "pt"]);
  }), 128))], 16, _hoisted_3), renderSlot(_ctx.$slots, "footer", {
    value: _ctx.value,
    expandedKeys: _ctx.expandedKeys,
    selectionKeys: _ctx.selectionKeys
  })], 16, _hoisted_2)], 16, _hoisted_1);
}

script.render = render;

const _sfc_main$8 = /* @__PURE__ */ defineComponent({
  __name: "Tree",
  setup(__props, { expose: __expose }) {
    __expose();
    const theme = ref({
      root: `bg-surface-0 dark:bg-surface-900 text-surface-700 dark:text-surface-0 p-4
        p-scrollable:flex p-scrollable:flex-1 p-scrollable:h-full p-scrollable:flex-col`,
      pcFilterContainer: {
        root: `relative mb-2`
      },
      pcFilterInput: {
        root: `w-full appearance-none rounded-md outline-hidden
            bg-surface-0 dark:bg-surface-950
            text-surface-700 dark:text-surface-0
            placeholder:text-surface-500 dark:placeholder:text-surface-400
            border border-surface-300 dark:border-surface-700
            hover:border-surface-400 dark:hover:border-surface-600
            focus:border-primary
            disabled:bg-surface-200 disabled:text-surface-500
            dark:disabled:bg-surface-700 dark:disabled:text-surface-400
            ps-3 pe-10 py-2 p-fluid:w-full
            transition-colors duration-200 shadow-[0_1px_2px_0_rgba(18,18,23,0.05)]`
      },
      pcFilterIconContainer: {
        root: `absolute top-1/2 -mt-2 leading-none end-3 z-1`
      },
      wrapper: `overflow-auto p-scrollable:flex-1`,
      rootChildren: `flex flex-col list-none m-0 gap-[4px] pt-[2px] pb-0 px-0`,
      node: `p-0 outline-none focus-visible:*:first:outline focus-visible:*:first:-outline-offset-1 focus-visible:*:first:outline-primary`,
      nodeContent: `group rounded-md px-2 py-1 flex items-center text-surface-700 dark:text-surface-0 gap-1 transition-colors duration-200
        hover:p-selectable:not-p-selected:bg-surface-100 hover:p-selectable:not-p-selected:text-surface-700 
        dark:hover:p-selectable:not-p-selected:bg-surface-800 dark:hover:p-selectable:not-p-selected:text-surface-0
        p-selected:bg-highlight 
        p-selectable:cursor-pointer p-selectable:select-none`,
      nodeToggleButton: `cursor-pointer select-none inline-flex justify-center rounded-full items-center overflow-hidden relative flex-shrink-0
        w-7 h-7 p-0 p-leaf:invisible transition-colors duration-200 border-none
        bg-transparent hover:bg-surface-100 dark:hover:bg-surface-800
        group-p-selected:hover:bg-surface-0 dark:group-p-selected:hover:bg-surface-900 group-p-selected:hover:text-primary
        text-surface-500 dark:text-surface-400 hover:text-surface-600 dark:hover:text-surface-300
        group-p-selected:text-inherit`,
      nodeToggleIcon: ``,
      nodeIcon: `text-surface-500 dark:text-surface-400 group-p-selected:text-primary
        group-hover:group-p-selectable:not-group-p-selected:text-surface-600 
        dark:group-hover:group-p-selectable:not-group-p-selected:text-surface-300
        transition-colors duration-200`,
      nodeLabel: ``,
      pcNodeCheckbox: {
        root: `relative inline-flex select-none w-5 h-5 align-bottom`,
        input: `peer cursor-pointer disabled:cursor-default appearance-none 
            absolute start-0 top-0 w-full h-full m-0 p-0 opacity-0 z-10
            border border-transparent rounded-xs`,
        box: `flex justify-center items-center rounded-sm w-5 h-5
            border border-surface-300 dark:border-surface-700
            bg-surface-0 dark:bg-surface-950
            text-surface-700 dark:text-surface-0
            peer-enabled:peer-hover:border-surface-400 dark:peer-enabled:peer-hover:border-surface-600
            p-checked:border-primary p-checked:bg-primary p-checked:text-primary-contrast
            peer-enabled:peer-hover:p-checked:bg-primary-emphasis peer-enabled:peer-hover:p-checked:border-primary-emphasis
            shadow-[0_1px_2px_0_rgba(18,18,23,0.05)] transition-colors duration-200`,
        icon: `text-sm w-[0.875rem] h-[0.875rem] transition-none`
      },
      nodeChildren: `flex flex-col list-none m-0 gap-[2px] pt-[2px] pe-0 pb-0 ps-4`,
      mask: `bg-black/50 text-surface-200 absolute z-10 flex items-center justify-center`,
      loadingIcon: `text-[2rem] h-8 w-8`
    });
    const __returned__ = { theme, get ChevronDownIcon() {
      return ChevronDownIcon;
    }, get ChevronRightIcon() {
      return ChevronRightIcon;
    }, get SearchIcon() {
      return SearchIcon;
    }, get Tree() {
      return script;
    }, get ptViewMerge() {
      return ptViewMerge;
    } };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$8(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(ssrRenderComponent($setup["Tree"], mergeProps({
    unstyled: "",
    pt: $setup.theme,
    ptOptions: {
      mergeProps: $setup.ptViewMerge
    }
  }, _attrs), createSlots({
    togglericon: withCtx(({ expanded }, _push2, _parent2, _scopeId) => {
      if (_push2) {
        if (expanded) {
          _push2(ssrRenderComponent($setup["ChevronDownIcon"], null, null, _parent2, _scopeId));
        } else {
          _push2(ssrRenderComponent($setup["ChevronRightIcon"], null, null, _parent2, _scopeId));
        }
      } else {
        return [
          expanded ? (openBlock(), createBlock($setup["ChevronDownIcon"], { key: 0 })) : (openBlock(), createBlock($setup["ChevronRightIcon"], { key: 1 }))
        ];
      }
    }),
    filtericon: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["SearchIcon"], { class: "text-surface-400" }, null, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["SearchIcon"], { class: "text-surface-400" })
        ];
      }
    }),
    _: 2
  }, [
    renderList(_ctx.$slots, (_, slotName) => {
      return {
        name: slotName,
        fn: withCtx((slotProps, _push2, _parent2, _scopeId) => {
          if (_push2) {
            ssrRenderSlot(_ctx.$slots, slotName, slotProps ?? {}, null, _push2, _parent2, _scopeId);
          } else {
            return [
              renderSlot(_ctx.$slots, slotName, slotProps ?? {})
            ];
          }
        })
      };
    })
  ]), _parent));
}
const _sfc_setup$8 = _sfc_main$8.setup;
_sfc_main$8.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/volt/Tree.vue");
  return _sfc_setup$8 ? _sfc_setup$8(props, ctx) : void 0;
};
const VTree = /* @__PURE__ */ _export_sfc(_sfc_main$8, [["ssrRender", _sfc_ssrRender$8]]);

const containerClass = `m-0 py-1.5 px-3 list-none cursor-text overflow-hidden flex items-center flex-wrap
        w-full text-surface-900 dark:text-surface-0 bg-surface-0 dark:bg-surface-950 
        border border-surface-300 dark:border-surface-600 rounded-md 
        transition-colors duration-200 appearance-none
        hover:border-surface-400 dark:hover:border-surface-500
        focus-within:outline-none focus-within:outline-offset-0 focus-within:ring-1 focus-within:ring-primary-500 focus-within:border-primary-500
        p-invalid:border-red-500 p-invalid:focus-within:ring-red-500 p-invalid:focus-within:border-red-500`;
const tokenClass = `py-1 px-2 mr-2 bg-surface-200 dark:bg-surface-700 text-surface-700 dark:text-surface-300 rounded-md 
        inline-flex items-center`;
const labelClass = `leading-none`;
const removeIconClass = `ml-2 w-4 h-4 cursor-pointer`;
const inputClass = `border-0 outline-none bg-transparent m-0 p-0 shadow-none rounded-none w-full
        text-surface-700 dark:text-surface-200 placeholder:text-surface-400 dark:placeholder:text-surface-500 flex-1 inline-flex`;
const _sfc_main$7 = /* @__PURE__ */ defineComponent({
  __name: "InputChips",
  props: {
    modelValue: {},
    separator: {},
    addOnBlur: { type: Boolean },
    allowDuplicate: { type: Boolean },
    max: {}
  },
  emits: ["update:modelValue"],
  setup(__props, { expose: __expose, emit: __emit }) {
    __expose();
    const props = __props;
    const emit = __emit;
    const model = computed({
      get: () => props.modelValue,
      set: (value) => emit("update:modelValue", value)
    });
    const inputValue = ref("");
    const inputRef = ref(null);
    const addValue = () => {
      if (inputValue.value.trim() !== "") {
        if (props.max && model.value.length >= props.max) {
          return;
        }
        if (!props.allowDuplicate && model.value.includes(inputValue.value.trim())) {
          inputValue.value = "";
          return;
        }
        model.value = [...model.value, inputValue.value.trim()];
        inputValue.value = "";
      }
    };
    const removeValue = (index) => {
      model.value = model.value.filter((_, i) => i !== index);
    };
    const handleBackspace = () => {
      if (inputValue.value === "" && model.value.length > 0) {
        removeValue(model.value.length - 1);
      }
    };
    const focusInput = async () => {
      await nextTick();
      inputRef.value?.focus();
    };
    const __returned__ = { props, emit, model, inputValue, inputRef, addValue, removeValue, handleBackspace, focusInput, containerClass, tokenClass, labelClass, removeIconClass, inputClass, get TimesIcon() {
      return TimesIcon;
    } };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$7(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: $setup.containerClass }, _attrs))}><!--[-->`);
  ssrRenderList($setup.model, (value, index) => {
    _push(`<div class="${ssrRenderClass($setup.tokenClass)}"><span class="${ssrRenderClass($setup.labelClass)}">${ssrInterpolate(value)}</span><span class="${ssrRenderClass($setup.removeIconClass)}">`);
    _push(ssrRenderComponent($setup["TimesIcon"], null, null, _parent));
    _push(`</span></div>`);
  });
  _push(`<!--]--><input type="text"${ssrRenderAttr("value", $setup.inputValue)} class="${ssrRenderClass($setup.inputClass)}"></div>`);
}
const _sfc_setup$7 = _sfc_main$7.setup;
_sfc_main$7.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/volt/InputChips.vue");
  return _sfc_setup$7 ? _sfc_setup$7(props, ctx) : void 0;
};
const InputChips = /* @__PURE__ */ _export_sfc(_sfc_main$7, [["ssrRender", _sfc_ssrRender$7]]);

const _sfc_main$6 = /* @__PURE__ */ defineComponent({
  __name: "EditSynonymGroupDialog",
  props: {
    visible: { type: Boolean },
    templateId: {},
    group: {}
  },
  emits: ["update:visible", "saved"],
  setup(__props, { expose: __expose, emit: __emit }) {
    __expose();
    const props = __props;
    const emit = __emit;
    const { attributeSynonyms } = useTrpc();
    const toast = useToast();
    const visible = computed({
      get: () => props.visible,
      set: (v) => emit("update:visible", v)
    });
    const isEdit = computed(() => !!props.group?.id);
    const dialogTitle = computed(() => isEdit.value ? "\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0433\u0440\u0443\u043F\u043F\u0443" : "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u0433\u0440\u0443\u043F\u043F\u0443");
    const compatibilityOptions = [
      { label: "EXACT", value: "EXACT" },
      { label: "NEAR", value: "NEAR" },
      { label: "LEGACY", value: "LEGACY" }
    ];
    const form = ref({
      name: "",
      canonicalValue: "",
      parentId: null,
      description: "",
      compatibilityLevel: "EXACT",
      notes: ""
    });
    const parentOptions = ref([]);
    const parentSelectOptions = computed(() => {
      const opts = Array.isArray(parentOptions.value) ? parentOptions.value : [];
      if (!props.group?.id) return [{ id: null, name: "\u041D\u0435\u0442 \u0440\u043E\u0434\u0438\u0442\u0435\u043B\u044F" }, ...opts];
      const childrenMap = /* @__PURE__ */ new Map();
      for (const o of opts) {
        if (typeof o.parentId === "number") {
          const arr = childrenMap.get(o.parentId) || [];
          arr.push(o.id);
          childrenMap.set(o.parentId, arr);
        }
      }
      const blocked = /* @__PURE__ */ new Set();
      const stack = [props.group.id];
      while (stack.length) {
        const id = stack.pop();
        const kids = childrenMap.get(id) || [];
        for (const kid of kids) if (!blocked.has(kid)) {
          blocked.add(kid);
          stack.push(kid);
        }
      }
      const filtered = opts.filter((o) => o && typeof o.id === "number" ? o.id !== props.group.id && !blocked.has(o.id) : true);
      return [{ id: null, name: "\u041D\u0435\u0442 \u0440\u043E\u0434\u0438\u0442\u0435\u043B\u044F" }, ...filtered];
    });
    const errors = ref({});
    const saving = ref(false);
    watch(() => props.group, (g) => {
      if (g) {
        form.value = {
          name: g.name || "",
          canonicalValue: g.canonicalValue || g.name || "",
          parentId: g.parentId ?? null,
          description: g.description || null,
          compatibilityLevel: g.compatibilityLevel || "EXACT",
          notes: g.notes || null
        };
      } else {
        form.value = { name: "", canonicalValue: "", parentId: null, description: null, compatibilityLevel: "EXACT", notes: null };
      }
    }, { immediate: true });
    watch(() => props.templateId, async (tid) => {
      if (!tid) return;
      const { attributeSynonyms: attributeSynonyms2 } = useTrpc();
      const limit = 100;
      let offset = 0;
      let all = [];
      let total = 0;
      while (true) {
        const res = await attributeSynonyms2.groups.findMany({ templateId: tid, limit, offset });
        const batch = res?.groups ?? [];
        total = res?.total ?? total;
        all = all.concat(batch);
        if (batch.length < limit) break;
        offset += limit;
        if (total && offset >= total) break;
      }
      parentOptions.value = all;
    }, { immediate: true });
    const validate = () => {
      errors.value = {};
      if (!form.value.name.trim()) errors.value.name = "\u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u0435";
      if (!form.value.parentId && !form.value.canonicalValue.trim()) errors.value.canonicalValue = "\u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u043A\u0430\u043D\u043E\u043D\u0438\u0447\u0435\u0441\u043A\u043E\u0435 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0435";
      return Object.keys(errors.value).length === 0;
    };
    const close = () => {
      visible.value = false;
    };
    const save = async () => {
      if (!validate()) return;
      saving.value = true;
      try {
        if (isEdit.value) {
          await attributeSynonyms.groups.update({ id: props.group.id, ...form.value });
          toast.success("\u0413\u0440\u0443\u043F\u043F\u0430 \u043E\u0431\u043D\u043E\u0432\u043B\u0435\u043D\u0430");
        } else {
          await attributeSynonyms.groups.create({ templateId: props.templateId, name: form.value.name, canonicalValue: form.value.canonicalValue || void 0, parentId: form.value.parentId, description: form.value.description, compatibilityLevel: form.value.compatibilityLevel, notes: form.value.notes });
          toast.success("\u0413\u0440\u0443\u043F\u043F\u0430 \u0441\u043E\u0437\u0434\u0430\u043D\u0430");
        }
        emit("saved");
        close();
      } catch (e) {
        toast.error(e?.message || "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0441\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C \u0433\u0440\u0443\u043F\u043F\u0443");
      } finally {
        saving.value = false;
      }
    };
    const __returned__ = { props, emit, attributeSynonyms, toast, visible, isEdit, dialogTitle, compatibilityOptions, form, parentOptions, parentSelectOptions, errors, saving, validate, close, save, VDialog: Dialog, VInputText: InputText, VTextarea, VButton, VSelect: Select };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$6(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(ssrRenderComponent($setup["VDialog"], mergeProps({
    visible: $setup.visible,
    "onUpdate:visible": ($event) => $setup.visible = $event,
    modal: "",
    header: $setup.dialogTitle,
    style: { width: "34rem" }
  }, _attrs), {
    footer: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["VButton"], {
          label: "\u041E\u0442\u043C\u0435\u043D\u0430",
          severity: "secondary",
          onClick: $setup.close
        }, null, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["VButton"], {
          label: $setup.isEdit ? "\u0421\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C" : "\u0421\u043E\u0437\u0434\u0430\u0442\u044C",
          loading: $setup.saving,
          onClick: $setup.save
        }, null, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["VButton"], {
            label: "\u041E\u0442\u043C\u0435\u043D\u0430",
            severity: "secondary",
            onClick: $setup.close
          }),
          createVNode($setup["VButton"], {
            label: $setup.isEdit ? "\u0421\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C" : "\u0421\u043E\u0437\u0434\u0430\u0442\u044C",
            loading: $setup.saving,
            onClick: $setup.save
          }, null, 8, ["label", "loading"])
        ];
      }
    }),
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="space-y-4"${_scopeId}><div${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}>\u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435 *</label>`);
        _push2(ssrRenderComponent($setup["VInputText"], {
          modelValue: $setup.form.name,
          "onUpdate:modelValue": ($event) => $setup.form.name = $event,
          placeholder: "\u0421\u0442\u0430\u043D\u0434\u0430\u0440\u0442\u043D\u044B\u0435 \u0442\u0438\u043F\u044B \u0443\u043F\u043B\u043E\u0442\u043D\u0435\u043D\u0438\u0439",
          class: ["w-full", { "p-invalid": !!$setup.errors.name }]
        }, null, _parent2, _scopeId));
        if ($setup.errors.name) {
          _push2(`<small class="p-error"${_scopeId}>${ssrInterpolate($setup.errors.name)}</small>`);
        } else {
          _push2(`<!---->`);
        }
        _push2(`</div><div${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}>\u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435</label>`);
        _push2(ssrRenderComponent($setup["VTextarea"], {
          modelValue: $setup.form.description,
          "onUpdate:modelValue": ($event) => $setup.form.description = $event,
          rows: "2",
          class: "w-full"
        }, null, _parent2, _scopeId));
        _push2(`</div><div${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}>\u041A\u0430\u043D\u043E\u043D\u0438\u0447\u0435\u0441\u043A\u043E\u0435 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0435</label>`);
        _push2(ssrRenderComponent($setup["VInputText"], {
          modelValue: $setup.form.canonicalValue,
          "onUpdate:modelValue": ($event) => $setup.form.canonicalValue = $event,
          placeholder: "\u041D\u0430\u043F\u0440\u0438\u043C\u0435\u0440: TC (\u0441\u0442\u0430\u043D\u0434\u0430\u0440\u0442)",
          class: ["w-full", { "p-invalid": !!$setup.errors.canonicalValue }]
        }, null, _parent2, _scopeId));
        if ($setup.errors.canonicalValue) {
          _push2(`<small class="p-error"${_scopeId}>${ssrInterpolate($setup.errors.canonicalValue)}</small>`);
        } else {
          _push2(`<!---->`);
        }
        _push2(`</div><div class="grid grid-cols-1 md:grid-cols-2 gap-4"${_scopeId}><div${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}>\u0420\u043E\u0434\u0438\u0442\u0435\u043B\u044C\u0441\u043A\u0430\u044F \u0433\u0440\u0443\u043F\u043F\u0430</label>`);
        _push2(ssrRenderComponent($setup["VSelect"], {
          modelValue: $setup.form.parentId,
          "onUpdate:modelValue": ($event) => $setup.form.parentId = $event,
          options: $setup.parentSelectOptions,
          "option-label": "name",
          "option-value": "id",
          class: "w-full",
          placeholder: "\u041D\u0435 \u0432\u044B\u0431\u0440\u0430\u043D\u043E"
        }, null, _parent2, _scopeId));
        _push2(`</div><div${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}>\u0423\u0440\u043E\u0432\u0435\u043D\u044C \u0441\u043E\u0432\u043C\u0435\u0441\u0442\u0438\u043C\u043E\u0441\u0442\u0438</label>`);
        _push2(ssrRenderComponent($setup["VSelect"], {
          modelValue: $setup.form.compatibilityLevel,
          "onUpdate:modelValue": ($event) => $setup.form.compatibilityLevel = $event,
          options: $setup.compatibilityOptions,
          "option-label": "label",
          "option-value": "value",
          class: "w-full"
        }, null, _parent2, _scopeId));
        _push2(`</div><div${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}>\u0417\u0430\u043C\u0435\u0442\u043A\u0438</label>`);
        _push2(ssrRenderComponent($setup["VInputText"], {
          modelValue: $setup.form.notes,
          "onUpdate:modelValue": ($event) => $setup.form.notes = $event,
          placeholder: "\u041D\u0430\u043F\u0440\u0438\u043C\u0435\u0440: \u0434\u043B\u044F \u0441\u0442\u0430\u0440\u044B\u0445 \u0441\u043F\u0435\u0446\u0438\u0444\u0438\u043A\u0430\u0446\u0438\u0439",
          class: "w-full"
        }, null, _parent2, _scopeId));
        _push2(`</div></div></div>`);
      } else {
        return [
          createVNode("div", { class: "space-y-4" }, [
            createVNode("div", null, [
              createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, "\u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435 *"),
              createVNode($setup["VInputText"], {
                modelValue: $setup.form.name,
                "onUpdate:modelValue": ($event) => $setup.form.name = $event,
                placeholder: "\u0421\u0442\u0430\u043D\u0434\u0430\u0440\u0442\u043D\u044B\u0435 \u0442\u0438\u043F\u044B \u0443\u043F\u043B\u043E\u0442\u043D\u0435\u043D\u0438\u0439",
                class: ["w-full", { "p-invalid": !!$setup.errors.name }]
              }, null, 8, ["modelValue", "onUpdate:modelValue", "class"]),
              $setup.errors.name ? (openBlock(), createBlock("small", {
                key: 0,
                class: "p-error"
              }, toDisplayString($setup.errors.name), 1)) : createCommentVNode("", true)
            ]),
            createVNode("div", null, [
              createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, "\u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435"),
              createVNode($setup["VTextarea"], {
                modelValue: $setup.form.description,
                "onUpdate:modelValue": ($event) => $setup.form.description = $event,
                rows: "2",
                class: "w-full"
              }, null, 8, ["modelValue", "onUpdate:modelValue"])
            ]),
            createVNode("div", null, [
              createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, "\u041A\u0430\u043D\u043E\u043D\u0438\u0447\u0435\u0441\u043A\u043E\u0435 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0435"),
              createVNode($setup["VInputText"], {
                modelValue: $setup.form.canonicalValue,
                "onUpdate:modelValue": ($event) => $setup.form.canonicalValue = $event,
                placeholder: "\u041D\u0430\u043F\u0440\u0438\u043C\u0435\u0440: TC (\u0441\u0442\u0430\u043D\u0434\u0430\u0440\u0442)",
                class: ["w-full", { "p-invalid": !!$setup.errors.canonicalValue }]
              }, null, 8, ["modelValue", "onUpdate:modelValue", "class"]),
              $setup.errors.canonicalValue ? (openBlock(), createBlock("small", {
                key: 0,
                class: "p-error"
              }, toDisplayString($setup.errors.canonicalValue), 1)) : createCommentVNode("", true)
            ]),
            createVNode("div", { class: "grid grid-cols-1 md:grid-cols-2 gap-4" }, [
              createVNode("div", null, [
                createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, "\u0420\u043E\u0434\u0438\u0442\u0435\u043B\u044C\u0441\u043A\u0430\u044F \u0433\u0440\u0443\u043F\u043F\u0430"),
                createVNode($setup["VSelect"], {
                  modelValue: $setup.form.parentId,
                  "onUpdate:modelValue": ($event) => $setup.form.parentId = $event,
                  options: $setup.parentSelectOptions,
                  "option-label": "name",
                  "option-value": "id",
                  class: "w-full",
                  placeholder: "\u041D\u0435 \u0432\u044B\u0431\u0440\u0430\u043D\u043E"
                }, null, 8, ["modelValue", "onUpdate:modelValue", "options"])
              ]),
              createVNode("div", null, [
                createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, "\u0423\u0440\u043E\u0432\u0435\u043D\u044C \u0441\u043E\u0432\u043C\u0435\u0441\u0442\u0438\u043C\u043E\u0441\u0442\u0438"),
                createVNode($setup["VSelect"], {
                  modelValue: $setup.form.compatibilityLevel,
                  "onUpdate:modelValue": ($event) => $setup.form.compatibilityLevel = $event,
                  options: $setup.compatibilityOptions,
                  "option-label": "label",
                  "option-value": "value",
                  class: "w-full"
                }, null, 8, ["modelValue", "onUpdate:modelValue"])
              ]),
              createVNode("div", null, [
                createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, "\u0417\u0430\u043C\u0435\u0442\u043A\u0438"),
                createVNode($setup["VInputText"], {
                  modelValue: $setup.form.notes,
                  "onUpdate:modelValue": ($event) => $setup.form.notes = $event,
                  placeholder: "\u041D\u0430\u043F\u0440\u0438\u043C\u0435\u0440: \u0434\u043B\u044F \u0441\u0442\u0430\u0440\u044B\u0445 \u0441\u043F\u0435\u0446\u0438\u0444\u0438\u043A\u0430\u0446\u0438\u0439",
                  class: "w-full"
                }, null, 8, ["modelValue", "onUpdate:modelValue"])
              ])
            ])
          ])
        ];
      }
    }),
    _: 1
  }, _parent));
}
const _sfc_setup$6 = _sfc_main$6.setup;
_sfc_main$6.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/attributes/EditSynonymGroupDialog.vue");
  return _sfc_setup$6 ? _sfc_setup$6(props, ctx) : void 0;
};
const EditSynonymGroupDialog = /* @__PURE__ */ _export_sfc(_sfc_main$6, [["ssrRender", _sfc_ssrRender$6]]);

const _sfc_main$5 = /* @__PURE__ */ defineComponent({
  __name: "SynonymValueEditor",
  props: {
    groupId: {}
  },
  setup(__props, { expose: __expose }) {
    __expose();
    const props = __props;
    const { attributeSynonyms, brands } = useTrpc();
    const toast = useToast();
    const synonyms = ref([]);
    const loading = ref(false);
    const newValue = ref("");
    const newNotes = ref(null);
    const newLevel = ref(null);
    const newBrandId = ref(null);
    const compatibilityOptions = [
      { label: "EXACT", value: "EXACT" },
      { label: "NEAR", value: "NEAR" },
      { label: "LEGACY", value: "LEGACY" }
    ];
    const brandOptions = ref([]);
    const loadBrands = async () => {
      const res = await brands.findMany({});
      brandOptions.value = Array.isArray(res) ? res.map((b) => ({ id: b.id, name: b.name })) : [];
    };
    const brandName = (id) => brandOptions.value.find((b) => b.id === id)?.name || "";
    const canAdd = ref(false);
    watch([newValue, newNotes, newLevel, newBrandId], () => {
      const trimmed = (newValue.value || "").trim();
      canAdd.value = trimmed.length > 0 && !synonyms.value.some((s) => s.value.toLowerCase() === trimmed.toLowerCase());
    });
    const load = async () => {
      loading.value = true;
      try {
        await loadBrands();
        const result = await attributeSynonyms.synonyms.findMany({ groupId: props.groupId });
        if (Array.isArray(result)) synonyms.value = result;
      } catch (e) {
        toast.error(e?.message || "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0437\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u044C \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u044F");
      } finally {
        loading.value = false;
      }
    };
    const addValue = async () => {
      const value = newValue.value.trim();
      if (!value) return;
      if (synonyms.value.some((s) => s.value.toLowerCase() === value.toLowerCase())) {
        toast.error("\u0414\u0443\u0431\u043B\u0438\u043A\u0430\u0442\u044B \u043D\u0435 \u0434\u043E\u043F\u0443\u0441\u043A\u0430\u044E\u0442\u0441\u044F");
        return;
      }
      try {
        const created = await attributeSynonyms.synonyms.create({ groupId: props.groupId, value, notes: newNotes.value, brandId: newBrandId.value ?? void 0, compatibilityLevel: newLevel.value ?? void 0 });
        if (created && typeof created === "object") {
          synonyms.value.push(created);
          newValue.value = "";
          newNotes.value = null;
          newLevel.value = null;
          newBrandId.value = null;
        }
      } catch (e) {
        toast.error(e?.message || "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0434\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0435");
      }
    };
    const showEdit = ref(false);
    const editForm = ref({ id: 0, notes: null, brandId: null, compatibilityLevel: null });
    const editRow = (row) => {
      editForm.value = { id: row.id, notes: row.notes ?? null, brandId: row.brandId ?? null, compatibilityLevel: row.compatibilityLevel ?? null };
      showEdit.value = true;
    };
    const saveEdit = async () => {
      try {
        const updated = await attributeSynonyms.synonyms.update({ id: editForm.value.id, notes: editForm.value.notes, brandId: editForm.value.brandId ?? void 0, compatibilityLevel: editForm.value.compatibilityLevel ?? void 0 });
        if (updated) {
          const idx = synonyms.value.findIndex((s) => s.id === editForm.value.id);
          if (idx !== -1) synonyms.value[idx] = { ...synonyms.value[idx], ...updated };
          showEdit.value = false;
        }
      } catch (e) {
        toast.error(e?.message || "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0441\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C \u0438\u0437\u043C\u0435\u043D\u0435\u043D\u0438\u044F");
      }
    };
    const removeValue = async (row) => {
      if (!confirm("\u0423\u0434\u0430\u043B\u0438\u0442\u044C \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0435?")) return;
      try {
        await attributeSynonyms.synonyms.delete({ id: row.id });
        synonyms.value = synonyms.value.filter((s) => s.id !== row.id);
      } catch (e) {
        toast.error(e?.message || "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0443\u0434\u0430\u043B\u0438\u0442\u044C \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0435");
      }
    };
    onMounted(load);
    watch(() => props.groupId, load);
    const __returned__ = { props, attributeSynonyms, brands, toast, synonyms, loading, newValue, newNotes, newLevel, newBrandId, compatibilityOptions, brandOptions, loadBrands, brandName, canAdd, load, addValue, showEdit, editForm, editRow, saveEdit, removeValue, VInputText: InputText, VButton, VDataTable: DataTable, VDialog: Dialog, VSelect: Select, get Column() {
      return script$7;
    }, get PlusIcon() {
      return PlusIcon;
    }, get PencilIcon() {
      return PencilIcon;
    }, get TrashIcon() {
      return TrashIcon;
    }, DangerButton };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$5(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "space-y-4" }, _attrs))}><div class="grid grid-cols-1 md:grid-cols-5 gap-2 items-end">`);
  _push(ssrRenderComponent($setup["VInputText"], {
    modelValue: $setup.newValue,
    "onUpdate:modelValue": ($event) => $setup.newValue = $event,
    placeholder: "\u0417\u043D\u0430\u0447\u0435\u043D\u0438\u0435",
    class: "w-full md:col-span-1",
    onKeyup: $setup.addValue
  }, null, _parent));
  _push(ssrRenderComponent($setup["VInputText"], {
    modelValue: $setup.newNotes,
    "onUpdate:modelValue": ($event) => $setup.newNotes = $event,
    placeholder: "\u0417\u0430\u043C\u0435\u0442\u043A\u0438 (\u043E\u043F\u0446.)",
    class: "w-full md:col-span-1",
    onKeyup: $setup.addValue
  }, null, _parent));
  _push(ssrRenderComponent($setup["VSelect"], {
    modelValue: $setup.newBrandId,
    "onUpdate:modelValue": ($event) => $setup.newBrandId = $event,
    options: $setup.brandOptions,
    "option-label": "name",
    "option-value": "id",
    class: "w-full md:col-span-1",
    placeholder: "\u0411\u0440\u0435\u043D\u0434 (\u043E\u043F\u0446.)"
  }, null, _parent));
  _push(ssrRenderComponent($setup["VSelect"], {
    modelValue: $setup.newLevel,
    "onUpdate:modelValue": ($event) => $setup.newLevel = $event,
    options: $setup.compatibilityOptions,
    "option-label": "label",
    "option-value": "value",
    class: "w-full md:col-span-1"
  }, null, _parent));
  _push(ssrRenderComponent($setup["VButton"], {
    onClick: $setup.addValue,
    disabled: !$setup.canAdd
  }, {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["PlusIcon"], { class: "w-4 h-4" }, null, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["PlusIcon"], { class: "w-4 h-4" })
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</div>`);
  _push(ssrRenderComponent($setup["VDataTable"], {
    value: $setup.synonyms,
    loading: $setup.loading,
    class: "p-datatable-sm",
    "table-style": "min-width: 44rem",
    "striped-rows": ""
  }, {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["Column"], {
          field: "value",
          header: "\u0417\u043D\u0430\u0447\u0435\u043D\u0438\u0435"
        }, null, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["Column"], {
          field: "brandId",
          header: "\u0411\u0440\u0435\u043D\u0434"
        }, {
          body: withCtx(({ data }, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(`<span${_scopeId2}>${ssrInterpolate($setup.brandName(data.brandId))}</span>`);
            } else {
              return [
                createVNode("span", null, toDisplayString($setup.brandName(data.brandId)), 1)
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["Column"], {
          field: "compatibilityLevel",
          header: "\u0423\u0440\u043E\u0432\u0435\u043D\u044C"
        }, null, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["Column"], {
          field: "notes",
          header: "\u0417\u0430\u043C\u0435\u0442\u043A\u0438"
        }, null, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["Column"], {
          header: "",
          style: { "width": "120px" }
        }, {
          body: withCtx(({ data }, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(`<div class="flex gap-2"${_scopeId2}>`);
              _push3(ssrRenderComponent($setup["VButton"], {
                size: "small",
                severity: "secondary",
                outlined: "",
                onClick: ($event) => $setup.editRow(data)
              }, {
                default: withCtx((_2, _push4, _parent4, _scopeId3) => {
                  if (_push4) {
                    _push4(ssrRenderComponent($setup["PencilIcon"], { class: "w-4 h-4" }, null, _parent4, _scopeId3));
                  } else {
                    return [
                      createVNode($setup["PencilIcon"], { class: "w-4 h-4" })
                    ];
                  }
                }),
                _: 2
              }, _parent3, _scopeId2));
              _push3(ssrRenderComponent($setup["DangerButton"], {
                size: "small",
                outlined: "",
                onClick: ($event) => $setup.removeValue(data)
              }, {
                default: withCtx((_2, _push4, _parent4, _scopeId3) => {
                  if (_push4) {
                    _push4(ssrRenderComponent($setup["TrashIcon"], { class: "w-4 h-4" }, null, _parent4, _scopeId3));
                  } else {
                    return [
                      createVNode($setup["TrashIcon"], { class: "w-4 h-4" })
                    ];
                  }
                }),
                _: 2
              }, _parent3, _scopeId2));
              _push3(`</div>`);
            } else {
              return [
                createVNode("div", { class: "flex gap-2" }, [
                  createVNode($setup["VButton"], {
                    size: "small",
                    severity: "secondary",
                    outlined: "",
                    onClick: ($event) => $setup.editRow(data)
                  }, {
                    default: withCtx(() => [
                      createVNode($setup["PencilIcon"], { class: "w-4 h-4" })
                    ]),
                    _: 2
                  }, 1032, ["onClick"]),
                  createVNode($setup["DangerButton"], {
                    size: "small",
                    outlined: "",
                    onClick: ($event) => $setup.removeValue(data)
                  }, {
                    default: withCtx(() => [
                      createVNode($setup["TrashIcon"], { class: "w-4 h-4" })
                    ]),
                    _: 2
                  }, 1032, ["onClick"])
                ])
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["Column"], {
            field: "value",
            header: "\u0417\u043D\u0430\u0447\u0435\u043D\u0438\u0435"
          }),
          createVNode($setup["Column"], {
            field: "brandId",
            header: "\u0411\u0440\u0435\u043D\u0434"
          }, {
            body: withCtx(({ data }) => [
              createVNode("span", null, toDisplayString($setup.brandName(data.brandId)), 1)
            ]),
            _: 1
          }),
          createVNode($setup["Column"], {
            field: "compatibilityLevel",
            header: "\u0423\u0440\u043E\u0432\u0435\u043D\u044C"
          }),
          createVNode($setup["Column"], {
            field: "notes",
            header: "\u0417\u0430\u043C\u0435\u0442\u043A\u0438"
          }),
          createVNode($setup["Column"], {
            header: "",
            style: { "width": "120px" }
          }, {
            body: withCtx(({ data }) => [
              createVNode("div", { class: "flex gap-2" }, [
                createVNode($setup["VButton"], {
                  size: "small",
                  severity: "secondary",
                  outlined: "",
                  onClick: ($event) => $setup.editRow(data)
                }, {
                  default: withCtx(() => [
                    createVNode($setup["PencilIcon"], { class: "w-4 h-4" })
                  ]),
                  _: 2
                }, 1032, ["onClick"]),
                createVNode($setup["DangerButton"], {
                  size: "small",
                  outlined: "",
                  onClick: ($event) => $setup.removeValue(data)
                }, {
                  default: withCtx(() => [
                    createVNode($setup["TrashIcon"], { class: "w-4 h-4" })
                  ]),
                  _: 2
                }, 1032, ["onClick"])
              ])
            ]),
            _: 1
          })
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(ssrRenderComponent($setup["VDialog"], {
    visible: $setup.showEdit,
    "onUpdate:visible": ($event) => $setup.showEdit = $event,
    modal: "",
    header: "\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0441\u0438\u043D\u043E\u043D\u0438\u043C",
    style: { width: "28rem" }
  }, {
    footer: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["VButton"], {
          label: "\u041E\u0442\u043C\u0435\u043D\u0430",
          severity: "secondary",
          onClick: ($event) => $setup.showEdit = false
        }, null, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["VButton"], {
          label: "\u0421\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C",
          onClick: $setup.saveEdit
        }, null, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["VButton"], {
            label: "\u041E\u0442\u043C\u0435\u043D\u0430",
            severity: "secondary",
            onClick: ($event) => $setup.showEdit = false
          }, null, 8, ["onClick"]),
          createVNode($setup["VButton"], {
            label: "\u0421\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C",
            onClick: $setup.saveEdit
          })
        ];
      }
    }),
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="space-y-3"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["VSelect"], {
          modelValue: $setup.editForm.brandId,
          "onUpdate:modelValue": ($event) => $setup.editForm.brandId = $event,
          options: $setup.brandOptions,
          "option-label": "name",
          "option-value": "id",
          class: "w-full",
          placeholder: "\u0411\u0440\u0435\u043D\u0434 (\u043E\u043F\u0446.)"
        }, null, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["VInputText"], {
          modelValue: $setup.editForm.notes,
          "onUpdate:modelValue": ($event) => $setup.editForm.notes = $event,
          placeholder: "\u0417\u0430\u043C\u0435\u0442\u043A\u0438",
          class: "w-full"
        }, null, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["VSelect"], {
          modelValue: $setup.editForm.compatibilityLevel,
          "onUpdate:modelValue": ($event) => $setup.editForm.compatibilityLevel = $event,
          options: $setup.compatibilityOptions,
          "option-label": "label",
          "option-value": "value",
          class: "w-full"
        }, null, _parent2, _scopeId));
        _push2(`</div>`);
      } else {
        return [
          createVNode("div", { class: "space-y-3" }, [
            createVNode($setup["VSelect"], {
              modelValue: $setup.editForm.brandId,
              "onUpdate:modelValue": ($event) => $setup.editForm.brandId = $event,
              options: $setup.brandOptions,
              "option-label": "name",
              "option-value": "id",
              class: "w-full",
              placeholder: "\u0411\u0440\u0435\u043D\u0434 (\u043E\u043F\u0446.)"
            }, null, 8, ["modelValue", "onUpdate:modelValue", "options"]),
            createVNode($setup["VInputText"], {
              modelValue: $setup.editForm.notes,
              "onUpdate:modelValue": ($event) => $setup.editForm.notes = $event,
              placeholder: "\u0417\u0430\u043C\u0435\u0442\u043A\u0438",
              class: "w-full"
            }, null, 8, ["modelValue", "onUpdate:modelValue"]),
            createVNode($setup["VSelect"], {
              modelValue: $setup.editForm.compatibilityLevel,
              "onUpdate:modelValue": ($event) => $setup.editForm.compatibilityLevel = $event,
              options: $setup.compatibilityOptions,
              "option-label": "label",
              "option-value": "value",
              class: "w-full"
            }, null, 8, ["modelValue", "onUpdate:modelValue"])
          ])
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</div>`);
}
const _sfc_setup$5 = _sfc_main$5.setup;
_sfc_main$5.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/attributes/SynonymValueEditor.vue");
  return _sfc_setup$5 ? _sfc_setup$5(props, ctx) : void 0;
};
const SynonymValueEditor = /* @__PURE__ */ _export_sfc(_sfc_main$5, [["ssrRender", _sfc_ssrRender$5]]);

const _sfc_main$4 = /* @__PURE__ */ defineComponent({
  __name: "AttributeSynonymManager",
  props: {
    template: {}
  },
  setup(__props, { expose: __expose }) {
    __expose();
    const props = __props;
    const toast = useToast();
    const { attributeSynonyms } = useTrpc();
    const groups = ref([]);
    const total = ref(0);
    const loadingGroups = ref(false);
    const selectedGroup = ref(null);
    const showGroupDialog = ref(false);
    const editingGroup = ref(null);
    const expandedKeys = ref({});
    const loadGroups = async () => {
      if (!props.template?.id) return;
      loadingGroups.value = true;
      try {
        const limit = 100;
        let offset = 0;
        let all = [];
        let totalCount = 0;
        while (true) {
          const res = await attributeSynonyms.groups.findMany({ templateId: props.template.id, limit, offset });
          const batch = res?.groups ?? [];
          totalCount = res?.total ?? totalCount;
          all = all.concat(batch);
          if (batch.length < limit) break;
          offset += limit;
          if (totalCount && offset >= totalCount) break;
        }
        groups.value = all;
        total.value = totalCount || all.length;
        if (selectedGroup.value) {
          const updated = groups.value.find((g) => g.id === selectedGroup.value.id);
          if (updated) selectedGroup.value = updated;
        }
        updateExpandedForSelection();
      } catch (e) {
        toast.error(e?.message || "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0437\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u044C \u0433\u0440\u0443\u043F\u043F\u044B");
      } finally {
        loadingGroups.value = false;
      }
    };
    const compatibilityLabel = (level) => {
      switch (level) {
        case "EXACT":
          return "EXACT";
        case "NEAR":
          return "NEAR";
        case "LEGACY":
          return "LEGACY";
        default:
          return String(level);
      }
    };
    const compatibilitySeverity = (level) => {
      return level === "EXACT" ? "success" : level === "NEAR" ? "warn" : "secondary";
    };
    const selectGroup = (group) => {
      selectedGroup.value = group;
      updateExpandedForSelection();
    };
    const openCreateGroup = () => {
      editingGroup.value = null;
      showGroupDialog.value = true;
    };
    const openEditGroup = (group) => {
      editingGroup.value = group;
      showGroupDialog.value = true;
    };
    const deleteGroup = async (group) => {
      if (!confirm(`\u0423\u0434\u0430\u043B\u0438\u0442\u044C \u0433\u0440\u0443\u043F\u043F\u0443 "${group.name}"?`)) return;
      try {
        await attributeSynonyms.groups.delete({ id: group.id });
        if (selectedGroup.value?.id === group.id) selectedGroup.value = null;
        loadGroups();
      } catch (e) {
        toast.error(e?.message || "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0443\u0434\u0430\u043B\u0438\u0442\u044C \u0433\u0440\u0443\u043F\u043F\u0443");
      }
    };
    const onGroupSaved = () => {
      showGroupDialog.value = false;
      loadGroups();
    };
    const idMap = computed(() => {
      const m = /* @__PURE__ */ new Map();
      for (const g of groups.value) m.set(g.id, g);
      return m;
    });
    const treeNodes = computed(() => {
      const nodesMap = /* @__PURE__ */ new Map();
      const roots = [];
      for (const g of groups.value) nodesMap.set(g.id, { key: String(g.id), label: g.name, data: g, children: [] });
      for (const g of groups.value) {
        const node = nodesMap.get(g.id);
        if (g.parentId && nodesMap.has(g.parentId)) nodesMap.get(g.parentId).children.push(node);
        else roots.push(node);
      }
      return roots;
    });
    function updateExpandedForSelection() {
      const keys = {};
      let current = selectedGroup.value ? idMap.value.get(selectedGroup.value.id) : null;
      while (current && current.parentId) {
        keys[String(current.parentId)] = true;
        current = idMap.value.get(current.parentId);
      }
      expandedKeys.value = keys;
    }
    const isGroupExpanded = (group) => {
      return expandedKeys.value[String(group.id)] === true;
    };
    const toggleGroupExpansion = (group) => {
      const key = String(group.id);
      const newKeys = { ...expandedKeys.value };
      if (newKeys[key]) {
        delete newKeys[key];
      } else {
        newKeys[key] = true;
      }
      expandedKeys.value = newKeys;
    };
    onMounted(() => {
      loadGroups();
    });
    const __returned__ = { props, toast, attributeSynonyms, groups, total, loadingGroups, selectedGroup, showGroupDialog, editingGroup, expandedKeys, loadGroups, compatibilityLabel, compatibilitySeverity, selectGroup, openCreateGroup, openEditGroup, deleteGroup, onGroupSaved, idMap, treeNodes, updateExpandedForSelection, isGroupExpanded, toggleGroupExpansion, VCard, VButton, VTag: Tag, VTree, EditSynonymGroupDialog, SynonymValueEditor, get PlusCircleIcon() {
      return PlusCircleIcon;
    }, get PencilIcon() {
      return PencilIcon;
    }, get TrashIcon() {
      return TrashIcon;
    }, get ChevronDownIcon() {
      return ChevronDownIcon$1;
    }, get ChevronRightIcon() {
      return ChevronRightIcon$1;
    }, DangerButton };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$4(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "flex justify-center gap-4" }, _attrs))}>`);
  _push(ssrRenderComponent($setup["VCard"], null, {
    content: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="p-4 space-y-3"${_scopeId}><div class="flex items-center justify-between"${_scopeId}><h3 class="text-lg font-semibold text-surface-900 dark:text-surface-0"${_scopeId}>\u0413\u0440\u0443\u043F\u043F\u044B \u0441\u0438\u043D\u043E\u043D\u0438\u043C\u043E\u0432</h3>`);
        _push2(ssrRenderComponent($setup["VButton"], {
          size: "small",
          onClick: $setup.openCreateGroup
        }, {
          default: withCtx((_2, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(ssrRenderComponent($setup["PlusCircleIcon"], { class: "w-4 h-4" }, null, _parent3, _scopeId2));
            } else {
              return [
                createVNode($setup["PlusCircleIcon"], { class: "w-4 h-4" })
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(`</div>`);
        _push2(ssrRenderComponent($setup["VTree"], {
          value: $setup.treeNodes,
          expandedKeys: $setup.expandedKeys,
          "onUpdate:expandedKeys": ($event) => $setup.expandedKeys = $event,
          filter: "",
          filterPlaceholder: "\u041F\u043E\u0438\u0441\u043A \u043F\u043E \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u044E / \u043E\u043F\u0438\u0441\u0430\u043D\u0438\u044E..."
        }, {
          default: withCtx(({ node }, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(`<div class="flex items-center justify-between w-full"${_scopeId2}><div class="${ssrRenderClass([{ "bg-primary text-primary-contrast": $setup.selectedGroup?.id === node.data.id, "hover:bg-surface-100 dark:hover:bg-surface-800": $setup.selectedGroup?.id !== node.data.id }, "flex items-center gap-2 flex-1 cursor-pointer rounded px-2 py-1 transition-colors"])}"${_scopeId2}><span class="font-medium"${_scopeId2}>${ssrInterpolate(node.data.name)}</span>`);
              if (node.data.description) {
                _push3(`<small class="text-surface-500"${_scopeId2}>${ssrInterpolate(node.data.description)}</small>`);
              } else {
                _push3(`<!---->`);
              }
              _push3(ssrRenderComponent($setup["VTag"], {
                value: $setup.compatibilityLabel(node.data.compatibilityLevel),
                severity: $setup.compatibilitySeverity(node.data.compatibilityLevel)
              }, null, _parent3, _scopeId2));
              _push3(ssrRenderComponent($setup["VTag"], {
                value: node.data._count?.synonyms || 0,
                severity: "secondary"
              }, null, _parent3, _scopeId2));
              _push3(`</div><div class="flex gap-2"${_scopeId2}>`);
              _push3(ssrRenderComponent($setup["VButton"], {
                size: "small",
                severity: "secondary",
                outlined: "",
                onClick: ($event) => $setup.openEditGroup(node.data)
              }, {
                default: withCtx((_2, _push4, _parent4, _scopeId3) => {
                  if (_push4) {
                    _push4(ssrRenderComponent($setup["PencilIcon"], { class: "w-4 h-4" }, null, _parent4, _scopeId3));
                  } else {
                    return [
                      createVNode($setup["PencilIcon"], { class: "w-4 h-4" })
                    ];
                  }
                }),
                _: 2
              }, _parent3, _scopeId2));
              _push3(ssrRenderComponent($setup["VButton"], {
                size: "small",
                severity: "danger",
                outlined: "",
                onClick: ($event) => $setup.deleteGroup(node.data)
              }, {
                default: withCtx((_2, _push4, _parent4, _scopeId3) => {
                  if (_push4) {
                    _push4(ssrRenderComponent($setup["TrashIcon"], { class: "w-4 h-4" }, null, _parent4, _scopeId3));
                  } else {
                    return [
                      createVNode($setup["TrashIcon"], { class: "w-4 h-4" })
                    ];
                  }
                }),
                _: 2
              }, _parent3, _scopeId2));
              _push3(`</div></div>`);
            } else {
              return [
                createVNode("div", {
                  class: "flex items-center justify-between w-full",
                  onClick: withModifiers(() => {
                  }, ["stop"])
                }, [
                  createVNode("div", {
                    class: ["flex items-center gap-2 flex-1 cursor-pointer rounded px-2 py-1 transition-colors", { "bg-primary text-primary-contrast": $setup.selectedGroup?.id === node.data.id, "hover:bg-surface-100 dark:hover:bg-surface-800": $setup.selectedGroup?.id !== node.data.id }],
                    onClick: ($event) => $setup.selectGroup(node.data)
                  }, [
                    createVNode("span", { class: "font-medium" }, toDisplayString(node.data.name), 1),
                    node.data.description ? (openBlock(), createBlock("small", {
                      key: 0,
                      class: "text-surface-500"
                    }, toDisplayString(node.data.description), 1)) : createCommentVNode("", true),
                    createVNode($setup["VTag"], {
                      value: $setup.compatibilityLabel(node.data.compatibilityLevel),
                      severity: $setup.compatibilitySeverity(node.data.compatibilityLevel)
                    }, null, 8, ["value", "severity"]),
                    createVNode($setup["VTag"], {
                      value: node.data._count?.synonyms || 0,
                      severity: "secondary"
                    }, null, 8, ["value"])
                  ], 10, ["onClick"]),
                  createVNode("div", { class: "flex gap-2" }, [
                    createVNode($setup["VButton"], {
                      size: "small",
                      severity: "secondary",
                      outlined: "",
                      onClick: withModifiers(($event) => $setup.openEditGroup(node.data), ["stop"])
                    }, {
                      default: withCtx(() => [
                        createVNode($setup["PencilIcon"], { class: "w-4 h-4" })
                      ]),
                      _: 2
                    }, 1032, ["onClick"]),
                    createVNode($setup["VButton"], {
                      size: "small",
                      severity: "danger",
                      outlined: "",
                      onClick: withModifiers(($event) => $setup.deleteGroup(node.data), ["stop"])
                    }, {
                      default: withCtx(() => [
                        createVNode($setup["TrashIcon"], { class: "w-4 h-4" })
                      ]),
                      _: 2
                    }, 1032, ["onClick"])
                  ])
                ], 8, ["onClick"])
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(`</div>`);
      } else {
        return [
          createVNode("div", { class: "p-4 space-y-3" }, [
            createVNode("div", { class: "flex items-center justify-between" }, [
              createVNode("h3", { class: "text-lg font-semibold text-surface-900 dark:text-surface-0" }, "\u0413\u0440\u0443\u043F\u043F\u044B \u0441\u0438\u043D\u043E\u043D\u0438\u043C\u043E\u0432"),
              createVNode($setup["VButton"], {
                size: "small",
                onClick: $setup.openCreateGroup
              }, {
                default: withCtx(() => [
                  createVNode($setup["PlusCircleIcon"], { class: "w-4 h-4" })
                ]),
                _: 1
              })
            ]),
            createVNode($setup["VTree"], {
              value: $setup.treeNodes,
              expandedKeys: $setup.expandedKeys,
              "onUpdate:expandedKeys": ($event) => $setup.expandedKeys = $event,
              filter: "",
              filterPlaceholder: "\u041F\u043E\u0438\u0441\u043A \u043F\u043E \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u044E / \u043E\u043F\u0438\u0441\u0430\u043D\u0438\u044E..."
            }, {
              default: withCtx(({ node }) => [
                createVNode("div", {
                  class: "flex items-center justify-between w-full",
                  onClick: withModifiers(() => {
                  }, ["stop"])
                }, [
                  createVNode("div", {
                    class: ["flex items-center gap-2 flex-1 cursor-pointer rounded px-2 py-1 transition-colors", { "bg-primary text-primary-contrast": $setup.selectedGroup?.id === node.data.id, "hover:bg-surface-100 dark:hover:bg-surface-800": $setup.selectedGroup?.id !== node.data.id }],
                    onClick: ($event) => $setup.selectGroup(node.data)
                  }, [
                    createVNode("span", { class: "font-medium" }, toDisplayString(node.data.name), 1),
                    node.data.description ? (openBlock(), createBlock("small", {
                      key: 0,
                      class: "text-surface-500"
                    }, toDisplayString(node.data.description), 1)) : createCommentVNode("", true),
                    createVNode($setup["VTag"], {
                      value: $setup.compatibilityLabel(node.data.compatibilityLevel),
                      severity: $setup.compatibilitySeverity(node.data.compatibilityLevel)
                    }, null, 8, ["value", "severity"]),
                    createVNode($setup["VTag"], {
                      value: node.data._count?.synonyms || 0,
                      severity: "secondary"
                    }, null, 8, ["value"])
                  ], 10, ["onClick"]),
                  createVNode("div", { class: "flex gap-2" }, [
                    createVNode($setup["VButton"], {
                      size: "small",
                      severity: "secondary",
                      outlined: "",
                      onClick: withModifiers(($event) => $setup.openEditGroup(node.data), ["stop"])
                    }, {
                      default: withCtx(() => [
                        createVNode($setup["PencilIcon"], { class: "w-4 h-4" })
                      ]),
                      _: 2
                    }, 1032, ["onClick"]),
                    createVNode($setup["VButton"], {
                      size: "small",
                      severity: "danger",
                      outlined: "",
                      onClick: withModifiers(($event) => $setup.deleteGroup(node.data), ["stop"])
                    }, {
                      default: withCtx(() => [
                        createVNode($setup["TrashIcon"], { class: "w-4 h-4" })
                      ]),
                      _: 2
                    }, 1032, ["onClick"])
                  ])
                ], 8, ["onClick"])
              ]),
              _: 1
            }, 8, ["value", "expandedKeys", "onUpdate:expandedKeys"])
          ])
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(ssrRenderComponent($setup["VCard"], null, {
    content: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="p-4 space-y-4"${_scopeId}><div class="flex items-center justify-between"${_scopeId}><div${_scopeId}><h3 class="text-lg font-semibold text-surface-900 dark:text-surface-0"${_scopeId}>${ssrInterpolate($setup.selectedGroup ? $setup.selectedGroup.name : "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0433\u0440\u0443\u043F\u043F\u0443")}</h3>`);
        if ($setup.selectedGroup) {
          _push2(`<div class="flex items-center gap-2 mt-1"${_scopeId}>`);
          _push2(ssrRenderComponent($setup["VTag"], {
            value: $setup.compatibilityLabel($setup.selectedGroup.compatibilityLevel),
            severity: $setup.compatibilitySeverity($setup.selectedGroup.compatibilityLevel)
          }, null, _parent2, _scopeId));
          if ($setup.selectedGroup.notes) {
            _push2(`<small class="text-surface-500"${_scopeId}>${ssrInterpolate($setup.selectedGroup.notes)}</small>`);
          } else {
            _push2(`<!---->`);
          }
          _push2(`</div>`);
        } else {
          _push2(`<!---->`);
        }
        _push2(`</div>`);
        if ($setup.selectedGroup) {
          _push2(`<div class="flex gap-2"${_scopeId}>`);
          _push2(ssrRenderComponent($setup["VButton"], {
            size: "small",
            severity: "secondary",
            outlined: "",
            onClick: ($event) => $setup.toggleGroupExpansion($setup.selectedGroup),
            title: $setup.isGroupExpanded($setup.selectedGroup) ? "\u0421\u0432\u0435\u0440\u043D\u0443\u0442\u044C \u0433\u0440\u0443\u043F\u043F\u0443" : "\u0420\u0430\u0437\u0432\u0435\u0440\u043D\u0443\u0442\u044C \u0433\u0440\u0443\u043F\u043F\u0443"
          }, {
            default: withCtx((_2, _push3, _parent3, _scopeId2) => {
              if (_push3) {
                if ($setup.isGroupExpanded($setup.selectedGroup)) {
                  _push3(ssrRenderComponent($setup["ChevronDownIcon"], { class: "w-4 h-4" }, null, _parent3, _scopeId2));
                } else {
                  _push3(ssrRenderComponent($setup["ChevronRightIcon"], { class: "w-4 h-4" }, null, _parent3, _scopeId2));
                }
              } else {
                return [
                  $setup.isGroupExpanded($setup.selectedGroup) ? (openBlock(), createBlock($setup["ChevronDownIcon"], {
                    key: 0,
                    class: "w-4 h-4"
                  })) : (openBlock(), createBlock($setup["ChevronRightIcon"], {
                    key: 1,
                    class: "w-4 h-4"
                  }))
                ];
              }
            }),
            _: 1
          }, _parent2, _scopeId));
          _push2(ssrRenderComponent($setup["VButton"], {
            size: "small",
            severity: "secondary",
            outlined: "",
            onClick: ($event) => $setup.openEditGroup($setup.selectedGroup)
          }, {
            default: withCtx((_2, _push3, _parent3, _scopeId2) => {
              if (_push3) {
                _push3(ssrRenderComponent($setup["PencilIcon"], { class: "w-4 h-4" }, null, _parent3, _scopeId2));
              } else {
                return [
                  createVNode($setup["PencilIcon"], { class: "w-4 h-4" })
                ];
              }
            }),
            _: 1
          }, _parent2, _scopeId));
          _push2(ssrRenderComponent($setup["DangerButton"], {
            size: "small",
            outlined: "",
            onClick: ($event) => $setup.deleteGroup($setup.selectedGroup)
          }, {
            default: withCtx((_2, _push3, _parent3, _scopeId2) => {
              if (_push3) {
                _push3(ssrRenderComponent($setup["TrashIcon"], { class: "w-4 h-4" }, null, _parent3, _scopeId2));
              } else {
                return [
                  createVNode($setup["TrashIcon"], { class: "w-4 h-4" })
                ];
              }
            }),
            _: 1
          }, _parent2, _scopeId));
          _push2(`</div>`);
        } else {
          _push2(`<!---->`);
        }
        _push2(`</div>`);
        if (!$setup.selectedGroup) {
          _push2(`<div class="text-surface-500"${_scopeId}>\u0421\u043B\u0435\u0432\u0430 \u0432\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0433\u0440\u0443\u043F\u043F\u0443, \u0447\u0442\u043E\u0431\u044B \u0440\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u044F.</div>`);
        } else {
          _push2(`<div${_scopeId}>`);
          _push2(ssrRenderComponent($setup["SynonymValueEditor"], {
            "group-id": $setup.selectedGroup.id
          }, null, _parent2, _scopeId));
          _push2(`</div>`);
        }
        _push2(`</div>`);
      } else {
        return [
          createVNode("div", { class: "p-4 space-y-4" }, [
            createVNode("div", { class: "flex items-center justify-between" }, [
              createVNode("div", null, [
                createVNode("h3", { class: "text-lg font-semibold text-surface-900 dark:text-surface-0" }, toDisplayString($setup.selectedGroup ? $setup.selectedGroup.name : "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0433\u0440\u0443\u043F\u043F\u0443"), 1),
                $setup.selectedGroup ? (openBlock(), createBlock("div", {
                  key: 0,
                  class: "flex items-center gap-2 mt-1"
                }, [
                  createVNode($setup["VTag"], {
                    value: $setup.compatibilityLabel($setup.selectedGroup.compatibilityLevel),
                    severity: $setup.compatibilitySeverity($setup.selectedGroup.compatibilityLevel)
                  }, null, 8, ["value", "severity"]),
                  $setup.selectedGroup.notes ? (openBlock(), createBlock("small", {
                    key: 0,
                    class: "text-surface-500"
                  }, toDisplayString($setup.selectedGroup.notes), 1)) : createCommentVNode("", true)
                ])) : createCommentVNode("", true)
              ]),
              $setup.selectedGroup ? (openBlock(), createBlock("div", {
                key: 0,
                class: "flex gap-2"
              }, [
                createVNode($setup["VButton"], {
                  size: "small",
                  severity: "secondary",
                  outlined: "",
                  onClick: ($event) => $setup.toggleGroupExpansion($setup.selectedGroup),
                  title: $setup.isGroupExpanded($setup.selectedGroup) ? "\u0421\u0432\u0435\u0440\u043D\u0443\u0442\u044C \u0433\u0440\u0443\u043F\u043F\u0443" : "\u0420\u0430\u0437\u0432\u0435\u0440\u043D\u0443\u0442\u044C \u0433\u0440\u0443\u043F\u043F\u0443"
                }, {
                  default: withCtx(() => [
                    $setup.isGroupExpanded($setup.selectedGroup) ? (openBlock(), createBlock($setup["ChevronDownIcon"], {
                      key: 0,
                      class: "w-4 h-4"
                    })) : (openBlock(), createBlock($setup["ChevronRightIcon"], {
                      key: 1,
                      class: "w-4 h-4"
                    }))
                  ]),
                  _: 1
                }, 8, ["onClick", "title"]),
                createVNode($setup["VButton"], {
                  size: "small",
                  severity: "secondary",
                  outlined: "",
                  onClick: ($event) => $setup.openEditGroup($setup.selectedGroup)
                }, {
                  default: withCtx(() => [
                    createVNode($setup["PencilIcon"], { class: "w-4 h-4" })
                  ]),
                  _: 1
                }, 8, ["onClick"]),
                createVNode($setup["DangerButton"], {
                  size: "small",
                  outlined: "",
                  onClick: ($event) => $setup.deleteGroup($setup.selectedGroup)
                }, {
                  default: withCtx(() => [
                    createVNode($setup["TrashIcon"], { class: "w-4 h-4" })
                  ]),
                  _: 1
                }, 8, ["onClick"])
              ])) : createCommentVNode("", true)
            ]),
            !$setup.selectedGroup ? (openBlock(), createBlock("div", {
              key: 0,
              class: "text-surface-500"
            }, "\u0421\u043B\u0435\u0432\u0430 \u0432\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0433\u0440\u0443\u043F\u043F\u0443, \u0447\u0442\u043E\u0431\u044B \u0440\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u044F.")) : (openBlock(), createBlock("div", { key: 1 }, [
              createVNode($setup["SynonymValueEditor"], {
                "group-id": $setup.selectedGroup.id
              }, null, 8, ["group-id"])
            ]))
          ])
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(ssrRenderComponent($setup["EditSynonymGroupDialog"], {
    visible: $setup.showGroupDialog,
    "onUpdate:visible": ($event) => $setup.showGroupDialog = $event,
    "template-id": $props.template.id,
    group: $setup.editingGroup,
    onSaved: $setup.onGroupSaved
  }, null, _parent));
  _push(`</div>`);
}
const _sfc_setup$4 = _sfc_main$4.setup;
_sfc_main$4.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/attributes/AttributeSynonymManager.vue");
  return _sfc_setup$4 ? _sfc_setup$4(props, ctx) : void 0;
};
const AttributeSynonymManager = /* @__PURE__ */ _export_sfc(_sfc_main$4, [["ssrRender", _sfc_ssrRender$4]]);

const _sfc_main$3 = /* @__PURE__ */ defineComponent({
  __name: "GroupTreeAutoComplete",
  props: {
    modelValue: {},
    groups: {},
    placeholder: { default: "\u041F\u043E\u0438\u0441\u043A \u0433\u0440\u0443\u043F\u043F\u044B..." },
    class: { default: "" },
    invalid: { type: Boolean, default: false }
  },
  emits: ["update:modelValue", "group-select", "group-clear"],
  setup(__props, { expose: __expose, emit: __emit }) {
    __expose();
    const props = __props;
    const emit = __emit;
    const selectedGroup = ref(null);
    const filteredGroups = ref([]);
    const searchTimeout = ref(null);
    const inputClass = computed(() => {
      let classes = "w-full";
      if (props.class) classes += ` ${props.class}`;
      if (props.invalid) classes += " p-invalid";
      return classes;
    });
    const flattenGroupsHierarchy = (groups, level = 0, parentPath = "") => {
      const result = [];
      for (const group of groups) {
        const displayName = parentPath ? `${parentPath} / ${group.name}` : group.name;
        result.push({
          ...group,
          level,
          displayName,
          hasChildren: group.children && group.children.length > 0,
          templatesCount: group._count?.templates || 0
        });
        if (group.children && group.children.length > 0) {
          result.push(...flattenGroupsHierarchy(group.children, level + 1, displayName));
        }
      }
      return result;
    };
    const searchGroups = async (event) => {
      console.log("searchGroups \u0432\u044B\u0437\u0432\u0430\u043D \u0432 GroupTreeAutoComplete", { event, groupsLength: props.groups.length });
      const query = event.query || "";
      if (searchTimeout.value) {
        clearTimeout(searchTimeout.value);
      }
      const flatGroups = flattenGroupsHierarchy(props.groups);
      if (!query.trim()) {
        filteredGroups.value = flatGroups;
        console.log("\u041F\u043E\u043A\u0430\u0437\u044B\u0432\u0430\u0435\u043C \u0432\u0441\u0435 \u0433\u0440\u0443\u043F\u043F\u044B:", filteredGroups.value.length);
        return;
      }
      searchTimeout.value = setTimeout(() => {
        try {
          filteredGroups.value = flatGroups.filter(
            (group) => group.name.toLowerCase().includes(query.toLowerCase()) || group.description && group.description.toLowerCase().includes(query.toLowerCase()) || group.displayName.toLowerCase().includes(query.toLowerCase())
          );
          console.log("\u0424\u0438\u043B\u044C\u0442\u0440\u0443\u0435\u043C \u0433\u0440\u0443\u043F\u043F\u044B:", filteredGroups.value.length);
        } catch (error) {
          console.error("\u041E\u0448\u0438\u0431\u043A\u0430 \u043F\u043E\u0438\u0441\u043A\u0430 \u0433\u0440\u0443\u043F\u043F:", error);
          filteredGroups.value = flatGroups;
        }
      }, 300);
    };
    const onGroupSelect = (event) => {
      const group = event.value;
      emit("update:modelValue", group.id);
      emit("group-select", group);
      selectedGroup.value = group;
    };
    const onGroupClear = () => {
      emit("update:modelValue", null);
      emit("group-clear");
      selectedGroup.value = null;
    };
    const findSelectedGroup = () => {
      if (props.modelValue) {
        const flatGroups = flattenGroupsHierarchy(props.groups);
        selectedGroup.value = flatGroups.find((g) => g.id === props.modelValue) || null;
      } else {
        selectedGroup.value = null;
      }
    };
    watch(() => props.modelValue, () => {
      findSelectedGroup();
    }, { immediate: true });
    watch(() => props.groups, () => {
      const flatGroups = flattenGroupsHierarchy(props.groups);
      filteredGroups.value = flatGroups;
      findSelectedGroup();
    }, { immediate: true });
    onMounted(() => {
      console.log("GroupTreeAutoComplete \u043C\u043E\u043D\u0442\u0438\u0440\u0443\u0435\u0442\u0441\u044F");
      const flatGroups = flattenGroupsHierarchy(props.groups);
      filteredGroups.value = flatGroups;
      findSelectedGroup();
      console.log("\u0418\u043D\u0438\u0446\u0438\u0430\u043B\u0438\u0437\u0438\u0440\u043E\u0432\u0430\u043D\u044B \u0433\u0440\u0443\u043F\u043F\u044B \u0432 GroupTreeAutoComplete:", {
        hierarchyGroups: props.groups.length,
        flatGroups: filteredGroups.value.length
      });
    });
    onUnmounted(() => {
      if (searchTimeout.value) {
        clearTimeout(searchTimeout.value);
      }
    });
    const __returned__ = { props, emit, selectedGroup, filteredGroups, searchTimeout, inputClass, flattenGroupsHierarchy, searchGroups, onGroupSelect, onGroupClear, findSelectedGroup, VAutoComplete, get FolderIcon() {
      return FolderIcon;
    }, get FileIcon() {
      return FileIcon;
    } };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$3(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "group-tree-autocomplete" }, _attrs))} data-v-be3fab62>`);
  _push(ssrRenderComponent($setup["VAutoComplete"], {
    modelValue: $setup.selectedGroup,
    "onUpdate:modelValue": ($event) => $setup.selectedGroup = $event,
    suggestions: $setup.filteredGroups,
    onComplete: $setup.searchGroups,
    onDropdownClick: () => $setup.searchGroups({ query: "" }),
    "option-label": "displayName",
    placeholder: $props.placeholder,
    class: $setup.inputClass,
    dropdown: "",
    "dropdown-mode": "current",
    "show-clear": "",
    onItemSelect: $setup.onGroupSelect,
    onClear: $setup.onGroupClear
  }, {
    option: withCtx((slotProps, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="flex items-center gap-2 py-1" data-v-be3fab62${_scopeId}><div style="${ssrRenderStyle({ paddingLeft: `${slotProps.option.level * 16}px` })}" class="flex items-center gap-2 w-full" data-v-be3fab62${_scopeId}>`);
        if (slotProps.option.hasChildren) {
          _push2(ssrRenderComponent($setup["FolderIcon"], { class: "w-4 h-4 text-surface-500" }, null, _parent2, _scopeId));
        } else {
          _push2(ssrRenderComponent($setup["FileIcon"], { class: "w-4 h-4 text-surface-400" }, null, _parent2, _scopeId));
        }
        _push2(`<span class="flex-1" data-v-be3fab62${_scopeId}>${ssrInterpolate(slotProps.option.name)}</span>`);
        if (slotProps.option.templatesCount > 0) {
          _push2(`<span class="text-xs text-surface-500 bg-surface-100 dark:bg-surface-800 px-2 py-1 rounded" data-v-be3fab62${_scopeId}>${ssrInterpolate(slotProps.option.templatesCount)}</span>`);
        } else {
          _push2(`<!---->`);
        }
        _push2(`</div></div>`);
      } else {
        return [
          createVNode("div", { class: "flex items-center gap-2 py-1" }, [
            createVNode("div", {
              style: { paddingLeft: `${slotProps.option.level * 16}px` },
              class: "flex items-center gap-2 w-full"
            }, [
              slotProps.option.hasChildren ? (openBlock(), createBlock($setup["FolderIcon"], {
                key: 0,
                class: "w-4 h-4 text-surface-500"
              })) : (openBlock(), createBlock($setup["FileIcon"], {
                key: 1,
                class: "w-4 h-4 text-surface-400"
              })),
              createVNode("span", { class: "flex-1" }, toDisplayString(slotProps.option.name), 1),
              slotProps.option.templatesCount > 0 ? (openBlock(), createBlock("span", {
                key: 2,
                class: "text-xs text-surface-500 bg-surface-100 dark:bg-surface-800 px-2 py-1 rounded"
              }, toDisplayString(slotProps.option.templatesCount), 1)) : createCommentVNode("", true)
            ], 4)
          ])
        ];
      }
    }),
    empty: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="p-3 text-surface-500 text-center" data-v-be3fab62${_scopeId}> \u0413\u0440\u0443\u043F\u043F\u044B \u043D\u0435 \u043D\u0430\u0439\u0434\u0435\u043D\u044B </div>`);
      } else {
        return [
          createVNode("div", { class: "p-3 text-surface-500 text-center" }, " \u0413\u0440\u0443\u043F\u043F\u044B \u043D\u0435 \u043D\u0430\u0439\u0434\u0435\u043D\u044B ")
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</div>`);
}
const _sfc_setup$3 = _sfc_main$3.setup;
_sfc_main$3.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/GroupTreeAutoComplete.vue");
  return _sfc_setup$3 ? _sfc_setup$3(props, ctx) : void 0;
};
const GroupTreeAutoComplete = /* @__PURE__ */ _export_sfc(_sfc_main$3, [["ssrRender", _sfc_ssrRender$3], ["__scopeId", "data-v-be3fab62"]]);

const _sfc_main$2 = /* @__PURE__ */ defineComponent({
  __name: "TemplateForm",
  props: {
    modelValue: {},
    groups: {},
    hierarchyGroups: { default: () => [] },
    loading: { type: Boolean, default: false }
  },
  emits: ["update:modelValue", "save", "cancel", "group-created"],
  setup(__props, { expose: __expose, emit: __emit }) {
    __expose();
    const props = __props;
    const emit = __emit;
    const { attributeTemplates } = useTrpc();
    const toast = useToast();
    const { userRole } = useAuth();
    const form = computed({
      get: () => props.modelValue,
      set: (value) => emit("update:modelValue", value)
    });
    const errors = ref({});
    const showCreateGroupDialog = ref(false);
    const creatingGroup = ref(false);
    const newGroupForm = ref({
      name: "",
      description: ""
    });
    const showSynonymsDialog = ref(false);
    const openSynonyms = () => {
      if (!form.value.id) {
        toast.info("\u0421\u043D\u0430\u0447\u0430\u043B\u0430 \u0441\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u0435 \u0448\u0430\u0431\u043B\u043E\u043D");
        return;
      }
      showSynonymsDialog.value = true;
    };
    const selectedGroup = ref(null);
    const dataTypeOptions = [
      { label: "\u0421\u0442\u0440\u043E\u043A\u0430", value: "STRING" },
      { label: "\u0427\u0438\u0441\u043B\u043E", value: "NUMBER" },
      { label: "\u041B\u043E\u0433\u0438\u0447\u0435\u0441\u043A\u043E\u0435", value: "BOOLEAN" },
      { label: "\u0414\u0430\u0442\u0430", value: "DATE" },
      { label: "JSON", value: "JSON" }
    ];
    const unitOptions = [
      // Длина
      { label: "\u043C\u043C", value: "MM" },
      { label: "\u0434\u044E\u0439\u043C\u044B", value: "INCH" },
      { label: "\u0444\u0443\u0442\u044B", value: "FT" },
      // Вес
      { label: "\u0433", value: "G" },
      { label: "\u043A\u0433", value: "KG" },
      { label: "\u0442", value: "T" },
      { label: "\u0444\u0443\u043D\u0442\u044B", value: "LB" },
      // Объем
      { label: "\u043C\u043B", value: "ML" },
      { label: "\u043B", value: "L" },
      { label: "\u0433\u0430\u043B\u043B\u043E\u043D\u044B", value: "GAL" },
      // Количество
      { label: "\u0448\u0442", value: "PCS" },
      { label: "\u043A\u043E\u043C\u043F\u043B\u0435\u043A\u0442", value: "SET" },
      { label: "\u043F\u0430\u0440\u0430", value: "PAIR" },
      // Давление
      { label: "\u0431\u0430\u0440", value: "BAR" },
      { label: "PSI", value: "PSI" },
      // Мощность
      { label: "\u043A\u0412\u0442", value: "KW" },
      { label: "\u043B.\u0441.", value: "HP" },
      // Крутящий момент
      { label: "\u041D\u22C5\u043C", value: "NM" },
      { label: "\u043E\u0431/\u043C\u0438\u043D", value: "RPM" },
      // Температура
      { label: "\xB0C", value: "C" },
      { label: "\xB0F", value: "F" },
      // Относительные
      { label: "%", value: "PERCENT" }
    ];
    const filteredDataTypeOptions = ref(dataTypeOptions);
    const filteredUnitOptions = ref(unitOptions);
    const selectedDataType = computed({
      get: () => {
        return dataTypeOptions.find((opt) => opt.value === form.value.dataType) || null;
      },
      set: (newValue) => {
        form.value.dataType = newValue ? newValue.value : null;
      }
    });
    const selectedUnit = computed({
      get: () => {
        return unitOptions.find((opt) => opt.value === form.value.unit) || null;
      },
      set: (newValue) => {
        form.value.unit = newValue ? newValue.value : null;
      }
    });
    const filterDataTypes = (event) => {
      console.log("filterDataTypes \u0432\u044B\u0437\u0432\u0430\u043D \u0432 TemplateForm", { event });
      const query = event.query?.toLowerCase() || "";
      if (!query.trim()) {
        filteredDataTypeOptions.value = [...dataTypeOptions];
        console.log("\u041F\u043E\u043A\u0430\u0437\u044B\u0432\u0430\u0435\u043C \u0432\u0441\u0435 \u0442\u0438\u043F\u044B \u0434\u0430\u043D\u043D\u044B\u0445:", filteredDataTypeOptions.value.length);
      } else {
        filteredDataTypeOptions.value = dataTypeOptions.filter(
          (option) => option.label.toLowerCase().includes(query)
        );
        console.log("\u0424\u0438\u043B\u044C\u0442\u0440\u0443\u0435\u043C \u0442\u0438\u043F\u044B \u0434\u0430\u043D\u043D\u044B\u0445:", filteredDataTypeOptions.value.length);
      }
    };
    const filterUnits = (event) => {
      console.log("filterUnits \u0432\u044B\u0437\u0432\u0430\u043D \u0432 TemplateForm", { event });
      const query = event.query?.toLowerCase() || "";
      if (!query.trim()) {
        filteredUnitOptions.value = [...unitOptions];
        console.log("\u041F\u043E\u043A\u0430\u0437\u044B\u0432\u0430\u0435\u043C \u0432\u0441\u0435 \u0435\u0434\u0438\u043D\u0438\u0446\u044B:", filteredUnitOptions.value.length);
      } else {
        filteredUnitOptions.value = unitOptions.filter(
          (option) => option.label.toLowerCase().includes(query)
        );
        console.log("\u0424\u0438\u043B\u044C\u0442\u0440\u0443\u0435\u043C \u0435\u0434\u0438\u043D\u0438\u0446\u044B:", filteredUnitOptions.value.length);
      }
    };
    const findSelectedGroup = () => {
      if (form.value.groupId) {
        selectedGroup.value = props.groups.find((g) => g.id === form.value.groupId) || null;
      } else {
        selectedGroup.value = null;
      }
    };
    const isEditing = computed(() => !!form.value.id);
    const isValid = computed(() => {
      return form.value.name && form.value.title && form.value.dataType && !Object.keys(errors.value).length;
    });
    const dataTypeClass = computed(() => {
      return `w-full ${errors.value.dataType ? "p-invalid" : ""}`;
    });
    const validateForm = () => {
      errors.value = {};
      if (!form.value.name) {
        errors.value.name = "\u0421\u0438\u0441\u0442\u0435\u043C\u043D\u043E\u0435 \u0438\u043C\u044F \u043E\u0431\u044F\u0437\u0430\u0442\u0435\u043B\u044C\u043D\u043E";
      } else if (!/^[a-z0-9_]+$/.test(form.value.name)) {
        errors.value.name = "\u0422\u043E\u043B\u044C\u043A\u043E \u0441\u0442\u0440\u043E\u0447\u043D\u044B\u0435 \u0431\u0443\u043A\u0432\u044B, \u0446\u0438\u0444\u0440\u044B \u0438 \u043F\u043E\u0434\u0447\u0435\u0440\u043A\u0438\u0432\u0430\u043D\u0438\u044F";
      }
      if (!form.value.title) {
        errors.value.title = "\u041E\u0442\u043E\u0431\u0440\u0430\u0436\u0430\u0435\u043C\u043E\u0435 \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u0435 \u043E\u0431\u044F\u0437\u0430\u0442\u0435\u043B\u044C\u043D\u043E";
      }
      if (!form.value.dataType) {
        errors.value.dataType = "\u0422\u0438\u043F \u0434\u0430\u043D\u043D\u044B\u0445 \u043E\u0431\u044F\u0437\u0430\u0442\u0435\u043B\u0435\u043D";
      }
    };
    const save = () => {
      validateForm();
      if (isValid.value) {
        emit("save", form.value);
      } else {
        toast.error("\u041F\u043E\u0436\u0430\u043B\u0443\u0439\u0441\u0442\u0430, \u0438\u0441\u043F\u0440\u0430\u0432\u044C\u0442\u0435 \u043E\u0448\u0438\u0431\u043A\u0438 \u0432 \u0444\u043E\u0440\u043C\u0435");
      }
    };
    const onGroupSelect = (group) => {
      toast.info(`\u0412\u044B\u0431\u0440\u0430\u043D\u0430 \u0433\u0440\u0443\u043F\u043F\u0430: ${group.name}`);
    };
    const onGroupClear = () => {
      toast.info("\u0413\u0440\u0443\u043F\u043F\u0430 \u0441\u0431\u0440\u043E\u0448\u0435\u043D\u0430");
    };
    const createGroup = async () => {
      if (!newGroupForm.value.name) {
        toast.error("\u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u0435 \u0433\u0440\u0443\u043F\u043F\u044B");
        return;
      }
      try {
        creatingGroup.value = true;
        toast.info("\u0421\u043E\u0437\u0434\u0430\u043D\u0438\u0435 \u0433\u0440\u0443\u043F\u043F\u044B...");
        const result = await attributeTemplates.createGroup(newGroupForm.value);
        if (result && typeof result === "object" && "id" in result) {
          form.value.groupId = result.id;
          selectedGroup.value = result;
          showCreateGroupDialog.value = false;
          newGroupForm.value = { name: "", description: "" };
          emit("group-created", result);
        }
      } catch (error) {
        console.error("\u041E\u0448\u0438\u0431\u043A\u0430 \u0441\u043E\u0437\u0434\u0430\u043D\u0438\u044F \u0433\u0440\u0443\u043F\u043F\u044B:", error);
        if (error.message?.includes("\u0443\u0436\u0435 \u0441\u0443\u0449\u0435\u0441\u0442\u0432\u0443\u0435\u0442")) {
          toast.error("\u0413\u0440\u0443\u043F\u043F\u0430 \u0441 \u0442\u0430\u043A\u0438\u043C \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u0435\u043C \u0443\u0436\u0435 \u0441\u0443\u0449\u0435\u0441\u0442\u0432\u0443\u0435\u0442");
        } else {
          toast.error(error.message || "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0441\u043E\u0437\u0434\u0430\u0442\u044C \u0433\u0440\u0443\u043F\u043F\u0443");
        }
      } finally {
        creatingGroup.value = false;
      }
    };
    watch(() => props.modelValue, (newValue) => {
      if (!newValue) {
        form.value = {
          dataType: "STRING",
          isRequired: false,
          allowedValues: []
        };
        return;
      }
      if (!newValue.dataType) {
        form.value.dataType = "STRING";
      }
      if (!newValue.isRequired) {
        form.value.isRequired = false;
      }
      if (!newValue.allowedValues) {
        form.value.allowedValues = [];
      }
      findSelectedGroup();
    }, { immediate: true });
    watch(() => props.groups, () => {
      findSelectedGroup();
    }, { immediate: true });
    onMounted(() => {
      console.log("TemplateForm \u043C\u043E\u043D\u0442\u0438\u0440\u0443\u0435\u0442\u0441\u044F");
      filteredDataTypeOptions.value = [...dataTypeOptions];
      filteredUnitOptions.value = [...unitOptions];
      console.log("\u0418\u043D\u0438\u0446\u0438\u0430\u043B\u0438\u0437\u0438\u0440\u043E\u0432\u0430\u043D\u044B \u0430\u0432\u0442\u043E\u043A\u043E\u043C\u043F\u043B\u0438\u0442\u044B \u0432 TemplateForm:", {
        dataTypes: filteredDataTypeOptions.value.length,
        units: filteredUnitOptions.value.length,
        groups: props.groups.length,
        hierarchyGroups: props.hierarchyGroups.length
      });
      findSelectedGroup();
    });
    watch(() => form.value.name, () => {
      if (errors.value.name) {
        delete errors.value.name;
      }
    });
    watch(() => form.value.title, () => {
      if (errors.value.title) {
        delete errors.value.title;
      }
    });
    watch(() => form.value.dataType, () => {
      if (errors.value.dataType) {
        delete errors.value.dataType;
      }
    });
    const __returned__ = { props, emit, attributeTemplates, toast, userRole, form, errors, showCreateGroupDialog, creatingGroup, newGroupForm, showSynonymsDialog, openSynonyms, selectedGroup, dataTypeOptions, unitOptions, filteredDataTypeOptions, filteredUnitOptions, selectedDataType, selectedUnit, filterDataTypes, filterUnits, findSelectedGroup, isEditing, isValid, dataTypeClass, validateForm, save, onGroupSelect, onGroupClear, createGroup, VInputText: InputText, VTextarea, VInputNumber, InputChips, VCheckbox: Checkbox, VButton, VDialog: Dialog, VAutoComplete, AttributeSynonymManager, GroupTreeAutoComplete, get PlusIcon() {
      return PlusIcon;
    }, get TagsIcon() {
      return TagsIcon;
    } };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$2(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "template-form" }, _attrs))}><div class="space-y-4"><div class="grid grid-cols-1 md:grid-cols-2 gap-4"><div><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"> \u0421\u0438\u0441\u0442\u0435\u043C\u043D\u043E\u0435 \u0438\u043C\u044F * </label>`);
  _push(ssrRenderComponent($setup["VInputText"], {
    modelValue: $setup.form.name,
    "onUpdate:modelValue": ($event) => $setup.form.name = $event,
    placeholder: "inner_diameter",
    class: ["w-full", { "p-invalid": $setup.errors.name }]
  }, null, _parent));
  if ($setup.errors.name) {
    _push(`<small class="p-error">${ssrInterpolate($setup.errors.name)}</small>`);
  } else {
    _push(`<!---->`);
  }
  _push(`<small class="text-surface-500 dark:text-surface-400"> \u0422\u043E\u043B\u044C\u043A\u043E \u0441\u0442\u0440\u043E\u0447\u043D\u044B\u0435 \u0431\u0443\u043A\u0432\u044B, \u0446\u0438\u0444\u0440\u044B \u0438 \u043F\u043E\u0434\u0447\u0435\u0440\u043A\u0438\u0432\u0430\u043D\u0438\u044F </small></div><div><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"> \u041E\u0442\u043E\u0431\u0440\u0430\u0436\u0430\u0435\u043C\u043E\u0435 \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u0435 * </label>`);
  _push(ssrRenderComponent($setup["VInputText"], {
    modelValue: $setup.form.title,
    "onUpdate:modelValue": ($event) => $setup.form.title = $event,
    placeholder: "\u0412\u043D\u0443\u0442\u0440\u0435\u043D\u043D\u0438\u0439 \u0434\u0438\u0430\u043C\u0435\u0442\u0440",
    class: ["w-full", { "p-invalid": $setup.errors.title }]
  }, null, _parent));
  if ($setup.errors.title) {
    _push(`<small class="p-error">${ssrInterpolate($setup.errors.title)}</small>`);
  } else {
    _push(`<!---->`);
  }
  _push(`</div></div><div><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"> \u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435 </label>`);
  _push(ssrRenderComponent($setup["VTextarea"], {
    modelValue: $setup.form.description,
    "onUpdate:modelValue": ($event) => $setup.form.description = $event,
    placeholder: "\u041F\u043E\u0434\u0440\u043E\u0431\u043D\u043E\u0435 \u043E\u043F\u0438\u0441\u0430\u043D\u0438\u0435 \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u0430...",
    rows: "3",
    class: "w-full"
  }, null, _parent));
  _push(`</div><div class="grid grid-cols-1 md:grid-cols-2 gap-4"><div><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"> \u0422\u0438\u043F \u0434\u0430\u043D\u043D\u044B\u0445 * </label>`);
  _push(ssrRenderComponent($setup["VAutoComplete"], {
    modelValue: $setup.selectedDataType,
    "onUpdate:modelValue": ($event) => $setup.selectedDataType = $event,
    suggestions: $setup.filteredDataTypeOptions,
    onComplete: $setup.filterDataTypes,
    onDropdownClick: () => $setup.filterDataTypes({ query: "" }),
    "option-label": "label",
    "option-value": "value",
    placeholder: "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0442\u0438\u043F",
    class: $setup.dataTypeClass,
    dropdown: ""
  }, null, _parent));
  if ($setup.errors.dataType) {
    _push(`<small class="p-error">${ssrInterpolate($setup.errors.dataType)}</small>`);
  } else {
    _push(`<!---->`);
  }
  _push(`</div><div><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"> \u0415\u0434\u0438\u043D\u0438\u0446\u0430 \u0438\u0437\u043C\u0435\u0440\u0435\u043D\u0438\u044F </label>`);
  _push(ssrRenderComponent($setup["VAutoComplete"], {
    modelValue: $setup.selectedUnit,
    "onUpdate:modelValue": ($event) => $setup.selectedUnit = $event,
    suggestions: $setup.filteredUnitOptions,
    onComplete: $setup.filterUnits,
    onDropdownClick: () => $setup.filterUnits({ query: "" }),
    "option-label": "label",
    "option-value": "value",
    placeholder: "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0435\u0434\u0438\u043D\u0438\u0446\u0443",
    class: "w-full",
    dropdown: "",
    "show-clear": ""
  }, null, _parent));
  _push(`</div></div><div><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"> \u0413\u0440\u0443\u043F\u043F\u0430 </label><div class="flex gap-2">`);
  _push(ssrRenderComponent($setup["GroupTreeAutoComplete"], {
    modelValue: $setup.form.groupId,
    "onUpdate:modelValue": ($event) => $setup.form.groupId = $event,
    groups: $props.hierarchyGroups,
    placeholder: "\u041F\u043E\u0438\u0441\u043A \u0433\u0440\u0443\u043F\u043F\u044B...",
    class: "flex-1",
    invalid: !!$setup.errors.groupId,
    onGroupSelect: $setup.onGroupSelect,
    onGroupClear: $setup.onGroupClear
  }, null, _parent));
  _push(ssrRenderComponent($setup["VButton"], {
    onClick: ($event) => $setup.showCreateGroupDialog = true,
    severity: "secondary",
    outlined: "",
    size: "small"
  }, {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(` \u0421\u043E\u0437\u0434\u0430\u0442\u044C `);
        _push2(ssrRenderComponent($setup["PlusIcon"], { class: "w-5 h-5" }, null, _parent2, _scopeId));
      } else {
        return [
          createTextVNode(" \u0421\u043E\u0437\u0434\u0430\u0442\u044C "),
          createVNode($setup["PlusIcon"], { class: "w-5 h-5" })
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</div>`);
  if ($setup.errors.groupId) {
    _push(`<small class="p-error">${ssrInterpolate($setup.errors.groupId)}</small>`);
  } else {
    _push(`<!---->`);
  }
  _push(`</div>`);
  if ($setup.form.dataType === "NUMBER") {
    _push(`<div class="grid grid-cols-1 md:grid-cols-2 gap-4"><div><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"> \u041C\u0438\u043D\u0438\u043C\u0430\u043B\u044C\u043D\u043E\u0435 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0435 </label>`);
    _push(ssrRenderComponent($setup["VInputNumber"], {
      modelValue: $setup.form.minValue,
      "onUpdate:modelValue": ($event) => $setup.form.minValue = $event,
      placeholder: "0",
      class: "w-full",
      "use-grouping": false,
      "min-fraction-digits": 2,
      "max-fraction-digits": 2
    }, null, _parent));
    _push(`</div><div><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"> \u041C\u0430\u043A\u0441\u0438\u043C\u0430\u043B\u044C\u043D\u043E\u0435 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0435 </label>`);
    _push(ssrRenderComponent($setup["VInputNumber"], {
      modelValue: $setup.form.maxValue,
      "onUpdate:modelValue": ($event) => $setup.form.maxValue = $event,
      placeholder: "100",
      class: "w-full",
      "use-grouping": false,
      "min-fraction-digits": 2,
      "max-fraction-digits": 2
    }, null, _parent));
    _push(`</div></div>`);
  } else {
    _push(`<!---->`);
  }
  if ($setup.form.dataType === "NUMBER") {
    _push(`<div><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"> \u0414\u043E\u043F\u0443\u0441\u0442\u0438\u043C\u043E\u0435 \u043E\u0442\u043A\u043B\u043E\u043D\u0435\u043D\u0438\u0435 (tolerance) </label>`);
    _push(ssrRenderComponent($setup["VInputNumber"], {
      modelValue: $setup.form.tolerance,
      "onUpdate:modelValue": ($event) => $setup.form.tolerance = $event,
      placeholder: "0.1",
      class: "w-full",
      "use-grouping": false,
      "min-fraction-digits": 1,
      "max-fraction-digits": 4,
      min: 0
    }, null, _parent));
    _push(`<small class="text-surface-500 dark:text-surface-400"> \u0414\u043E\u043F\u0443\u0441\u0442\u0438\u043C\u043E\u0435 \u043E\u0442\u043A\u043B\u043E\u043D\u0435\u043D\u0438\u0435 \u043F\u0440\u0438 \u0441\u043E\u043F\u043E\u0441\u0442\u0430\u0432\u043B\u0435\u043D\u0438\u0438 \u0447\u0438\u0441\u043B\u043E\u0432\u044B\u0445 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0439. \u041D\u0430\u043F\u0440\u0438\u043C\u0435\u0440: \u0435\u0441\u043B\u0438 \u044D\u0442\u0430\u043B\u043E\u043D = 30.0 \u0438 \u0434\u043E\u043F\u0443\u0441\u043A = 0.1, \u0442\u043E \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u044F \u043E\u0442 29.9 \u0434\u043E 30.1 \u0431\u0443\u0434\u0443\u0442 \u0441\u0447\u0438\u0442\u0430\u0442\u044C\u0441\u044F \u044D\u043A\u0432\u0438\u0432\u0430\u043B\u0435\u043D\u0442\u043D\u044B\u043C\u0438. </small></div>`);
  } else {
    _push(`<!---->`);
  }
  if ($setup.form.dataType === "STRING") {
    _push(`<div><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"> \u0414\u043E\u043F\u0443\u0441\u0442\u0438\u043C\u044B\u0435 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u044F </label>`);
    _push(ssrRenderComponent($setup["InputChips"], {
      modelValue: $setup.form.allowedValues,
      "onUpdate:modelValue": ($event) => $setup.form.allowedValues = $event,
      placeholder: "\u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0435 \u0438 \u043D\u0430\u0436\u043C\u0438\u0442\u0435 Enter",
      class: "w-full"
    }, null, _parent));
    _push(`<small class="text-surface-500 dark:text-surface-400"> \u041E\u0441\u0442\u0430\u0432\u044C\u0442\u0435 \u043F\u0443\u0441\u0442\u044B\u043C \u0434\u043B\u044F \u043B\u044E\u0431\u044B\u0445 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0439. \u041D\u0430\u043F\u0440\u0438\u043C\u0435\u0440: steel, aluminum, plastic </small><div class="mt-3">`);
    _push(ssrRenderComponent($setup["VButton"], {
      severity: "secondary",
      outlined: "",
      onClick: $setup.openSynonyms
    }, {
      default: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(` \u0421\u0438\u043D\u043E\u043D\u0438\u043C\u044B `);
          _push2(ssrRenderComponent($setup["TagsIcon"], { class: "w-5 h-5" }, null, _parent2, _scopeId));
        } else {
          return [
            createTextVNode(" \u0421\u0438\u043D\u043E\u043D\u0438\u043C\u044B "),
            createVNode($setup["TagsIcon"], { class: "w-5 h-5" })
          ];
        }
      }),
      _: 1
    }, _parent));
    _push(`<small class="ml-2 text-surface-500">\u0414\u043E\u0441\u0442\u0443\u043F\u043D\u043E \u043F\u043E\u0441\u043B\u0435 \u0441\u043E\u0445\u0440\u0430\u043D\u0435\u043D\u0438\u044F \u0448\u0430\u0431\u043B\u043E\u043D\u0430</small></div></div>`);
  } else {
    _push(`<!---->`);
  }
  _push(`<div class="flex items-center gap-4">`);
  _push(ssrRenderComponent($setup["VCheckbox"], {
    modelValue: $setup.form.isRequired,
    "onUpdate:modelValue": ($event) => $setup.form.isRequired = $event,
    "input-id": "required",
    binary: ""
  }, null, _parent));
  _push(`<label for="required" class="text-sm text-surface-700 dark:text-surface-300"> \u041E\u0431\u044F\u0437\u0430\u0442\u0435\u043B\u044C\u043D\u044B\u0439 \u0430\u0442\u0440\u0438\u0431\u0443\u0442 </label></div></div><div class="flex justify-end gap-3 mt-6 pt-4 border-t border-surface-200 dark:border-surface-700">`);
  _push(ssrRenderComponent($setup["VButton"], {
    label: "\u041E\u0442\u043C\u0435\u043D\u0430",
    severity: "secondary",
    onClick: ($event) => _ctx.$emit("cancel")
  }, null, _parent));
  _push(ssrRenderComponent($setup["VButton"], {
    label: $setup.isEditing ? "\u041E\u0431\u043D\u043E\u0432\u0438\u0442\u044C" : "\u0421\u043E\u0437\u0434\u0430\u0442\u044C",
    onClick: $setup.save,
    loading: $props.loading,
    disabled: !$setup.isValid
  }, null, _parent));
  _push(`</div>`);
  _push(ssrRenderComponent($setup["VDialog"], {
    visible: $setup.showCreateGroupDialog,
    "onUpdate:visible": ($event) => $setup.showCreateGroupDialog = $event,
    modal: "",
    header: "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u0433\u0440\u0443\u043F\u043F\u0443 \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432",
    style: { width: "30rem" }
  }, {
    footer: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["VButton"], {
          label: "\u041E\u0442\u043C\u0435\u043D\u0430",
          severity: "secondary",
          onClick: ($event) => $setup.showCreateGroupDialog = false
        }, null, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["VButton"], {
          label: "\u0421\u043E\u0437\u0434\u0430\u0442\u044C",
          onClick: $setup.createGroup,
          loading: $setup.creatingGroup,
          disabled: !$setup.newGroupForm.name
        }, null, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["VButton"], {
            label: "\u041E\u0442\u043C\u0435\u043D\u0430",
            severity: "secondary",
            onClick: ($event) => $setup.showCreateGroupDialog = false
          }, null, 8, ["onClick"]),
          createVNode($setup["VButton"], {
            label: "\u0421\u043E\u0437\u0434\u0430\u0442\u044C",
            onClick: $setup.createGroup,
            loading: $setup.creatingGroup,
            disabled: !$setup.newGroupForm.name
          }, null, 8, ["loading", "disabled"])
        ];
      }
    }),
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="space-y-4"${_scopeId}><div${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}> \u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435 \u0433\u0440\u0443\u043F\u043F\u044B * </label>`);
        _push2(ssrRenderComponent($setup["VInputText"], {
          modelValue: $setup.newGroupForm.name,
          "onUpdate:modelValue": ($event) => $setup.newGroupForm.name = $event,
          placeholder: "\u0420\u0430\u0437\u043C\u0435\u0440\u044B",
          class: "w-full"
        }, null, _parent2, _scopeId));
        _push2(`</div><div${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}> \u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435 </label>`);
        _push2(ssrRenderComponent($setup["VTextarea"], {
          modelValue: $setup.newGroupForm.description,
          "onUpdate:modelValue": ($event) => $setup.newGroupForm.description = $event,
          placeholder: "\u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435 \u0433\u0440\u0443\u043F\u043F\u044B...",
          rows: "2",
          class: "w-full"
        }, null, _parent2, _scopeId));
        _push2(`</div></div>`);
      } else {
        return [
          createVNode("div", { class: "space-y-4" }, [
            createVNode("div", null, [
              createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435 \u0433\u0440\u0443\u043F\u043F\u044B * "),
              createVNode($setup["VInputText"], {
                modelValue: $setup.newGroupForm.name,
                "onUpdate:modelValue": ($event) => $setup.newGroupForm.name = $event,
                placeholder: "\u0420\u0430\u0437\u043C\u0435\u0440\u044B",
                class: "w-full"
              }, null, 8, ["modelValue", "onUpdate:modelValue"])
            ]),
            createVNode("div", null, [
              createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435 "),
              createVNode($setup["VTextarea"], {
                modelValue: $setup.newGroupForm.description,
                "onUpdate:modelValue": ($event) => $setup.newGroupForm.description = $event,
                placeholder: "\u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435 \u0433\u0440\u0443\u043F\u043F\u044B...",
                rows: "2",
                class: "w-full"
              }, null, 8, ["modelValue", "onUpdate:modelValue"])
            ])
          ])
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(ssrRenderComponent($setup["VDialog"], {
    visible: $setup.showSynonymsDialog,
    "onUpdate:visible": ($event) => $setup.showSynonymsDialog = $event,
    modal: "",
    header: "\u0421\u0438\u043D\u043E\u043D\u0438\u043C\u044B \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u044F",
    style: { width: "80rem" },
    breakpoints: { "1199px": "90vw", "575px": "98vw" }
  }, {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        if ($setup.form.id && $setup.form.dataType === "STRING") {
          _push2(ssrRenderComponent($setup["AttributeSynonymManager"], {
            template: { id: $setup.form.id, dataType: $setup.form.dataType, title: $setup.form.title, name: $setup.form.name }
          }, null, _parent2, _scopeId));
        } else {
          _push2(`<div class="p-4 text-surface-500"${_scopeId}>\u0421\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u0435 \u0448\u0430\u0431\u043B\u043E\u043D, \u0447\u0442\u043E\u0431\u044B \u0443\u043F\u0440\u0430\u0432\u043B\u044F\u0442\u044C \u0441\u0438\u043D\u043E\u043D\u0438\u043C\u0430\u043C\u0438.</div>`);
        }
      } else {
        return [
          $setup.form.id && $setup.form.dataType === "STRING" ? (openBlock(), createBlock($setup["AttributeSynonymManager"], {
            key: 0,
            template: { id: $setup.form.id, dataType: $setup.form.dataType, title: $setup.form.title, name: $setup.form.name }
          }, null, 8, ["template"])) : (openBlock(), createBlock("div", {
            key: 1,
            class: "p-4 text-surface-500"
          }, "\u0421\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u0435 \u0448\u0430\u0431\u043B\u043E\u043D, \u0447\u0442\u043E\u0431\u044B \u0443\u043F\u0440\u0430\u0432\u043B\u044F\u0442\u044C \u0441\u0438\u043D\u043E\u043D\u0438\u043C\u0430\u043C\u0438."))
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</div>`);
}
const _sfc_setup$2 = _sfc_main$2.setup;
_sfc_main$2.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/TemplateForm.vue");
  return _sfc_setup$2 ? _sfc_setup$2(props, ctx) : void 0;
};
const TemplateForm = /* @__PURE__ */ _export_sfc(_sfc_main$2, [["ssrRender", _sfc_ssrRender$2]]);

const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "AttributeTemplateManager",
  setup(__props, { expose: __expose }) {
    __expose();
    const { attributeTemplates, loading } = useTrpc();
    const templates = ref([]);
    const groups = ref([]);
    const hierarchyGroups = ref([]);
    const totalCount = ref(0);
    const pageSize = ref(25);
    const currentPage = ref(0);
    const tableLoading = ref(false);
    let lastRequestId = 0;
    const searchQuery = ref("");
    const selectedGroup = ref(null);
    const selectedDataType = ref(null);
    const viewMode = ref("table");
    const urlSync = useUrlParams(
      {
        search: "",
        groupId: void 0,
        dataType: void 0
      },
      {
        prefix: "attr_",
        numberParams: ["groupId"],
        debounceMs: 300
      }
    );
    watch(searchQuery, (val) => {
      urlSync.updateFilter("search", val || void 0);
    });
    watch(selectedGroup, (val) => {
      const gid = val && typeof val === "object" ? val.id : val;
      urlSync.updateFilter("groupId", gid ?? void 0);
    });
    watch(selectedDataType, (val) => {
      const dt = val && typeof val === "object" ? val.value : val;
      urlSync.updateFilter("dataType", dt ?? void 0);
    });
    watch(urlSync.filters, (f) => {
      const nextSearch = f.search || "";
      const nextGroupId = f.groupId ?? null;
      const nextDataType = f.dataType ?? null;
      if (searchQuery.value !== nextSearch) searchQuery.value = nextSearch;
      if (selectedGroup.value !== nextGroupId) selectedGroup.value = nextGroupId;
      if (selectedDataType.value !== nextDataType) selectedDataType.value = nextDataType;
      currentPage.value = 0;
      loadTemplates();
    });
    const showCreateDialog = ref(false);
    const editingTemplate = ref(null);
    const templateForm = ref({});
    const saving = ref(false);
    const showSynonymsDialog = ref(false);
    const selectedTemplateForSynonyms = ref(null);
    const showGroupDialog = ref(false);
    const editingGroup = ref(null);
    const groupForm = ref({ name: "", description: "", parentId: null });
    const groupErrors = ref({});
    const savingGroup = ref(false);
    const selectedTreeGroup = ref(null);
    const expandedKeys = ref({});
    const showGroupTemplates = ref(false);
    const groupTemplates = ref([]);
    const loadingGroupTemplates = ref(false);
    const dataTypeOptions = [
      { label: "\u0421\u0442\u0440\u043E\u043A\u0430", value: "STRING" },
      { label: "\u0427\u0438\u0441\u043B\u043E", value: "NUMBER" },
      { label: "\u041B\u043E\u0433\u0438\u0447\u0435\u0441\u043A\u043E\u0435", value: "BOOLEAN" },
      { label: "\u0414\u0430\u0442\u0430", value: "DATE" },
      { label: "JSON", value: "JSON" }
    ];
    const viewModeOptions = [
      { label: "\u0422\u0430\u0431\u043B\u0438\u0446\u0430", value: "table" },
      { label: "\u041A\u0430\u0440\u0442\u043E\u0447\u043A\u0438", value: "cards" }
    ];
    const groupSuggestions = ref([]);
    const dataTypeSuggestions = ref([]);
    const viewModeSuggestions = ref([]);
    const filterGroups = (event) => {
      const query = event.query?.toLowerCase() || "";
      const noGroupOption = { id: null, name: "\u0411\u0435\u0437 \u0433\u0440\u0443\u043F\u043F\u044B", isSpecial: true };
      if (!query.trim()) {
        groupSuggestions.value = [noGroupOption, ...groups.value];
      } else {
        const filteredGroups = groups.value.filter((group) => group.name.toLowerCase().includes(query));
        const suggestions = query.includes("\u0431\u0435\u0437") || query.includes("\u0433\u0440\u0443\u043F\u043F\u044B") || query.includes("\u0433\u0440\u0443\u043F\u043F\u0430") ? [noGroupOption, ...filteredGroups] : filteredGroups;
        groupSuggestions.value = suggestions;
      }
    };
    const filterDataTypes = (event) => {
      const query = event.query?.toLowerCase() || "";
      if (!query.trim()) {
        dataTypeSuggestions.value = [...dataTypeOptions];
      } else {
        dataTypeSuggestions.value = dataTypeOptions.filter((option) => option.label.toLowerCase().includes(query));
      }
    };
    const filterViewModes = (event) => {
      const query = event.query?.toLowerCase() || "";
      if (!query.trim()) {
        viewModeSuggestions.value = [...viewModeOptions];
      } else {
        viewModeSuggestions.value = viewModeOptions.filter((option) => option.label.toLowerCase().includes(query));
      }
    };
    const usedTemplatesCount = computed(() => {
      return templates.value.filter((template) => getTotalUsage(template._count) > 0).length;
    });
    const unusedTemplatesCount = computed(() => {
      return templates.value.filter((template) => getTotalUsage(template._count) === 0).length;
    });
    const idMap = computed(() => {
      const m = /* @__PURE__ */ new Map();
      for (const g of groups.value) m.set(g.id, g);
      return m;
    });
    const treeNodes = computed(() => {
      const map = /* @__PURE__ */ new Map();
      const roots = [];
      const noGroupNode = {
        key: "no-group",
        label: "\u0411\u0435\u0437 \u0433\u0440\u0443\u043F\u043F\u044B",
        data: { id: null, name: "\u0411\u0435\u0437 \u0433\u0440\u0443\u043F\u043F\u044B", isSpecial: true },
        children: []
      };
      roots.push(noGroupNode);
      for (const g of groups.value) map.set(g.id, { key: String(g.id), label: g.name, data: g, children: [] });
      for (const g of groups.value) {
        const node = map.get(g.id);
        if (g.parentId && map.has(g.parentId)) map.get(g.parentId).children.push(node);
        else roots.push(node);
      }
      return roots;
    });
    const parentSelectOptions = computed(() => [
      { id: null, name: "\u041D\u0435\u0442 \u0440\u043E\u0434\u0438\u0442\u0435\u043B\u044F" },
      ...groups.value.filter((g) => !editingGroup.value || g.id !== editingGroup.value.id)
    ]);
    const getDataTypeLabel = (dataType) => {
      const option = dataTypeOptions.find((opt) => opt.value === dataType);
      return option?.label || dataType;
    };
    const getUnitLabel = (unit) => {
      const labels = {
        "MM": "\u043C\u043C",
        "INCH": "\u0434\u044E\u0439\u043C\u044B",
        "FT": "\u0444\u0443\u0442\u044B",
        "G": "\u0433",
        "KG": "\u043A\u0433",
        "T": "\u0442",
        "LB": "\u0444\u0443\u043D\u0442\u044B",
        "ML": "\u043C\u043B",
        "L": "\u043B",
        "GAL": "\u0433\u0430\u043B\u043B\u043E\u043D\u044B",
        "PCS": "\u0448\u0442",
        "SET": "\u043A\u043E\u043C\u043F\u043B\u0435\u043A\u0442",
        "PAIR": "\u043F\u0430\u0440\u0430",
        "BAR": "\u0431\u0430\u0440",
        "PSI": "PSI",
        "KW": "\u043A\u0412\u0442",
        "HP": "\u043B.\u0441.",
        "NM": "\u041D\u22C5\u043C",
        "RPM": "\u043E\u0431/\u043C\u0438\u043D",
        "C": "\xB0C",
        "F": "\xB0F",
        "PERCENT": "%"
      };
      return labels[unit] || unit;
    };
    const getTotalUsage = (count) => {
      if (!count) return 0;
      return (count.partAttributes || 0) + (count.catalogItemAttributes || 0) + (count.equipmentAttributes || 0);
    };
    const getUsageDetails = (count) => {
      if (!count) return "";
      const parts = count.partAttributes || 0;
      const items = count.catalogItemAttributes || 0;
      const equipment = count.equipmentAttributes || 0;
      const details = [];
      if (parts > 0) details.push(`${parts} \u0437\u0430\u043F.`);
      if (items > 0) details.push(`${items} \u043A\u0430\u0442.`);
      if (equipment > 0) details.push(`${equipment} \u0442\u0435\u0445.`);
      return details.join(", ");
    };
    const loadTemplates = async () => {
      const current = ++lastRequestId;
      tableLoading.value = true;
      try {
        let groupId = void 0;
        if (selectedGroup.value) {
          if (typeof selectedGroup.value === "object") {
            groupId = selectedGroup.value.id;
          } else {
            groupId = selectedGroup.value;
          }
        }
        const dataType = selectedDataType.value && typeof selectedDataType.value === "object" ? selectedDataType.value.value : void 0;
        const result = await attributeTemplates.findMany({
          groupId,
          search: searchQuery.value || void 0,
          dataType,
          limit: pageSize.value,
          offset: currentPage.value * pageSize.value
        });
        if (current === lastRequestId && result && typeof result === "object") {
          templates.value = result.templates || [];
          totalCount.value = result.total || 0;
        }
      } catch (error) {
        console.error("\u041E\u0448\u0438\u0431\u043A\u0430 \u0437\u0430\u0433\u0440\u0443\u0437\u043A\u0438 \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u0432:", error);
        console.error("\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0437\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u044C \u0448\u0430\u0431\u043B\u043E\u043D\u044B");
      } finally {
        if (current === lastRequestId) {
          tableLoading.value = false;
        }
      }
    };
    const loadGroups = async () => {
      try {
        const [flatResult, hierarchyResult] = await Promise.all([
          attributeTemplates.findAllGroups(),
          attributeTemplates.findGroupsHierarchy()
        ]);
        if (flatResult && Array.isArray(flatResult)) {
          groups.value = flatResult;
          const noGroupOption = { id: null, name: "\u0411\u0435\u0437 \u0433\u0440\u0443\u043F\u043F\u044B", isSpecial: true };
          groupSuggestions.value = [noGroupOption, ...flatResult];
        }
        if (hierarchyResult && Array.isArray(hierarchyResult)) {
          hierarchyGroups.value = hierarchyResult;
        }
      } catch (error) {
        console.error("\u041E\u0448\u0438\u0431\u043A\u0430 \u0437\u0430\u0433\u0440\u0443\u0437\u043A\u0438 \u0433\u0440\u0443\u043F\u043F:", error);
      }
    };
    let searchTimeout;
    const debouncedSearch = () => {
      clearTimeout(searchTimeout);
      searchTimeout = setTimeout(() => {
        currentPage.value = 0;
        urlSync.updateFilter("search", searchQuery.value || "");
        loadTemplates();
      }, 500);
    };
    const refreshData = async () => {
      await loadGroups();
      await loadTemplates();
    };
    const onPageChange = (event) => {
      currentPage.value = event.page;
      pageSize.value = event.rows;
      loadTemplates();
    };
    const editTemplate = (template) => {
      editingTemplate.value = template;
      templateForm.value = { ...template };
      showCreateDialog.value = true;
    };
    const openSynonyms = (template) => {
      selectedTemplateForSynonyms.value = template;
      showSynonymsDialog.value = true;
    };
    const createNewTemplate = () => {
      editingTemplate.value = null;
      templateForm.value = {
        dataType: "STRING",
        isRequired: false,
        allowedValues: []
      };
      showCreateDialog.value = true;
    };
    const deleteTemplate = async (template) => {
      const confirmed = window.confirm(`\u0412\u044B \u0443\u0432\u0435\u0440\u0435\u043D\u044B, \u0447\u0442\u043E \u0445\u043E\u0442\u0438\u0442\u0435 \u0443\u0434\u0430\u043B\u0438\u0442\u044C \u0448\u0430\u0431\u043B\u043E\u043D "${template.title}"?`);
      if (confirmed) {
        try {
          await attributeTemplates.delete({ id: template.id });
          console.log("\u0428\u0430\u0431\u043B\u043E\u043D \u0443\u0441\u043F\u0435\u0448\u043D\u043E \u0443\u0434\u0430\u043B\u0435\u043D");
          loadTemplates();
        } catch (error) {
          console.error("\u041E\u0448\u0438\u0431\u043A\u0430 \u0443\u0434\u0430\u043B\u0435\u043D\u0438\u044F \u0448\u0430\u0431\u043B\u043E\u043D\u0430:", error);
          alert(error.message || "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0443\u0434\u0430\u043B\u0438\u0442\u044C \u0448\u0430\u0431\u043B\u043E\u043D");
        }
      }
    };
    const saveTemplate = async (formData) => {
      try {
        saving.value = true;
        if (editingTemplate.value) {
          await attributeTemplates.update({ id: editingTemplate.value.id, ...formData });
          console.log("\u0428\u0430\u0431\u043B\u043E\u043D \u0443\u0441\u043F\u0435\u0448\u043D\u043E \u043E\u0431\u043D\u043E\u0432\u043B\u0435\u043D");
        } else {
          await attributeTemplates.create(formData);
          console.log("\u0428\u0430\u0431\u043B\u043E\u043D \u0443\u0441\u043F\u0435\u0448\u043D\u043E \u0441\u043E\u0437\u0434\u0430\u043D");
        }
        showCreateDialog.value = false;
        editingTemplate.value = null;
        templateForm.value = {};
        await loadGroups();
        await loadTemplates();
      } catch (error) {
        console.error("\u041E\u0448\u0438\u0431\u043A\u0430 \u0441\u043E\u0445\u0440\u0430\u043D\u0435\u043D\u0438\u044F \u0448\u0430\u0431\u043B\u043E\u043D\u0430:", error);
        alert(error.message || "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0441\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C \u0448\u0430\u0431\u043B\u043E\u043D");
      } finally {
        saving.value = false;
      }
    };
    onMounted(async () => {
      dataTypeSuggestions.value = [...dataTypeOptions];
      viewModeSuggestions.value = [...viewModeOptions];
      const f = urlSync.filters.value;
      searchQuery.value = f.search || "";
      selectedGroup.value = f.groupId ?? null;
      selectedDataType.value = f.dataType ?? null;
      await loadGroups();
      await loadTemplates();
      const first = groups.value.find((g) => !g.parentId);
      if (first) {
        selectedTreeGroup.value = first;
        updateExpandedForSelection();
      }
    });
    const selectTreeGroup = (group) => {
      selectedTreeGroup.value = group;
      updateExpandedForSelection();
      showGroupTemplates.value = false;
      groupTemplates.value = [];
    };
    const isGroupExpanded = (group) => {
      return expandedKeys.value[String(group.id)] === true;
    };
    const toggleGroupExpansion = (group) => {
      const key = String(group.id);
      const newKeys = { ...expandedKeys.value };
      if (newKeys[key]) {
        delete newKeys[key];
      } else {
        newKeys[key] = true;
      }
      expandedKeys.value = newKeys;
    };
    const updateExpandedForSelection = () => {
      const keys = {};
      let current = selectedTreeGroup.value ? idMap.value.get(selectedTreeGroup.value.id) : null;
      while (current && current.parentId) {
        keys[String(current.parentId)] = true;
        current = idMap.value.get(current.parentId);
      }
      expandedKeys.value = keys;
    };
    const refreshGroupData = async () => {
      await loadGroups();
    };
    const editGroup = (group) => {
      editingGroup.value = group;
      groupForm.value = {
        name: group.name,
        description: group.description || "",
        parentId: group.parentId ?? null
      };
      showGroupDialog.value = true;
    };
    const deleteGroup = async (group) => {
      if (!confirm(`\u0423\u0434\u0430\u043B\u0438\u0442\u044C \u0433\u0440\u0443\u043F\u043F\u0443 "${group.name}"?`)) return;
      try {
        await attributeTemplates.deleteGroup({ id: group.id });
        await loadGroups();
        if (selectedTreeGroup.value?.id === group.id) {
          selectedTreeGroup.value = null;
        }
      } catch (error) {
        console.error("\u041E\u0448\u0438\u0431\u043A\u0430 \u0443\u0434\u0430\u043B\u0435\u043D\u0438\u044F \u0433\u0440\u0443\u043F\u043F\u044B:", error);
      }
    };
    const saveGroup = async () => {
      if (!groupForm.value.name.trim()) {
        groupErrors.value.name = "\u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435 \u043E\u0431\u044F\u0437\u0430\u0442\u0435\u043B\u044C\u043D\u043E";
        return;
      }
      savingGroup.value = true;
      groupErrors.value = {};
      try {
        if (editingGroup.value) {
          await attributeTemplates.updateGroup({
            id: editingGroup.value.id,
            name: groupForm.value.name.trim(),
            description: groupForm.value.description || void 0,
            parentId: groupForm.value.parentId
          });
        } else {
          await attributeTemplates.createGroup({
            name: groupForm.value.name.trim(),
            description: groupForm.value.description || void 0,
            parentId: groupForm.value.parentId
          });
        }
        closeGroupDialog();
        await loadGroups();
      } catch (error) {
        if (error.message?.includes("\u0443\u0436\u0435 \u0441\u0443\u0449\u0435\u0441\u0442\u0432\u0443\u0435\u0442")) {
          groupErrors.value.name = "\u0413\u0440\u0443\u043F\u043F\u0430 \u0441 \u0442\u0430\u043A\u0438\u043C \u0438\u043C\u0435\u043D\u0435\u043C \u0443\u0436\u0435 \u0441\u0443\u0449\u0435\u0441\u0442\u0432\u0443\u0435\u0442";
        } else {
          console.error("\u041E\u0448\u0438\u0431\u043A\u0430 \u0441\u043E\u0445\u0440\u0430\u043D\u0435\u043D\u0438\u044F \u0433\u0440\u0443\u043F\u043F\u044B:", error);
        }
      } finally {
        savingGroup.value = false;
      }
    };
    const onGroupCreated = async () => {
      await loadGroups();
    };
    const closeGroupDialog = () => {
      showGroupDialog.value = false;
      editingGroup.value = null;
      groupForm.value = { name: "", description: "", parentId: null };
      groupErrors.value = {};
    };
    const toggleGroupTemplates = async () => {
      if (!selectedTreeGroup.value) return;
      showGroupTemplates.value = !showGroupTemplates.value;
      if (showGroupTemplates.value && groupTemplates.value.length === 0) {
        loadingGroupTemplates.value = true;
        try {
          const result = await attributeTemplates.findMany({ groupId: selectedTreeGroup.value.id, limit: 100 });
          groupTemplates.value = result?.templates || [];
        } catch (error) {
          console.error("\u041E\u0448\u0438\u0431\u043A\u0430 \u0437\u0430\u0433\u0440\u0443\u0437\u043A\u0438 \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u0432 \u0433\u0440\u0443\u043F\u043F\u044B:", error);
        } finally {
          loadingGroupTemplates.value = false;
        }
      }
    };
    const __returned__ = { attributeTemplates, loading, templates, groups, hierarchyGroups, totalCount, pageSize, currentPage, tableLoading, get lastRequestId() {
      return lastRequestId;
    }, set lastRequestId(v) {
      lastRequestId = v;
    }, searchQuery, selectedGroup, selectedDataType, viewMode, urlSync, showCreateDialog, editingTemplate, templateForm, saving, showSynonymsDialog, selectedTemplateForSynonyms, showGroupDialog, editingGroup, groupForm, groupErrors, savingGroup, selectedTreeGroup, expandedKeys, showGroupTemplates, groupTemplates, loadingGroupTemplates, dataTypeOptions, viewModeOptions, groupSuggestions, dataTypeSuggestions, viewModeSuggestions, filterGroups, filterDataTypes, filterViewModes, usedTemplatesCount, unusedTemplatesCount, idMap, treeNodes, parentSelectOptions, getDataTypeLabel, getUnitLabel, getTotalUsage, getUsageDetails, loadTemplates, loadGroups, get searchTimeout() {
      return searchTimeout;
    }, set searchTimeout(v) {
      searchTimeout = v;
    }, debouncedSearch, refreshData, onPageChange, editTemplate, openSynonyms, createNewTemplate, deleteTemplate, saveTemplate, selectTreeGroup, isGroupExpanded, toggleGroupExpansion, updateExpandedForSelection, refreshGroupData, editGroup, deleteGroup, saveGroup, onGroupCreated, closeGroupDialog, toggleGroupTemplates, VCard, VButton, VInputText: InputText, VTextarea, VSelect: Select, VDataTable: DataTable, VTag: Tag, VDialog: Dialog, VTree, get Column() {
      return script$7;
    }, get Paginator() {
      return script$8;
    }, TemplateForm, VAutoComplete, AttributeSynonymManager, Icon, get TagsIcon() {
      return TagsIcon;
    }, get PencilIcon() {
      return PencilIcon;
    }, get TrashIcon() {
      return TrashIcon;
    }, get RefreshCwIcon() {
      return RefreshCwIcon;
    }, get TagIcon() {
      return TagIcon;
    }, get PencilLineIcon() {
      return PencilLineIcon;
    }, get LoaderCircleIcon() {
      return LoaderCircleIcon;
    }, get FolderIcon() {
      return FolderIcon;
    }, get ChevronDownIcon() {
      return ChevronDownIcon$1;
    }, get ChevronRightIcon() {
      return ChevronRightIcon$1;
    }, DangerButton };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$1(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "attribute-template-manager" }, _attrs))}>`);
  if ($setup.loading) {
    _push(`<div class="py-12 text-center">`);
    _push(ssrRenderComponent($setup["Icon"], {
      name: "pi pi-spinner pi-spin",
      class: "text-primary mb-4 inline-block text-4xl"
    }, null, _parent));
    _push(`<p class="text-surface-600 dark:text-surface-400">\u0417\u0430\u0433\u0440\u0443\u0437\u043A\u0430 \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u0432 \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432...</p></div>`);
  } else {
    _push(`<div><div class="mb-6 flex items-center justify-between"><div><h2 class="text-surface-900 dark:text-surface-0 text-xl font-semibold">\u0423\u043F\u0440\u0430\u0432\u043B\u0435\u043D\u0438\u0435 \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u0430\u043C\u0438</h2><p class="text-surface-600 dark:text-surface-400 mt-1 text-sm">\u0413\u0440\u0443\u043F\u043F\u044B \u0438 \u0448\u0430\u0431\u043B\u043E\u043D\u044B \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432 \u0434\u043B\u044F \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439, \u043A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u044B\u0445 \u043F\u043E\u0437\u0438\u0446\u0438\u0439 \u0438 \u0442\u0435\u0445\u043D\u0438\u043A\u0438</p></div><div class="flex gap-3">`);
    _push(ssrRenderComponent($setup["VButton"], {
      onClick: $setup.refreshData,
      disabled: $setup.loading,
      severity: "secondary",
      outlined: "",
      label: "\u041E\u0431\u043D\u043E\u0432\u0438\u0442\u044C"
    }, null, _parent));
    _push(ssrRenderComponent($setup["VButton"], {
      onClick: ($event) => $setup.showGroupDialog = true,
      severity: "secondary",
      outlined: "",
      label: "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u0433\u0440\u0443\u043F\u043F\u0443"
    }, null, _parent));
    _push(ssrRenderComponent($setup["VButton"], {
      onClick: $setup.createNewTemplate,
      label: "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u0448\u0430\u0431\u043B\u043E\u043D"
    }, null, _parent));
    _push(`</div></div><div class="mb-6 grid grid-cols-1 gap-4 lg:grid-cols-3">`);
    _push(ssrRenderComponent($setup["VCard"], null, {
      content: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`<div class="p-4 space-y-3"${_scopeId}><div class="flex items-center justify-between"${_scopeId}><h3 class="text-lg font-semibold text-surface-900 dark:text-surface-0"${_scopeId}>\u0414\u0435\u0440\u0435\u0432\u043E \u0433\u0440\u0443\u043F\u043F</h3>`);
          _push2(ssrRenderComponent($setup["VButton"], {
            size: "small",
            severity: "secondary",
            outlined: "",
            onClick: $setup.refreshGroupData
          }, {
            default: withCtx((_2, _push3, _parent3, _scopeId2) => {
              if (_push3) {
                _push3(ssrRenderComponent($setup["RefreshCwIcon"], { class: "w-4 h-4" }, null, _parent3, _scopeId2));
              } else {
                return [
                  createVNode($setup["RefreshCwIcon"], { class: "w-4 h-4" })
                ];
              }
            }),
            _: 1
          }, _parent2, _scopeId));
          _push2(`</div>`);
          _push2(ssrRenderComponent($setup["VTree"], {
            value: $setup.treeNodes,
            expandedKeys: $setup.expandedKeys,
            "onUpdate:expandedKeys": ($event) => $setup.expandedKeys = $event,
            filter: "",
            filterPlaceholder: "\u041F\u043E\u0438\u0441\u043A \u0433\u0440\u0443\u043F\u043F..."
          }, {
            default: withCtx(({ node }, _push3, _parent3, _scopeId2) => {
              if (_push3) {
                _push3(`<div class="flex items-center justify-between w-full"${_scopeId2}><div class="${ssrRenderClass([{ "bg-primary text-primary-contrast": $setup.selectedTreeGroup?.id === node.data.id, "hover:bg-surface-100 dark:hover:bg-surface-800": $setup.selectedTreeGroup?.id !== node.data.id }, "flex items-center gap-2 flex-1 cursor-pointer rounded px-2 py-1 transition-colors"])}"${_scopeId2}><span class="font-medium"${_scopeId2}>${ssrInterpolate(node.data.name)}</span>`);
                _push3(ssrRenderComponent($setup["VTag"], {
                  value: node.data._count?.templates || 0,
                  severity: "secondary",
                  size: "small"
                }, null, _parent3, _scopeId2));
                _push3(`</div><div class="flex gap-1"${_scopeId2}>`);
                _push3(ssrRenderComponent($setup["VButton"], {
                  size: "small",
                  severity: "secondary",
                  outlined: "",
                  onClick: ($event) => $setup.toggleGroupExpansion(node.data),
                  title: $setup.isGroupExpanded(node.data) ? "\u0421\u0432\u0435\u0440\u043D\u0443\u0442\u044C \u0433\u0440\u0443\u043F\u043F\u0443" : "\u0420\u0430\u0437\u0432\u0435\u0440\u043D\u0443\u0442\u044C \u0433\u0440\u0443\u043F\u043F\u0443"
                }, {
                  default: withCtx((_2, _push4, _parent4, _scopeId3) => {
                    if (_push4) {
                      if ($setup.isGroupExpanded(node.data)) {
                        _push4(ssrRenderComponent($setup["ChevronDownIcon"], { class: "w-3 h-3" }, null, _parent4, _scopeId3));
                      } else {
                        _push4(ssrRenderComponent($setup["ChevronRightIcon"], { class: "w-3 h-3" }, null, _parent4, _scopeId3));
                      }
                    } else {
                      return [
                        $setup.isGroupExpanded(node.data) ? (openBlock(), createBlock($setup["ChevronDownIcon"], {
                          key: 0,
                          class: "w-3 h-3"
                        })) : (openBlock(), createBlock($setup["ChevronRightIcon"], {
                          key: 1,
                          class: "w-3 h-3"
                        }))
                      ];
                    }
                  }),
                  _: 2
                }, _parent3, _scopeId2));
                _push3(ssrRenderComponent($setup["VButton"], {
                  size: "small",
                  severity: "secondary",
                  outlined: "",
                  onClick: ($event) => $setup.editGroup(node.data)
                }, {
                  default: withCtx((_2, _push4, _parent4, _scopeId3) => {
                    if (_push4) {
                      _push4(ssrRenderComponent($setup["PencilIcon"], { class: "w-3 h-3" }, null, _parent4, _scopeId3));
                    } else {
                      return [
                        createVNode($setup["PencilIcon"], { class: "w-3 h-3" })
                      ];
                    }
                  }),
                  _: 2
                }, _parent3, _scopeId2));
                _push3(ssrRenderComponent($setup["VButton"], {
                  size: "small",
                  severity: "danger",
                  outlined: "",
                  onClick: ($event) => $setup.deleteGroup(node.data),
                  disabled: (node.data._count?.templates || 0) > 0 || (node.data._count?.children || 0) > 0
                }, {
                  default: withCtx((_2, _push4, _parent4, _scopeId3) => {
                    if (_push4) {
                      _push4(ssrRenderComponent($setup["TrashIcon"], { class: "w-3 h-3" }, null, _parent4, _scopeId3));
                    } else {
                      return [
                        createVNode($setup["TrashIcon"], { class: "w-3 h-3" })
                      ];
                    }
                  }),
                  _: 2
                }, _parent3, _scopeId2));
                _push3(`</div></div>`);
              } else {
                return [
                  createVNode("div", {
                    class: "flex items-center justify-between w-full",
                    onClick: withModifiers(() => {
                    }, ["stop"])
                  }, [
                    createVNode("div", {
                      class: ["flex items-center gap-2 flex-1 cursor-pointer rounded px-2 py-1 transition-colors", { "bg-primary text-primary-contrast": $setup.selectedTreeGroup?.id === node.data.id, "hover:bg-surface-100 dark:hover:bg-surface-800": $setup.selectedTreeGroup?.id !== node.data.id }],
                      onClick: ($event) => $setup.selectTreeGroup(node.data)
                    }, [
                      createVNode("span", { class: "font-medium" }, toDisplayString(node.data.name), 1),
                      createVNode($setup["VTag"], {
                        value: node.data._count?.templates || 0,
                        severity: "secondary",
                        size: "small"
                      }, null, 8, ["value"])
                    ], 10, ["onClick"]),
                    createVNode("div", { class: "flex gap-1" }, [
                      createVNode($setup["VButton"], {
                        size: "small",
                        severity: "secondary",
                        outlined: "",
                        onClick: withModifiers(($event) => $setup.toggleGroupExpansion(node.data), ["stop"]),
                        title: $setup.isGroupExpanded(node.data) ? "\u0421\u0432\u0435\u0440\u043D\u0443\u0442\u044C \u0433\u0440\u0443\u043F\u043F\u0443" : "\u0420\u0430\u0437\u0432\u0435\u0440\u043D\u0443\u0442\u044C \u0433\u0440\u0443\u043F\u043F\u0443"
                      }, {
                        default: withCtx(() => [
                          $setup.isGroupExpanded(node.data) ? (openBlock(), createBlock($setup["ChevronDownIcon"], {
                            key: 0,
                            class: "w-3 h-3"
                          })) : (openBlock(), createBlock($setup["ChevronRightIcon"], {
                            key: 1,
                            class: "w-3 h-3"
                          }))
                        ]),
                        _: 2
                      }, 1032, ["onClick", "title"]),
                      createVNode($setup["VButton"], {
                        size: "small",
                        severity: "secondary",
                        outlined: "",
                        onClick: withModifiers(($event) => $setup.editGroup(node.data), ["stop"])
                      }, {
                        default: withCtx(() => [
                          createVNode($setup["PencilIcon"], { class: "w-3 h-3" })
                        ]),
                        _: 2
                      }, 1032, ["onClick"]),
                      createVNode($setup["VButton"], {
                        size: "small",
                        severity: "danger",
                        outlined: "",
                        onClick: withModifiers(($event) => $setup.deleteGroup(node.data), ["stop"]),
                        disabled: (node.data._count?.templates || 0) > 0 || (node.data._count?.children || 0) > 0
                      }, {
                        default: withCtx(() => [
                          createVNode($setup["TrashIcon"], { class: "w-3 h-3" })
                        ]),
                        _: 2
                      }, 1032, ["onClick", "disabled"])
                    ])
                  ], 8, ["onClick"])
                ];
              }
            }),
            _: 1
          }, _parent2, _scopeId));
          _push2(`</div>`);
        } else {
          return [
            createVNode("div", { class: "p-4 space-y-3" }, [
              createVNode("div", { class: "flex items-center justify-between" }, [
                createVNode("h3", { class: "text-lg font-semibold text-surface-900 dark:text-surface-0" }, "\u0414\u0435\u0440\u0435\u0432\u043E \u0433\u0440\u0443\u043F\u043F"),
                createVNode($setup["VButton"], {
                  size: "small",
                  severity: "secondary",
                  outlined: "",
                  onClick: $setup.refreshGroupData
                }, {
                  default: withCtx(() => [
                    createVNode($setup["RefreshCwIcon"], { class: "w-4 h-4" })
                  ]),
                  _: 1
                })
              ]),
              createVNode($setup["VTree"], {
                value: $setup.treeNodes,
                expandedKeys: $setup.expandedKeys,
                "onUpdate:expandedKeys": ($event) => $setup.expandedKeys = $event,
                filter: "",
                filterPlaceholder: "\u041F\u043E\u0438\u0441\u043A \u0433\u0440\u0443\u043F\u043F..."
              }, {
                default: withCtx(({ node }) => [
                  createVNode("div", {
                    class: "flex items-center justify-between w-full",
                    onClick: withModifiers(() => {
                    }, ["stop"])
                  }, [
                    createVNode("div", {
                      class: ["flex items-center gap-2 flex-1 cursor-pointer rounded px-2 py-1 transition-colors", { "bg-primary text-primary-contrast": $setup.selectedTreeGroup?.id === node.data.id, "hover:bg-surface-100 dark:hover:bg-surface-800": $setup.selectedTreeGroup?.id !== node.data.id }],
                      onClick: ($event) => $setup.selectTreeGroup(node.data)
                    }, [
                      createVNode("span", { class: "font-medium" }, toDisplayString(node.data.name), 1),
                      createVNode($setup["VTag"], {
                        value: node.data._count?.templates || 0,
                        severity: "secondary",
                        size: "small"
                      }, null, 8, ["value"])
                    ], 10, ["onClick"]),
                    createVNode("div", { class: "flex gap-1" }, [
                      createVNode($setup["VButton"], {
                        size: "small",
                        severity: "secondary",
                        outlined: "",
                        onClick: withModifiers(($event) => $setup.toggleGroupExpansion(node.data), ["stop"]),
                        title: $setup.isGroupExpanded(node.data) ? "\u0421\u0432\u0435\u0440\u043D\u0443\u0442\u044C \u0433\u0440\u0443\u043F\u043F\u0443" : "\u0420\u0430\u0437\u0432\u0435\u0440\u043D\u0443\u0442\u044C \u0433\u0440\u0443\u043F\u043F\u0443"
                      }, {
                        default: withCtx(() => [
                          $setup.isGroupExpanded(node.data) ? (openBlock(), createBlock($setup["ChevronDownIcon"], {
                            key: 0,
                            class: "w-3 h-3"
                          })) : (openBlock(), createBlock($setup["ChevronRightIcon"], {
                            key: 1,
                            class: "w-3 h-3"
                          }))
                        ]),
                        _: 2
                      }, 1032, ["onClick", "title"]),
                      createVNode($setup["VButton"], {
                        size: "small",
                        severity: "secondary",
                        outlined: "",
                        onClick: withModifiers(($event) => $setup.editGroup(node.data), ["stop"])
                      }, {
                        default: withCtx(() => [
                          createVNode($setup["PencilIcon"], { class: "w-3 h-3" })
                        ]),
                        _: 2
                      }, 1032, ["onClick"]),
                      createVNode($setup["VButton"], {
                        size: "small",
                        severity: "danger",
                        outlined: "",
                        onClick: withModifiers(($event) => $setup.deleteGroup(node.data), ["stop"]),
                        disabled: (node.data._count?.templates || 0) > 0 || (node.data._count?.children || 0) > 0
                      }, {
                        default: withCtx(() => [
                          createVNode($setup["TrashIcon"], { class: "w-3 h-3" })
                        ]),
                        _: 2
                      }, 1032, ["onClick", "disabled"])
                    ])
                  ], 8, ["onClick"])
                ]),
                _: 1
              }, 8, ["value", "expandedKeys", "onUpdate:expandedKeys"])
            ])
          ];
        }
      }),
      _: 1
    }, _parent));
    _push(ssrRenderComponent($setup["VCard"], { class: "lg:col-span-2" }, {
      content: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`<div class="p-4 space-y-3"${_scopeId}><div class="flex items-center justify-between"${_scopeId}><h3 class="text-lg font-semibold text-surface-900 dark:text-surface-0"${_scopeId}>${ssrInterpolate($setup.selectedTreeGroup ? `\u0413\u0440\u0443\u043F\u043F\u0430: ${$setup.selectedTreeGroup.name}` : "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0433\u0440\u0443\u043F\u043F\u0443")}</h3>`);
          if ($setup.selectedTreeGroup) {
            _push2(`<div class="flex gap-2"${_scopeId}>`);
            _push2(ssrRenderComponent($setup["VButton"], {
              outlined: "",
              onClick: ($event) => $setup.editGroup($setup.selectedTreeGroup)
            }, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(ssrRenderComponent($setup["PencilLineIcon"], { class: "w-4 h-4" }, null, _parent3, _scopeId2));
                  _push3(` \u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C `);
                } else {
                  return [
                    createVNode($setup["PencilLineIcon"], { class: "w-4 h-4" }),
                    createTextVNode(" \u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C ")
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
            _push2(`</div>`);
          } else {
            _push2(`<!---->`);
          }
          _push2(`</div>`);
          if (!$setup.selectedTreeGroup) {
            _push2(`<div class="text-surface-500 text-center py-8"${_scopeId}> \u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0433\u0440\u0443\u043F\u043F\u0443 \u0432 \u0434\u0435\u0440\u0435\u0432\u0435 \u0441\u043B\u0435\u0432\u0430 \u0434\u043B\u044F \u043F\u0440\u043E\u0441\u043C\u043E\u0442\u0440\u0430 \u0434\u0435\u0442\u0430\u043B\u0435\u0439 \u0438 \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u0432 </div>`);
          } else {
            _push2(`<div class="space-y-4"${_scopeId}><div class="flex items-center gap-3"${_scopeId}>`);
            _push2(ssrRenderComponent($setup["FolderIcon"], { class: "text-blue-600 w-5 h-5" }, null, _parent2, _scopeId));
            _push2(`<div class="flex-1"${_scopeId}><div class="font-medium"${_scopeId}>${ssrInterpolate($setup.selectedTreeGroup.name)}</div>`);
            if ($setup.selectedTreeGroup.description) {
              _push2(`<div class="text-sm text-surface-500"${_scopeId}>${ssrInterpolate($setup.selectedTreeGroup.description)}</div>`);
            } else {
              _push2(`<!---->`);
            }
            _push2(`</div>`);
            _push2(ssrRenderComponent($setup["VTag"], {
              value: `${$setup.selectedTreeGroup._count?.templates || 0} \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u0432`,
              severity: "secondary"
            }, null, _parent2, _scopeId));
            if ($setup.selectedTreeGroup._count?.children) {
              _push2(ssrRenderComponent($setup["VTag"], {
                value: `${$setup.selectedTreeGroup._count.children} \u0434\u043E\u0447\u0435\u0440\u043D\u0438\u0445`,
                severity: "info"
              }, null, _parent2, _scopeId));
            } else {
              _push2(`<!---->`);
            }
            _push2(`</div>`);
            if ($setup.selectedTreeGroup._count?.templates > 0) {
              _push2(`<div class="border-t border-surface-200 dark:border-surface-700 pt-4"${_scopeId}><div class="flex items-center justify-between mb-3"${_scopeId}><span class="text-sm font-medium"${_scopeId}>\u0428\u0430\u0431\u043B\u043E\u043D\u044B \u0432 \u0433\u0440\u0443\u043F\u043F\u0435:</span>`);
              _push2(ssrRenderComponent($setup["VButton"], {
                label: $setup.showGroupTemplates ? "\u0421\u043A\u0440\u044B\u0442\u044C" : "\u041F\u043E\u043A\u0430\u0437\u0430\u0442\u044C",
                onClick: $setup.toggleGroupTemplates,
                severity: "secondary",
                text: "",
                size: "small"
              }, null, _parent2, _scopeId));
              _push2(`</div>`);
              if ($setup.showGroupTemplates) {
                _push2(`<div class="space-y-2"${_scopeId}>`);
                if ($setup.groupTemplates.length > 0) {
                  _push2(`<div class="grid gap-2"${_scopeId}><!--[-->`);
                  ssrRenderList($setup.groupTemplates, (template) => {
                    _push2(`<div class="flex items-center justify-between p-3 bg-surface-50 dark:bg-surface-900 rounded-lg"${_scopeId}><div class="flex items-center gap-3"${_scopeId}>`);
                    _push2(ssrRenderComponent($setup["TagIcon"], { class: "text-green-600 w-4 h-4" }, null, _parent2, _scopeId));
                    _push2(`<div${_scopeId}><div class="font-medium text-surface-900 dark:text-surface-0"${_scopeId}>${ssrInterpolate(template.title)}</div><div class="text-sm text-surface-600 dark:text-surface-400"${_scopeId}>${ssrInterpolate(template.name)} \u2022 ${ssrInterpolate($setup.getDataTypeLabel(template.dataType))}</div></div></div><div class="flex gap-1"${_scopeId}>`);
                    _push2(ssrRenderComponent($setup["VTag"], {
                      value: $setup.getDataTypeLabel(template.dataType),
                      severity: "info",
                      size: "small"
                    }, null, _parent2, _scopeId));
                    if (template.unit) {
                      _push2(ssrRenderComponent($setup["VTag"], {
                        value: $setup.getUnitLabel(template.unit),
                        severity: "success",
                        size: "small"
                      }, null, _parent2, _scopeId));
                    } else {
                      _push2(`<!---->`);
                    }
                    _push2(`</div></div>`);
                  });
                  _push2(`<!--]--></div>`);
                } else if ($setup.loadingGroupTemplates) {
                  _push2(`<div class="text-center py-4"${_scopeId}>`);
                  _push2(ssrRenderComponent($setup["LoaderCircleIcon"], { class: "w-4 h-4 animate-spin" }, null, _parent2, _scopeId));
                  _push2(` \u0417\u0430\u0433\u0440\u0443\u0437\u043A\u0430... </div>`);
                } else {
                  _push2(`<div class="text-center py-4 text-surface-500"${_scopeId}> \u041D\u0435\u0442 \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u0432 \u0432 \u0433\u0440\u0443\u043F\u043F\u0435 </div>`);
                }
                _push2(`</div>`);
              } else {
                _push2(`<!---->`);
              }
              _push2(`</div>`);
            } else {
              _push2(`<!---->`);
            }
            _push2(`</div>`);
          }
          _push2(`</div>`);
        } else {
          return [
            createVNode("div", { class: "p-4 space-y-3" }, [
              createVNode("div", { class: "flex items-center justify-between" }, [
                createVNode("h3", { class: "text-lg font-semibold text-surface-900 dark:text-surface-0" }, toDisplayString($setup.selectedTreeGroup ? `\u0413\u0440\u0443\u043F\u043F\u0430: ${$setup.selectedTreeGroup.name}` : "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0433\u0440\u0443\u043F\u043F\u0443"), 1),
                $setup.selectedTreeGroup ? (openBlock(), createBlock("div", {
                  key: 0,
                  class: "flex gap-2"
                }, [
                  createVNode($setup["VButton"], {
                    outlined: "",
                    onClick: ($event) => $setup.editGroup($setup.selectedTreeGroup)
                  }, {
                    default: withCtx(() => [
                      createVNode($setup["PencilLineIcon"], { class: "w-4 h-4" }),
                      createTextVNode(" \u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C ")
                    ]),
                    _: 1
                  }, 8, ["onClick"])
                ])) : createCommentVNode("", true)
              ]),
              !$setup.selectedTreeGroup ? (openBlock(), createBlock("div", {
                key: 0,
                class: "text-surface-500 text-center py-8"
              }, " \u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0433\u0440\u0443\u043F\u043F\u0443 \u0432 \u0434\u0435\u0440\u0435\u0432\u0435 \u0441\u043B\u0435\u0432\u0430 \u0434\u043B\u044F \u043F\u0440\u043E\u0441\u043C\u043E\u0442\u0440\u0430 \u0434\u0435\u0442\u0430\u043B\u0435\u0439 \u0438 \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u0432 ")) : (openBlock(), createBlock("div", {
                key: 1,
                class: "space-y-4"
              }, [
                createVNode("div", { class: "flex items-center gap-3" }, [
                  createVNode($setup["FolderIcon"], { class: "text-blue-600 w-5 h-5" }),
                  createVNode("div", { class: "flex-1" }, [
                    createVNode("div", { class: "font-medium" }, toDisplayString($setup.selectedTreeGroup.name), 1),
                    $setup.selectedTreeGroup.description ? (openBlock(), createBlock("div", {
                      key: 0,
                      class: "text-sm text-surface-500"
                    }, toDisplayString($setup.selectedTreeGroup.description), 1)) : createCommentVNode("", true)
                  ]),
                  createVNode($setup["VTag"], {
                    value: `${$setup.selectedTreeGroup._count?.templates || 0} \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u0432`,
                    severity: "secondary"
                  }, null, 8, ["value"]),
                  $setup.selectedTreeGroup._count?.children ? (openBlock(), createBlock($setup["VTag"], {
                    key: 0,
                    value: `${$setup.selectedTreeGroup._count.children} \u0434\u043E\u0447\u0435\u0440\u043D\u0438\u0445`,
                    severity: "info"
                  }, null, 8, ["value"])) : createCommentVNode("", true)
                ]),
                $setup.selectedTreeGroup._count?.templates > 0 ? (openBlock(), createBlock("div", {
                  key: 0,
                  class: "border-t border-surface-200 dark:border-surface-700 pt-4"
                }, [
                  createVNode("div", { class: "flex items-center justify-between mb-3" }, [
                    createVNode("span", { class: "text-sm font-medium" }, "\u0428\u0430\u0431\u043B\u043E\u043D\u044B \u0432 \u0433\u0440\u0443\u043F\u043F\u0435:"),
                    createVNode($setup["VButton"], {
                      label: $setup.showGroupTemplates ? "\u0421\u043A\u0440\u044B\u0442\u044C" : "\u041F\u043E\u043A\u0430\u0437\u0430\u0442\u044C",
                      onClick: $setup.toggleGroupTemplates,
                      severity: "secondary",
                      text: "",
                      size: "small"
                    }, null, 8, ["label"])
                  ]),
                  $setup.showGroupTemplates ? (openBlock(), createBlock("div", {
                    key: 0,
                    class: "space-y-2"
                  }, [
                    $setup.groupTemplates.length > 0 ? (openBlock(), createBlock("div", {
                      key: 0,
                      class: "grid gap-2"
                    }, [
                      (openBlock(true), createBlock(Fragment, null, renderList($setup.groupTemplates, (template) => {
                        return openBlock(), createBlock("div", {
                          key: template.id,
                          class: "flex items-center justify-between p-3 bg-surface-50 dark:bg-surface-900 rounded-lg"
                        }, [
                          createVNode("div", { class: "flex items-center gap-3" }, [
                            createVNode($setup["TagIcon"], { class: "text-green-600 w-4 h-4" }),
                            createVNode("div", null, [
                              createVNode("div", { class: "font-medium text-surface-900 dark:text-surface-0" }, toDisplayString(template.title), 1),
                              createVNode("div", { class: "text-sm text-surface-600 dark:text-surface-400" }, toDisplayString(template.name) + " \u2022 " + toDisplayString($setup.getDataTypeLabel(template.dataType)), 1)
                            ])
                          ]),
                          createVNode("div", { class: "flex gap-1" }, [
                            createVNode($setup["VTag"], {
                              value: $setup.getDataTypeLabel(template.dataType),
                              severity: "info",
                              size: "small"
                            }, null, 8, ["value"]),
                            template.unit ? (openBlock(), createBlock($setup["VTag"], {
                              key: 0,
                              value: $setup.getUnitLabel(template.unit),
                              severity: "success",
                              size: "small"
                            }, null, 8, ["value"])) : createCommentVNode("", true)
                          ])
                        ]);
                      }), 128))
                    ])) : $setup.loadingGroupTemplates ? (openBlock(), createBlock("div", {
                      key: 1,
                      class: "text-center py-4"
                    }, [
                      createVNode($setup["LoaderCircleIcon"], { class: "w-4 h-4 animate-spin" }),
                      createTextVNode(" \u0417\u0430\u0433\u0440\u0443\u0437\u043A\u0430... ")
                    ])) : (openBlock(), createBlock("div", {
                      key: 2,
                      class: "text-center py-4 text-surface-500"
                    }, " \u041D\u0435\u0442 \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u0432 \u0432 \u0433\u0440\u0443\u043F\u043F\u0435 "))
                  ])) : createCommentVNode("", true)
                ])) : createCommentVNode("", true)
              ]))
            ])
          ];
        }
      }),
      _: 1
    }, _parent));
    _push(`</div>`);
    _push(ssrRenderComponent($setup["VCard"], { class: "mb-6" }, {
      content: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`<div class="p-4"${_scopeId}><div class="grid grid-cols-1 gap-4 md:grid-cols-4"${_scopeId}><div${_scopeId}><label class="text-surface-700 dark:text-surface-300 mb-2 block text-sm font-medium"${_scopeId}> \u041F\u043E\u0438\u0441\u043A </label>`);
          _push2(ssrRenderComponent($setup["VInputText"], {
            modelValue: $setup.searchQuery,
            "onUpdate:modelValue": ($event) => $setup.searchQuery = $event,
            placeholder: "\u041F\u043E\u0438\u0441\u043A \u043F\u043E \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u044E, \u0438\u043C\u0435\u043D\u0438 \u0438\u043B\u0438 \u043E\u043F\u0438\u0441\u0430\u043D\u0438\u044E...",
            class: "w-full",
            onInput: $setup.debouncedSearch
          }, null, _parent2, _scopeId));
          _push2(`</div><div${_scopeId}><label class="text-surface-700 dark:text-surface-300 mb-2 block text-sm font-medium"${_scopeId}> \u0413\u0440\u0443\u043F\u043F\u0430 </label>`);
          _push2(ssrRenderComponent($setup["VAutoComplete"], {
            modelValue: $setup.selectedGroup,
            "onUpdate:modelValue": ($event) => $setup.selectedGroup = $event,
            suggestions: $setup.groupSuggestions,
            onComplete: $setup.filterGroups,
            "option-label": "name",
            "option-value": "id",
            placeholder: "\u0412\u0441\u0435 \u0433\u0440\u0443\u043F\u043F\u044B",
            class: "w-full",
            dropdown: "",
            "show-clear": "",
            onChange: $setup.loadTemplates
          }, null, _parent2, _scopeId));
          _push2(`</div><div${_scopeId}><label class="text-surface-700 dark:text-surface-300 mb-2 block text-sm font-medium"${_scopeId}> \u0422\u0438\u043F \u0434\u0430\u043D\u043D\u044B\u0445 </label>`);
          _push2(ssrRenderComponent($setup["VAutoComplete"], {
            modelValue: $setup.selectedDataType,
            "onUpdate:modelValue": ($event) => $setup.selectedDataType = $event,
            suggestions: $setup.dataTypeSuggestions,
            onComplete: $setup.filterDataTypes,
            "option-label": "label",
            "option-value": "value",
            placeholder: "\u0412\u0441\u0435 \u0442\u0438\u043F\u044B",
            class: "w-full",
            dropdown: "",
            "show-clear": "",
            onChange: $setup.loadTemplates
          }, null, _parent2, _scopeId));
          _push2(`</div></div></div>`);
        } else {
          return [
            createVNode("div", { class: "p-4" }, [
              createVNode("div", { class: "grid grid-cols-1 gap-4 md:grid-cols-4" }, [
                createVNode("div", null, [
                  createVNode("label", { class: "text-surface-700 dark:text-surface-300 mb-2 block text-sm font-medium" }, " \u041F\u043E\u0438\u0441\u043A "),
                  createVNode($setup["VInputText"], {
                    modelValue: $setup.searchQuery,
                    "onUpdate:modelValue": ($event) => $setup.searchQuery = $event,
                    placeholder: "\u041F\u043E\u0438\u0441\u043A \u043F\u043E \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u044E, \u0438\u043C\u0435\u043D\u0438 \u0438\u043B\u0438 \u043E\u043F\u0438\u0441\u0430\u043D\u0438\u044E...",
                    class: "w-full",
                    onInput: $setup.debouncedSearch
                  }, null, 8, ["modelValue", "onUpdate:modelValue"])
                ]),
                createVNode("div", null, [
                  createVNode("label", { class: "text-surface-700 dark:text-surface-300 mb-2 block text-sm font-medium" }, " \u0413\u0440\u0443\u043F\u043F\u0430 "),
                  createVNode($setup["VAutoComplete"], {
                    modelValue: $setup.selectedGroup,
                    "onUpdate:modelValue": ($event) => $setup.selectedGroup = $event,
                    suggestions: $setup.groupSuggestions,
                    onComplete: $setup.filterGroups,
                    "option-label": "name",
                    "option-value": "id",
                    placeholder: "\u0412\u0441\u0435 \u0433\u0440\u0443\u043F\u043F\u044B",
                    class: "w-full",
                    dropdown: "",
                    "show-clear": "",
                    onChange: $setup.loadTemplates
                  }, null, 8, ["modelValue", "onUpdate:modelValue", "suggestions"])
                ]),
                createVNode("div", null, [
                  createVNode("label", { class: "text-surface-700 dark:text-surface-300 mb-2 block text-sm font-medium" }, " \u0422\u0438\u043F \u0434\u0430\u043D\u043D\u044B\u0445 "),
                  createVNode($setup["VAutoComplete"], {
                    modelValue: $setup.selectedDataType,
                    "onUpdate:modelValue": ($event) => $setup.selectedDataType = $event,
                    suggestions: $setup.dataTypeSuggestions,
                    onComplete: $setup.filterDataTypes,
                    "option-label": "label",
                    "option-value": "value",
                    placeholder: "\u0412\u0441\u0435 \u0442\u0438\u043F\u044B",
                    class: "w-full",
                    dropdown: "",
                    "show-clear": "",
                    onChange: $setup.loadTemplates
                  }, null, 8, ["modelValue", "onUpdate:modelValue", "suggestions"])
                ])
              ])
            ])
          ];
        }
      }),
      _: 1
    }, _parent));
    _push(`<div class="mb-6 grid grid-cols-1 gap-4 md:grid-cols-4">`);
    _push(ssrRenderComponent($setup["VCard"], null, {
      content: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`<div class="p-4 text-center"${_scopeId}><div class="text-primary mb-2 text-2xl font-bold"${_scopeId}>${ssrInterpolate($setup.totalCount)}</div><div class="text-surface-600 dark:text-surface-400 text-sm"${_scopeId}>\u0412\u0441\u0435\u0433\u043E \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u0432</div></div>`);
        } else {
          return [
            createVNode("div", { class: "p-4 text-center" }, [
              createVNode("div", { class: "text-primary mb-2 text-2xl font-bold" }, toDisplayString($setup.totalCount), 1),
              createVNode("div", { class: "text-surface-600 dark:text-surface-400 text-sm" }, "\u0412\u0441\u0435\u0433\u043E \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u0432")
            ])
          ];
        }
      }),
      _: 1
    }, _parent));
    _push(ssrRenderComponent($setup["VCard"], null, {
      content: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`<div class="p-4 text-center"${_scopeId}><div class="mb-2 text-2xl font-bold text-green-600"${_scopeId}>${ssrInterpolate($setup.groups.length)}</div><div class="text-surface-600 dark:text-surface-400 text-sm"${_scopeId}>\u0413\u0440\u0443\u043F\u043F</div></div>`);
        } else {
          return [
            createVNode("div", { class: "p-4 text-center" }, [
              createVNode("div", { class: "mb-2 text-2xl font-bold text-green-600" }, toDisplayString($setup.groups.length), 1),
              createVNode("div", { class: "text-surface-600 dark:text-surface-400 text-sm" }, "\u0413\u0440\u0443\u043F\u043F")
            ])
          ];
        }
      }),
      _: 1
    }, _parent));
    _push(ssrRenderComponent($setup["VCard"], null, {
      content: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`<div class="p-4 text-center"${_scopeId}><div class="mb-2 text-2xl font-bold text-blue-600"${_scopeId}>${ssrInterpolate($setup.usedTemplatesCount)}</div><div class="text-surface-600 dark:text-surface-400 text-sm"${_scopeId}>\u0418\u0441\u043F\u043E\u043B\u044C\u0437\u0443\u0435\u0442\u0441\u044F</div></div>`);
        } else {
          return [
            createVNode("div", { class: "p-4 text-center" }, [
              createVNode("div", { class: "mb-2 text-2xl font-bold text-blue-600" }, toDisplayString($setup.usedTemplatesCount), 1),
              createVNode("div", { class: "text-surface-600 dark:text-surface-400 text-sm" }, "\u0418\u0441\u043F\u043E\u043B\u044C\u0437\u0443\u0435\u0442\u0441\u044F")
            ])
          ];
        }
      }),
      _: 1
    }, _parent));
    _push(ssrRenderComponent($setup["VCard"], null, {
      content: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`<div class="p-4 text-center"${_scopeId}><div class="mb-2 text-2xl font-bold text-orange-600"${_scopeId}>${ssrInterpolate($setup.unusedTemplatesCount)}</div><div class="text-surface-600 dark:text-surface-400 text-sm"${_scopeId}>\u041D\u0435 \u0438\u0441\u043F\u043E\u043B\u044C\u0437\u0443\u0435\u0442\u0441\u044F</div></div>`);
        } else {
          return [
            createVNode("div", { class: "p-4 text-center" }, [
              createVNode("div", { class: "mb-2 text-2xl font-bold text-orange-600" }, toDisplayString($setup.unusedTemplatesCount), 1),
              createVNode("div", { class: "text-surface-600 dark:text-surface-400 text-sm" }, "\u041D\u0435 \u0438\u0441\u043F\u043E\u043B\u044C\u0437\u0443\u0435\u0442\u0441\u044F")
            ])
          ];
        }
      }),
      _: 1
    }, _parent));
    _push(`</div>`);
    if ($setup.viewMode === "table") {
      _push(ssrRenderComponent($setup["VCard"], null, {
        content: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(ssrRenderComponent($setup["VDataTable"], {
              value: $setup.templates,
              loading: $setup.tableLoading && $setup.templates.length === 0,
              paginator: "",
              rows: $setup.pageSize,
              "total-records": $setup.totalCount,
              "rows-per-page-options": [10, 25, 50],
              lazy: "",
              onPage: $setup.onPageChange,
              "table-style": "min-width: 50rem",
              class: "p-datatable-sm",
              "striped-rows": ""
            }, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(ssrRenderComponent($setup["Column"], {
                    field: "id",
                    header: "ID",
                    sortable: "",
                    style: { "width": "80px" }
                  }, {
                    body: withCtx(({ data }, _push4, _parent4, _scopeId3) => {
                      if (_push4) {
                        _push4(`<span class="text-surface-700 dark:text-surface-300 font-mono text-sm"${_scopeId3}>#${ssrInterpolate(data.id)}</span>`);
                      } else {
                        return [
                          createVNode("span", { class: "text-surface-700 dark:text-surface-300 font-mono text-sm" }, "#" + toDisplayString(data.id), 1)
                        ];
                      }
                    }),
                    _: 1
                  }, _parent3, _scopeId2));
                  _push3(ssrRenderComponent($setup["Column"], {
                    field: "title",
                    header: "\u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435",
                    sortable: ""
                  }, {
                    body: withCtx(({ data }, _push4, _parent4, _scopeId3) => {
                      if (_push4) {
                        _push4(`<div${_scopeId3}><div class="text-surface-900 dark:text-surface-0 font-medium"${_scopeId3}>${ssrInterpolate(data.title)}</div><div class="text-surface-600 dark:text-surface-400 font-mono text-sm"${_scopeId3}>${ssrInterpolate(data.name)}</div></div>`);
                      } else {
                        return [
                          createVNode("div", null, [
                            createVNode("div", { class: "text-surface-900 dark:text-surface-0 font-medium" }, toDisplayString(data.title), 1),
                            createVNode("div", { class: "text-surface-600 dark:text-surface-400 font-mono text-sm" }, toDisplayString(data.name), 1)
                          ])
                        ];
                      }
                    }),
                    _: 1
                  }, _parent3, _scopeId2));
                  _push3(ssrRenderComponent($setup["Column"], {
                    field: "group.name",
                    header: "\u0413\u0440\u0443\u043F\u043F\u0430",
                    sortable: ""
                  }, {
                    body: withCtx(({ data }, _push4, _parent4, _scopeId3) => {
                      if (_push4) {
                        if (data.group) {
                          _push4(ssrRenderComponent($setup["VTag"], {
                            value: data.group.name,
                            severity: "secondary"
                          }, null, _parent4, _scopeId3));
                        } else {
                          _push4(`<span class="text-surface-400 dark:text-surface-600"${_scopeId3}>\u2014</span>`);
                        }
                      } else {
                        return [
                          data.group ? (openBlock(), createBlock($setup["VTag"], {
                            key: 0,
                            value: data.group.name,
                            severity: "secondary"
                          }, null, 8, ["value"])) : (openBlock(), createBlock("span", {
                            key: 1,
                            class: "text-surface-400 dark:text-surface-600"
                          }, "\u2014"))
                        ];
                      }
                    }),
                    _: 1
                  }, _parent3, _scopeId2));
                  _push3(ssrRenderComponent($setup["Column"], {
                    field: "dataType",
                    header: "\u0422\u0438\u043F",
                    sortable: ""
                  }, {
                    body: withCtx(({ data }, _push4, _parent4, _scopeId3) => {
                      if (_push4) {
                        _push4(ssrRenderComponent($setup["VTag"], {
                          value: $setup.getDataTypeLabel(data.dataType),
                          severity: "info"
                        }, null, _parent4, _scopeId3));
                      } else {
                        return [
                          createVNode($setup["VTag"], {
                            value: $setup.getDataTypeLabel(data.dataType),
                            severity: "info"
                          }, null, 8, ["value"])
                        ];
                      }
                    }),
                    _: 1
                  }, _parent3, _scopeId2));
                  _push3(ssrRenderComponent($setup["Column"], {
                    field: "unit",
                    header: "\u0415\u0434\u0438\u043D\u0438\u0446\u0430"
                  }, {
                    body: withCtx(({ data }, _push4, _parent4, _scopeId3) => {
                      if (_push4) {
                        if (data.unit) {
                          _push4(ssrRenderComponent($setup["VTag"], {
                            value: $setup.getUnitLabel(data.unit),
                            severity: "success"
                          }, null, _parent4, _scopeId3));
                        } else {
                          _push4(`<span class="text-surface-400 dark:text-surface-600"${_scopeId3}>\u2014</span>`);
                        }
                      } else {
                        return [
                          data.unit ? (openBlock(), createBlock($setup["VTag"], {
                            key: 0,
                            value: $setup.getUnitLabel(data.unit),
                            severity: "success"
                          }, null, 8, ["value"])) : (openBlock(), createBlock("span", {
                            key: 1,
                            class: "text-surface-400 dark:text-surface-600"
                          }, "\u2014"))
                        ];
                      }
                    }),
                    _: 1
                  }, _parent3, _scopeId2));
                  _push3(ssrRenderComponent($setup["Column"], {
                    header: "\u0418\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u043D\u0438\u0435",
                    style: { "width": "120px" }
                  }, {
                    body: withCtx(({ data }, _push4, _parent4, _scopeId3) => {
                      if (_push4) {
                        _push4(`<div class="text-sm"${_scopeId3}>`);
                        if (data._count) {
                          _push4(`<div${_scopeId3}><div class="text-surface-700 dark:text-surface-300"${_scopeId3}>${ssrInterpolate($setup.getTotalUsage(data._count))} \u0438\u0441\u043F.</div><div class="text-surface-500 dark:text-surface-400 text-xs"${_scopeId3}>${ssrInterpolate($setup.getUsageDetails(data._count))}</div></div>`);
                        } else {
                          _push4(`<span class="text-surface-400 dark:text-surface-600"${_scopeId3}>\u2014</span>`);
                        }
                        _push4(`</div>`);
                      } else {
                        return [
                          createVNode("div", { class: "text-sm" }, [
                            data._count ? (openBlock(), createBlock("div", { key: 0 }, [
                              createVNode("div", { class: "text-surface-700 dark:text-surface-300" }, toDisplayString($setup.getTotalUsage(data._count)) + " \u0438\u0441\u043F.", 1),
                              createVNode("div", { class: "text-surface-500 dark:text-surface-400 text-xs" }, toDisplayString($setup.getUsageDetails(data._count)), 1)
                            ])) : (openBlock(), createBlock("span", {
                              key: 1,
                              class: "text-surface-400 dark:text-surface-600"
                            }, "\u2014"))
                          ])
                        ];
                      }
                    }),
                    _: 1
                  }, _parent3, _scopeId2));
                  _push3(ssrRenderComponent($setup["Column"], {
                    header: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F",
                    style: { "width": "120px" }
                  }, {
                    body: withCtx(({ data }, _push4, _parent4, _scopeId3) => {
                      if (_push4) {
                        _push4(`<div class="flex gap-2"${_scopeId3}>`);
                        if (data.dataType === "STRING") {
                          _push4(ssrRenderComponent($setup["VButton"], {
                            onClick: ($event) => $setup.openSynonyms(data),
                            severity: "secondary",
                            outlined: "",
                            size: "small"
                          }, {
                            default: withCtx((_3, _push5, _parent5, _scopeId4) => {
                              if (_push5) {
                                _push5(` \u0421\u0438\u043D\u043E\u043D\u0438\u043C\u044B `);
                                _push5(ssrRenderComponent($setup["TagsIcon"], { class: "h-5 w-5" }, null, _parent5, _scopeId4));
                              } else {
                                return [
                                  createTextVNode(" \u0421\u0438\u043D\u043E\u043D\u0438\u043C\u044B "),
                                  createVNode($setup["TagsIcon"], { class: "h-5 w-5" })
                                ];
                              }
                            }),
                            _: 2
                          }, _parent4, _scopeId3));
                        } else {
                          _push4(`<!---->`);
                        }
                        _push4(ssrRenderComponent($setup["VButton"], {
                          onClick: ($event) => $setup.editTemplate(data),
                          severity: "secondary",
                          outlined: "",
                          size: "small"
                        }, {
                          default: withCtx((_3, _push5, _parent5, _scopeId4) => {
                            if (_push5) {
                              _push5(ssrRenderComponent($setup["PencilIcon"], { class: "h-5 w-5" }, null, _parent5, _scopeId4));
                            } else {
                              return [
                                createVNode($setup["PencilIcon"], { class: "h-5 w-5" })
                              ];
                            }
                          }),
                          _: 2
                        }, _parent4, _scopeId3));
                        _push4(ssrRenderComponent($setup["DangerButton"], {
                          onClick: ($event) => $setup.deleteTemplate(data),
                          severity: "danger",
                          outlined: "",
                          size: "small",
                          disabled: $setup.getTotalUsage(data._count) > 0
                        }, {
                          default: withCtx((_3, _push5, _parent5, _scopeId4) => {
                            if (_push5) {
                              _push5(ssrRenderComponent($setup["TrashIcon"], { class: "h-5 w-5" }, null, _parent5, _scopeId4));
                            } else {
                              return [
                                createVNode($setup["TrashIcon"], { class: "h-5 w-5" })
                              ];
                            }
                          }),
                          _: 2
                        }, _parent4, _scopeId3));
                        _push4(`</div>`);
                      } else {
                        return [
                          createVNode("div", { class: "flex gap-2" }, [
                            data.dataType === "STRING" ? (openBlock(), createBlock($setup["VButton"], {
                              key: 0,
                              onClick: ($event) => $setup.openSynonyms(data),
                              severity: "secondary",
                              outlined: "",
                              size: "small"
                            }, {
                              default: withCtx(() => [
                                createTextVNode(" \u0421\u0438\u043D\u043E\u043D\u0438\u043C\u044B "),
                                createVNode($setup["TagsIcon"], { class: "h-5 w-5" })
                              ]),
                              _: 2
                            }, 1032, ["onClick"])) : createCommentVNode("", true),
                            createVNode($setup["VButton"], {
                              onClick: ($event) => $setup.editTemplate(data),
                              severity: "secondary",
                              outlined: "",
                              size: "small"
                            }, {
                              default: withCtx(() => [
                                createVNode($setup["PencilIcon"], { class: "h-5 w-5" })
                              ]),
                              _: 2
                            }, 1032, ["onClick"]),
                            createVNode($setup["DangerButton"], {
                              onClick: ($event) => $setup.deleteTemplate(data),
                              severity: "danger",
                              outlined: "",
                              size: "small",
                              disabled: $setup.getTotalUsage(data._count) > 0
                            }, {
                              default: withCtx(() => [
                                createVNode($setup["TrashIcon"], { class: "h-5 w-5" })
                              ]),
                              _: 2
                            }, 1032, ["onClick", "disabled"])
                          ])
                        ];
                      }
                    }),
                    _: 1
                  }, _parent3, _scopeId2));
                } else {
                  return [
                    createVNode($setup["Column"], {
                      field: "id",
                      header: "ID",
                      sortable: "",
                      style: { "width": "80px" }
                    }, {
                      body: withCtx(({ data }) => [
                        createVNode("span", { class: "text-surface-700 dark:text-surface-300 font-mono text-sm" }, "#" + toDisplayString(data.id), 1)
                      ]),
                      _: 1
                    }),
                    createVNode($setup["Column"], {
                      field: "title",
                      header: "\u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435",
                      sortable: ""
                    }, {
                      body: withCtx(({ data }) => [
                        createVNode("div", null, [
                          createVNode("div", { class: "text-surface-900 dark:text-surface-0 font-medium" }, toDisplayString(data.title), 1),
                          createVNode("div", { class: "text-surface-600 dark:text-surface-400 font-mono text-sm" }, toDisplayString(data.name), 1)
                        ])
                      ]),
                      _: 1
                    }),
                    createVNode($setup["Column"], {
                      field: "group.name",
                      header: "\u0413\u0440\u0443\u043F\u043F\u0430",
                      sortable: ""
                    }, {
                      body: withCtx(({ data }) => [
                        data.group ? (openBlock(), createBlock($setup["VTag"], {
                          key: 0,
                          value: data.group.name,
                          severity: "secondary"
                        }, null, 8, ["value"])) : (openBlock(), createBlock("span", {
                          key: 1,
                          class: "text-surface-400 dark:text-surface-600"
                        }, "\u2014"))
                      ]),
                      _: 1
                    }),
                    createVNode($setup["Column"], {
                      field: "dataType",
                      header: "\u0422\u0438\u043F",
                      sortable: ""
                    }, {
                      body: withCtx(({ data }) => [
                        createVNode($setup["VTag"], {
                          value: $setup.getDataTypeLabel(data.dataType),
                          severity: "info"
                        }, null, 8, ["value"])
                      ]),
                      _: 1
                    }),
                    createVNode($setup["Column"], {
                      field: "unit",
                      header: "\u0415\u0434\u0438\u043D\u0438\u0446\u0430"
                    }, {
                      body: withCtx(({ data }) => [
                        data.unit ? (openBlock(), createBlock($setup["VTag"], {
                          key: 0,
                          value: $setup.getUnitLabel(data.unit),
                          severity: "success"
                        }, null, 8, ["value"])) : (openBlock(), createBlock("span", {
                          key: 1,
                          class: "text-surface-400 dark:text-surface-600"
                        }, "\u2014"))
                      ]),
                      _: 1
                    }),
                    createVNode($setup["Column"], {
                      header: "\u0418\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u043D\u0438\u0435",
                      style: { "width": "120px" }
                    }, {
                      body: withCtx(({ data }) => [
                        createVNode("div", { class: "text-sm" }, [
                          data._count ? (openBlock(), createBlock("div", { key: 0 }, [
                            createVNode("div", { class: "text-surface-700 dark:text-surface-300" }, toDisplayString($setup.getTotalUsage(data._count)) + " \u0438\u0441\u043F.", 1),
                            createVNode("div", { class: "text-surface-500 dark:text-surface-400 text-xs" }, toDisplayString($setup.getUsageDetails(data._count)), 1)
                          ])) : (openBlock(), createBlock("span", {
                            key: 1,
                            class: "text-surface-400 dark:text-surface-600"
                          }, "\u2014"))
                        ])
                      ]),
                      _: 1
                    }),
                    createVNode($setup["Column"], {
                      header: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F",
                      style: { "width": "120px" }
                    }, {
                      body: withCtx(({ data }) => [
                        createVNode("div", { class: "flex gap-2" }, [
                          data.dataType === "STRING" ? (openBlock(), createBlock($setup["VButton"], {
                            key: 0,
                            onClick: ($event) => $setup.openSynonyms(data),
                            severity: "secondary",
                            outlined: "",
                            size: "small"
                          }, {
                            default: withCtx(() => [
                              createTextVNode(" \u0421\u0438\u043D\u043E\u043D\u0438\u043C\u044B "),
                              createVNode($setup["TagsIcon"], { class: "h-5 w-5" })
                            ]),
                            _: 2
                          }, 1032, ["onClick"])) : createCommentVNode("", true),
                          createVNode($setup["VButton"], {
                            onClick: ($event) => $setup.editTemplate(data),
                            severity: "secondary",
                            outlined: "",
                            size: "small"
                          }, {
                            default: withCtx(() => [
                              createVNode($setup["PencilIcon"], { class: "h-5 w-5" })
                            ]),
                            _: 2
                          }, 1032, ["onClick"]),
                          createVNode($setup["DangerButton"], {
                            onClick: ($event) => $setup.deleteTemplate(data),
                            severity: "danger",
                            outlined: "",
                            size: "small",
                            disabled: $setup.getTotalUsage(data._count) > 0
                          }, {
                            default: withCtx(() => [
                              createVNode($setup["TrashIcon"], { class: "h-5 w-5" })
                            ]),
                            _: 2
                          }, 1032, ["onClick", "disabled"])
                        ])
                      ]),
                      _: 1
                    })
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
          } else {
            return [
              createVNode($setup["VDataTable"], {
                value: $setup.templates,
                loading: $setup.tableLoading && $setup.templates.length === 0,
                paginator: "",
                rows: $setup.pageSize,
                "total-records": $setup.totalCount,
                "rows-per-page-options": [10, 25, 50],
                lazy: "",
                onPage: $setup.onPageChange,
                "table-style": "min-width: 50rem",
                class: "p-datatable-sm",
                "striped-rows": ""
              }, {
                default: withCtx(() => [
                  createVNode($setup["Column"], {
                    field: "id",
                    header: "ID",
                    sortable: "",
                    style: { "width": "80px" }
                  }, {
                    body: withCtx(({ data }) => [
                      createVNode("span", { class: "text-surface-700 dark:text-surface-300 font-mono text-sm" }, "#" + toDisplayString(data.id), 1)
                    ]),
                    _: 1
                  }),
                  createVNode($setup["Column"], {
                    field: "title",
                    header: "\u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435",
                    sortable: ""
                  }, {
                    body: withCtx(({ data }) => [
                      createVNode("div", null, [
                        createVNode("div", { class: "text-surface-900 dark:text-surface-0 font-medium" }, toDisplayString(data.title), 1),
                        createVNode("div", { class: "text-surface-600 dark:text-surface-400 font-mono text-sm" }, toDisplayString(data.name), 1)
                      ])
                    ]),
                    _: 1
                  }),
                  createVNode($setup["Column"], {
                    field: "group.name",
                    header: "\u0413\u0440\u0443\u043F\u043F\u0430",
                    sortable: ""
                  }, {
                    body: withCtx(({ data }) => [
                      data.group ? (openBlock(), createBlock($setup["VTag"], {
                        key: 0,
                        value: data.group.name,
                        severity: "secondary"
                      }, null, 8, ["value"])) : (openBlock(), createBlock("span", {
                        key: 1,
                        class: "text-surface-400 dark:text-surface-600"
                      }, "\u2014"))
                    ]),
                    _: 1
                  }),
                  createVNode($setup["Column"], {
                    field: "dataType",
                    header: "\u0422\u0438\u043F",
                    sortable: ""
                  }, {
                    body: withCtx(({ data }) => [
                      createVNode($setup["VTag"], {
                        value: $setup.getDataTypeLabel(data.dataType),
                        severity: "info"
                      }, null, 8, ["value"])
                    ]),
                    _: 1
                  }),
                  createVNode($setup["Column"], {
                    field: "unit",
                    header: "\u0415\u0434\u0438\u043D\u0438\u0446\u0430"
                  }, {
                    body: withCtx(({ data }) => [
                      data.unit ? (openBlock(), createBlock($setup["VTag"], {
                        key: 0,
                        value: $setup.getUnitLabel(data.unit),
                        severity: "success"
                      }, null, 8, ["value"])) : (openBlock(), createBlock("span", {
                        key: 1,
                        class: "text-surface-400 dark:text-surface-600"
                      }, "\u2014"))
                    ]),
                    _: 1
                  }),
                  createVNode($setup["Column"], {
                    header: "\u0418\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u043D\u0438\u0435",
                    style: { "width": "120px" }
                  }, {
                    body: withCtx(({ data }) => [
                      createVNode("div", { class: "text-sm" }, [
                        data._count ? (openBlock(), createBlock("div", { key: 0 }, [
                          createVNode("div", { class: "text-surface-700 dark:text-surface-300" }, toDisplayString($setup.getTotalUsage(data._count)) + " \u0438\u0441\u043F.", 1),
                          createVNode("div", { class: "text-surface-500 dark:text-surface-400 text-xs" }, toDisplayString($setup.getUsageDetails(data._count)), 1)
                        ])) : (openBlock(), createBlock("span", {
                          key: 1,
                          class: "text-surface-400 dark:text-surface-600"
                        }, "\u2014"))
                      ])
                    ]),
                    _: 1
                  }),
                  createVNode($setup["Column"], {
                    header: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F",
                    style: { "width": "120px" }
                  }, {
                    body: withCtx(({ data }) => [
                      createVNode("div", { class: "flex gap-2" }, [
                        data.dataType === "STRING" ? (openBlock(), createBlock($setup["VButton"], {
                          key: 0,
                          onClick: ($event) => $setup.openSynonyms(data),
                          severity: "secondary",
                          outlined: "",
                          size: "small"
                        }, {
                          default: withCtx(() => [
                            createTextVNode(" \u0421\u0438\u043D\u043E\u043D\u0438\u043C\u044B "),
                            createVNode($setup["TagsIcon"], { class: "h-5 w-5" })
                          ]),
                          _: 2
                        }, 1032, ["onClick"])) : createCommentVNode("", true),
                        createVNode($setup["VButton"], {
                          onClick: ($event) => $setup.editTemplate(data),
                          severity: "secondary",
                          outlined: "",
                          size: "small"
                        }, {
                          default: withCtx(() => [
                            createVNode($setup["PencilIcon"], { class: "h-5 w-5" })
                          ]),
                          _: 2
                        }, 1032, ["onClick"]),
                        createVNode($setup["DangerButton"], {
                          onClick: ($event) => $setup.deleteTemplate(data),
                          severity: "danger",
                          outlined: "",
                          size: "small",
                          disabled: $setup.getTotalUsage(data._count) > 0
                        }, {
                          default: withCtx(() => [
                            createVNode($setup["TrashIcon"], { class: "h-5 w-5" })
                          ]),
                          _: 2
                        }, 1032, ["onClick", "disabled"])
                      ])
                    ]),
                    _: 1
                  })
                ]),
                _: 1
              }, 8, ["value", "loading", "rows", "total-records"])
            ];
          }
        }),
        _: 1
      }, _parent));
    } else if ($setup.viewMode === "cards") {
      _push(`<div class="grid gap-4"><!--[-->`);
      ssrRenderList($setup.templates, (template) => {
        _push(ssrRenderComponent($setup["VCard"], {
          key: template.id,
          class: "border-surface-200 dark:border-surface-700 hover:border-primary border transition-colors"
        }, {
          content: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(`<div class="p-6"${_scopeId}><div class="mb-4 flex items-start justify-between"${_scopeId}><div class="flex-1"${_scopeId}><div class="mb-2 flex items-center gap-3"${_scopeId}>`);
              _push2(ssrRenderComponent($setup["TagsIcon"], { class: "h-5 w-5" }, null, _parent2, _scopeId));
              _push2(`<h3 class="text-surface-900 dark:text-surface-0 text-lg font-semibold"${_scopeId}>${ssrInterpolate(template.title)}</h3>`);
              if (template.isRequired) {
                _push2(ssrRenderComponent($setup["VTag"], {
                  value: "\u041E\u0431\u044F\u0437\u0430\u0442\u0435\u043B\u044C\u043D\u044B\u0439",
                  severity: "danger",
                  size: "small"
                }, null, _parent2, _scopeId));
              } else {
                _push2(`<!---->`);
              }
              _push2(`</div><div class="text-surface-600 dark:text-surface-400 mb-2 font-mono text-sm"${_scopeId}>${ssrInterpolate(template.name)}</div>`);
              if (template.description) {
                _push2(`<p class="text-surface-600 dark:text-surface-400 mb-3"${_scopeId}>${ssrInterpolate(template.description)}</p>`);
              } else {
                _push2(`<!---->`);
              }
              _push2(`</div><div class="ml-4 flex gap-2"${_scopeId}>`);
              _push2(ssrRenderComponent($setup["VButton"], {
                onClick: ($event) => $setup.editTemplate(template),
                severity: "secondary",
                outlined: "",
                size: "small"
              }, {
                default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                  if (_push3) {
                    _push3(ssrRenderComponent($setup["PencilIcon"], { class: "h-5 w-5" }, null, _parent3, _scopeId2));
                  } else {
                    return [
                      createVNode($setup["PencilIcon"], { class: "h-5 w-5" })
                    ];
                  }
                }),
                _: 2
              }, _parent2, _scopeId));
              _push2(ssrRenderComponent($setup["VButton"], {
                onClick: ($event) => $setup.deleteTemplate(template),
                severity: "danger",
                outlined: "",
                size: "small",
                disabled: $setup.getTotalUsage(template._count) > 0
              }, {
                default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                  if (_push3) {
                    _push3(ssrRenderComponent($setup["TrashIcon"], { class: "h-5 w-5" }, null, _parent3, _scopeId2));
                  } else {
                    return [
                      createVNode($setup["TrashIcon"], { class: "h-5 w-5" })
                    ];
                  }
                }),
                _: 2
              }, _parent2, _scopeId));
              _push2(`</div></div><div class="mb-4 flex items-center gap-4"${_scopeId}>`);
              _push2(ssrRenderComponent($setup["VTag"], {
                value: $setup.getDataTypeLabel(template.dataType),
                severity: "info"
              }, null, _parent2, _scopeId));
              if (template.unit) {
                _push2(ssrRenderComponent($setup["VTag"], {
                  value: $setup.getUnitLabel(template.unit),
                  severity: "success"
                }, null, _parent2, _scopeId));
              } else {
                _push2(`<!---->`);
              }
              if (template.group) {
                _push2(ssrRenderComponent($setup["VTag"], {
                  value: template.group.name,
                  severity: "secondary"
                }, null, _parent2, _scopeId));
              } else {
                _push2(`<!---->`);
              }
              _push2(`</div>`);
              if (template._count) {
                _push2(`<div class="border-surface-200 dark:border-surface-700 border-t pt-4"${_scopeId}><div class="text-surface-600 dark:text-surface-400 mb-2 text-sm"${_scopeId}>\u0418\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u043D\u0438\u0435:</div><div class="grid grid-cols-3 gap-4 text-center"${_scopeId}><div${_scopeId}><div class="text-surface-900 dark:text-surface-0 text-lg font-semibold"${_scopeId}>${ssrInterpolate(template._count.partAttributes || 0)}</div><div class="text-surface-500 text-xs"${_scopeId}>\u0417\u0430\u043F\u0447\u0430\u0441\u0442\u0438</div></div><div${_scopeId}><div class="text-surface-900 dark:text-surface-0 text-lg font-semibold"${_scopeId}>${ssrInterpolate(template._count.catalogItemAttributes || 0)}</div><div class="text-surface-500 text-xs"${_scopeId}>\u041A\u0430\u0442\u0430\u043B\u043E\u0433</div></div><div${_scopeId}><div class="text-surface-900 dark:text-surface-0 text-lg font-semibold"${_scopeId}>${ssrInterpolate(template._count.equipmentAttributes || 0)}</div><div class="text-surface-500 text-xs"${_scopeId}>\u0422\u0435\u0445\u043D\u0438\u043A\u0430</div></div></div></div>`);
              } else {
                _push2(`<!---->`);
              }
              _push2(`</div>`);
            } else {
              return [
                createVNode("div", { class: "p-6" }, [
                  createVNode("div", { class: "mb-4 flex items-start justify-between" }, [
                    createVNode("div", { class: "flex-1" }, [
                      createVNode("div", { class: "mb-2 flex items-center gap-3" }, [
                        createVNode($setup["TagsIcon"], { class: "h-5 w-5" }),
                        createVNode("h3", { class: "text-surface-900 dark:text-surface-0 text-lg font-semibold" }, toDisplayString(template.title), 1),
                        template.isRequired ? (openBlock(), createBlock($setup["VTag"], {
                          key: 0,
                          value: "\u041E\u0431\u044F\u0437\u0430\u0442\u0435\u043B\u044C\u043D\u044B\u0439",
                          severity: "danger",
                          size: "small"
                        })) : createCommentVNode("", true)
                      ]),
                      createVNode("div", { class: "text-surface-600 dark:text-surface-400 mb-2 font-mono text-sm" }, toDisplayString(template.name), 1),
                      template.description ? (openBlock(), createBlock("p", {
                        key: 0,
                        class: "text-surface-600 dark:text-surface-400 mb-3"
                      }, toDisplayString(template.description), 1)) : createCommentVNode("", true)
                    ]),
                    createVNode("div", { class: "ml-4 flex gap-2" }, [
                      createVNode($setup["VButton"], {
                        onClick: ($event) => $setup.editTemplate(template),
                        severity: "secondary",
                        outlined: "",
                        size: "small"
                      }, {
                        default: withCtx(() => [
                          createVNode($setup["PencilIcon"], { class: "h-5 w-5" })
                        ]),
                        _: 2
                      }, 1032, ["onClick"]),
                      createVNode($setup["VButton"], {
                        onClick: ($event) => $setup.deleteTemplate(template),
                        severity: "danger",
                        outlined: "",
                        size: "small",
                        disabled: $setup.getTotalUsage(template._count) > 0
                      }, {
                        default: withCtx(() => [
                          createVNode($setup["TrashIcon"], { class: "h-5 w-5" })
                        ]),
                        _: 2
                      }, 1032, ["onClick", "disabled"])
                    ])
                  ]),
                  createVNode("div", { class: "mb-4 flex items-center gap-4" }, [
                    createVNode($setup["VTag"], {
                      value: $setup.getDataTypeLabel(template.dataType),
                      severity: "info"
                    }, null, 8, ["value"]),
                    template.unit ? (openBlock(), createBlock($setup["VTag"], {
                      key: 0,
                      value: $setup.getUnitLabel(template.unit),
                      severity: "success"
                    }, null, 8, ["value"])) : createCommentVNode("", true),
                    template.group ? (openBlock(), createBlock($setup["VTag"], {
                      key: 1,
                      value: template.group.name,
                      severity: "secondary"
                    }, null, 8, ["value"])) : createCommentVNode("", true)
                  ]),
                  template._count ? (openBlock(), createBlock("div", {
                    key: 0,
                    class: "border-surface-200 dark:border-surface-700 border-t pt-4"
                  }, [
                    createVNode("div", { class: "text-surface-600 dark:text-surface-400 mb-2 text-sm" }, "\u0418\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u043D\u0438\u0435:"),
                    createVNode("div", { class: "grid grid-cols-3 gap-4 text-center" }, [
                      createVNode("div", null, [
                        createVNode("div", { class: "text-surface-900 dark:text-surface-0 text-lg font-semibold" }, toDisplayString(template._count.partAttributes || 0), 1),
                        createVNode("div", { class: "text-surface-500 text-xs" }, "\u0417\u0430\u043F\u0447\u0430\u0441\u0442\u0438")
                      ]),
                      createVNode("div", null, [
                        createVNode("div", { class: "text-surface-900 dark:text-surface-0 text-lg font-semibold" }, toDisplayString(template._count.catalogItemAttributes || 0), 1),
                        createVNode("div", { class: "text-surface-500 text-xs" }, "\u041A\u0430\u0442\u0430\u043B\u043E\u0433")
                      ]),
                      createVNode("div", null, [
                        createVNode("div", { class: "text-surface-900 dark:text-surface-0 text-lg font-semibold" }, toDisplayString(template._count.equipmentAttributes || 0), 1),
                        createVNode("div", { class: "text-surface-500 text-xs" }, "\u0422\u0435\u0445\u043D\u0438\u043A\u0430")
                      ])
                    ])
                  ])) : createCommentVNode("", true)
                ])
              ];
            }
          }),
          _: 2
        }, _parent));
      });
      _push(`<!--]-->`);
      if ($setup.totalCount > $setup.pageSize) {
        _push(ssrRenderComponent($setup["VCard"], null, {
          content: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(`<div class="p-4"${_scopeId}>`);
              _push2(ssrRenderComponent($setup["Paginator"], {
                rows: $setup.pageSize,
                "total-records": $setup.totalCount,
                "rows-per-page-options": [10, 25, 50],
                onPage: $setup.onPageChange
              }, null, _parent2, _scopeId));
              _push2(`</div>`);
            } else {
              return [
                createVNode("div", { class: "p-4" }, [
                  createVNode($setup["Paginator"], {
                    rows: $setup.pageSize,
                    "total-records": $setup.totalCount,
                    "rows-per-page-options": [10, 25, 50],
                    onPage: $setup.onPageChange
                  }, null, 8, ["rows", "total-records"])
                ])
              ];
            }
          }),
          _: 1
        }, _parent));
      } else {
        _push(`<!---->`);
      }
      _push(`</div>`);
    } else {
      _push(`<!---->`);
    }
    _push(ssrRenderComponent($setup["VDialog"], {
      visible: $setup.showCreateDialog,
      "onUpdate:visible": ($event) => $setup.showCreateDialog = $event,
      modal: "",
      header: $setup.editingTemplate ? "\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0448\u0430\u0431\u043B\u043E\u043D" : "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u0448\u0430\u0431\u043B\u043E\u043D",
      style: { width: "50rem" },
      breakpoints: { "1199px": "75vw", "575px": "90vw" }
    }, {
      default: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(ssrRenderComponent($setup["TemplateForm"], {
            modelValue: $setup.templateForm,
            "onUpdate:modelValue": ($event) => $setup.templateForm = $event,
            groups: $setup.groups,
            "hierarchy-groups": $setup.hierarchyGroups,
            loading: $setup.saving,
            onSave: $setup.saveTemplate,
            onCancel: ($event) => $setup.showCreateDialog = false,
            onGroupCreated: $setup.onGroupCreated
          }, null, _parent2, _scopeId));
        } else {
          return [
            createVNode($setup["TemplateForm"], {
              modelValue: $setup.templateForm,
              "onUpdate:modelValue": ($event) => $setup.templateForm = $event,
              groups: $setup.groups,
              "hierarchy-groups": $setup.hierarchyGroups,
              loading: $setup.saving,
              onSave: $setup.saveTemplate,
              onCancel: ($event) => $setup.showCreateDialog = false,
              onGroupCreated: $setup.onGroupCreated
            }, null, 8, ["modelValue", "onUpdate:modelValue", "groups", "hierarchy-groups", "loading", "onCancel"])
          ];
        }
      }),
      _: 1
    }, _parent));
    _push(ssrRenderComponent($setup["VDialog"], {
      visible: $setup.showSynonymsDialog,
      "onUpdate:visible": ($event) => $setup.showSynonymsDialog = $event,
      modal: "",
      header: "\u0423\u043F\u0440\u0430\u0432\u043B\u0435\u043D\u0438\u0435 \u0441\u0438\u043D\u043E\u043D\u0438\u043C\u0430\u043C\u0438",
      style: { width: "80rem" },
      breakpoints: { "1199px": "90vw", "575px": "98vw" }
    }, {
      default: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          if ($setup.selectedTemplateForSynonyms) {
            _push2(ssrRenderComponent($setup["AttributeSynonymManager"], { template: $setup.selectedTemplateForSynonyms }, null, _parent2, _scopeId));
          } else {
            _push2(`<!---->`);
          }
        } else {
          return [
            $setup.selectedTemplateForSynonyms ? (openBlock(), createBlock($setup["AttributeSynonymManager"], {
              key: 0,
              template: $setup.selectedTemplateForSynonyms
            }, null, 8, ["template"])) : createCommentVNode("", true)
          ];
        }
      }),
      _: 1
    }, _parent));
    _push(ssrRenderComponent($setup["VDialog"], {
      visible: $setup.showGroupDialog,
      "onUpdate:visible": ($event) => $setup.showGroupDialog = $event,
      modal: "",
      header: $setup.editingGroup ? "\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0433\u0440\u0443\u043F\u043F\u0443" : "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u0433\u0440\u0443\u043F\u043F\u0443",
      style: { "width": "500px" }
    }, {
      footer: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`<div class="flex justify-end gap-2"${_scopeId}>`);
          _push2(ssrRenderComponent($setup["VButton"], {
            onClick: $setup.closeGroupDialog,
            severity: "secondary",
            outlined: "",
            label: "\u041E\u0442\u043C\u0435\u043D\u0430"
          }, null, _parent2, _scopeId));
          _push2(ssrRenderComponent($setup["VButton"], {
            onClick: $setup.saveGroup,
            loading: $setup.savingGroup,
            label: $setup.editingGroup ? "\u0421\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C" : "\u0421\u043E\u0437\u0434\u0430\u0442\u044C"
          }, null, _parent2, _scopeId));
          _push2(`</div>`);
        } else {
          return [
            createVNode("div", { class: "flex justify-end gap-2" }, [
              createVNode($setup["VButton"], {
                onClick: $setup.closeGroupDialog,
                severity: "secondary",
                outlined: "",
                label: "\u041E\u0442\u043C\u0435\u043D\u0430"
              }),
              createVNode($setup["VButton"], {
                onClick: $setup.saveGroup,
                loading: $setup.savingGroup,
                label: $setup.editingGroup ? "\u0421\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C" : "\u0421\u043E\u0437\u0434\u0430\u0442\u044C"
              }, null, 8, ["loading", "label"])
            ])
          ];
        }
      }),
      default: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`<div class="space-y-4"${_scopeId}><div${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}> \u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435 \u0433\u0440\u0443\u043F\u043F\u044B * </label>`);
          _push2(ssrRenderComponent($setup["VInputText"], {
            modelValue: $setup.groupForm.name,
            "onUpdate:modelValue": ($event) => $setup.groupForm.name = $event,
            placeholder: "\u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u0435 \u0433\u0440\u0443\u043F\u043F\u044B...",
            class: ["w-full", { "p-invalid": $setup.groupErrors.name }]
          }, null, _parent2, _scopeId));
          if ($setup.groupErrors.name) {
            _push2(`<small class="p-error"${_scopeId}>${ssrInterpolate($setup.groupErrors.name)}</small>`);
          } else {
            _push2(`<!---->`);
          }
          _push2(`</div><div${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}> \u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435 </label>`);
          _push2(ssrRenderComponent($setup["VTextarea"], {
            modelValue: $setup.groupForm.description,
            "onUpdate:modelValue": ($event) => $setup.groupForm.description = $event,
            placeholder: "\u041F\u043E\u0434\u0440\u043E\u0431\u043D\u043E\u0435 \u043E\u043F\u0438\u0441\u0430\u043D\u0438\u0435 \u0433\u0440\u0443\u043F\u043F\u044B \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432...",
            rows: "3",
            class: "w-full"
          }, null, _parent2, _scopeId));
          _push2(`</div><div${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}> \u0420\u043E\u0434\u0438\u0442\u0435\u043B\u044C\u0441\u043A\u0430\u044F \u0433\u0440\u0443\u043F\u043F\u0430 </label>`);
          _push2(ssrRenderComponent($setup["VSelect"], {
            modelValue: $setup.groupForm.parentId,
            "onUpdate:modelValue": ($event) => $setup.groupForm.parentId = $event,
            options: $setup.parentSelectOptions,
            "option-label": "name",
            "option-value": "id",
            class: "w-full",
            placeholder: "\u041D\u0435 \u0432\u044B\u0431\u0440\u0430\u043D\u043E"
          }, null, _parent2, _scopeId));
          _push2(`</div></div>`);
        } else {
          return [
            createVNode("div", { class: "space-y-4" }, [
              createVNode("div", null, [
                createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435 \u0433\u0440\u0443\u043F\u043F\u044B * "),
                createVNode($setup["VInputText"], {
                  modelValue: $setup.groupForm.name,
                  "onUpdate:modelValue": ($event) => $setup.groupForm.name = $event,
                  placeholder: "\u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u0435 \u0433\u0440\u0443\u043F\u043F\u044B...",
                  class: ["w-full", { "p-invalid": $setup.groupErrors.name }]
                }, null, 8, ["modelValue", "onUpdate:modelValue", "class"]),
                $setup.groupErrors.name ? (openBlock(), createBlock("small", {
                  key: 0,
                  class: "p-error"
                }, toDisplayString($setup.groupErrors.name), 1)) : createCommentVNode("", true)
              ]),
              createVNode("div", null, [
                createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435 "),
                createVNode($setup["VTextarea"], {
                  modelValue: $setup.groupForm.description,
                  "onUpdate:modelValue": ($event) => $setup.groupForm.description = $event,
                  placeholder: "\u041F\u043E\u0434\u0440\u043E\u0431\u043D\u043E\u0435 \u043E\u043F\u0438\u0441\u0430\u043D\u0438\u0435 \u0433\u0440\u0443\u043F\u043F\u044B \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432...",
                  rows: "3",
                  class: "w-full"
                }, null, 8, ["modelValue", "onUpdate:modelValue"])
              ]),
              createVNode("div", null, [
                createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u0420\u043E\u0434\u0438\u0442\u0435\u043B\u044C\u0441\u043A\u0430\u044F \u0433\u0440\u0443\u043F\u043F\u0430 "),
                createVNode($setup["VSelect"], {
                  modelValue: $setup.groupForm.parentId,
                  "onUpdate:modelValue": ($event) => $setup.groupForm.parentId = $event,
                  options: $setup.parentSelectOptions,
                  "option-label": "name",
                  "option-value": "id",
                  class: "w-full",
                  placeholder: "\u041D\u0435 \u0432\u044B\u0431\u0440\u0430\u043D\u043E"
                }, null, 8, ["modelValue", "onUpdate:modelValue", "options"])
              ])
            ])
          ];
        }
      }),
      _: 1
    }, _parent));
    _push(`</div>`);
  }
  _push(`</div>`);
}
const _sfc_setup$1 = _sfc_main$1.setup;
_sfc_main$1.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/attributes/AttributeTemplateManager.vue");
  return _sfc_setup$1 ? _sfc_setup$1(props, ctx) : void 0;
};
const AttributeTemplateManager = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["ssrRender", _sfc_ssrRender$1]]);

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "AttributeTemplateManagerBoundary",
  setup(__props, { expose: __expose }) {
    __expose();
    const key = ref(0);
    const onRetry = () => {
      key.value++;
    };
    const __returned__ = { key, onRetry, ErrorBoundary, AttributeTemplateManager };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(ssrRenderComponent($setup["ErrorBoundary"], mergeProps({
    variant: "detailed",
    onRetry: $setup.onRetry,
    title: "\u041F\u0440\u043E\u0431\u043B\u0435\u043C\u0430 \u043F\u0440\u0438 \u0437\u0430\u0433\u0440\u0443\u0437\u043A\u0435 \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u0432",
    message: "\u041F\u043E\u043F\u0440\u043E\u0431\u0443\u0439\u0442\u0435 \u043E\u0431\u043D\u043E\u0432\u0438\u0442\u044C \u0441\u043F\u0438\u0441\u043E\u043A \u0438\u043B\u0438 \u043F\u043E\u0432\u0442\u043E\u0440\u0438\u0442\u044C \u043F\u043E\u043F\u044B\u0442\u043A\u0443."
  }, _attrs), {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["AttributeTemplateManager"], { key: $setup.key }, null, _parent2, _scopeId));
      } else {
        return [
          (openBlock(), createBlock($setup["AttributeTemplateManager"], { key: $setup.key }))
        ];
      }
    }),
    _: 1
  }, _parent));
}
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/attributes/AttributeTemplateManagerBoundary.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const AttributeTemplateManagerBoundary = /* @__PURE__ */ _export_sfc(_sfc_main, [["ssrRender", _sfc_ssrRender]]);

const $$Attributes = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${renderComponent($$result, "Layout", $$AdminLayout, { "title": "\u0423\u043F\u0440\u0430\u0432\u043B\u0435\u043D\u0438\u0435 \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u0430\u043C\u0438" }, { "default": ($$result2) => renderTemplate` ${renderComponent($$result2, "AttributeTemplateManagerBoundary", AttributeTemplateManagerBoundary, { "client:load": true, "client:component-hydration": "load", "client:component-path": "@/components/admin/attributes/AttributeTemplateManagerBoundary.vue", "client:component-export": "default" })} ` })}`;
}, "D:/Dev/parttec/cpanel/src/pages/admin/attributes.astro", void 0);

const $$file = "D:/Dev/parttec/cpanel/src/pages/admin/attributes.astro";
const $$url = "/admin/attributes";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Attributes,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
