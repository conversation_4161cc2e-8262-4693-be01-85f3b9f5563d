---
import MainLayout from "../layouts/MainLayout.astro";
import { trpcClient } from "@/lib/trpc";
import { TrpcProvider } from "@/components/providers/TrpcProvider";
import { SearchForm } from "@/components/catalog/SearchForm";
import { PartCard } from "@/components/catalog/PartCard";
import { CategoryCard } from "@/components/catalog/CategoryCard";
import { BrandCard } from "@/components/catalog/BrandCard";

// Получаем параметры поиска из URL
const searchParams = Astro.url.searchParams;
const searchQuery = searchParams.get('q') || '';

// Загружаем результаты поиска
let searchResults = {
  parts: [],
  categories: [],
  brands: []
};

if (searchQuery.trim()) {
  try {
    // Поиск запчастей
    const partsResponse = await trpcClient.crud.part.findMany.query({
      where: {
        OR: [
          { name: { contains: searchQuery, mode: 'insensitive' } },
          { partCategory: { name: { contains: searchQuery, mode: 'insensitive' } } },
          { attributes: { some: { value: { contains: searchQuery, mode: 'insensitive' } } } }
        ]
      },
      include: {
        partCategory: true,
        image: true,
        attributes: {
          include: { template: true },
          take: 3
        }
      },
      take: 20,
      orderBy: { updatedAt: 'desc' }
    });
    searchResults.parts = partsResponse || [];

    // Поиск категорий
    const categoriesResponse = await trpcClient.crud.partCategory.findMany.query({
      where: {
        OR: [
          { name: { contains: searchQuery, mode: 'insensitive' } },
          { description: { contains: searchQuery, mode: 'insensitive' } }
        ]
      },
      include: {
        image: true,
        _count: { select: { parts: true } }
      },
      take: 10,
      orderBy: { name: 'asc' }
    });
    searchResults.categories = categoriesResponse || [];

    // Поиск брендов
    const brandsResponse = await trpcClient.crud.brand.findMany.query({
      where: {
        OR: [
          { name: { contains: searchQuery, mode: 'insensitive' } },
          { country: { contains: searchQuery, mode: 'insensitive' } }
        ]
      },
      include: {
        _count: { 
          select: { 
            catalogItems: true,
            equipmentModel: true 
          } 
        }
      },
      take: 10,
      orderBy: { name: 'asc' }
    });
    searchResults.brands = brandsResponse || [];

  } catch (error) {
    console.error('Error performing search:', error);
  }
}

const totalResults = searchResults.parts.length + searchResults.categories.length + searchResults.brands.length;

const handleSearch = (query: string) => {
  if (query.trim()) {
    window.location.href = `/search?q=${encodeURIComponent(query)}`;
  }
};
---

<MainLayout title="Поиск" description="Расширенный поиск по каталогу запчастей">
  <TrpcProvider client:load>
    <div class="container mx-auto px-4 py-8">
      <!-- Заголовок и форма поиска -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold tracking-tight mb-4">Поиск по каталогу</h1>
        <div class="max-w-md">
          <SearchForm client:load onSearch={handleSearch} defaultValue={searchQuery} placeholder="Введите запрос для поиска..." />
        </div>
      </div>

      {searchQuery.trim() ? (
        <div>
          <!-- Результаты поиска -->
          <div class="mb-6">
            <h2 class="text-xl font-semibold mb-2">
              Результаты поиска для "{searchQuery}"
            </h2>
            <p class="text-muted-foreground">
              Найдено {totalResults} результатов
            </p>
          </div>

          {totalResults > 0 ? (
            <div class="space-y-8">
              <!-- Запчасти -->
              {searchResults.parts.length > 0 && (
                <div>
                  <h3 class="text-lg font-semibold mb-4">
                    Запчасти ({searchResults.parts.length})
                  </h3>
                  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    {searchResults.parts.map((part) => (
                      <PartCard part={part} client:load />
                    ))}
                  </div>
                  {searchResults.parts.length === 20 && (
                    <div class="mt-4">
                      <a 
                        href={`/catalog?search=${encodeURIComponent(searchQuery)}`}
                        class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2"
                      >
                        Показать все запчасти
                      </a>
                    </div>
                  )}
                </div>
              )}

              <!-- Категории -->
              {searchResults.categories.length > 0 && (
                <div>
                  <h3 class="text-lg font-semibold mb-4">
                    Категории ({searchResults.categories.length})
                  </h3>
                  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {searchResults.categories.map((category) => (
                      <CategoryCard category={category} client:load />
                    ))}
                  </div>
                </div>
              )}

              <!-- Бренды -->
              {searchResults.brands.length > 0 && (
                <div>
                  <h3 class="text-lg font-semibold mb-4">
                    Бренды ({searchResults.brands.length})
                  </h3>
                  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    {searchResults.brands.map((brand) => (
                      <BrandCard brand={brand} client:load />
                    ))}
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div class="text-center py-12">
              <h3 class="text-lg font-semibold mb-2">Ничего не найдено</h3>
              <p class="text-muted-foreground mb-4">
                Попробуйте изменить поисковый запрос или воспользуйтесь каталогом
              </p>
              <div class="flex justify-center gap-4">
                <a 
                  href="/catalog" 
                  class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground shadow hover:bg-primary/90 h-9 px-4 py-2"
                >
                  Открыть каталог
                </a>
                <a 
                  href="/categories" 
                  class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2"
                >
                  Просмотреть категории
                </a>
              </div>
            </div>
          )}
        </div>
      ) : (
        <div class="text-center py-12">
          <h3 class="text-lg font-semibold mb-2">Введите поисковый запрос</h3>
          <p class="text-muted-foreground mb-6">
            Используйте форму выше для поиска запчастей, категорий или брендов
          </p>
          
          <!-- Популярные поисковые запросы -->
          <div class="max-w-md mx-auto">
            <h4 class="text-sm font-medium mb-3">Популярные запросы:</h4>
            <div class="flex flex-wrap gap-2 justify-center">
              <a href="/search?q=фильтр" class="px-3 py-1 text-xs bg-muted rounded-full hover:bg-muted/80 transition-colors">
                фильтр
              </a>
              <a href="/search?q=сальник" class="px-3 py-1 text-xs bg-muted rounded-full hover:bg-muted/80 transition-colors">
                сальник
              </a>
              <a href="/search?q=подшипник" class="px-3 py-1 text-xs bg-muted rounded-full hover:bg-muted/80 transition-colors">
                подшипник
              </a>
              <a href="/search?q=прокладка" class="px-3 py-1 text-xs bg-muted rounded-full hover:bg-muted/80 transition-colors">
                прокладка
              </a>
              <a href="/search?q=ремень" class="px-3 py-1 text-xs bg-muted rounded-full hover:bg-muted/80 transition-colors">
                ремень
              </a>
            </div>
          </div>
        </div>
      )}
    </div>
  </TrpcProvider>
</MainLayout>
