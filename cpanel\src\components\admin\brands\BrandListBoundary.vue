<template>
  <ErrorBoundary variant="minimal" title="Ошибка брендов" message="Список брендов не отрисовался. Повторите попытку."
    @retry="onRetry">
    <BrandList :initialData="initialData" :key="key" />
  </ErrorBoundary>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ErrorBoundary from '@/components/ui/ErrorBoundary.vue'
import BrandList from './BrandList.vue'

const props = defineProps<{ initialData: any[] }>()

const key = ref(0)
const onRetry = () => {
  key.value++
}
</script>

