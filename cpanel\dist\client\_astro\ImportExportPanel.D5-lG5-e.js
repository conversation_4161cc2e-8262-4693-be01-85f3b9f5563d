import{u as U}from"./useTrpc.CAkGIEe7.js";import J from"./Card.CAr8QcdG.js";import{C as Q}from"./Checkbox.B9YJ1Jrl.js";import{I as W}from"./InputText.COaPodMV.js";import Y from"./Button.oEwD-lSq.js";import{S as Z}from"./Select.CKfyRLxl.js";import{D as $}from"./DangerButton.PrtrqpTW.js";import{_ as uu}from"./_plugin-vue_export-helper.BX07OBiL.js";import{d as eu,c as m,a as t,e as r,j as c,o as s,f as g,b as f,g as tu,F as _,r as q}from"./index.BglzLLgy.js";import{r as b,t as i}from"./reactivity.esm-bundler.D5IypM4U.js";import"./trpc.BpyaUO08.js";import"./useToast.DbdIHNOo.js";import"./index.CzaMXvxd.js";import"./index.D7_1DwEX.js";import"./index.B_yc9D3m.js";import"./index.CRcBj2l1.js";import"./index.CXDqTVvt.js";import"./index.CLkTvMlq.js";import"./index.DQmIdHOH.js";import"./index.CJuyVe3p.js";import"./index.CLs7nh7g.js";import"./index.BHZvt6Rq.js";import"./index.Tc5ZRw49.js";import"./index.irlSZ_18.js";import"./index.B0GjtQuk.js";import"./runtime-dom.esm-bundler.C-dfRCGi.js";const lu=eu({__name:"ImportExportPanel",setup(L,{expose:e}){e();const{importExport:V,excelImport:u}=U(),A=b({Brand:!0,PartCategory:!0,AttributeGroup:!1,AttributeTemplate:!0,AttributeSynonymGroup:!1,AttributeSynonym:!1,Part:!0,CatalogItem:!0,EquipmentModel:!0,PartAttribute:!1,CatalogItemAttribute:!1,EquipmentModelAttribute:!1,PartApplicability:!1,EquipmentApplicability:!1}),D=b(""),l=b("upsert"),x=b(!1),p=b(null),y=b(!1),F=b(!1),E=b(null),C=b(null),G=[{label:"Создать или обновить (upsert)",value:"upsert"},{label:"Только обновить (update_only)",value:"update_only"},{label:"Пропустить (skip)",value:"skip"},{label:"Ошибка (error)",value:"error"}],O=o=>{if(o===0)return"0 Bytes";const a=1024,d=["Bytes","KB","MB","GB"],n=Math.floor(Math.log(o)/Math.log(a));return parseFloat((o/Math.pow(a,n)).toFixed(2))+" "+d[n]},j=()=>{p.value=null,C.value=null},h=async()=>{y.value=!0;try{const o=D.value.split(",").map(T=>T.trim()).filter(Boolean),a=await V.exportXlsx({include:A.value,filters:{brandSlugs:o},meta:{createMissingRefs:x.value,onConflict:l.value}});if(!a)return;const d=B(a.base64,"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"),n=URL.createObjectURL(d),v=document.createElement("a");v.href=n,v.download=a.fileName,v.click(),URL.revokeObjectURL(n)}catch(o){console.error("Export failed:",o)}finally{y.value=!1}},N=async()=>{F.value=!0;try{const o=await V.exportTemplate({include:A.value});if(!o)return;const a=B(o.base64,"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"),d=URL.createObjectURL(a),n=document.createElement("a");n.href=d,n.download=o.fileName,n.click(),URL.revokeObjectURL(d)}catch(o){console.error("Export template failed:",o)}finally{F.value=!1}},z=o=>{const a=o.target;p.value=a.files?.[0]??null,C.value=null};function I(o){return new Promise((a,d)=>{const n=new FileReader;n.onload=()=>a(String(n.result).split(",")[1]||""),n.onerror=d,n.readAsDataURL(o)})}const K=async()=>{if(p.value){E.value="dryRun",y.value=!0;try{const o=await I(p.value),a=await U().excelImport.dryRun({base64:o,overrides:{createMissingRefs:x.value,onConflict:l.value}});C.value=a}catch(o){console.error("Dry run failed:",o)}finally{y.value=!1,E.value=null}}},X=async()=>{if(p.value){E.value="execute",y.value=!0;try{const o=await I(p.value),a=await U().excelImport.execute({base64:o,overrides:{createMissingRefs:x.value,onConflict:l.value}});if(!a)return;C.value=a,a.reportBase64&&M(a.reportBase64)}catch(o){console.error("Execute failed:",o)}finally{y.value=!1,E.value=null}}};function M(o){const a=B(o,"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"),d=URL.createObjectURL(a),n=document.createElement("a");n.href=d,n.download=`import-report-${Date.now()}.xlsx`,n.click(),URL.revokeObjectURL(d)}function B(o,a="",d=512){const n=atob(o),v=[];for(let k=0;k<n.length;k+=d){const w=n.slice(k,k+d),P=new Array(w.length);for(let R=0;R<w.length;R++)P[R]=w.charCodeAt(R);const H=new Uint8Array(P);v.push(H)}return new Blob(v,{type:a})}const S={importExport:V,excelImport:u,include:A,brandSlugsInput:D,onConflict:l,createMissingRefs:x,file:p,loading:y,loadingTemplate:F,currentAction:E,dryRunResult:C,conflictOptions:G,formatFileSize:O,clearFile:j,onExport:h,onExportTemplate:N,onFileChange:z,fileToBase64:I,onDryRun:K,onExecute:X,downloadReport:M,b64toBlob:B,VCard:J,VCheckbox:Q,VInputText:W,VButton:Y,VSelect:Z,DangerButton:$};return Object.defineProperty(S,"__isScriptSetup",{enumerable:!1,value:!0}),S}}),ou={class:"w-full max-w-7xl"},ru={class:"grid grid-cols-1 xl:grid-cols-3 gap-6"},au={class:"space-y-4"},nu={class:"grid grid-cols-1 gap-2"},iu={class:"flex items-center space-x-2"},su={class:"flex items-center space-x-2"},du={class:"flex items-center space-x-2"},mu={class:"flex items-center space-x-2"},cu={class:"flex items-center space-x-2"},fu={class:"flex items-center space-x-2"},xu={class:"flex items-center space-x-2"},bu={class:"flex items-center space-x-2"},pu={class:"flex items-center space-x-2"},yu={class:"grid grid-cols-1 gap-2"},gu={class:"flex items-center space-x-2"},vu={class:"flex items-center space-x-2"},Eu={class:"flex items-center space-x-2"},Cu={class:"flex items-center space-x-2"},Vu={class:"flex items-center space-x-2"},Au={class:"flex flex-col gap-2"},Bu={class:"space-y-4"},ku={class:"flex items-center gap-2"},Ru={key:0,class:"mt-2 text-sm text-surface-600"},Du={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Fu={class:"flex items-center space-x-2 pt-6"},Iu={class:"flex flex-col gap-2"},wu={key:0,class:"space-y-3"},Uu={key:0,class:"grid grid-cols-2 gap-2"},Mu={class:"text-xs font-medium text-surface-700 dark:text-surface-300"},Su={class:"text-xs text-surface-600 dark:text-surface-400"},Tu={key:0},Pu={key:1},_u={key:2},qu={key:1,class:"space-y-2"},Lu={class:"max-h-32 overflow-y-auto space-y-1"},Gu={key:0,class:"text-xs text-surface-500"},Ou={key:2};function ju(L,e,V,u,A,D){return s(),m("div",ou,[t("div",ru,[r(u.VCard,{class:"p-6"},{title:c(()=>e[18]||(e[18]=[t("h2",{class:"text-lg font-semibold mb-4"},"Экспорт каталога",-1)])),content:c(()=>[t("div",au,[t("div",null,[e[28]||(e[28]=t("h3",{class:"text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"},"Основные сущности",-1)),t("div",nu,[t("div",iu,[r(u.VCheckbox,{modelValue:u.include.Brand,"onUpdate:modelValue":e[0]||(e[0]=l=>u.include.Brand=l),inputId:"brand",binary:!0},null,8,["modelValue"]),e[19]||(e[19]=t("label",{for:"brand",class:"text-sm"},"Бренды",-1))]),t("div",su,[r(u.VCheckbox,{modelValue:u.include.PartCategory,"onUpdate:modelValue":e[1]||(e[1]=l=>u.include.PartCategory=l),inputId:"partCategory",binary:!0},null,8,["modelValue"]),e[20]||(e[20]=t("label",{for:"partCategory",class:"text-sm"},"Категории запчастей",-1))]),t("div",du,[r(u.VCheckbox,{modelValue:u.include.AttributeGroup,"onUpdate:modelValue":e[2]||(e[2]=l=>u.include.AttributeGroup=l),inputId:"attributeGroup",binary:!0},null,8,["modelValue"]),e[21]||(e[21]=t("label",{for:"attributeGroup",class:"text-sm"},"Группы атрибутов",-1))]),t("div",mu,[r(u.VCheckbox,{modelValue:u.include.AttributeTemplate,"onUpdate:modelValue":e[3]||(e[3]=l=>u.include.AttributeTemplate=l),inputId:"attributeTemplate",binary:!0},null,8,["modelValue"]),e[22]||(e[22]=t("label",{for:"attributeTemplate",class:"text-sm"},"Шаблоны атрибутов",-1))]),t("div",cu,[r(u.VCheckbox,{modelValue:u.include.AttributeSynonymGroup,"onUpdate:modelValue":e[4]||(e[4]=l=>u.include.AttributeSynonymGroup=l),inputId:"synonymGroup",binary:!0},null,8,["modelValue"]),e[23]||(e[23]=t("label",{for:"synonymGroup",class:"text-sm"},"Группы синонимов",-1))]),t("div",fu,[r(u.VCheckbox,{modelValue:u.include.AttributeSynonym,"onUpdate:modelValue":e[5]||(e[5]=l=>u.include.AttributeSynonym=l),inputId:"synonym",binary:!0},null,8,["modelValue"]),e[24]||(e[24]=t("label",{for:"synonym",class:"text-sm"},"Синонимы атрибутов",-1))]),t("div",xu,[r(u.VCheckbox,{modelValue:u.include.Part,"onUpdate:modelValue":e[6]||(e[6]=l=>u.include.Part=l),inputId:"part",binary:!0},null,8,["modelValue"]),e[25]||(e[25]=t("label",{for:"part",class:"text-sm"},"Группы взаимозаменяемости",-1))]),t("div",bu,[r(u.VCheckbox,{modelValue:u.include.CatalogItem,"onUpdate:modelValue":e[7]||(e[7]=l=>u.include.CatalogItem=l),inputId:"catalogItem",binary:!0},null,8,["modelValue"]),e[26]||(e[26]=t("label",{for:"catalogItem",class:"text-sm"},"Каталожные позиции",-1))]),t("div",pu,[r(u.VCheckbox,{modelValue:u.include.EquipmentModel,"onUpdate:modelValue":e[8]||(e[8]=l=>u.include.EquipmentModel=l),inputId:"equipmentModel",binary:!0},null,8,["modelValue"]),e[27]||(e[27]=t("label",{for:"equipmentModel",class:"text-sm"},"Модели техники",-1))])])]),t("div",null,[e[34]||(e[34]=t("h3",{class:"text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"},"Атрибуты и связи",-1)),t("div",yu,[t("div",gu,[r(u.VCheckbox,{modelValue:u.include.PartAttribute,"onUpdate:modelValue":e[9]||(e[9]=l=>u.include.PartAttribute=l),inputId:"partAttribute",binary:!0},null,8,["modelValue"]),e[29]||(e[29]=t("label",{for:"partAttribute",class:"text-sm"},"Атрибуты запчастей",-1))]),t("div",vu,[r(u.VCheckbox,{modelValue:u.include.CatalogItemAttribute,"onUpdate:modelValue":e[10]||(e[10]=l=>u.include.CatalogItemAttribute=l),inputId:"catalogItemAttribute",binary:!0},null,8,["modelValue"]),e[30]||(e[30]=t("label",{for:"catalogItemAttribute",class:"text-sm"},"Атрибуты позиций",-1))]),t("div",Eu,[r(u.VCheckbox,{modelValue:u.include.EquipmentModelAttribute,"onUpdate:modelValue":e[11]||(e[11]=l=>u.include.EquipmentModelAttribute=l),inputId:"equipmentModelAttribute",binary:!0},null,8,["modelValue"]),e[31]||(e[31]=t("label",{for:"equipmentModelAttribute",class:"text-sm"},"Атрибуты техники",-1))]),t("div",Cu,[r(u.VCheckbox,{modelValue:u.include.PartApplicability,"onUpdate:modelValue":e[12]||(e[12]=l=>u.include.PartApplicability=l),inputId:"partApplicability",binary:!0},null,8,["modelValue"]),e[32]||(e[32]=t("label",{for:"partApplicability",class:"text-sm"},"Применимость запчастей",-1))]),t("div",Vu,[r(u.VCheckbox,{modelValue:u.include.EquipmentApplicability,"onUpdate:modelValue":e[13]||(e[13]=l=>u.include.EquipmentApplicability=l),inputId:"equipmentApplicability",binary:!0},null,8,["modelValue"]),e[33]||(e[33]=t("label",{for:"equipmentApplicability",class:"text-sm"},"Применимость к технике",-1))])])]),t("div",null,[e[35]||(e[35]=t("h3",{class:"text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"},"Фильтры",-1)),r(u.VInputText,{modelValue:u.brandSlugsInput,"onUpdate:modelValue":e[14]||(e[14]=l=>u.brandSlugsInput=l),placeholder:"Бренды через запятую (например: cat,komatsu)",class:"w-full"},null,8,["modelValue"])]),t("div",Au,[r(u.VButton,{onClick:u.onExport,disabled:u.loading,loading:u.loading,class:"w-full"},{default:c(()=>[g(i(u.loading?"Экспорт...":"Скачать данные"),1)]),_:1},8,["disabled","loading"]),r(u.VButton,{onClick:u.onExportTemplate,disabled:u.loading,loading:u.loadingTemplate,severity:"secondary",outlined:"",class:"w-full"},{default:c(()=>[g(i(u.loadingTemplate?"Создание...":"Скачать шаблон"),1)]),_:1},8,["disabled","loading"])])])]),_:1}),r(u.VCard,{class:"p-6"},{title:c(()=>e[36]||(e[36]=[t("h2",{class:"text-lg font-semibold mb-4"},"Импорт каталога",-1)])),content:c(()=>[t("div",Bu,[t("div",null,[e[38]||(e[38]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Файл Excel (.xlsx) ",-1)),t("div",ku,[t("input",{type:"file",onChange:u.onFileChange,accept:".xlsx",class:"flex-1 text-sm text-surface-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-primary-50 file:text-primary-700 hover:file:bg-primary-100"},null,32),u.file?(s(),tu(u.DangerButton,{key:0,onClick:u.clearFile,severity:"secondary",outlined:"",size:"small"},{default:c(()=>e[37]||(e[37]=[g(" Очистить ")])),_:1,__:[37]})):f("",!0)]),u.file?(s(),m("div",Ru," Выбран файл: "+i(u.file.name)+" ("+i(u.formatFileSize(u.file.size))+") ",1)):f("",!0)]),t("div",Du,[t("div",null,[e[39]||(e[39]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Режим конфликтов ",-1)),r(u.VSelect,{modelValue:u.onConflict,"onUpdate:modelValue":e[15]||(e[15]=l=>u.onConflict=l),options:u.conflictOptions,optionLabel:"label",optionValue:"value",placeholder:"Выберите режим",class:"w-full"},null,8,["modelValue"])]),t("div",Fu,[r(u.VCheckbox,{modelValue:u.createMissingRefs,"onUpdate:modelValue":e[16]||(e[16]=l=>u.createMissingRefs=l),inputId:"createMissingRefs",binary:!0},null,8,["modelValue"]),e[40]||(e[40]=t("label",{for:"createMissingRefs",class:"text-sm"},"Создавать отсутствующие ссылки",-1))])]),t("div",Iu,[r(u.VButton,{onClick:u.onDryRun,disabled:u.loading||!u.file,loading:u.loading&&u.currentAction==="dryRun",severity:"info",class:"w-full"},{default:c(()=>[g(i(u.loading&&u.currentAction==="dryRun"?"Проверка...":"Проверить файл (Dry Run)"),1)]),_:1},8,["disabled","loading"]),r(u.VButton,{onClick:u.onExecute,disabled:u.loading||!u.file,loading:u.loading&&u.currentAction==="execute",severity:"success",class:"w-full"},{default:c(()=>[g(i(u.loading&&u.currentAction==="execute"?"Выполняю...":"Выполнить импорт"),1)]),_:1},8,["disabled","loading"])]),u.dryRunResult?(s(),m("div",wu,[e[43]||(e[43]=t("h3",{class:"text-sm font-medium text-surface-700 dark:text-surface-300"},"Результат:",-1)),u.dryRunResult.perSheet?(s(),m("div",Uu,[(s(!0),m(_,null,q(u.dryRunResult.perSheet,(l,x)=>(s(),m("div",{key:x,class:"p-3 bg-surface-50 dark:bg-surface-900 rounded border"},[t("div",Mu,i(x),1),t("div",Su,[g(" Строк: "+i(l.rowsSeen)+" | Валидных: "+i(l.rowsValid)+" ",1),l.created?(s(),m("span",Tu,"| Создано: "+i(l.created),1)):f("",!0),l.updated?(s(),m("span",Pu,"| Обновлено: "+i(l.updated),1)):f("",!0),l.errorsCount?(s(),m("span",_u,"| Ошибок: "+i(l.errorsCount),1)):f("",!0)])]))),128))])):f("",!0),u.dryRunResult.errors?.length?(s(),m("div",qu,[e[41]||(e[41]=t("h4",{class:"text-sm font-medium text-red-700 dark:text-red-400"},"Ошибки:",-1)),t("div",Lu,[(s(!0),m(_,null,q(u.dryRunResult.errors.slice(0,10),(l,x)=>(s(),m("div",{key:x,class:"text-xs p-2 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 rounded"},i(l.sheet)+":"+i(l.rowIndex)+" - "+i(l.message),1))),128)),u.dryRunResult.errors.length>10?(s(),m("div",Gu," ... и еще "+i(u.dryRunResult.errors.length-10)+" ошибок ",1)):f("",!0)])])):f("",!0),u.dryRunResult.reportBase64?(s(),m("div",Ou,[r(u.VButton,{onClick:e[17]||(e[17]=l=>u.downloadReport(u.dryRunResult.reportBase64)),severity:"secondary",outlined:"",size:"small"},{default:c(()=>e[42]||(e[42]=[g(" Скачать отчёт ")])),_:1,__:[42]})])):f("",!0)])):f("",!0)])]),_:1}),r(u.VCard,{class:"p-6"},{title:c(()=>e[44]||(e[44]=[t("h2",{class:"text-lg font-semibold mb-4"},"История импортов",-1)])),content:c(()=>e[45]||(e[45]=[t("div",{class:"text-sm text-surface-600 dark:text-surface-400"}," Функционал истории импортов будет добавлен в следующей итерации ",-1)])),_:1})])])}const x4=uu(lu,[["render",ju]]);export{x4 as default};
