import { PrismaClient } from '@prisma/client'
import { enhance } from '@zenstackhq/runtime'
import type { ExtendedUser } from './types/auth'
import { 
  createAttributeMiddleware, 
  updateAttributeMiddleware, 
  upsertAttributeMiddleware 
} from './lib/attribute-value-sync'
import { resolveUrlToFilePath, deleteFileIfExists } from './lib/media'

const basePrisma = new PrismaClient({
  log: ["query", "info", "warn", "error"],
});

// Расширяем Prisma Client с автоматической синхронизацией value <-> numericValue
const prisma = basePrisma.$extends({
  query: {
    partAttribute: {
      create: createAttributeMiddleware('partAttribute'),
      update: updateAttributeMiddleware('partAttribute'),
      upsert: upsertAttributeMiddleware('partAttribute'),
      createMany: createAttributeMiddleware('partAttribute'),
      updateMany: updateAttributeMiddleware('partAttribute'),
    },
    catalogItemAttribute: {
      create: createAttributeMiddleware('catalogItemAttribute'),
      update: updateAttributeMiddleware('catalogItemAttribute'),
      upsert: upsertAttributeMiddleware('catalogItemAttribute'),
      createMany: createAttributeMiddleware('catalogItemAttribute'),
      updateMany: updateAttributeMiddleware('catalogItemAttribute'),
    },
    equipmentModelAttribute: {
      create: createAttributeMiddleware('equipmentModelAttribute'),
      update: updateAttributeMiddleware('equipmentModelAttribute'),
      upsert: upsertAttributeMiddleware('equipmentModelAttribute'),
      createMany: createAttributeMiddleware('equipmentModelAttribute'),
      updateMany: updateAttributeMiddleware('equipmentModelAttribute'),
    },

    // Автоматическое удаление связанных изображений при удалении Part
    part: {
      async delete({ args, query }) {
        const toDelete = await basePrisma.part.findUnique({
          where: (args as any).where,
          include: { image: true },
        })
        const result = await query(args)
        if (toDelete?.imageId && toDelete.image?.url) {
          try { await basePrisma.mediaAsset.delete({ where: { id: toDelete.imageId } }) } catch {}
          const path = resolveUrlToFilePath(toDelete.image.url)
          await deleteFileIfExists(path)
        }
        return result
      },
      async deleteMany({ args, query }) {
        const toDelete = await basePrisma.part.findMany({
          where: (args as any).where,
          select: { imageId: true, image: { select: { url: true } } },
        })
        const result = await query(args)
        for (const item of toDelete) {
          if (item.imageId && item.image?.url) {
            try { await basePrisma.mediaAsset.delete({ where: { id: item.imageId } }) } catch {}
            const path = resolveUrlToFilePath(item.image.url)
            await deleteFileIfExists(path)
          }
        }
        return result
      },
    },

    // Автоматическое удаление связанных изображений при удалении PartCategory
    partCategory: {
      async delete({ args, query }) {
        const toDelete = await basePrisma.partCategory.findUnique({
          where: (args as any).where,
          include: { image: true },
        })
        const result = await query(args)
        if (toDelete?.imageId && toDelete.image?.url) {
          try { await basePrisma.mediaAsset.delete({ where: { id: toDelete.imageId } }) } catch {}
          const path = resolveUrlToFilePath(toDelete.image.url)
          await deleteFileIfExists(path)
        }
        return result
      },
      async deleteMany({ args, query }) {
        const toDelete = await basePrisma.partCategory.findMany({
          where: (args as any).where,
          select: { imageId: true, image: { select: { url: true } } },
        })
        const result = await query(args)
        for (const item of toDelete) {
          if (item.imageId && item.image?.url) {
            try { await basePrisma.mediaAsset.delete({ where: { id: item.imageId } }) } catch {}
            const path = resolveUrlToFilePath(item.image.url)
            await deleteFileIfExists(path)
          }
        }
        return result
      },
    },

    // Если удаляют MediaAsset напрямую — чистим файл на диске
    mediaAsset: {
      async delete({ args, query }) {
        const toDelete = await basePrisma.mediaAsset.findUnique({ where: (args as any).where })
        const result = await query(args)
        if (toDelete?.url) {
          const path = resolveUrlToFilePath(toDelete.url)
          await deleteFileIfExists(path)
        }
        return result
      },
      async deleteMany({ args, query }) {
        const toDelete = await basePrisma.mediaAsset.findMany({ where: (args as any).where })
        const result = await query(args)
        for (const item of toDelete) {
          if (item.url) {
            const path = resolveUrlToFilePath(item.url)
            await deleteFileIfExists(path)
          }
        }
        return result
      },
    },
  },
}) as unknown as PrismaClient;

// Экспорт обычного Prisma клиента для системных операций
export const $prisma = prisma

// Тип для контекста авторизации ZenStack
export interface AuthContext {
  user: ExtendedUser | null
}

/**
 * Создает enhanced Prisma клиент с контекстом пользователя
 * Автоматически применяет правила авторизации из schema.zmodel
 */
export function getEnhancedDB(user: ExtendedUser | null = null) {
  return enhance(prisma, { user: (user ?? undefined) as any });
}

/**
 * Создает публичный DB клиент для неавторизованных пользователей
 * Доступны только данные с правилом @@allow('read', true)
 */
export function getPublicDB() {
  return enhance(prisma, { user: undefined as any });
}

/**
 * Системный DB клиент без ограничений авторизации
 * Использовать ТОЛЬКО для внутренних операций (миграции, сиды, админ-панель)
 * ⚠️ ОСТОРОЖНО: Обходит все правила безопасности!
 */
export function getSystemDB() {
  return prisma;
}
