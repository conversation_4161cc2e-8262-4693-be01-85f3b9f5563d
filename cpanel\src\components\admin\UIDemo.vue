<template>
  <div class="space-y-8">
    <!-- Заголовок -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-[--color-foreground] mb-2">UI Компоненты</h1>
      <p class="text-[--color-muted]">
        Демонстрация всех доступных UI компонентов с поддержкой тем
      </p>
    </div>

    <!-- Breadcrumb -->
    <div class="bg-[--color-card] rounded-[--radius-lg] p-6 border border-[--color-border]">
      <h2 class="text-xl font-semibold text-[--color-foreground] mb-4">Breadcrumb</h2>
      <Breadcrumb :model="breadcrumbItems" />
    </div>

    <!-- Кнопки и переключатель тем -->
    <div class="bg-[--color-card] rounded-[--radius-lg] p-6 border border-[--color-border]">
      <h2 class="text-xl font-semibold text-[--color-foreground] mb-4">Кнопки и переключатель тем</h2>
      <div class="flex flex-wrap gap-4 items-center">
        <Button label="Основная кнопка" />
        <SecondaryButton label="Вторичная кнопка" />
        <SecondaryButton label="С иконкой" icon="pi pi-plus" />
        <ThemeToggle mode="buttons" show-label />
      </div>
    </div>

    <!-- Toast уведомления -->
    <div class="bg-[--color-card] rounded-[--radius-lg] p-6 border border-[--color-border]">
      <h2 class="text-xl font-semibold text-[--color-foreground] mb-4">Toast уведомления</h2>
      <div class="flex flex-wrap gap-4">
        <SecondaryButton 
          label="Успех" 
          @click="showSuccessToast"
          class="bg-[--p-success-color] text-[--p-success-contrast-color] hover:bg-[color-mix(in_srgb,var(--p-success-color),black_10%)]"
        />
        <SecondaryButton 
          label="Информация" 
          @click="showInfoToast"
          class="bg-[--p-primary-color] text-[--p-primary-contrast-color] hover:bg-[color-mix(in_srgb,var(--p-primary-color),black_10%)]"
        />
        <SecondaryButton 
          label="Предупреждение" 
          @click="showWarnToast"
          class="bg-[--p-warning-color] text-[--p-warning-contrast-color] hover:bg-[color-mix(in_srgb,var(--p-warning-color),black_10%)]"
        />
        <SecondaryButton 
          label="Ошибка" 
          @click="showErrorToast"
          class="bg-[--p-danger-color] text-[--p-danger-contrast-color] hover:bg-[color-mix(in_srgb,var(--p-danger-color),black_10%)]"
        />
      </div>
    </div>

    <!-- Диалоги -->
    <div class="bg-[--color-card] rounded-[--radius-lg] p-6 border border-[--color-border]">
      <h2 class="text-xl font-semibold text-[--color-foreground] mb-4">Диалоги</h2>
      <div class="flex flex-wrap gap-4">
        <SecondaryButton 
          label="Обычный диалог" 
          @click="showDialog"
        />
        <SecondaryButton 
          label="Подтверждение удаления" 
          @click="showDeleteConfirm"
          class="bg-[--p-danger-color] text-[--p-danger-contrast-color] hover:bg-[color-mix(in_srgb,var(--p-danger-color),black_10%)]"
        />
        <SecondaryButton 
          label="Подтверждение сохранения" 
          @click="showSaveConfirm"
          class="bg-[--p-success-color] text-[--p-success-contrast-color] hover:bg-[color-mix(in_srgb,var(--p-success-color),black_10%)]"
        />
      </div>
    </div>

    <!-- DataTable -->
    <div class="bg-[--color-card] rounded-[--radius-lg] p-6 border border-[--color-border]">
      <h2 class="text-xl font-semibold text-[--color-foreground] mb-4">DataTable</h2>
      <DataTable 
        :value="tableData" 
        paginator 
        :rows="5"
        :rowsPerPageOptions="[5, 10, 20]"
        tableStyle="min-width: 50rem"
      >
        <Column field="id" header="ID" sortable style="width: 10%"></Column>
        <Column field="name" header="Название" sortable style="width: 30%"></Column>
        <Column field="category" header="Категория" sortable style="width: 25%"></Column>
        <Column field="status" header="Статус" style="width: 15%">
          <template #body="slotProps">
            <span
              :class="{
                'px-2 py-1 rounded-full text-xs bg-[color-mix(in_srgb,var(--p-success-color),transparent_85%)] text-[--p-success-color]': slotProps.data.status === 'Активен',
                'px-2 py-1 rounded-full text-xs bg-[color-mix(in_srgb,var(--p-danger-color),transparent_85%)] text-[--p-danger-color]': slotProps.data.status === 'Неактивен',
                'px-2 py-1 rounded-full text-xs bg-[color-mix(in_srgb,var(--p-warning-color),transparent_85%)] text-[--p-warning-color]': slotProps.data.status === 'Ожидание'
              }"
            >
              {{ slotProps.data.status }}
            </span>
          </template>
        </Column>
        <Column header="Действия" style="width: 20%">
          <template #body="slotProps">
            <div class="flex gap-2">
              <SecondaryButton 
                icon="pi pi-pencil" 
                text 
                size="small"
                @click="editItem(slotProps.data)"
              />
              <SecondaryButton 
                icon="pi pi-trash" 
                text 
                size="small"
                class="text-red-500 hover:text-red-700"
                @click="deleteItem(slotProps.data)"
              />
            </div>
          </template>
        </Column>
      </DataTable>
    </div>

    <!-- Диалог -->
    <Dialog 
      v-model:visible="dialogVisible" 
      modal 
      header="Пример диалога"
      style="width: 25rem"
    >
      <p class="text-[--color-muted] mb-4">
        Это пример диалогового окна с поддержкой тем.
      </p>
      <div class="flex justify-end gap-2">
        <SecondaryButton 
          label="Отмена" 
          @click="dialogVisible = false"
        />
        <Button 
          label="Сохранить" 
          @click="dialogVisible = false"
        />
      </div>
    </Dialog>

    <!-- Toast контейнер -->
    <Toast />

    <!-- ConfirmDialog -->
    <ConfirmDialog />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useToast } from '@/composables/useToast'
import { useConfirm } from '@/composables/useConfirm'

// Импорт компонентов
import Button from '@/volt/Button.vue'
import SecondaryButton from '@/volt/SecondaryButton.vue'
import DataTable from '@/volt/DataTable.vue'
import Column from 'primevue/column'
import Dialog from '@/volt/Dialog.vue'
import Toast from '@/volt/Toast.vue'
import ConfirmDialog from '@/volt/ConfirmDialog.vue'
import Breadcrumb from '@/volt/Breadcrumb.vue'
import ThemeToggle from '@/components/ui/ThemeToggle.vue'

// Composables
const toast = useToast()
const confirm = useConfirm()

// Состояние
const dialogVisible = ref(false)

// Данные для breadcrumb
const breadcrumbItems = ref([
  { label: 'Админ панель', url: '/admin' },
  { label: 'UI Компоненты' }
])

// Данные для таблицы
const tableData = ref([
  { id: 1, name: 'Сальник коленвала', category: 'Двигатель', status: 'Активен' },
  { id: 2, name: 'Фильтр масляный', category: 'Система смазки', status: 'Активен' },
  { id: 3, name: 'Прокладка ГБЦ', category: 'Двигатель', status: 'Неактивен' },
  { id: 4, name: 'Тормозные колодки', category: 'Тормозная система', status: 'Ожидание' },
  { id: 5, name: 'Амортизатор передний', category: 'Подвеска', status: 'Активен' },
  { id: 6, name: 'Свеча зажигания', category: 'Система зажигания', status: 'Активен' },
  { id: 7, name: 'Ремень ГРМ', category: 'Двигатель', status: 'Неактивен' },
  { id: 8, name: 'Радиатор охлаждения', category: 'Система охлаждения', status: 'Активен' }
])

// Методы для Toast
const showSuccessToast = () => {
  toast.success('Успешно!', 'Операция выполнена успешно')
}

const showInfoToast = () => {
  toast.info('Информация', 'Это информационное сообщение')
}

const showWarnToast = () => {
  toast.warn('Внимание!', 'Это предупреждение')
}

const showErrorToast = () => {
  toast.error('Ошибка!', 'Произошла ошибка при выполнении операции')
}

// Методы для диалогов
const showDialog = () => {
  dialogVisible.value = true
}

const showDeleteConfirm = () => {
  confirm.confirmDelete('запись', () => {
    toast.success('Удалено', 'Запись успешно удалена')
  })
}

const showSaveConfirm = () => {
  confirm.confirmSave('Сохранить изменения?', () => {
    toast.success('Сохранено', 'Изменения успешно сохранены')
  })
}

// Методы для таблицы
const editItem = (item: any) => {
  toast.info('Редактирование', `Редактирование записи: ${item.name}`)
}

const deleteItem = (item: any) => {
  confirm.confirmDelete(`запись "${item.name}"`, () => {
    toast.success('Удалено', `Запись "${item.name}" успешно удалена`)
  })
}
</script>
