version: '3.8'

services:
  # База данных PostgreSQL
  postgres:
    image: postgres:17-alpine
    container_name: parttec-postgres
    environment:
      POSTGRES_DB: parttec
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres}
    volumes:
      - ./data/postgres:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - parttec-network
    restart: unless-stopped



  # API сервис (Bun + Hono + tRPC)
  api:
    build:
      context: ./api
      dockerfile: Dockerfile
    container_name: parttec-api
    environment:
      - NODE_ENV=production
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD:-postgres}@postgres:5432/parttec
      - BETTER_AUTH_SECRET=${BETTER_AUTH_SECRET}
      - BETTER_AUTH_URL=${API_URL:-http://localhost:3000}
    ports:
      - "3000:3000"
    depends_on:
      - postgres
    volumes:
      - ./data/media:/app/uploads
    networks:
      - parttec-network
    restart: unless-stopped

  # CPPanel (Astro + Vue)
  cpanel:
    build:
      context: ./cpanel
      dockerfile: Dockerfile
    container_name: parttec-cpanel
    environment:
      - NODE_ENV=production
      - PUBLIC_API_URL=${API_URL:-http://localhost:3000}
    ports:
      - "4322:4322"
    depends_on:
      - api
    networks:
      - parttec-network
    restart: unless-stopped

  # Site (Astro + React)
  site:
    build:
      context: ./site
      dockerfile: Dockerfile
    container_name: parttec-site
    environment:
      - NODE_ENV=production
      - PUBLIC_API_URL=${API_URL:-http://localhost:3000}
    ports:
      - "4323:4323"
    depends_on:
      - api
    networks:
      - parttec-network
    restart: unless-stopped

# Volumes больше не нужны - используем bind mounts

networks:
  parttec-network:
    driver: bridge
