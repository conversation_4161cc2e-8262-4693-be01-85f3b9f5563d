// Вспомогательные ключи для TanStack Vue Query, стабилизированные для tRPC
export const qk = {
  parts: {
    list: (input?: Record<string, any>) => ['trpc', 'catalog.listParts', input ?? {}] as const,
    detail: (input: { where: { id: number }; include?: Record<string, any> }) => ['trpc', 'crud.part.findUnique', input] as const,
    count: (input?: Record<string, any>) => ['trpc', 'catalog.countParts', input ?? {}] as const,
  },
  brands: {
    list: (input?: Record<string, any>) => ['trpc', 'catalog.listBrands', input ?? {}] as const,
  },
  categories: {
    list: (input?: Record<string, any>) => ['trpc', 'catalog.listCategories', input ?? {}] as const,
  },
  search: {
    parts: (input?: Record<string, any>) => ['trpc', 'search.searchParts', input ?? {}] as const,
  },
  attributeTemplates: {
    list: (input?: Record<string, any>) => ['trpc', 'crud.attributeTemplate.findMany', input ?? {}] as const,
  },
  attributeGroups: {
    list: (input?: Record<string, any>) => ['trpc', 'crud.attributeGroup.findMany', input ?? {}] as const,
  },
  partApplicability: {
    list: (input?: Record<string, any>) => ['trpc', 'crud.partApplicability.findMany', input ?? {}] as const,
  },
  stats: {
    parts: () => ['trpc', 'crud.part.count'] as const,
    brands: () => ['trpc', 'crud.brand.count'] as const,
    categories: () => ['trpc', 'crud.partCategory.count'] as const,
  },
};

