<template>
  <div class="relative">
    <div v-if="$slots.icon" class="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400">
      <slot name="icon" />
    </div>
    
    <input
      :class="inputClasses"
      :type="type"
      :placeholder="placeholder"
      :disabled="disabled"
      :value="modelValue"
      @input="$emit('update:modelValue', ($event.target as HTMLInputElement).value)"
      @focus="$emit('focus', $event)"
      @blur="$emit('blur', $event)"
      v-bind="$attrs"
    />
    
    <div v-if="$slots.suffix" class="absolute right-4 top-1/2 transform -translate-y-1/2">
      <slot name="suffix" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  modelValue?: string
  type?: string
  placeholder?: string
  disabled?: boolean
  size?: 'sm' | 'md' | 'lg'
  variant?: 'default' | 'filled'
}

const props = withDefaults(defineProps<Props>(), {
  type: 'text',
  size: 'md',
  variant: 'default'
})

defineEmits<{
  'update:modelValue': [value: string]
  focus: [event: FocusEvent]
  blur: [event: FocusEvent]
}>()

const inputClasses = computed(() => {
  const base = 'w-full bg-[--color-card] border border-[--color-border] rounded-[--radius-md] text-[--color-foreground] placeholder-[--color-muted] focus:outline-none focus:ring-2 focus:ring-[--color-ring] focus:border-[--color-ring] transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed'
  
  const sizes = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-3 text-base',
    lg: 'px-4 py-4 text-lg'
  }
  
  const variants = {
    default: '',
    filled: 'bg-[--p-content-hover-background] border-[--color-border]'
  }
  
  const iconPadding = props.$slots?.icon ? 'pl-12' : ''
  const suffixPadding = props.$slots?.suffix ? 'pr-12' : ''
  
  return `${base} ${sizes[props.size]} ${variants[props.variant]} ${iconPadding} ${suffixPadding}`
})
</script>