import { readFileSync, writeFileSync } from 'node:fs';
import { dirname, resolve } from 'node:path';

type JsonRecord = Record<string, unknown>;

function getFlagValue(flag: string, aliases: string[] = []): string | undefined {
    const argv = process.argv.slice(2);
    const all = [flag, ...aliases];
    for (const f of all) {
        const idx = argv.indexOf(f)

        if (idx !== -1 && idx + 1 < argv.length) return argv[idx + 1];
        // Support --flag=value form
        const withEq = argv.find((a) => a.startsWith(`${f}=`));
        if (withEq) return withEq.substring(withEq.indexOf('=') + 1);
    }
    return undefined;
}

function normalizeSeparators(value: string): string {
    // Replace commas, pipes and spaces between emails with semicolons, normalize whitespace
    return value.replace(/[\s,|]+/g, ';');
}

function extractFirstEmail(input: unknown): string | null {
    if (typeof input !== 'string') return null;
    const normalized = normalizeSeparators(input.trim());
    if (!normalized) return null;

    const parts = normalized
        .split(';')
        .map((s) => s.trim())
        .filter((s) => s.length > 0);

    const emailRegex = /[A-Za-z0-9._%+\-]+@[A-Za-z0-9.\-]+\.[A-Za-z]{2,}/;
    for (const part of parts) {
        const match = part.match(emailRegex);
        if (match && match[0]) return match[0];
    }
    return null;
}

function findEmailKey(record: JsonRecord, preferredKeys: string[]): string | null {
    for (const key of preferredKeys) {
        if (typeof record[key] === 'string') return key;
    }
    const dynamic = Object.keys(record).find((k) => /mail/i.test(k) && typeof record[k] === 'string');
    return dynamic ?? null;
}

function main(): void {
    const inputPath = getFlagValue('--in', ['--input']) ?? 'api/_temp/Книга1.json';
    const outputPath = getFlagValue('--out', ['--output']) ?? 'api/_temp/Книга1.cleaned.json';
    const emailKeyArg = getFlagValue('--emailKey') ?? 'E-mail 1';

    const resolvedIn = resolve(process.cwd(), inputPath);
    const resolvedOut = resolve(process.cwd(), outputPath);
    const outDir = dirname(resolvedOut);

    const raw = readFileSync(resolvedIn, 'utf8');
    const json: unknown = JSON.parse(raw);

    if (!Array.isArray(json)) {
        throw new Error('Ожидался JSON-массив записей.');
    }

    const preferredKeys = [emailKeyArg, 'E-mail', 'Email', 'email'];

    const cleaned: JsonRecord[] = [];
    let processed = 0;
    let kept = 0;

    for (const item of json) {
        processed += 1;
        if (item == null || typeof item !== 'object') continue;

        const record: JsonRecord = { ...(item as JsonRecord) };
        const emailKey = findEmailKey(record, preferredKeys);
        if (!emailKey) continue;

        const firstEmail = extractFirstEmail(record[emailKey]);
        if (!firstEmail) continue;

        // Сохраняем первый email, остальные отбрасываем. Сохраняем исходное имя поля.
        record[emailKey] = firstEmail;

        cleaned.push(record);
        kept += 1;
    }

    // Пишем красиво с отступами
    const output = JSON.stringify(cleaned, null, 2) + '\n';
    // Ensure directory exists (Bun/Node will error if dir missing). For simplicity, rely on existing dir.
    writeFileSync(resolvedOut, output, 'utf8');

    // Лаконичная сводка в консоль
    console.log(`Обработано записей: ${processed}`);
    console.log(`Сохранено с email: ${kept}`);
    console.log(`Вывод: ${resolvedOut}`);
}

main();

