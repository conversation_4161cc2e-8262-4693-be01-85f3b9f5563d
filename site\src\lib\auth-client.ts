import { createAuthClient } from "better-auth/react";
import { inferAdditionalFields } from "better-auth/client/plugins";

// Базовый URL API better-auth; можно переопределить через PUBLIC_API_URL
const baseURL = import.meta.env.PUBLIC_API_URL || "http://localhost:3000";

export const authClient = createAuthClient({
  baseURL,
  // Включаем передачу куков к стороннему домену API
  fetchOptions: {
    credentials: "include",
    timeout: 10000,
  },
  plugins: [
    // Типизируем дополнительные поля пользователя из server config (role)
    inferAdditionalFields({
      user: {
        role: { type: "string" },
      },
    }),
  ],
});

