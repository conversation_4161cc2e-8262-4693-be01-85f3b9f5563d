import type { CatalogSearchFilters } from "@/types/catalog"

// Глобальное состояние каталога через localStorage и события
export class CatalogStateManager {
  private static readonly STORAGE_KEY = 'catalog-search-filters'
  private static readonly EVENT_NAME = 'catalog-filters-changed'

  static getFilters(): CatalogSearchFilters {
    if (typeof window === 'undefined') {
      return this.getDefaultFilters()
    }

    try {
      const stored = localStorage.getItem(this.STORAGE_KEY)
      if (stored) {
        return { ...this.getDefaultFilters(), ...JSON.parse(stored) }
      }
    } catch (error) {
      console.warn('Failed to parse stored catalog filters:', error)
    }

    return this.getDefaultFilters()
  }

  static setFilters(filters: CatalogSearchFilters): void {
    if (typeof window === 'undefined') return

    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(filters))
      window.dispatchEvent(new CustomEvent(this.EVENT_NAME, { detail: filters }))
    } catch (error) {
      console.warn('Failed to store catalog filters:', error)
    }
  }

  static updateFilters(updates: Partial<CatalogSearchFilters>): void {
    const current = this.getFilters()
    this.setFilters({ ...current, ...updates })
  }

  static clearFilters(): void {
    this.setFilters(this.getDefaultFilters())
  }

  static subscribe(callback: (filters: CatalogSearchFilters) => void): () => void {
    if (typeof window === 'undefined') return () => {}

    const handler = (event: CustomEvent<CatalogSearchFilters>) => {
      callback(event.detail)
    }

    window.addEventListener(this.EVENT_NAME, handler as EventListener)
    
    return () => {
      window.removeEventListener(this.EVENT_NAME, handler as EventListener)
    }
  }

  private static getDefaultFilters(): CatalogSearchFilters {
    return {
      query: "",
      categoryIds: [],
      brandIds: [],
      attributeFilters: {},
      accuracyLevels: [],
      isOemOnly: false,
    }
  }
}

// Хук для использования в React компонентах
import { useState, useEffect } from "react"

export function useCatalogGlobalState() {
  const [filters, setFiltersState] = useState<CatalogSearchFilters>(() => 
    CatalogStateManager.getFilters()
  )

  useEffect(() => {
    const unsubscribe = CatalogStateManager.subscribe(setFiltersState)
    return unsubscribe
  }, [])

  const setFilters = (newFilters: CatalogSearchFilters) => {
    CatalogStateManager.setFilters(newFilters)
  }

  const updateFilters = (updates: Partial<CatalogSearchFilters>) => {
    CatalogStateManager.updateFilters(updates)
  }

  const clearFilters = () => {
    CatalogStateManager.clearFilters()
  }

  return {
    filters,
    setFilters,
    updateFilters,
    clearFilters,
  }
}
