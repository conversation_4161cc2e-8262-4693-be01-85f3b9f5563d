import{j as r}from"./jsx-runtime.D_zvdyIk.js";import{r as d}from"./index.0yr9KlQE.js";import{c as n}from"./index.3rXK4OIH.js";import{c as g}from"./utils.CBfrqCZ4.js";import{B as T}from"./badge.CrhVNNKw.js";const l=n("inline-flex items-center gap-1 font-medium",{variants:{status:{EXACT_MATCH:"bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800",MATCH_WITH_NOTES:"bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800",REQUIRES_MODIFICATION:"bg-orange-100 text-orange-800 border-orange-200 dark:bg-orange-900/20 dark:text-orange-400 dark:border-orange-800",PARTIAL_MATCH:"bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800"},size:{sm:"text-xs px-2 py-1",default:"text-sm px-2.5 py-1.5",lg:"text-base px-3 py-2"}},defaultVariants:{status:"EXACT_MATCH",size:"default"}}),x={EXACT_MATCH:"Точное совпадение",MATCH_WITH_NOTES:"С примечаниями",REQUIRES_MODIFICATION:"Требует доработки",PARTIAL_MATCH:"Частичное совпадение"},A={EXACT_MATCH:"●",MATCH_WITH_NOTES:"◐",REQUIRES_MODIFICATION:"◑",PARTIAL_MATCH:"○"},i=d.forwardRef(({className:t,status:e,size:a,...s},o)=>r.jsxs(T,{ref:o,className:g(l({status:e,size:a,className:t})),variant:"outline",...s,children:[r.jsx("span",{className:"text-current",children:A[e]}),x[e]]}));i.displayName="StatusBadge";export{i as S};
