# PartTec3 - Клиентская часть

Клиентская часть каталога взаимозаменяемых запчастей PartTec3, построенная на Astro + React + shadcn/ui + Tailwind CSS 4.

## 🚀 Технологический стек

- **Astro 5** - основной фреймворк
- **React 19** - для интерактивных компонентов (islands)
- **shadcn/ui** - библиотека UI компонентов
- **Tailwind CSS 4** - стилизация
- **tRPC** - типобезопасное API
- **React Query** - кеширование данных
- **Lucide React** - иконки

## 📁 Структура проекта

```text
site/
├── src/
│   ├── components/
│   │   ├── catalog/          # Компоненты каталога
│   │   │   ├── PartCard.tsx
│   │   │   ├── CategoryCard.tsx
│   │   │   ├── BrandCard.tsx
│   │   │   └── SearchForm.tsx
│   │   ├── providers/        # React провайдеры
│   │   │   └── TrpcProvider.tsx
│   │   └── ui/              # shadcn/ui компоненты
│   │       ├── button.tsx
│   │       ├── card.tsx
│   │       ├── input.tsx
│   │       └── badge.tsx
│   ├── layouts/
│   │   └── MainLayout.astro  # Основной layout
│   ├── lib/
│   │   ├── trpc.ts          # tRPC клиент
│   │   └── utils.ts         # Утилиты
│   ├── pages/               # Страницы приложения
│   │   ├── index.astro      # Главная страница
│   │   ├── catalog.astro    # Каталог запчастей
│   │   ├── categories.astro # Список категорий
│   │   ├── brands.astro     # Список брендов
│   │   ├── search.astro     # Поиск
│   │   └── catalog/
│   │       ├── parts/[id].astro        # Детальная страница запчасти
│   │       ├── categories/[slug].astro # Страница категории
│   │       └── brands/[slug].astro     # Страница бренда
│   ├── styles/
│   │   └── global.css       # Глобальные стили
│   └── types/
│       └── catalog.ts       # TypeScript типы
└── package.json
```

## 🎯 Реализованный функционал

### Основные страницы
- **Главная** (`/`) - обзор каталога, статистика, популярные категории
- **Каталог** (`/catalog`) - список запчастей с фильтрами и поиском
- **Категории** (`/categories`) - иерархический каталог категорий
- **Бренды** (`/brands`) - список производителей (OEM и Aftermarket)
- **Поиск** (`/search`) - расширенный поиск по всем сущностям

### Детальные страницы
- **Группа запчастей** (`/catalog/parts/[id]`) - подробная информация о группе взаимозаменяемости
- **Категория** (`/catalog/categories/[slug]`) - запчасти и подкатегории
- **Бренд** (`/catalog/brands/[slug]`) - каталог запчастей и модели техники

### Компоненты
- **PartCard** - карточка группы запчастей с атрибутами
- **CategoryCard** - карточка категории с иерархией
- **BrandCard** - карточка бренда с типом (OEM/Aftermarket)
- **SearchForm** - форма поиска с автодополнением

### Особенности
- **Типобезопасность** - полная типизация через tRPC и TypeScript
- **Responsive дизайн** - адаптивная верстка для всех устройств
- **Навигация** - хлебные крошки и интуитивная навигация
- **Фильтрация** - по категориям, брендам, атрибутам
- **Пагинация** - для больших списков данных
- **Кеширование** - React Query для оптимизации запросов

## 🧞 Команды

```bash
# Установка зависимостей
npm install

# Запуск dev сервера
npm run dev

# Сборка для продакшена
npm run build

# Предварительный просмотр сборки
npm run preview
```

## 🔗 Интеграция с API

Клиентская часть подключается к API серверу через tRPC:
- **URL API**: `http://localhost:3000/trpc`
- **Автогенерированные роуты**: используются CRUD операции из ZenStack
- **Кастомные роуты**: поиск, фильтрация, статистика

## 📋 Схема данных

Основные сущности каталога:
- **Part** - группы взаимозаменяемости
- **PartCategory** - иерархические категории
- **CatalogItem** - конкретные артикулы от производителей
- **Brand** - производители (OEM и Aftermarket)
- **EquipmentModel** - модели техники
- **Attributes** - динамические характеристики

## 📝 Примечания

- Следует принципу DRY - нет дублирования компонентов
- Максимально простой и прозрачный код
- Использует автогенерированные Zod схемы и tRPC роуты
- Реализован только реальный функционал без демо данных
