<template>
  <i
    :class="iconClass"
    :style="iconStyle"
    v-bind="$attrs"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  name: string;
  size?: number | string;
  color?: string;
}

const props = withDefaults(defineProps<Props>(), {
  size: 16
});

// Simple icon mapping - in a real app you'd use a proper icon library
const iconMap: Record<string, string> = {
  'plus': '➕',
  'edit': '✏️',
  'trash': '🗑️',
  'search': '🔍',
  'filter': '🔽',
  'check': '✅',
  'x': '❌',
  'chevron-up': '⬆️',
  'chevron-down': '⬇️',
  'folder': '📁',
  'ruler': '📏',
  'tag': '🏷️',
  'template': '📋',
  'info': 'ℹ️',
  'target': '🎯',
  'grid': '⊞',
  'list': '☰'
};

const iconClass = computed(() => {
  return 'inline-block';
});

const iconStyle = computed(() => {
  return {
    fontSize: typeof props.size === 'number' ? `${props.size}px` : props.size,
    color: props.color
  };
});

const iconContent = computed(() => {
  return iconMap[props.name] || '?';
});
</script>

<style scoped>
i {
  font-style: normal;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

i::before {
  content: v-bind(iconContent);
}
</style>