import React from "react";
import { authClient } from "@/lib/auth-client";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

interface Props {
  initialUser: any;
}

export default function AccountCard({ initialUser }: Props) {
  const { data: session } = authClient.useSession();
  const user = session?.user ?? initialUser;

  return (
    <Card>
      <CardContent className="p-6 space-y-4">
        <div className="space-y-1">
          <div className="text-sm text-muted-foreground">Email</div>
          <div className="font-medium">{user?.email}</div>
        </div>
        {user?.name && (
          <div className="space-y-1">
            <div className="text-sm text-muted-foreground">Имя</div>
            <div className="font-medium">{user?.name}</div>
          </div>
        )}
        {user?.role && (
          <div className="space-y-1">
            <div className="text-sm text-muted-foreground">Роль</div>
            <div className="font-medium">{user?.role}</div>
          </div>
        )}
        <div>
          <Button
            variant="secondary"
            onClick={async () => {
              await authClient.signOut({
                fetchOptions: {
                  onSuccess: () => (window.location.href = "/"),
                },
              });
            }}
          >
            Выйти
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

