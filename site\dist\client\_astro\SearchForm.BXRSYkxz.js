import{j as t}from"./jsx-runtime.D_zvdyIk.js";import{r as p}from"./index.0yr9KlQE.js";import{I as u}from"./input.aGo1BNk4.js";import{B as f}from"./button.BfGcEciz.js";import{n as x}from"./router.WLLD8StG.js";import{S as d}from"./search.ooeMVQga.js";import"./utils.CBfrqCZ4.js";import"./index.BWNh2K4G.js";import"./index.3rXK4OIH.js";import"./createLucideIcon.DdiNmGRb.js";function E({onSearch:s,placeholder:m="Поиск запчастей...",defaultValue:n=""}){const[o,i]=p.useState(n),l=e=>{const r=e.trim();r&&(s?s(r):x(`/catalog?search=${encodeURIComponent(r)}`))},a=e=>{e.preventDefault(),l(o)},c=e=>{e.key==="Enter"&&a(e)};return t.jsxs("form",{onSubmit:a,className:"flex w-full max-w-sm items-center space-x-2",children:[t.jsxs("div",{className:"relative flex-1",children:[t.jsx(d,{className:"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground"}),t.jsx(u,{type:"text",placeholder:m,value:o,onChange:e=>i(e.target.value),onKeyDown:c,className:"pl-10"})]}),t.jsx(f,{type:"submit",size:"sm",children:"Найти"})]})}export{E as SearchForm};
