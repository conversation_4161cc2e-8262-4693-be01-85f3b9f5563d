<template>
  <div class="relative">
    <div class="w-full bg-[--p-surface-200] dark:bg-[--p-surface-300] rounded-full h-2 overflow-hidden">
      <Motion
        :initial="{ width: 0 }"
        :whileInView="{ width: `${value}%` }"
        :transition="{ duration: 1.5, ease: 'easeOut', delay: delay }"
        :viewport="{ once: true }"
        class="h-full bg-[--color-primary] rounded-full relative overflow-hidden"
      >
        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer" />
      </Motion>
    </div>
    
    <div v-if="showLabel" class="flex justify-between items-center mt-2 text-sm">
      <span class="text-[--color-muted]">{{ label }}</span>
      <Motion
        :initial="{ opacity: 0 }"
        :whileInView="{ opacity: 1 }"
        :transition="{ duration: 0.5, delay: delay + 0.5 }"
        :viewport="{ once: true }"
        class="text-[--color-primary] font-medium"
      >
        {{ value }}%
      </Motion>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Motion } from 'motion-v'

interface Props {
  value: number
  label?: string
  showLabel?: boolean
  delay?: number
}

const props = withDefaults(defineProps<Props>(), {
  showLabel: true,
  delay: 0
})
</script>

<style scoped>
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}
</style>