import { QueryClient } from '@tanstack/vue-query';

// Фабрика для изолированного QueryClient (при необходимости для вне-глобального сценария)
export function createQueryClient() {
  return new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 30_000,
        gcTime: 5 * 60_000,
        retry: 1,
        refetchOnWindowFocus: false,
        refetchOnReconnect: true,
        // keepPreviousData полезен для пагинации, можно включать точечно в useQuery
      },
      mutations: {
        retry: 1,
      },
    },
  });
}

