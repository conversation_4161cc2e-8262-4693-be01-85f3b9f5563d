import{E as M}from"./ErrorBoundary.Def_Csyr.js";import{t as g}from"./trpc.BpyaUO08.js";import O from"./Button.oEwD-lSq.js";import{D as A,s as P}from"./index.xFbNStuK.js";import{D as L}from"./Dialog.Dcz8Sg9i.js";import{I}from"./InputText.COaPodMV.js";import{S as U}from"./SecondaryButton.DjJyItVx.js";import{V as j}from"./AutoComplete.XhB-0aUS.js";import{_ as E}from"./_plugin-vue_export-helper.BX07OBiL.js";import{V as R}from"./ToggleSwitch.7R1jWLlg.js";import{d as h,g as x,o as B,j as m,a as t,e as a,Q as D,R as q,w as k,i as N,c as S,f as z,F as K,r as Q}from"./index.BglzLLgy.js";import{r as d}from"./reactivity.esm-bundler.D5IypM4U.js";import{n as T}from"./router.DKcY2uv6.js";import G from"./Toast.SYHAzbvX.js";import{a as H,T as J,P as W}from"./trash.BzwTNQJC.js";import"./createLucideIcon.3yDVQAYz.js";import"./index.DQmIdHOH.js";import"./index.B_yc9D3m.js";import"./index.CRcBj2l1.js";import"./index.BHZvt6Rq.js";import"./index.Tc5ZRw49.js";import"./index.CJuyVe3p.js";import"./index.CLs7nh7g.js";import"./index.D7_1DwEX.js";import"./index.CLkTvMlq.js";import"./index.irlSZ_18.js";import"./index.B0GjtQuk.js";import"./runtime-dom.esm-bundler.C-dfRCGi.js";import"./index.D6LCJW96.js";import"./index.CXDqTVvt.js";import"./index.CBatl9QV.js";import"./index.BsVcoVHU.js";import"./index.BksvaNwr.js";import"./index.CzaMXvxd.js";const X=h({__name:"EditBrandDialog",props:D({brand:{}},{isVisible:{type:Boolean,required:!0},isVisibleModifiers:{}}),emits:D(["save","cancel"],["update:isVisible"]),setup(s,{expose:u,emit:i}){u();const e=d([]),l=d([]),r=q(s,"isVisible"),o=s,f=i,p=d({}),_=d("Создать бренд"),V=c=>{const n=c.query.toLowerCase();n?e.value=l.value.filter(v=>v.toLowerCase().includes(n)):e.value=[...l.value]};k(r,c=>{c&&(p.value={...o.brand||{}},_.value=o.brand?.id?`Редактировать: ${o.brand.name}`:"Создать бренд")});function C(){r.value=!1,f("cancel")}function w(){f("save",{...p.value}),r.value=!1}async function b(){const c=await g.crud.brand.findMany.query({where:{country:{not:null}},select:{country:!0},distinct:["country"],orderBy:{country:"asc"}});l.value=c.map(n=>n.country),e.value=[...l.value]}N(()=>{b()});const y={countryOptions:e,allCountries:l,isVisible:r,props:o,emit:f,localBrand:p,dialogTitle:_,filterCountries:V,handleCancel:C,handleSave:w,loadCountries:b,Button:O,Dialog:L,InputText:I,SecondaryButton:U,AutoComplete:j,ToggleSwitch:R};return Object.defineProperty(y,"__isScriptSetup",{enumerable:!1,value:!0}),y}}),Y={class:"flex flex-col gap-4 py-4"},Z={class:"flex flex-col"},$={class:"flex flex-col"},ee={class:"flex flex-col"},ue={class:"flex gap-3"},oe={class:"flex justify-end gap-2"};function ne(s,u,i,e,l,r){return B(),x(e.Dialog,{visible:e.isVisible,"onUpdate:visible":u[4]||(u[4]=o=>e.isVisible=o),modal:"",header:e.dialogTitle,class:"sm:w-100 w-9/10"},{default:m(()=>[t("div",Y,[t("div",Z,[u[5]||(u[5]=t("label",{for:"name"},"Наименование",-1)),a(e.InputText,{id:"name",modelValue:e.localBrand.name,"onUpdate:modelValue":u[0]||(u[0]=o=>e.localBrand.name=o)},null,8,["modelValue"])]),t("div",$,[u[6]||(u[6]=t("label",{for:"slug"},"URL слаг",-1)),a(e.InputText,{id:"slug",modelValue:e.localBrand.slug,"onUpdate:modelValue":u[1]||(u[1]=o=>e.localBrand.slug=o)},null,8,["modelValue"])]),t("div",ee,[u[7]||(u[7]=t("label",{for:"country"},"Страна",-1)),a(e.AutoComplete,{onComplete:e.filterCountries,id:"country",dropdownMode:"current",modelValue:e.localBrand.country,"onUpdate:modelValue":u[2]||(u[2]=o=>e.localBrand.country=o),dropdown:"",suggestions:e.countryOptions},null,8,["modelValue","suggestions"])]),t("div",ue,[u[8]||(u[8]=t("label",{for:"isOem"},"OEM",-1)),a(e.ToggleSwitch,{id:"isOem",modelValue:e.localBrand.isOem,"onUpdate:modelValue":u[3]||(u[3]=o=>e.localBrand.isOem=o)},null,8,["modelValue"])])]),t("div",oe,[a(e.SecondaryButton,{type:"button",label:"Cancel",onClick:e.handleCancel}),a(e.Button,{type:"button",label:"Save",onClick:e.handleSave})])]),_:1},8,["visible","header"])}const ae=E(X,[["render",ne]]),le=h({__name:"BrandList",props:{initialData:{}},setup(s,{expose:u}){u();const i=d(""),e=d(!1),l=d(null),r=s,o=d(r.initialData),f={id:"ID",name:"Наименование",slug:"url слаг",country:"Страна",isOem:"OEM"},p=["id","name","slug","country","isOem"];function _(){l.value={},e.value=!0}function V(n){l.value={...n},e.value=!0}async function C(n){if(n)try{if(n.id){const{id:v,...F}=n;await g.crud.brand.update.mutate({where:{id:v},data:F})}else if(n.name&&n.slug)await g.crud.brand.create.mutate({data:{name:n.name,slug:n.slug,country:n.country,isOem:n.isOem||!1}});else{console.error("Name and slug are required to create a brand.");return}T(window.location.href)}catch(v){console.error("Failed to save brand:",v)}finally{e.value=!1}}function w(){e.value=!1,l.value=null}async function b(n){e.value=!1,await g.crud.brand.delete.mutate({where:{id:n.id}}),T(window.location.href)}k(i,n=>{y(n)});async function y(n=""){console.log("value",n),o.value=await g.crud.brand.findMany.query({where:{OR:[{name:{contains:n}},{slug:{contains:n}},{country:{contains:n}}]}})}const c={searchValue:i,dialogVisible:e,editingBrand:l,props:r,items:o,keyMapping:f,columnKeys:p,createBrand:_,editBrand:V,handleSave:C,handleCancel:w,deleteBrand:b,debouncedSearch:y,Button:O,DataTable:A,get PencilIcon(){return W},get TrashIcon(){return J},get PlusIcon(){return H},get Column(){return P},EditBrandDialog:ae,InputText:I,Toast:G};return Object.defineProperty(c,"__isScriptSetup",{enumerable:!1,value:!0}),c}}),te={class:"flex justify-between items-center mb-4"},re={class:"flex justify-end"},ie={class:"flex gap-2"};function se(s,u,i,e,l,r){return B(),S("div",null,[t("div",te,[u[3]||(u[3]=t("h1",{class:"text-2xl font-bold"},"Бренды",-1)),a(e.Button,{onClick:e.createBrand},{default:m(()=>[a(e.PlusIcon,{class:"w-5 h-5 mr-2"}),u[2]||(u[2]=z(" Создать бренд "))]),_:1,__:[2]})]),a(e.DataTable,{"show-headers":"",value:e.items},{header:m(()=>[t("div",re,[a(e.InputText,{modelValue:e.searchValue,"onUpdate:modelValue":u[0]||(u[0]=o=>e.searchValue=o),placeholder:"Поиск"},null,8,["modelValue"])])]),default:m(()=>[(B(),S(K,null,Q(e.columnKeys,o=>a(e.Column,{key:o,field:o,header:e.keyMapping[o]||o},null,8,["field","header"])),64)),a(e.Column,{field:"_count.catalogItems",header:"Кол-во кат.поз."}),a(e.Column,{header:"Действия"},{body:m(({data:o})=>[t("div",ie,[a(e.Button,{onClick:f=>e.editBrand(o),outlined:"",size:"small"},{default:m(()=>[a(e.PencilIcon,{class:"w-5 h-5"})]),_:2},1032,["onClick"]),a(e.Button,{onClick:f=>e.deleteBrand(o),outlined:"",severity:"danger",size:"small"},{default:m(()=>[a(e.TrashIcon,{class:"w-5 h-5"})]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["value"]),a(e.EditBrandDialog,{isVisible:e.dialogVisible,"onUpdate:isVisible":u[1]||(u[1]=o=>e.dialogVisible=o),brand:e.editingBrand,onSave:e.handleSave,onCancel:e.handleCancel},null,8,["isVisible","brand"]),a(e.Toast)])}const de=E(le,[["render",se]]),ce=h({__name:"BrandListBoundary",props:{initialData:{}},setup(s,{expose:u}){u();const i=s,e=d(0),r={props:i,key:e,onRetry:()=>{e.value++},ErrorBoundary:M,BrandList:de};return Object.defineProperty(r,"__isScriptSetup",{enumerable:!1,value:!0}),r}});function me(s,u,i,e,l,r){return B(),x(e.ErrorBoundary,{variant:"minimal",title:"Ошибка брендов",message:"Список брендов не отрисовался. Повторите попытку.",onRetry:e.onRetry},{default:m(()=>[(B(),x(e.BrandList,{initialData:i.initialData,key:e.key},null,8,["initialData"]))]),_:1})}const He=E(ce,[["render",me]]);export{He as default};
