import type { App } from 'vue';
import PrimeVue from 'primevue/config';
import ToastService from 'primevue/toastservice';
import ConfirmationService from 'primevue/confirmationservice';
import Tooltip from 'primevue/tooltip';
import { VueQueryPlugin, QueryClient } from '@tanstack/vue-query';

export default (app: App) => {
  app.use(PrimeVue, {
    unstyled: true
  });

  // Vue Query — глобальный QueryClient для всех islands
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 30_000,
        gcTime: 5 * 60_000,
        retry: 1,
        refetchOnWindowFocus: false,
        refetchOnReconnect: true,
      },
      mutations: {
        retry: 1,
      },
    },
  });
  app.use(VueQueryPlugin, { queryClient });

  // Добавляем сервисы для Toast и ConfirmDialog
  app.use(ToastService);
  app.use(ConfirmationService);

  // Глобальная директива tooltip
  app.directive('tooltip', Tooltip);
};
