<template>
  <div class="w-full max-w-6xl">
    <div class="flex items-center justify-between mb-4">
      <h2 class="text-xl font-semibold">Пользователи</h2>
      <div class="flex gap-2">
        <InputText v-model="query.search" placeholder="Поиск по email/имени" class="w-64" />
        <Select v-model="query.sortBy" :options="sortOptions" optionLabel="label" optionValue="value" class="w-44" />
        <Select v-model="query.sortDirection" :options="directionOptions" optionLabel="label" optionValue="value" class="w-36" />
        <Button label="Обновить" @click="refetch()" />
        <Button label="Создать" icon="pi pi-plus" severity="success" @click="openCreate()" />
        <Button label="Проверка прав" severity="secondary" @click="aclVisible = true" />
      </div>
    </div>

    <DataTable :value="users" :loading="isLoading" dataKey="id" class="w-full" :rows="pageSize" :paginator="true" :totalRecords="total" @page="onPage">
      <Column field="createdAt" header="Создан" :sortable="false">
        <template #body="{ data }">
          {{ formatDate(data.createdAt) }}
        </template>
      </Column>
      <Column field="email" header="Email" />
      <Column field="name" header="Имя" />
      <Column field="role" header="Роль">
        <template #body="{ data }">
          <Select v-model="roleEdits[data.id]" :options="roleOptions" class="w-36" @change="saveRole(data)" />
        </template>
      </Column>
      <Column header="Статус">
        <template #body="{ data }">
          <Tag :value="data.banned ? 'Заблокирован' : 'Активен'" :severity="data.banned ? 'danger' : 'success'" />
        </template>
      </Column>
      <Column header="Действия">
        <template #body="{ data }">
          <div class="relative inline-flex">
            <Button size="small" label="Действия" icon="pi pi-ellipsis-v" @click="toggleRowMenu($event, data.id)" />
            <Menu :model="getRowMenuItems(data)" :ref="setRowMenuRef(data.id)" :popup="true" />
          </div>
        </template>
      </Column>
    </DataTable>

    <!-- Диалог сессий пользователя -->
    <Dialog v-model:visible="sessionsVisible" header="Сессии пользователя" :modal="true" :style="{ width: '720px' }">
      <div v-if="sessionsUser" class="mb-3 text-sm text-surface-600">{{ sessionsUser.email }}</div>
      <DataTable :value="sessions" :loading="sessionsLoading" dataKey="token" :rows="10" :paginator="true">
        <Column field="createdAt" header="Создана">
          <template #body="{ data }">{{ formatDate(data.createdAt) }}</template>
        </Column>
        <Column field="expiresAt" header="Истекает">
          <template #body="{ data }">{{ formatDate(data.expiresAt) }}</template>
        </Column>
        <Column field="ipAddress" header="IP" />
        <Column field="userAgent" header="UA" />
        <Column header="Действия">
          <template #body="{ data }">
            <Button size="small" label="Отозвать" severity="warning" @click="revokeSession(data)" />
          </template>
        </Column>
      </DataTable>
      <div class="flex justify-end gap-2 mt-3">
        <Button label="Отозвать все" severity="warning" @click="revokeAllSessions()" />
        <Button label="Закрыть" severity="secondary" @click="sessionsVisible = false" />
      </div>
    </Dialog>

    <!-- Диалог контроля доступа -->
    <Dialog v-model:visible="aclPanelVisible" header="Контроль доступа" :modal="true" :style="{ width: '720px' }">
      <div class="text-sm mb-3" v-if="aclSelectedUser">
        Пользователь: <b>{{ aclSelectedUser.email }}</b> ({{ aclSelectedUser.name || 'без имени' }})
      </div>

      <div class="grid grid-cols-2 gap-3 mb-3">
        <InputText v-model="aclForm.userId" placeholder="User ID (опционально)" />
        <InputText v-model="aclForm.role" placeholder="Role (опционально)" />
      </div>

      <div class="grid grid-cols-2 gap-3 mb-3">
        <Select v-model="aclSelectedResource" :options="resourceOptions" optionLabel="label" optionValue="value" placeholder="Ресурс" class="w-full" />
        <MultiSelect v-model="aclSelectedActions" :options="availableActions" optionLabel="label" optionValue="value" placeholder="Действия" class="w-full" />
      </div>

      <div class="flex gap-2 mb-3">
        <Button label="Добавить permission" @click="aclAddPermission()" />
        <Button label="Очистить" severity="secondary" @click="aclClearPermissions()" />
      </div>

      <div class="font-mono text-xs bg-[--color-muted]/10 p-3 rounded mb-3" v-if="Object.keys(aclPermissions).length">{{ aclPermissions }}</div>

      <div class="flex justify-end gap-2">
        <Button label="Проверить" @click="checkPermissions()" :disabled="!canCheckAcl" />
        <Button label="Закрыть" severity="secondary" @click="aclPanelVisible = false" />
      </div>

      <div v-if="aclResult" class="mt-4 text-sm">
        <pre>{{ JSON.stringify(aclResult, null, 2) }}</pre>
      </div>
    </Dialog>

    <Dialog v-model:visible="createVisible" header="Создать пользователя" :modal="true" :style="{ width: '480px' }">
      <div class="flex flex-col gap-3">
        <InputText v-model="createForm.name" placeholder="Имя" />
        <InputText v-model="createForm.email" placeholder="Email" />
        <Password v-model="createForm.password" placeholder="Пароль" toggleMask />
        <Select v-model="createForm.role" :options="roleOptions" class="w-36" />
        <div class="flex justify-end gap-2 mt-2">
          <Button label="Отмена" severity="secondary" @click="createVisible = false" />
          <Button label="Создать" severity="success" @click="createUser()" :loading="actionLoading" />
        </div>
      </div>
    </Dialog>
    <!-- Toast контейнер для этого изолята -->
    <Toast />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { trpc } from '@/lib/trpc'
import InputText from '@/volt/InputText.vue'
import Select from '@/volt/Select.vue'
import MultiSelect from '@/volt/MultiSelect.vue'
import Button from '@/volt/Button.vue'
import Menu from '@/volt/Menu.vue'
import DataTable from '@/volt/DataTable.vue'
import Column from 'primevue/column'
import Dialog from '@/volt/Dialog.vue'
import Password from '@/volt/Password.vue'
import Tag from '@/volt/Tag.vue'
import { useToast } from '@/composables/useToast'
import Toast from '@/volt/Toast.vue'

const toast = useToast()

// Табличные состояния
const page = ref(1)
const pageSize = ref(20)
const total = ref(0)
const isLoading = ref(false)

// Форма поиска/сортировки
const query = reactive({
  search: '',
  sortBy: 'createdAt',
  sortDirection: 'desc' as 'asc' | 'desc'
})

const sortOptions = [
  { label: 'Создан', value: 'createdAt' },
  { label: 'Email', value: 'email' },
  { label: 'Имя', value: 'name' },
]
const directionOptions = [
  { label: 'По убыванию', value: 'desc' },
  { label: 'По возрастанию', value: 'asc' },
]

// Данные
const users = ref<any[]>([])
const roleEdits = reactive<Record<string, 'USER' | 'SHOP' | 'ADMIN'>>({})
const roleOptions = [
  { label: 'USER', value: 'USER' },
  { label: 'SHOP', value: 'SHOP' },
  { label: 'ADMIN', value: 'ADMIN' },
]

function formatDate(value?: string) {
  return value ? new Date(value).toLocaleString() : ''
}

async function fetchUsers() {
  isLoading.value = true
  try {
    const result = await trpc.admin.listUsers.query({
      search: query.search || undefined,
      sortBy: query.sortBy,
      sortDirection: query.sortDirection,
      limit: pageSize.value,
      offset: (page.value - 1) * pageSize.value,
    })
    users.value = result.users || result.data || []
    total.value = result.total || result.count || users.value.length
    // подготовим локальное состояние ролей
    roleEditsReset()
  } catch (e: any) {
    toast.error(e?.message || 'Не удалось загрузить пользователей')
  } finally {
    isLoading.value = false
  }
}

function roleEditsReset() {
  for (const u of users.value) {
    roleEdits[u.id] = u.role
  }
}

async function saveRole(user: any) {
  const newRole = roleEdits[user.id]
  if (newRole === user.role) return
  try {
    await trpc.admin.setUserRole.mutate({ userId: user.id, role: newRole })
    toast.success('Роль обновлена')
    await fetchUsers()
  } catch (e: any) {
    toast.error(e?.message || 'Не удалось обновить роль')
    roleEdits[user.id] = user.role
  }
}

async function toggleBan(user: any) {
  try {
    if (user.isBanned) {
      await trpc.admin.unbanUser.mutate({ userId: user.id })
      toast.success('Пользователь разблокирован')
    } else {
      await trpc.admin.banUser.mutate({ userId: user.id })
      toast.success('Пользователь заблокирован')
    }
    await fetchUsers()
  } catch (e: any) {
    toast.error(e?.message || 'Не удалось изменить статус блокировки')
  }
}

async function impersonate(user: any) {
  try {
    await trpc.admin.impersonateUser.mutate({ userId: user.id })
    toast.success('Запущена имперсонация — обновите страницу')
    // Обычно лучше сделать redirect на главную
    window.location.href = '/admin'
  } catch (e: any) {
    toast.error(e?.message || 'Не удалось выполнить имперсонацию')
  }
}

async function resetPassword(user: any) {
  try {
    const newPassword = prompt('Введите новый пароль (мин. 6 символов)')
    if (!newPassword) return
    await trpc.admin.resetUserPassword.mutate({ userId: user.id, newPassword })
    toast.success('Пароль сброшен')
  } catch (e: any) {
    toast.error(e?.message || 'Не удалось сбросить пароль')
  }
}

async function removeUser(user: any) {
  if (!confirm('Удалить пользователя безвозвратно?')) return
  try {
    await trpc.admin.removeUser.mutate({ userId: user.id })
    toast.success('Пользователь удалён')
    await fetchUsers()
  } catch (e: any) {
    toast.error(e?.message || 'Не удалось удалить пользователя')
  }
}

function onPage(event: any) {
  page.value = Math.floor(event.first / event.rows) + 1
  pageSize.value = event.rows
  fetchUsers()
}

function refetch() {
  page.value = 1
  fetchUsers()
}

// Создание пользователя
const createVisible = ref(false)
const actionLoading = ref(false)
const createForm = reactive({ name: '', email: '', password: '', role: 'USER' as 'USER' | 'SHOP' | 'ADMIN' })
function openCreate() { createVisible.value = true }
async function createUser() {
  if (!createForm.email || !createForm.password) {
    toast.error('Email и пароль обязательны')
    return
  }
  actionLoading.value = true
  try {
    await trpc.admin.createUser.mutate({ ...createForm, emailVerified: true })
    toast.success('Пользователь создан')
    createVisible.value = false
    createForm.name = ''
    createForm.email = ''
    createForm.password = ''
    createForm.role = 'USER'
    await fetchUsers()
  } catch (e: any) {
    toast.error(e?.message || 'Не удалось создать пользователя')
  } finally {
    actionLoading.value = false
  }
}

// Автозагрузка
watch(() => [page.value, pageSize.value], fetchUsers)
fetchUsers()

// Загрузка каталога ресурсов/действий для ACL
loadAccessCatalog()

// ============ Работа с сессиями ============
const sessionsVisible = ref(false)
const sessionsLoading = ref(false)
const sessionsUser = ref<any | null>(null)
const sessions = ref<any[]>([])

async function openSessions(user: any) {
  sessionsUser.value = user
  sessionsVisible.value = true
  sessionsLoading.value = true
  try {
    const res = await trpc.admin.listUserSessions.query({ userId: user.id })
    sessions.value = res?.sessions || res?.data || []
  } catch (e: any) {
    toast.error(e?.message || 'Не удалось загрузить сессии')
  } finally {
    sessionsLoading.value = false
  }
}

// ============ Проверка прав (ACL) ============
const aclPanelVisible = ref(false)
const aclForm = reactive<{ userId?: string; role?: string; json: string }>({ json: '' })
const aclResult = ref<any>(null)
const aclSelectedUser = ref<any | null>(null)
const aclPermissions = reactive<Record<string, string[]>>({})
const aclSelectedResource = ref<string | null>(null)
const aclSelectedActions = ref<string[]>([])
const resourceOptions = ref<{ label: string; value: string }[]>([])
const actionMap = ref<Record<string, string[]>>({})
const availableActions = computed(() => {
  const res = aclSelectedResource.value
  if (!res) return []
  return (actionMap.value[res] || []).map((a) => ({ label: a, value: a }))
})

async function loadAccessCatalog() {
  try {
    const catalog = await trpc.access.list.query()
    actionMap.value = catalog as Record<string, string[]>
    resourceOptions.value = Object.keys(actionMap.value).map((k) => ({ label: k, value: k }))
  } catch (e: any) {
    // fallback оставим пустым
  }
}

const canCheckAcl = computed(() => {
  if (Object.keys(aclPermissions).length > 0) return true
  try {
    const obj = JSON.parse(aclForm.json || '{}')
    return obj && typeof obj === 'object' && Object.keys(obj).length > 0
  } catch {
    return false
  }
})

async function checkPermissions() {
  try {
    let permissions: Record<string, string[]>
    if (Object.keys(aclPermissions).length > 0) {
      permissions = aclPermissions
    } else {
      const obj = JSON.parse(aclForm.json || '{}')
      if (!obj || Object.keys(obj).length === 0) {
        toast.warn('Добавьте хотя бы один permission')
        return
      }
      permissions = obj
    }
    const res = await trpc.admin.hasPermission.query({
      permissions,
      userId: aclForm.userId || undefined,
      role: aclForm.role || undefined,
    })
    aclResult.value = res
    toast.success('Проверка выполнена')
  } catch (e: any) {
    toast.error(e?.message || 'Некорректный JSON или ошибка проверки прав')
  }
}

function openAclPanel(user: any) {
  aclPanelVisible.value = true
  aclSelectedUser.value = user
  aclForm.userId = user.id
  aclForm.role = user.role
}

function aclAddPermission() {
  const res = aclSelectedResource.value
  const actions = aclSelectedActions.value
  if (!res || actions.length === 0) return
  aclPermissions[res] = [...new Set(actions)]
  aclSelectedResource.value = null
  aclSelectedActions.value = []
}

function aclClearPermissions() {
  for (const k of Object.keys(aclPermissions)) delete aclPermissions[k]
}

// Dropdown меню на строку
const rowMenus = ref<Record<string, any>>({})
function setRowMenuRef(id: string) {
  return (el: any) => {
    if (el) rowMenus.value[id] = el
  }
}
function toggleRowMenu(event: Event, id: string) {
  rowMenus.value[id]?.toggle(event)
}
function getRowMenuItems(user: any) {
  return [
    { label: 'Имперсонация', icon: 'pi pi-user', command: () => impersonate(user) },
    {
      label: user.banned ? 'Разблокировать' : 'Заблокировать',
      icon: user.banned ? 'pi pi-unlock' : 'pi pi-lock',
      command: () => toggleBan(user),
    },
    { label: 'Сброс пароля', icon: 'pi pi-key', command: () => resetPassword(user) },
    { label: 'Сессии', icon: 'pi pi-clock', command: () => openSessions(user) },
    { label: 'Права', icon: 'pi pi-shield', command: () => openAclPanel(user) },
    { separator: true },
    { label: 'Удалить', icon: 'pi pi-trash', command: () => removeUser(user) },
  ]
}

async function revokeSession(session: any) {
  try {
    await trpc.admin.revokeUserSession.mutate({ sessionToken: session.token })
    toast.success('Сессия отозвана')
    if (sessionsUser.value) await openSessions(sessionsUser.value)
  } catch (e: any) {
    toast.error(e?.message || 'Не удалось отозвать сессию')
  }
}

async function revokeAllSessions() {
  if (!sessionsUser.value) return
  try {
    await trpc.admin.revokeAllUserSessions.mutate({ userId: sessionsUser.value.id })
    toast.success('Все сессии отозваны')
    await openSessions(sessionsUser.value)
  } catch (e: any) {
    toast.error(e?.message || 'Не удалось отозвать все сессии')
  }
}
</script>

<style scoped>
</style>


