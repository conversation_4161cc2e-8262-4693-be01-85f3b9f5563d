import { e as createComponent, k as renderComponent, r as renderTemplate } from '../../chunks/astro/server_DbndhTWv.mjs';
import 'kleur/colors';
import { $ as $$AdminLayout } from '../../chunks/AdminLayout_b20tykPC.mjs';
import { defineComponent, useSSRContext, ref, reactive, mergeProps } from 'vue';
import { I as InputText } from '../../chunks/InputText_CtReD0EA.mjs';
import { V as VButton } from '../../chunks/Button_CuwpNmer.mjs';
import { S as Select } from '../../chunks/Select_Bk34xTKM.mjs';
import { t as trpc } from '../../chunks/trpc_B_6Rt2Mk.mjs';
import { u as useToast } from '../../chunks/useToast_CXW6AFGp.mjs';
import { ssrRenderAttrs, ssrRenderComponent, ssr<PERSON><PERSON>List, ssrRenderClass, ssrInterpolate } from 'vue/server-renderer';
import { _ as _export_sfc } from '../../chunks/ClientRouter_B8Zzhk9G.mjs';
export { r as renderers } from '../../chunks/_@astro-renderers_CicWY1rm.mjs';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "AccessControl",
  setup(__props, { expose: __expose }) {
    __expose();
    const toast = useToast();
    const userQuery = ref("");
    const userResults = ref([]);
    const selectedUser = ref(null);
    async function searchUsers() {
      try {
        const res = await trpc.crud.user.findMany.query({
          where: userQuery.value ? {
            OR: [
              { email: { contains: userQuery.value, mode: "insensitive" } },
              { name: { contains: userQuery.value, mode: "insensitive" } }
            ]
          } : void 0,
          take: 20,
          orderBy: { createdAt: "desc" },
          select: { id: true, email: true, name: true, role: true }
        });
        userResults.value = res;
      } catch (e) {
        toast.error(e?.message || "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0432\u044B\u043F\u043E\u043B\u043D\u0438\u0442\u044C \u043F\u043E\u0438\u0441\u043A \u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u0435\u0439");
      }
    }
    function selectUser(u) {
      selectedUser.value = u;
    }
    const roleOptions = [
      { label: "\u041D\u0435 \u0432\u044B\u0431\u0440\u0430\u043D\u043E", value: "" },
      { label: "ADMIN", value: "ADMIN" },
      { label: "SHOP", value: "SHOP" },
      { label: "USER", value: "USER" }
    ];
    const selectedRole = ref("");
    const permissions = reactive({});
    const permissionResource = ref("");
    const permissionActions = ref("");
    function addPermission() {
      const resource = permissionResource.value.trim();
      if (!resource) return;
      const actions = permissionActions.value.split(",").map((a) => a.trim()).filter(Boolean);
      if (!actions.length) return;
      permissions[resource] = actions;
      permissionResource.value = "";
      permissionActions.value = "";
    }
    function clearPermissions() {
      for (const key of Object.keys(permissions)) delete permissions[key];
    }
    const result = ref(null);
    async function check() {
      try {
        const res = await trpc.admin.hasPermission.query({
          permissions,
          userId: selectedUser.value?.id,
          role: selectedRole.value || void 0
        });
        result.value = res;
        toast.success("\u041F\u0440\u043E\u0432\u0435\u0440\u043A\u0430 \u0432\u044B\u043F\u043E\u043B\u043D\u0435\u043D\u0430");
      } catch (e) {
        toast.error(e?.message || "\u041E\u0448\u0438\u0431\u043A\u0430 \u043F\u0440\u043E\u0432\u0435\u0440\u043A\u0438 \u043F\u0440\u0430\u0432");
      }
    }
    function checkRoleLocal() {
      if (!selectedRole.value) {
        toast.warn("\u0423\u043A\u0430\u0436\u0438\u0442\u0435 \u0440\u043E\u043B\u044C \u0434\u043B\u044F \u043B\u043E\u043A\u0430\u043B\u044C\u043D\u043E\u0439 \u043F\u0440\u043E\u0432\u0435\u0440\u043A\u0438");
        return;
      }
      result.value = { role: selectedRole.value, permissions };
    }
    const __returned__ = { toast, userQuery, userResults, selectedUser, searchUsers, selectUser, roleOptions, selectedRole, permissions, permissionResource, permissionActions, addPermission, clearPermissions, result, check, checkRoleLocal, InputText, Button: VButton, Select };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "w-full max-w-5xl" }, _attrs))}><h2 class="text-xl font-semibold mb-4">\u041A\u043E\u043D\u0442\u0440\u043E\u043B\u044C \u0434\u043E\u0441\u0442\u0443\u043F\u0430</h2><div class="bg-[--color-card] border border-[--color-border] rounded-[--radius-md] p-4 mb-4"><h3 class="font-medium mb-2">\u041F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044C</h3><div class="flex gap-2 items-center">`);
  _push(ssrRenderComponent($setup["InputText"], {
    modelValue: $setup.userQuery,
    "onUpdate:modelValue": ($event) => $setup.userQuery = $event,
    placeholder: "\u041F\u043E\u0438\u0441\u043A \u043F\u043E email/\u0438\u043C\u0435\u043D\u0438",
    class: "w-80"
  }, null, _parent));
  _push(ssrRenderComponent($setup["Button"], {
    label: "\u041D\u0430\u0439\u0442\u0438",
    onClick: $setup.searchUsers
  }, null, _parent));
  _push(`</div>`);
  if ($setup.userResults.length) {
    _push(`<div class="mt-3 max-h-56 overflow-auto"><!--[-->`);
    ssrRenderList($setup.userResults, (u) => {
      _push(`<div class="${ssrRenderClass([{ "bg-[--color-primary]/10": $setup.selectedUser?.id === u.id }, "px-3 py-2 cursor-pointer hover:bg-[--color-muted]/10 rounded"])}"><div class="text-sm">${ssrInterpolate(u.email)} <span class="opacity-70">\u2014 ${ssrInterpolate(u.name || "\u0431\u0435\u0437 \u0438\u043C\u0435\u043D\u0438")}</span></div><div class="text-xs opacity-70">role: ${ssrInterpolate(u.role)}</div></div>`);
    });
    _push(`<!--]--></div>`);
  } else {
    _push(`<!---->`);
  }
  _push(`</div><div class="bg-[--color-card] border border-[--color-border] rounded-[--radius-md] p-4 mb-4"><h3 class="font-medium mb-2">\u0420\u043E\u043B\u044C (\u043E\u043F\u0446\u0438\u043E\u043D\u0430\u043B\u044C\u043D\u043E)</h3><div class="flex gap-2 items-center">`);
  _push(ssrRenderComponent($setup["Select"], {
    modelValue: $setup.selectedRole,
    "onUpdate:modelValue": ($event) => $setup.selectedRole = $event,
    options: $setup.roleOptions,
    class: "w-60"
  }, null, _parent));
  _push(ssrRenderComponent($setup["Button"], {
    label: "\u0421\u0431\u0440\u043E\u0441\u0438\u0442\u044C \u0440\u043E\u043B\u044C",
    severity: "secondary",
    onClick: ($event) => $setup.selectedRole = ""
  }, null, _parent));
  _push(`</div><div class="text-xs opacity-70 mt-2">\u0415\u0441\u043B\u0438 \u0443\u043A\u0430\u0437\u0430\u043D user \u0438 \u0440\u043E\u043B\u044C \u043E\u0434\u043D\u043E\u0432\u0440\u0435\u043C\u0435\u043D\u043D\u043E \u2014 \u0441\u0435\u0440\u0432\u0435\u0440 \u043C\u043E\u0436\u0435\u0442 \u0438\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u044C \u043F\u0440\u0438\u043E\u0440\u0438\u0442\u0435\u0442/\u0432\u0430\u043B\u0438\u0434\u0430\u0446\u0438\u044E \u043F\u043E \u043D\u0435\u043E\u0431\u0445\u043E\u0434\u0438\u043C\u043E\u0441\u0442\u0438.</div></div><div class="bg-[--color-card] border border-[--color-border] rounded-[--radius-md] p-4 mb-4"><h3 class="font-medium mb-2">\u041F\u0440\u043E\u0432\u0435\u0440\u044F\u0435\u043C\u044B\u0435 permissions</h3><div class="grid grid-cols-2 gap-3 mb-3">`);
  _push(ssrRenderComponent($setup["InputText"], {
    modelValue: $setup.permissionResource,
    "onUpdate:modelValue": ($event) => $setup.permissionResource = $event,
    placeholder: "\u0420\u0435\u0441\u0443\u0440\u0441 (\u043D\u0430\u043F\u0440\u0438\u043C\u0435\u0440, project)"
  }, null, _parent));
  _push(ssrRenderComponent($setup["InputText"], {
    modelValue: $setup.permissionActions,
    "onUpdate:modelValue": ($event) => $setup.permissionActions = $event,
    placeholder: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F \u0447\u0435\u0440\u0435\u0437 \u0437\u0430\u043F\u044F\u0442\u0443\u044E (\u043D\u0430\u043F\u0440\u0438\u043C\u0435\u0440, create,update)"
  }, null, _parent));
  _push(`</div><div class="flex gap-2">`);
  _push(ssrRenderComponent($setup["Button"], {
    label: "\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C",
    onClick: $setup.addPermission
  }, null, _parent));
  _push(ssrRenderComponent($setup["Button"], {
    label: "\u041E\u0447\u0438\u0441\u0442\u0438\u0442\u044C",
    severity: "secondary",
    onClick: $setup.clearPermissions
  }, null, _parent));
  _push(`</div>`);
  if (Object.keys($setup.permissions).length) {
    _push(`<div class="mt-4 text-sm"><div class="font-mono text-xs bg-[--color-muted]/10 p-3 rounded">${ssrInterpolate($setup.permissions)}</div></div>`);
  } else {
    _push(`<!---->`);
  }
  _push(`</div><div class="flex gap-2">`);
  _push(ssrRenderComponent($setup["Button"], {
    label: "\u041F\u0440\u043E\u0432\u0435\u0440\u0438\u0442\u044C \u043F\u0440\u0430\u0432\u0430",
    onClick: $setup.check
  }, null, _parent));
  _push(ssrRenderComponent($setup["Button"], {
    label: "\u041F\u0440\u043E\u0432\u0435\u0440\u0438\u0442\u044C \u0440\u043E\u043B\u044C \u043B\u043E\u043A\u0430\u043B\u044C\u043D\u043E",
    severity: "secondary",
    onClick: $setup.checkRoleLocal
  }, null, _parent));
  _push(`</div>`);
  if ($setup.result) {
    _push(`<div class="mt-4"><div class="bg-[--color-card] border border-[--color-border] rounded-[--radius-md] p-4"><div class="font-mono text-xs whitespace-pre-wrap">${ssrInterpolate(JSON.stringify($setup.result, null, 2))}</div></div></div>`);
  } else {
    _push(`<!---->`);
  }
  _push(`</div>`);
}
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/access/AccessControl.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const AccessControlComponent = /* @__PURE__ */ _export_sfc(_sfc_main, [["ssrRender", _sfc_ssrRender]]);

const $$AccessControl = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${renderComponent($$result, "AdminLayout", $$AdminLayout, {}, { "default": ($$result2) => renderTemplate` ${renderComponent($$result2, "AccessControlComponent", AccessControlComponent, { "client:load": true, "client:component-hydration": "load", "client:component-path": "@/components/admin/access/AccessControl.vue", "client:component-export": "default" })} ` })}`;
}, "D:/Dev/parttec/cpanel/src/pages/admin/access-control.astro", void 0);

const $$file = "D:/Dev/parttec/cpanel/src/pages/admin/access-control.astro";
const $$url = "/admin/access-control";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$AccessControl,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
