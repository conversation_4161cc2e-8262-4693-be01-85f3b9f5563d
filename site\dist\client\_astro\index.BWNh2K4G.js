import{r as i}from"./index.0yr9KlQE.js";import{j as p}from"./jsx-runtime.D_zvdyIk.js";function d(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function m(...e){return t=>{let r=!1;const o=e.map(n=>{const l=d(n,t);return!r&&typeof l=="function"&&(r=!0),l});if(r)return()=>{for(let n=0;n<o.length;n++){const l=o[n];typeof l=="function"?l():d(e[n],null)}}}}function j(...e){return i.useCallback(m(...e),e)}function C(e){const t=E(e),r=i.forwardRef((o,n)=>{const{children:l,...c}=o,s=i.Children.toArray(l),u=s.find(g);if(u){const a=u.props.children,y=s.map(f=>f===u?i.Children.count(a)>1?i.Children.only(null):i.isValidElement(a)?a.props.children:null:f);return p.jsx(t,{...c,ref:n,children:i.isValidElement(a)?i.cloneElement(a,void 0,y):null})}return p.jsx(t,{...c,ref:n,children:l})});return r.displayName=`${e}.Slot`,r}var V=C("Slot");function E(e){const t=i.forwardRef((r,o)=>{const{children:n,...l}=r;if(i.isValidElement(n)){const c=h(n),s=R(l,n.props);return n.type!==i.Fragment&&(s.ref=o?m(o,c):c),i.cloneElement(n,s)}return i.Children.count(n)>1?i.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var S=Symbol("radix.slottable");function g(e){return i.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===S}function R(e,t){const r={...t};for(const o in t){const n=e[o],l=t[o];/^on[A-Z]/.test(o)?n&&l?r[o]=(...s)=>{const u=l(...s);return n(...s),u}:n&&(r[o]=n):o==="style"?r[o]={...n,...l}:o==="className"&&(r[o]=[n,l].filter(Boolean).join(" "))}return{...e,...r}}function h(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning,r?e.props.ref:e.props.ref||e.ref)}export{V as S,C as c,j as u};
