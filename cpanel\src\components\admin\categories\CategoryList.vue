<script setup lang="ts">
import { trpc } from '@/lib/trpc'
import Button from '@/volt/Button.vue'
import DataTable from '@/volt/DataTable.vue'
import { PencilIcon, TrashIcon, PlusIcon } from 'lucide-vue-next'
import Column from 'primevue/column'
import EditCategoryDialog from './EditCategoryDialog.vue'
import { ref, watch } from 'vue'
import { navigate } from 'astro:transitions/client'
import type { CategoryWithCount, CategoryFormData } from '@/types/category'
import InputText from '@/volt/InputText.vue'
import Toast from '@/volt/Toast.vue'

const searchValue = ref('')
const dialogVisible = ref(false)
const editingCategory = ref<CategoryFormData | null>(null)
const expandedRows = ref<CategoryWithCount[]>([])
const childrenCache = ref<Record<number, CategoryWithCount[]>>({})

const props = defineProps<{
  initialData: CategoryWithCount[]
}>()

const items = ref(props.initialData)

// Тип для ключей CategoryWithCount, которые мы хотим отображать в таблице
type DisplayableCategoryKey = keyof Omit<CategoryWithCount, '_count' | 'parent' | 'children' | 'parts' | 'createdAt' | 'updatedAt'>

const keyMapping: Record<DisplayableCategoryKey, string> = {
  id: 'ID',
  name: 'Наименование',
  slug: 'URL слаг',
  description: 'Описание',
  parentId: 'Родитель ID',
  level: 'Уровень',
  path: 'Путь',
  icon: 'Иконка',
}

// Явный список ключей для колонок, чтобы избежать зависимости от runtime данных
const columnKeys: DisplayableCategoryKey[] = [
  'id',
  'name',
  'slug',
  'description',
  'level',
  'icon',
]

function createCategory() {
  editingCategory.value = {} // Пустой объект для создания
  dialogVisible.value = true
}

function editCategory(data: CategoryWithCount) {
  editingCategory.value = { ...data }
  dialogVisible.value = true
}

async function handleSave(categoryData: CategoryFormData) {
  if (!categoryData) return;

  try {
    if (categoryData.id) {
      // Обновление существующей категории
      const { id } = categoryData
      // Разрешённые к обновлению поля
      const allowed = {
        name: categoryData.name,
        slug: categoryData.slug,
        description: categoryData.description,
        parentId: categoryData.parentId ?? null,
        icon: categoryData.icon,
      } as Record<string, any>
      // Убираем undefined, чтобы не перетирать поля
      const sanitizedData = Object.fromEntries(
        Object.entries(allowed).filter(([_, v]) => v !== undefined)
      )

      await trpc.crud.partCategory.update.mutate({
        where: { id: id as number },
        data: sanitizedData,
      })
    } else {
      // Создание новой категории
      // Убедимся, что все обязательные поля присутствуют
      if (categoryData.name && categoryData.slug) {
        // Генерируем path и level на основе родительской категории
        let path = '01'
        let level = 0
        
        if (categoryData.parentId) {
          // Найдем родительскую категорию для получения path и level
          const parentCategory = await trpc.crud.partCategory.findUnique.query({
            where: { id: categoryData.parentId }
          })
          
          if (parentCategory) {
            level = parentCategory.level + 1
            // Генерируем новый path (упрощенная версия)
            const childrenCount = await trpc.crud.partCategory.count.query({
              where: { parentId: categoryData.parentId }
            })
            const nextNumber = String(childrenCount + 1).padStart(2, '0')
            path = `${parentCategory.path}/${nextNumber}`
          }
        }

        await trpc.crud.partCategory.create.mutate({
          data: {
            name: categoryData.name,
            slug: categoryData.slug,
            description: categoryData.description,
            parentId: categoryData.parentId,
            level,
            path,
            icon: categoryData.icon
          },
        })
      } else {
        console.error("Name and slug are required to create a category.")
        return
      }
    }
    // Перезагрузка страницы для отображения изменений
    navigate(window.location.href)
  } catch (error) {
    console.error('Failed to save category:', error)
  } finally {
    dialogVisible.value = false
  }
}

/**
 * Обработчик события отмены редактирования
 */
function handleCancel() {
  dialogVisible.value = false
  editingCategory.value = null
}

async function deleteCategory(data: CategoryWithCount) {
  dialogVisible.value = false
  // Здесь можно добавить диалог подтверждения удаления
  await trpc.crud.partCategory.delete.mutate({
    where: {
      id: data.id,
    },
  })

  navigate(window.location.href)
}

watch(searchValue, (newValue) => {
  debouncedSearch(newValue)
})

async function debouncedSearch(value = '') {
  console.log('value', value)

  const searchConditions = value ? {
    AND: [
      { level: 0 }, // Только корневые категории
      {
        OR: [
          {
            name: {
              contains: value,
            }
          },
          {
            slug: {
              contains: value,
            }
          },
          {
            description: {
              contains: value,
            }
          },
        ],
      }
    ]
  } : { level: 0 }

  items.value = await trpc.crud.partCategory.findMany.query({
    where: searchConditions,
    include: {
      image: true,
      _count: {
        select: {
          parts: true,
          children: true,
        }
      }
    },
    orderBy: {
      name: 'asc'
    }
  })
}

// Функция для загрузки дочерних категорий
async function loadChildren(parentId: number) {
  if (childrenCache.value[parentId]) {
    return childrenCache.value[parentId]
  }

  const children = await trpc.crud.partCategory.findMany.query({
    where: {
      parentId: parentId
    },
    include: {
      image: true,
      _count: {
        select: {
          parts: true,
          children: true,
        }
      }
    },
    orderBy: {
      name: 'asc'
    }
  })

  childrenCache.value[parentId] = children
  return children
}



// Проверяем, есть ли дочерние категории
function hasChildren(data: CategoryWithCount): boolean {
  return data._count.children > 0
}

// Обработчик события разворачивания строки
async function onRowExpand(event: { data: CategoryWithCount }) {
  if (event.data._count.children > 0) {
    await loadChildren(event.data.id)
  }
}
</script>

<template>
  <div>
    <div class="flex justify-between items-center mb-4">
      <h1 class="text-2xl font-bold">Категории запчастей</h1>
      <Button @click="createCategory">
        <PlusIcon class="w-5 h-5 mr-2" />
        Создать категорию
      </Button>
    </div>

    <DataTable 
      show-headers 
      :value="items" 
      v-model:expandedRows="expandedRows"
      @row-expand="onRowExpand"
      :rowHover="true"
    >
      <template #header>
        <div class="flex justify-end">
          <InputText v-model="searchValue" placeholder="Поиск" />
        </div>
      </template>
      
      <!-- Колонка для разворачивания -->
      <Column :expander="true" headerStyle="width: 3rem" />

      <Column
        v-for="key in columnKeys"
        :key="key"
        :field="key"
        :header="keyMapping[key] || key"
      >
        <template #body="{ data }" v-if="key === 'name'">
          <div class="flex items-center">
            <!-- Отступ для визуализации уровня вложенности -->
            <div :style="{ marginLeft: `${data.level * 20}px` }">
              {{ data[key] }}
            </div>
          </div>
        </template>
        <template #body="{ data }" v-else-if="key === 'description'">
          <div class="max-w-xs truncate" :title="data[key]">
            {{ data[key] || '-' }}
          </div>
        </template>
      </Column>
      
      <!-- Отдельные колонки для вложенных свойств -->
      <Column field="_count.parts" header="Кол-во деталей" />
      <Column field="_count.children" header="Подкатегории" />

      <Column header="Действия">
        <template #body="{ data }">
          <div class="flex gap-2">
            <Button @click="editCategory(data)" outlined size="small">
              <PencilIcon class="w-5 h-5" />
            </Button>
            <Button
              @click="deleteCategory(data)"
              outlined
              severity="danger"
              size="small"
            >
              <TrashIcon class="w-5 h-5" />
            </Button>
          </div>
        </template>
      </Column>

      <!-- Шаблон для развернутого содержимого -->
      <template #expansion="{ data }">
        <div class="p-4 bg-surface-50 dark:bg-surface-800">
          <h5 class="mb-3 font-semibold">Подкатегории: {{ data.name }}</h5>
          <div v-if="childrenCache[data.id] && childrenCache[data.id].length > 0">
            <DataTable :value="childrenCache[data.id]" class="mb-4">
              <Column field="name" header="Наименование">
                <template #body="{ data: child }">
                  <div class="flex items-center">
                    <div :style="{ marginLeft: `${(child.level - data.level - 1) * 20}px` }">
                      {{ child.name }}
                    </div>
                  </div>
                </template>
              </Column>
              <Column field="slug" header="URL слаг" />
              <Column field="description" header="Описание">
                <template #body="{ data: child }">
                  <div class="max-w-xs truncate" :title="child.description">
                    {{ child.description || '-' }}
                  </div>
                </template>
              </Column>
              <Column field="_count.parts" header="Кол-во деталей" />
              <Column field="_count.children" header="Подкатегории" />
              <Column header="Действия">
                <template #body="{ data: child }">
                  <div class="flex gap-2">
                    <Button @click="editCategory(child)" outlined size="small">
                      <PencilIcon class="w-4 h-4" />
                    </Button>
                    <Button
                      @click="deleteCategory(child)"
                      outlined
                      severity="danger"
                      size="small"
                    >
                      <TrashIcon class="w-4 h-4" />
                    </Button>
                  </div>
                </template>
              </Column>
            </DataTable>
          </div>
          <div v-else class="text-surface-500">
            Подкатегории отсутствуют
          </div>
        </div>
      </template>
    </DataTable>

    <EditCategoryDialog
      v-model:isVisible="dialogVisible"
      :category="editingCategory"
      @save="handleSave"
      @cancel="handleCancel"
    />

    <!-- Toast контейнер для изолята категорий -->
    <Toast />
  </div>
</template>