# Инструкция по настройке монорепозитория Parttec

## Предварительные требования

Перед выполнением настройки необходимо создать следующие репозитории на GitHub:

1. **parttec3-cpanel** - https://github.com/xferqr/parttec3-cpanel.git
2. **parttec3-site** - https://github.com/xferqr/parttec3-site.git

⚠️ **ВАЖНО**: Создайте пустые репозитории без README, .gitignore или лицензии!

## Автоматическая настройка

### Для Linux/macOS:
```bash
chmod +x setup-monorepo.sh
./setup-monorepo.sh
```

### Для Windows:
```cmd
setup-monorepo.bat
```

## Ручная настройка

Если автоматический скрипт не работает, выполните следующие шаги вручную:

### 1. Создайте репозитории на GitHub
- Перейдите на https://github.com/xferqr
- Создайте новый репозиторий `parttec3-cpanel` (пустой)
- Создайте новый репозиторий `parttec3-site` (пустой)

### 2. Отправьте код cpanel в его репозиторий
```bash
cd cpanel
git remote add origin https://github.com/xferqr/parttec3-cpanel.git
git push -u origin master
cd ..
```

### 3. Отправьте код site в его репозиторий
```bash
cd site
git remote add origin https://github.com/xferqr/parttec3-site.git
git push -u origin master
cd ..
```

### 4. Удалите папки из git индекса
```bash
git rm -r --cached cpanel site
```

### 5. Временно переименуйте папки
```bash
mv cpanel cpanel_temp
mv site site_temp
```

### 6. Добавьте подмодули
```bash
git submodule add https://github.com/xferqr/parttec3-cpanel.git cpanel
git submodule add https://github.com/xferqr/parttec3-site.git site
```

### 7. Инициализируйте подмодули
```bash
git submodule update --init --recursive
```

### 8. Удалите временные папки
```bash
rm -rf cpanel_temp site_temp
```

### 9. Зафиксируйте изменения
```bash
git add .
git commit -m "Setup monorepo with submodules"
git push origin master
```

## Проверка настройки

После завершения настройки проверьте:

```bash
# Проверьте статус подмодулей
git submodule status

# Проверьте содержимое .gitmodules
cat .gitmodules

# Проверьте структуру
ls -la
```

Должна быть следующая структура:
```
parttec/
├── .gitmodules
├── README.md
├── api/           (подмодуль)
├── cpanel/        (подмодуль)
└── site/          (подмодуль)
```

## Работа с подмодулями

### Клонирование монорепозитория
```bash
git clone --recursive https://github.com/xferqr/parttec3.git
```

### Обновление подмодулей
```bash
git submodule update --remote
```

### Работа в подмодуле
```bash
cd api  # или cpanel, site
git checkout main
# делаем изменения
git add .
git commit -m "Update"
git push

# Возвращаемся в основной репозиторий
cd ..
git add api  # или cpanel, site
git commit -m "Update submodule"
git push
```

### Переключение на конкретный коммит подмодуля
```bash
cd api
git checkout <commit-hash>
cd ..
git add api
git commit -m "Pin API to specific commit"
```

## Устранение проблем

### Если подмодуль показывает изменения
```bash
cd <submodule>
git status
git checkout .  # отменить локальные изменения
# или
git add . && git commit -m "Save changes"
```

### Если подмодуль отстает от удаленного репозитория
```bash
git submodule update --remote <submodule>
git add <submodule>
git commit -m "Update submodule to latest"
```

### Полная переинициализация подмодулей
```bash
git submodule deinit --all
git submodule update --init --recursive
```
