import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import type { PartListItem } from '@/types/catalog';
import { navigate } from 'astro:transitions/client';

interface PartCardProps {
  part: PartListItem;
  onClick?: () => void;
}

export function PartCard({ part, onClick }: PartCardProps) {
  const handleClick = () => {
    if (onClick) {
      onClick();
    } else {
      navigate(`/catalog/parts/${part.id}`);
    }
  };

  return (
    <Card
      className="cursor-pointer transition-all hover:shadow-md hover:scale-[1.02]"
      onClick={handleClick}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg line-clamp-2">
              {part.name || `Запчасть #${part.id}`}
            </CardTitle>
            <CardDescription className="mt-1">
              {part.partCategory.name}
            </CardDescription>
          </div>
          {part.image && (
            <img
              src={part.image.url}
              alt={part.name || 'Изображение запчасти'}
              className="w-16 h-16 object-cover rounded-md ml-3"
            />
          )}
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {/* Основные атрибуты */}
        {part.attributes.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-muted-foreground">Характеристики:</h4>
            <div className="grid grid-cols-1 gap-1 text-sm">
              {part.attributes.slice(0, 3).map((attr) => (
                <div key={attr.id} className="flex justify-between">
                  <span className="text-muted-foreground">{attr.template.title}:</span>
                  <span className="font-medium">
                    {attr.value}
                    {attr.template.unit && ` ${attr.template.unit}`}
                  </span>
                </div>
              ))}
              {part.attributes.length > 3 && (
                <div className="text-xs text-muted-foreground">
                  +{part.attributes.length - 3} характеристик
                </div>
              )}
            </div>
          </div>
        )}

        {/* Дата обновления */}
        <div className="mt-3 pt-3 border-t">
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <span>Обновлено:</span>
            <span>{new Date(part.updatedAt).toLocaleDateString('ru-RU')}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
