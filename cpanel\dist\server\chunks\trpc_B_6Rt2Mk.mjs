import { createTR<PERSON><PERSON><PERSON>, loggerLink, httpBatchLink } from '@trpc/client';
import superjson from 'superjson';

const getBaseUrl = () => {
  if (typeof window !== "undefined") return "http://localhost";
  return process.env.API_URL || "http://localhost";
};
const trpc = createTRPCClient({
  links: [
    loggerLink({ enabled: () => false }),
    httpBatchLink({
      url: `${getBaseUrl()}:3000/trpc`,
      transformer: superjson,
      fetch: (url, options) => fetch(url, { ...options, credentials: "include" })
    })
  ]
});

export { trpc as t };
