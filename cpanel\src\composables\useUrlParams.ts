/**
 * Composable для работы с URL параметрами и состоянием фильтров
 * Обеспечивает синхронизацию состояния фильтров с URL параметрами в Astro
 */

import { ref, computed, watch, onMounted, type Ref } from 'vue'
import { navigate } from 'astro:transitions/client'

// Backward-compat re-exports for typed helpers
export { useSearchFilters } from './useSearchFilters'

export interface FilterState {
    [key: string]: string | string[] | number | number[] | boolean | null | undefined
}

export interface UrlParamsOptions {
    // Префикс для параметров (например, 'filter_')
    prefix?: string
    // Параметры, которые должны быть массивами
    arrayParams?: string[]
    // Параметры, которые должны быть числами
    numberParams?: string[]
    // Параметры, которые должны быть булевыми
    booleanParams?: string[]
    // Дебаунс для обновления URL (мс)
    debounceMs?: number
    // Заменять ли историю браузера вместо добавления новой записи
    replaceHistory?: boolean
}

export const useUrlParams = <T extends FilterState>(
    initialState: T,
    options: UrlParamsOptions = {}
) => {
    const {
        prefix = '',
        arrayParams = [],
        numberParams = [],
        booleanParams = [],
        debounceMs = 300,
        replaceHistory = true
    } = options

    // Реактивное состояние фильтров
    const filters = ref<T>({ ...initialState }) as Ref<T>

    // Таймер для дебаунса
    let debounceTimer: NodeJS.Timeout | null = null

    // Функция для преобразования значения в строку для URL
    const valueToString = (key: string, value: any): string | string[] | undefined => {
        if (value === null || value === undefined || value === '') {
            return undefined
        }

        if (arrayParams.includes(key)) {
            if (Array.isArray(value)) {
                return value.map(v => String(v)).filter(v => v !== '')
            }
            return [String(value)].filter(v => v !== '')
        }

        return String(value)
    }

    // Функция для преобразования строки из URL в значение
    const stringToValue = (key: string, value: string | string[]): any => {
        if (!value || (Array.isArray(value) && value.length === 0)) {
            return arrayParams.includes(key) ? [] : null
        }

        if (arrayParams.includes(key)) {
            const arr = Array.isArray(value) ? value : [value]
            if (numberParams.includes(key)) {
                return arr.map(v => {
                    const num = Number(v)
                    return isNaN(num) ? null : num
                }).filter(v => v !== null)
            }
            if (booleanParams.includes(key)) {
                return arr.map(v => v === 'true' || v === '1')
            }
            return arr
        }

        if (numberParams.includes(key)) {
            const num = Number(Array.isArray(value) ? value[0] : value)
            return isNaN(num) ? null : num
        }

        if (booleanParams.includes(key)) {
            const val = Array.isArray(value) ? value[0] : value
            return val === 'true' || val === '1'
        }

        return Array.isArray(value) ? value[0] : value
    }

    // Функция для получения текущих URL параметров
    const getCurrentParams = (): URLSearchParams => {
        if (typeof window === 'undefined') {
            return new URLSearchParams()
        }
        return new URLSearchParams(window.location.search)
    }

    // Функция для загрузки состояния из URL
    const loadFromUrl = () => {
        const params = getCurrentParams()
        const newFilters = { ...initialState }

        Object.keys(newFilters).forEach(key => {
            const paramKey = prefix + key
            const paramValue = params.get(paramKey)
            const paramValues = params.getAll(paramKey)

            if (paramValue !== null || paramValues.length > 0) {
                if (arrayParams.includes(key) && paramValues.length > 0) {
                    newFilters[key as keyof T] = stringToValue(key, paramValues)
                } else if (paramValue !== null) {
                    newFilters[key as keyof T] = stringToValue(key, paramValue)
                }
            }
        })

        filters.value = newFilters
    }

    // Функция для сохранения состояния в URL
    const saveToUrl = () => {
        if (typeof window === 'undefined') return

        if (debounceTimer) {
            clearTimeout(debounceTimer)
        }

        debounceTimer = setTimeout(() => {
            const params = getCurrentParams()

            // Удаляем старые параметры фильтров
            const keysToDelete: string[] = []
            params.forEach((_, key) => {
                if (key.startsWith(prefix)) {
                    keysToDelete.push(key)
                }
            })
            keysToDelete.forEach(key => params.delete(key))

            // Добавляем новые параметры
            Object.entries(filters.value).forEach(([key, value]) => {
                const paramKey = prefix + key
                const stringValue = valueToString(key, value)

                if (stringValue !== undefined) {
                    if (Array.isArray(stringValue)) {
                        stringValue.forEach(v => params.append(paramKey, v))
                    } else {
                        params.set(paramKey, stringValue)
                    }
                }
            })

            // Обновляем URL без навигации (без перерендера островов), чтобы не было моргания
            const nextSearch = params.toString()
            const currentSearch = (typeof window !== 'undefined' && window.location.search)
              ? window.location.search.replace(/^\?/, '')
              : ''
            if (nextSearch !== currentSearch) {
              const url = new URL(window.location.href)
              url.search = nextSearch
              if (replaceHistory) {
                window.history.replaceState(null, '', url)
              } else {
                window.history.pushState(null, '', url)
              }
            }
        }, debounceMs)
    }

    // Функция для обновления фильтра
    const updateFilter = <K extends keyof T>(key: K, value: T[K]) => {
        filters.value[key] = value
    }

    // Функция для обновления нескольких фильтров
    const updateFilters = (updates: Partial<T>) => {
        Object.assign(filters.value, updates)
    }

    // Функция для сброса фильтров
    const resetFilters = () => {
        filters.value = { ...initialState }
    }

    // Функция для сброса конкретного фильтра
    const resetFilter = <K extends keyof T>(key: K) => {
        filters.value[key] = initialState[key]
    }

    // Функция для проверки, есть ли активные фильтры
    const hasActiveFilters = computed(() => {
        return Object.entries(filters.value).some(([key, value]) => {
            const initialValue = initialState[key as keyof T]

            if (Array.isArray(value) && Array.isArray(initialValue)) {
                return value.length !== initialValue.length ||
                    !value.every((v, i) => v === initialValue[i])
            }

            return value !== initialValue
        })
    })

    // Функция для получения количества активных фильтров
    const activeFiltersCount = computed(() => {
        return Object.entries(filters.value).reduce((count, [key, value]) => {
            const initialValue = initialState[key as keyof T]

            if (Array.isArray(value)) {
                return count + (value.length > 0 ? 1 : 0)
            }

            if (value !== initialValue && value !== null && value !== undefined && value !== '') {
                return count + 1
            }

            return count
        }, 0)
    })

    // Функция для получения строкового представления фильтров (для отладки)
    const filtersString = computed(() => {
        return JSON.stringify(filters.value, null, 2)
    })

    // Отслеживаем изменения фильтров и сохраняем в URL
    watch(filters, saveToUrl, { deep: true })

    // Загружаем состояние из URL при монтировании
    onMounted(() => {
        loadFromUrl()

        // Слушаем изменения URL (например, при навигации назад/вперед)
        if (typeof window !== 'undefined') {
            const handlePopState = () => {
                loadFromUrl()
            }

            window.addEventListener('popstate', handlePopState)

            // Очистка при размонтировании
            return () => {
                window.removeEventListener('popstate', handlePopState)
            }
        }
    })

    return {
        // Состояние
        filters: computed(() => filters.value),
        hasActiveFilters,
        activeFiltersCount,
        filtersString,

        // Методы
        updateFilter,
        updateFilters,
        resetFilters,
        resetFilter,
        loadFromUrl,
        saveToUrl
    }
}

// Типизированные хелперы перенесены в специализированные composables
// см. useCatalogFilters.ts и useSearchFilters.ts
