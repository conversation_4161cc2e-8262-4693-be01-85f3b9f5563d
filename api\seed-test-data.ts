import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('Создание тестовых данных...');

  // Создаем бренды
  const caterpillar = await prisma.brand.upsert({
    where: { slug: 'caterpillar' },
    update: {},
    create: {
      name: 'Caterpillar',
      slug: 'caterpillar',
      country: 'США',
      isOem: true
    }
  });

  const komatsu = await prisma.brand.upsert({
    where: { slug: 'komatsu' },
    update: {},
    create: {
      name: '<PERSON><PERSON><PERSON>',
      slug: 'komatsu',
      country: 'Япония',
      isOem: true
    }
  });

  const skf = await prisma.brand.upsert({
    where: { slug: 'skf' },
    update: {},
    create: {
      name: 'SKF',
      slug: 'skf',
      country: 'Швеция',
      isOem: false
    }
  });

  console.log('Бренды созданы:', { caterpillar: caterpillar.name, komatsu: komatsu.name, skf: skf.name });

  // Создаем модели техники
  const cat320d = await prisma.equipmentModel.upsert({
    where: { id: 'cat-320d' },
    update: {},
    create: {
      id: 'cat-320d',
      name: 'CAT 320D Экскаватор',
      brandId: caterpillar.id
    }
  });

  const cat330d = await prisma.equipmentModel.upsert({
    where: { id: 'cat-330d' },
    update: {},
    create: {
      id: 'cat-330d',
      name: 'CAT 330D Экскаватор',
      brandId: caterpillar.id
    }
  });

  const komatsuPC200 = await prisma.equipmentModel.upsert({
    where: { id: 'komatsu-pc200-8' },
    update: {},
    create: {
      id: 'komatsu-pc200-8',
      name: 'Komatsu PC200-8 Экскаватор',
      brandId: komatsu.id
    }
  });

  console.log('Модели техники созданы:', { 
    cat320d: cat320d.name, 
    cat330d: cat330d.name, 
    komatsuPC200: komatsuPC200.name 
  });

  // Создаем категорию запчастей
  const engineCategory = await prisma.partCategory.upsert({
    where: { slug: 'engine' },
    update: {},
    create: {
      name: 'Двигатель',
      slug: 'engine',
      description: 'Запчасти для двигателя',
      level: 0,
      path: '/engine'
    }
  });

  console.log('Категория создана:', engineCategory.name);

  // Создаем каталожные позиции
  const oilSeal = await prisma.catalogItem.upsert({
    where: {
      sku_brandId: {
        sku: 'SKF-12345',
        brandId: skf.id
      }
    },
    update: {},
    create: {
      sku: 'SKF-12345',
      brandId: skf.id,
      description: 'Сальник коленвала передний',
      isPublic: true
    }
  });

  console.log('Каталожная позиция создана:', oilSeal.sku);

  // Создаем тестового пользователя-администратора
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Администратор',
      role: 'ADMIN',
      emailVerified: true
    }
  });

  console.log('Администратор создан:', adminUser.email);

  console.log('Тестовые данные успешно созданы!');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
