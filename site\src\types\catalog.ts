// Типы, соответствующие реальной схеме ZenStack из catalog_schema.zmodel

export interface MediaAsset {
  id: number
  fileName: string
  mimeType: string
  fileSize?: number | null
  url: string
  createdAt: string
  updatedAt: string
}

export interface AttributeTemplate {
  id: number
  name: string
  title: string
  description?: string | null
  dataType: "STRING" | "NUMBER" | "BOOLEAN" | "DATE" | "JSON"
  unit?: "MM" | "INCH" | "FT" | "G" | "KG" | "T" | "LB" | "ML" | "L" | "GAL" | "SEC" | "MIN" | "H" | "PCS" | "SET" | "PAIR" | "BAR" | "PSI" | "KW" | "HP" | "NM" | "RPM" | "C" | "F" | "PERCENT" | null
  isRequired: boolean
  minValue?: number | null
  maxValue?: number | null
  allowedValues: string[]
  tolerance?: number | null
  groupId?: number | null
}

export interface PartAttribute {
  id: number
  value: string
  numericValue?: number | null
  partId: number
  templateId: number
  template: AttributeTemplate
}

export interface CatalogItemAttribute {
  id: number
  value: string
  numericValue?: number | null
  catalogItemId: number
  templateId: number
  template: AttributeTemplate
}

export interface Brand {
  id: number
  name: string
  slug: string
  country?: string | null
  isOem: boolean
}

export interface PartCategory {
  id: number
  name: string
  slug: string
  description?: string | null
  parentId?: number | null
  level: number
  path: string
  icon?: string | null
  image?: MediaAsset | null
}

export interface Part {
  id: number
  name?: string | null
  parentId?: number | null
  level: number
  path: string
  partCategoryId: number
  partCategory: PartCategory
  attributes: PartAttribute[]
  image?: MediaAsset | null
  mediaAssets: MediaAsset[]
  createdAt: string
  updatedAt: string
}

export interface CatalogItem {
  id: number
  sku: string
  source?: string | null
  description?: string | null
  brandId: number
  brand: Brand
  isPublic: boolean
  attributes: CatalogItemAttribute[]
  image?: MediaAsset | null
  mediaAssets: MediaAsset[]
}

export interface PartApplicability {
  id: number
  partId: number
  catalogItemId: number
  accuracy: "EXACT_MATCH" | "MATCH_WITH_NOTES" | "REQUIRES_MODIFICATION" | "PARTIAL_MATCH"
  notes?: string | null
  part: Part
  catalogItem: CatalogItem
}

// Дополнительные типы из схемы
export interface AttributeSynonymGroup {
  id: number
  name: string
  description?: string | null
  templateId: number
  parentId?: number | null
  canonicalValue?: string | null
  compatibilityLevel: "EXACT" | "NEAR" | "LEGACY"
  notes?: string | null
}

export interface AttributeSynonym {
  id: number
  value: string
  groupId: number
  notes?: string | null
  brandId?: number | null
  compatibilityLevel?: "EXACT" | "NEAR" | "LEGACY" | null
}

export interface EquipmentModel {
  id: string
  name: string
  brandId?: number | null
  brand?: Brand | null
  createdAt: string
  updatedAt: string
}

export interface EquipmentApplicability {
  id: number
  partId: number
  equipmentModelId: string
  notes?: string | null
  part: Part
  equipmentModel: EquipmentModel
}

// Дополнительные типы для поиска
export interface CatalogSearchFilters {
  query: string
  categoryIds: number[]
  brandIds: number[]
  attributeFilters: Record<
    number,
    {
      values: string[]
      numericRange?: [number, number]
    }
  >
  accuracyLevels: string[]
  isOemOnly: boolean
}

// Типы уже экспортированы выше

// Совместимость с существующими типами
export interface PartListItem {
  id: number;
  name: string | null;
  path: string;
  updatedAt: Date;
  partCategory: {
    id: number;
    name: string;
    slug: string;
  };
  image?: {
    id: number;
    url: string;
  } | null;
  attributes: Array<{
    id: number;
    value: string;
    numericValue: number | null;
    template: {
      id: number;
      name: string;
      title: string;
      dataType: string;
      unit: string | null;
    };
  }>;
}

export interface PartCategoryItem {
  id: number;
  name: string;
  slug: string;
  description: string | null;
  level: number;
  path: string;
  icon: string | null;
  image?: {
    id: number;
    url: string;
  } | null;
  children?: PartCategoryItem[];
  _count?: {
    parts: number;
  };
}

export interface BrandItem {
  id: number;
  name: string;
  slug: string;
  country: string | null;
  isOem: boolean;
  _count?: {
    catalogItems: number;
    equipmentModel: number;
  };
}

export interface CatalogItemDetail {
  id: number;
  sku: string;
  description: string | null;
  source: string | null;
  brand: BrandItem;
  attributes: Array<{
    id: number;
    value: string;
    numericValue: number | null;
    template: {
      id: number;
      name: string;
      title: string;
      dataType: string;
      unit: string | null;
    };
  }>;
}

export interface PartDetail extends PartListItem {
  parent?: {
    id: number;
    name: string | null;
    partCategory: {
      id: number;
      name: string;
    };
  } | null;
  children: Array<{
    id: number;
    name: string | null;
    partCategory: {
      id: number;
      name: string;
    };
  }>;
  applicabilities: Array<{
    id: number;
    accuracy: 'EXACT_MATCH' | 'MATCH_WITH_NOTES' | 'REQUIRES_MODIFICATION' | 'PARTIAL_MATCH';
    notes: string | null;
    catalogItem: CatalogItemDetail;
  }>;
  equipmentApplicabilities: Array<{
    id: number;
    notes: string | null;
    equipmentModel: {
      id: string;
      name: string;
      brand?: BrandItem | null;
    };
  }>;
}

export interface SearchFilters {
  query?: string;
  categoryId?: number;
  brandId?: number;
  attributes?: Record<string, string>;
  isOem?: boolean;
}

export interface PaginationParams {
  page: number;
  limit: number;
}

export interface SearchParams extends SearchFilters, PaginationParams {
  sortBy?: 'name' | 'updatedAt' | 'category';
  sortOrder?: 'asc' | 'desc';
}
