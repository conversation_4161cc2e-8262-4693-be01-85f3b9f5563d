import{j as e}from"./jsx-runtime.D_zvdyIk.js";import{r as c}from"./index.0yr9KlQE.js";import{M as Le,t as oe,T as Fe}from"./TrpcBoundary.g7wmOpBw.js";import{P as k,u as pe,c as fe,a as A,b as Be,d as Ke,M as K,e as $,f as q,g as U}from"./index.Di12BYGB.js";import{M as Q}from"./modern-input.Cd5D-6fr.js";import{B as R}from"./badge.CrhVNNKw.js";import{u as $e,a as ze,C as z,S as Ve}from"./separator.1hI8CDE4.js";import{c as W}from"./utils.CBfrqCZ4.js";import{u as _}from"./index.BWNh2K4G.js";import{u as He,X as qe}from"./index.NogD4Y5M.js";import{B as Ue}from"./button.BfGcEciz.js";import{c as Ye,u as Xe}from"./index.k1eM7uTc.js";import{c as ie}from"./createLucideIcon.DdiNmGRb.js";import{S as J}from"./search.ooeMVQga.js";import{S as Ge}from"./status-badge.BsGsEBtY.js";import{F as Qe}from"./funnel.Ck0Ax3XR.js";import{u as We}from"./useCatalogSearch.3hHK2Zsb.js";import{u as le}from"./trpc.9HS4D061.js";import"./index.3rXK4OIH.js";import"./index.ViApDAiE.js";/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Je=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],V=ie("chevron-down",Je);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ze=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],H=ie("chevron-up",Ze);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const et=[["line",{x1:"4",x2:"4",y1:"21",y2:"14",key:"1p332r"}],["line",{x1:"4",x2:"4",y1:"10",y2:"3",key:"gb41h5"}],["line",{x1:"12",x2:"12",y1:"21",y2:"12",key:"hf2csr"}],["line",{x1:"12",x2:"12",y1:"8",y2:"3",key:"1kfi7u"}],["line",{x1:"20",x2:"20",y1:"21",y2:"16",key:"1lhrwl"}],["line",{x1:"20",x2:"20",y1:"12",y2:"3",key:"16vvfq"}],["line",{x1:"2",x2:"6",y1:"14",y2:"14",key:"1uebub"}],["line",{x1:"10",x2:"14",y1:"8",y2:"8",key:"1yglbp"}],["line",{x1:"18",x2:"22",y1:"16",y2:"16",key:"1jxqpz"}]],tt=ie("sliders-vertical",et);var st="Label",ge=c.forwardRef((t,s)=>e.jsx(k.label,{...t,ref:s,onMouseDown:r=>{r.target.closest("button, input, select, textarea")||(t.onMouseDown?.(r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));ge.displayName=st;var rt=ge;function O({className:t,...s}){return e.jsx(rt,{"data-slot":"label",className:W("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...s})}var Z="Collapsible",[at,ts]=fe(Z),[nt,ce]=at(Z),ye=c.forwardRef((t,s)=>{const{__scopeCollapsible:r,open:n,defaultOpen:o,disabled:l,onOpenChange:a,...d}=t,[h,f]=pe({prop:n,defaultProp:o??!1,onChange:a,caller:Z});return e.jsx(nt,{scope:r,disabled:l,contentId:He(),open:h,onOpenToggle:c.useCallback(()=>f(x=>!x),[f]),children:e.jsx(k.div,{"data-state":ue(h),"data-disabled":l?"":void 0,...d,ref:s})})});ye.displayName=Z;var be="CollapsibleTrigger",ve=c.forwardRef((t,s)=>{const{__scopeCollapsible:r,...n}=t,o=ce(be,r);return e.jsx(k.button,{type:"button","aria-controls":o.contentId,"aria-expanded":o.open||!1,"data-state":ue(o.open),"data-disabled":o.disabled?"":void 0,disabled:o.disabled,...n,ref:s,onClick:A(t.onClick,o.onOpenToggle)})});ve.displayName=be;var de="CollapsibleContent",je=c.forwardRef((t,s)=>{const{forceMount:r,...n}=t,o=ce(de,t.__scopeCollapsible);return e.jsx(Be,{present:r||o.open,children:({present:l})=>e.jsx(ot,{...n,ref:s,present:l})})});je.displayName=de;var ot=c.forwardRef((t,s)=>{const{__scopeCollapsible:r,present:n,children:o,...l}=t,a=ce(de,r),[d,h]=c.useState(n),f=c.useRef(null),x=_(s,f),u=c.useRef(0),p=u.current,b=c.useRef(0),N=b.current,m=a.open||d,y=c.useRef(m),g=c.useRef(void 0);return c.useEffect(()=>{const v=requestAnimationFrame(()=>y.current=!1);return()=>cancelAnimationFrame(v)},[]),Ke(()=>{const v=f.current;if(v){g.current=g.current||{transitionDuration:v.style.transitionDuration,animationName:v.style.animationName},v.style.transitionDuration="0s",v.style.animationName="none";const j=v.getBoundingClientRect();u.current=j.height,b.current=j.width,y.current||(v.style.transitionDuration=g.current.transitionDuration,v.style.animationName=g.current.animationName),h(n)}},[a.open,n]),e.jsx(k.div,{"data-state":ue(a.open),"data-disabled":a.disabled?"":void 0,id:a.contentId,hidden:!m,...l,ref:x,style:{"--radix-collapsible-content-height":p?`${p}px`:void 0,"--radix-collapsible-content-width":N?`${N}px`:void 0,...t.style},children:m&&o})});function ue(t){return t?"open":"closed"}var it=ye;function Y({...t}){return e.jsx(it,{"data-slot":"collapsible",...t})}function X({...t}){return e.jsx(ve,{"data-slot":"collapsible-trigger",...t})}function G({...t}){return e.jsx(je,{"data-slot":"collapsible-content",...t})}function Ne(t,[s,r]){return Math.min(r,Math.max(s,t))}var we=["PageUp","PageDown"],Se=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],Ce={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},D="Slider",[re,lt,ct]=Ye(D),[Re,ss]=fe(D,[ct]),[dt,ee]=Re(D),ke=c.forwardRef((t,s)=>{const{name:r,min:n=0,max:o=100,step:l=1,orientation:a="horizontal",disabled:d=!1,minStepsBetweenThumbs:h=0,defaultValue:f=[n],value:x,onValueChange:u=()=>{},onValueCommit:p=()=>{},inverted:b=!1,form:N,...m}=t,y=c.useRef(new Set),g=c.useRef(0),j=a==="horizontal"?ut:mt,[w=[],E]=pe({prop:x,defaultProp:f,onChange:C=>{[...y.current][g.current]?.focus(),u(C)}}),L=c.useRef(w);function F(C){const P=gt(w,C);M(C,P)}function i(C){M(C,g.current)}function S(){const C=L.current[g.current];w[g.current]!==C&&p(w)}function M(C,P,{commit:te}={commit:!1}){const he=jt(l),se=Nt(Math.round((C-n)/l)*l+n,he),B=Ne(se,[n,o]);E((T=[])=>{const I=pt(T,B,P);if(vt(I,h*l)){g.current=I.indexOf(B);const xe=String(I)!==String(T);return xe&&te&&p(I),xe?I:T}else return T})}return e.jsx(dt,{scope:t.__scopeSlider,name:r,disabled:d,min:n,max:o,valueIndexToChangeRef:g,thumbs:y.current,values:w,orientation:a,form:N,children:e.jsx(re.Provider,{scope:t.__scopeSlider,children:e.jsx(re.Slot,{scope:t.__scopeSlider,children:e.jsx(j,{"aria-disabled":d,"data-disabled":d?"":void 0,...m,ref:s,onPointerDown:A(m.onPointerDown,()=>{d||(L.current=w)}),min:n,max:o,inverted:b,onSlideStart:d?void 0:F,onSlideMove:d?void 0:i,onSlideEnd:d?void 0:S,onHomeKeyDown:()=>!d&&M(n,0,{commit:!0}),onEndKeyDown:()=>!d&&M(o,w.length-1,{commit:!0}),onStepKeyDown:({event:C,direction:P})=>{if(!d){const se=we.includes(C.key)||C.shiftKey&&Se.includes(C.key)?10:1,B=g.current,T=w[B],I=l*se*P;M(T+I,B,{commit:!0})}}})})})})});ke.displayName=D;var[Me,Ee]=Re(D,{startEdge:"left",endEdge:"right",size:"width",direction:1}),ut=c.forwardRef((t,s)=>{const{min:r,max:n,dir:o,inverted:l,onSlideStart:a,onSlideMove:d,onSlideEnd:h,onStepKeyDown:f,...x}=t,[u,p]=c.useState(null),b=_(s,j=>p(j)),N=c.useRef(void 0),m=Xe(o),y=m==="ltr",g=y&&!l||!y&&l;function v(j){const w=N.current||u.getBoundingClientRect(),E=[0,w.width],F=me(E,g?[r,n]:[n,r]);return N.current=w,F(j-w.left)}return e.jsx(Me,{scope:t.__scopeSlider,startEdge:g?"left":"right",endEdge:g?"right":"left",direction:g?1:-1,size:"width",children:e.jsx(Pe,{dir:m,"data-orientation":"horizontal",...x,ref:b,style:{...x.style,"--radix-slider-thumb-transform":"translateX(-50%)"},onSlideStart:j=>{const w=v(j.clientX);a?.(w)},onSlideMove:j=>{const w=v(j.clientX);d?.(w)},onSlideEnd:()=>{N.current=void 0,h?.()},onStepKeyDown:j=>{const E=Ce[g?"from-left":"from-right"].includes(j.key);f?.({event:j,direction:E?-1:1})}})})}),mt=c.forwardRef((t,s)=>{const{min:r,max:n,inverted:o,onSlideStart:l,onSlideMove:a,onSlideEnd:d,onStepKeyDown:h,...f}=t,x=c.useRef(null),u=_(s,x),p=c.useRef(void 0),b=!o;function N(m){const y=p.current||x.current.getBoundingClientRect(),g=[0,y.height],j=me(g,b?[n,r]:[r,n]);return p.current=y,j(m-y.top)}return e.jsx(Me,{scope:t.__scopeSlider,startEdge:b?"bottom":"top",endEdge:b?"top":"bottom",size:"height",direction:b?1:-1,children:e.jsx(Pe,{"data-orientation":"vertical",...f,ref:u,style:{...f.style,"--radix-slider-thumb-transform":"translateY(50%)"},onSlideStart:m=>{const y=N(m.clientY);l?.(y)},onSlideMove:m=>{const y=N(m.clientY);a?.(y)},onSlideEnd:()=>{p.current=void 0,d?.()},onStepKeyDown:m=>{const g=Ce[b?"from-bottom":"from-top"].includes(m.key);h?.({event:m,direction:g?-1:1})}})})}),Pe=c.forwardRef((t,s)=>{const{__scopeSlider:r,onSlideStart:n,onSlideMove:o,onSlideEnd:l,onHomeKeyDown:a,onEndKeyDown:d,onStepKeyDown:h,...f}=t,x=ee(D,r);return e.jsx(k.span,{...f,ref:s,onKeyDown:A(t.onKeyDown,u=>{u.key==="Home"?(a(u),u.preventDefault()):u.key==="End"?(d(u),u.preventDefault()):we.concat(Se).includes(u.key)&&(h(u),u.preventDefault())}),onPointerDown:A(t.onPointerDown,u=>{const p=u.target;p.setPointerCapture(u.pointerId),u.preventDefault(),x.thumbs.has(p)?p.focus():n(u)}),onPointerMove:A(t.onPointerMove,u=>{u.target.hasPointerCapture(u.pointerId)&&o(u)}),onPointerUp:A(t.onPointerUp,u=>{const p=u.target;p.hasPointerCapture(u.pointerId)&&(p.releasePointerCapture(u.pointerId),l(u))})})}),Ie="SliderTrack",Ae=c.forwardRef((t,s)=>{const{__scopeSlider:r,...n}=t,o=ee(Ie,r);return e.jsx(k.span,{"data-disabled":o.disabled?"":void 0,"data-orientation":o.orientation,...n,ref:s})});Ae.displayName=Ie;var ae="SliderRange",_e=c.forwardRef((t,s)=>{const{__scopeSlider:r,...n}=t,o=ee(ae,r),l=Ee(ae,r),a=c.useRef(null),d=_(s,a),h=o.values.length,f=o.values.map(p=>De(p,o.min,o.max)),x=h>1?Math.min(...f):0,u=100-Math.max(...f);return e.jsx(k.span,{"data-orientation":o.orientation,"data-disabled":o.disabled?"":void 0,...n,ref:d,style:{...t.style,[l.startEdge]:x+"%",[l.endEdge]:u+"%"}})});_e.displayName=ae;var ne="SliderThumb",Te=c.forwardRef((t,s)=>{const r=lt(t.__scopeSlider),[n,o]=c.useState(null),l=_(s,d=>o(d)),a=c.useMemo(()=>n?r().findIndex(d=>d.ref.current===n):-1,[r,n]);return e.jsx(ht,{...t,ref:l,index:a})}),ht=c.forwardRef((t,s)=>{const{__scopeSlider:r,index:n,name:o,...l}=t,a=ee(ne,r),d=Ee(ne,r),[h,f]=c.useState(null),x=_(s,v=>f(v)),u=h?a.form||!!h.closest("form"):!0,p=$e(h),b=a.values[n],N=b===void 0?0:De(b,a.min,a.max),m=ft(n,a.values.length),y=p?.[d.size],g=y?yt(y,N,d.direction):0;return c.useEffect(()=>{if(h)return a.thumbs.add(h),()=>{a.thumbs.delete(h)}},[h,a.thumbs]),e.jsxs("span",{style:{transform:"var(--radix-slider-thumb-transform)",position:"absolute",[d.startEdge]:`calc(${N}% + ${g}px)`},children:[e.jsx(re.ItemSlot,{scope:t.__scopeSlider,children:e.jsx(k.span,{role:"slider","aria-label":t["aria-label"]||m,"aria-valuemin":a.min,"aria-valuenow":b,"aria-valuemax":a.max,"aria-orientation":a.orientation,"data-orientation":a.orientation,"data-disabled":a.disabled?"":void 0,tabIndex:a.disabled?void 0:0,...l,ref:x,style:b===void 0?{display:"none"}:t.style,onFocus:A(t.onFocus,()=>{a.valueIndexToChangeRef.current=n})})}),u&&e.jsx(Oe,{name:o??(a.name?a.name+(a.values.length>1?"[]":""):void 0),form:a.form,value:b},n)]})});Te.displayName=ne;var xt="RadioBubbleInput",Oe=c.forwardRef(({__scopeSlider:t,value:s,...r},n)=>{const o=c.useRef(null),l=_(o,n),a=ze(s);return c.useEffect(()=>{const d=o.current;if(!d)return;const h=window.HTMLInputElement.prototype,x=Object.getOwnPropertyDescriptor(h,"value").set;if(a!==s&&x){const u=new Event("input",{bubbles:!0});x.call(d,s),d.dispatchEvent(u)}},[a,s]),e.jsx(k.input,{style:{display:"none"},...r,ref:l,defaultValue:s})});Oe.displayName=xt;function pt(t=[],s,r){const n=[...t];return n[r]=s,n.sort((o,l)=>o-l)}function De(t,s,r){const l=100/(r-s)*(t-s);return Ne(l,[0,100])}function ft(t,s){return s>2?`Value ${t+1} of ${s}`:s===2?["Minimum","Maximum"][t]:void 0}function gt(t,s){if(t.length===1)return 0;const r=t.map(o=>Math.abs(o-s)),n=Math.min(...r);return r.indexOf(n)}function yt(t,s,r){const n=t/2,l=me([0,50],[0,n]);return(n-l(s)*r)*r}function bt(t){return t.slice(0,-1).map((s,r)=>t[r+1]-s)}function vt(t,s){if(s>0){const r=bt(t);return Math.min(...r)>=s}return!0}function me(t,s){return r=>{if(t[0]===t[1]||s[0]===s[1])return s[0];const n=(s[1]-s[0])/(t[1]-t[0]);return s[0]+n*(r-t[0])}}function jt(t){return(String(t).split(".")[1]||"").length}function Nt(t,s){const r=Math.pow(10,s);return Math.round(t*r)/r}var wt=ke,St=Ae,Ct=_e,Rt=Te;function kt({className:t,defaultValue:s,value:r,min:n=0,max:o=100,...l}){const a=c.useMemo(()=>Array.isArray(r)?r:Array.isArray(s)?s:[n,o],[r,s,n,o]);return e.jsxs(wt,{"data-slot":"slider",defaultValue:s,value:r,min:n,max:o,className:W("relative flex w-full touch-none items-center select-none data-[disabled]:opacity-50 data-[orientation=vertical]:h-full data-[orientation=vertical]:min-h-44 data-[orientation=vertical]:w-auto data-[orientation=vertical]:flex-col",t),...l,children:[e.jsx(St,{"data-slot":"slider-track",className:W("bg-muted relative grow overflow-hidden rounded-full data-[orientation=horizontal]:h-1.5 data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-1.5"),children:e.jsx(Ct,{"data-slot":"slider-range",className:W("bg-primary absolute data-[orientation=horizontal]:h-full data-[orientation=vertical]:w-full")})}),Array.from({length:a.length},(d,h)=>e.jsx(Rt,{"data-slot":"slider-thumb",className:"border-primary bg-background ring-ring/50 block size-4 shrink-0 rounded-full border shadow-sm transition-[color,box-shadow] hover:ring-4 focus-visible:ring-4 focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50"},h))]})}function Mt({template:t,selectedValues:s,numericRange:r,onValuesChange:n,onRangeChange:o,availableValues:l,numericStats:a}){const[d,h]=c.useState(!1),[f,x]=c.useState(""),u=m=>{s.includes(m)?n(s.filter(y=>y!==m)):n([...s,m])},p=t.dataType==="NUMBER",b=s.length>0||r&&a&&(r[0]>a.min||r[1]<a.max),N=l.filter(m=>m.toLowerCase().includes(f.toLowerCase()));return e.jsxs("div",{className:"border-2 border-border-strong rounded-lg bg-card animate-theme-transition",children:[e.jsxs(Ue,{variant:"ghost",onClick:()=>h(!d),className:"w-full justify-between p-4 text-foreground hover:bg-accent/50 transition-colors",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"font-medium",children:t.title}),t.unit&&e.jsx(R,{variant:"outline",className:"text-xs border-border text-muted-foreground",children:t.unit}),b&&e.jsx(R,{variant:"default",className:"text-xs bg-primary text-primary-foreground",children:s.length||"range"})]}),d?e.jsx(H,{className:"w-4 h-4"}):e.jsx(V,{className:"w-4 h-4"})]}),d&&e.jsxs("div",{className:"p-4 pt-0 space-y-3 border-t border-border",children:[t.description&&e.jsx("p",{className:"text-xs text-muted-foreground",children:t.description}),p&&a?e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex justify-between text-sm text-muted-foreground",children:[e.jsxs("span",{children:["Диапазон: ",a.min," - ",a.max]}),e.jsxs("span",{children:["Среднее: ",a.avg.toFixed(1)]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(O,{className:"text-sm text-foreground",children:["Выбранный диапазон: ",r?.[0]||a.min," - ",r?.[1]||a.max]}),e.jsx(kt,{value:r||[a.min,a.max],onValueChange:m=>o([m[0],m[1]]),min:a.min,max:a.max,step:t.tolerance||.1,className:"w-full"})]}),t.tolerance&&e.jsxs("p",{className:"text-xs text-muted-foreground",children:["Допуск: ±",t.tolerance," ",t.unit]})]}):e.jsxs("div",{className:"space-y-3",children:[l.length>5&&e.jsxs("div",{className:"relative",children:[e.jsx(J,{className:"absolute left-3 top-1/2 h-3 w-3 -translate-y-1/2 text-muted-foreground"}),e.jsx(Q,{placeholder:"Поиск значений...",value:f,onChange:m=>x(m.target.value),className:"pl-9 h-8 text-sm",variant:"ghost"})]}),e.jsxs("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:[N.map(m=>e.jsxs("div",{className:"flex items-center space-x-2 group",children:[e.jsx(z,{id:`${t.id}-${m}`,checked:s.includes(m),onCheckedChange:()=>u(m),className:"border-border data-[state=checked]:bg-primary data-[state=checked]:border-primary"}),e.jsx(O,{htmlFor:`${t.id}-${m}`,className:"text-sm text-foreground cursor-pointer flex-1 group-hover:text-primary transition-colors",children:m})]},m)),N.length===0&&f&&e.jsxs("p",{className:"text-sm text-muted-foreground text-center py-2",children:['Ничего не найдено по запросу "',f,'"']}),l.length===0&&e.jsx("p",{className:"text-sm text-muted-foreground text-center py-2",children:"Нет доступных значений"})]})]})]})]})}function Et({filters:t,setFilters:s,activeFiltersCount:r,onClearAll:n,mockCategories:o,mockBrands:l,mockAttributeTemplates:a,availableAttributeValues:d}){const[h,f]=c.useState(!1),[x,u]=c.useState({categories:!0,brands:!0,accuracy:!0}),[p,b]=c.useState(""),[N,m]=c.useState(""),[y,g]=c.useState(""),v=(i,S,M)=>{S.includes(i)?M(S.filter(C=>C!==i)):M([...S,i])},j=i=>u(S=>({...S,[i]:!S[i]})),w=[{value:"EXACT_MATCH",label:"Точное совпадение"},{value:"MATCH_WITH_NOTES",label:"С примечаниями"},{value:"REQUIRES_MODIFICATION",label:"Требует доработки"},{value:"PARTIAL_MATCH",label:"Частичное совпадение"}],E=o.filter(i=>i.name.toLowerCase().includes(p.toLowerCase())),L=l.filter(i=>i.name.toLowerCase().includes(N.toLowerCase())),F=w.filter(i=>i.label.toLowerCase().includes(y.toLowerCase()));return e.jsx("div",{className:"w-72 border-r border-border-strong bg-surface/50",children:e.jsx("div",{className:"sticky top-16 h-[calc(100vh-4rem)] overflow-y-auto",children:e.jsxs("div",{className:"p-4 space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("div",{className:"relative",children:[e.jsx(Qe,{className:"h-4 w-4 text-primary"}),r>0&&e.jsx("div",{className:"absolute -top-1 -right-1 h-3 w-3 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-bold",children:r})]}),e.jsxs("div",{children:[e.jsx("h2",{className:"font-semibold text-sm",children:"Фильтры"}),r>0&&e.jsxs("p",{className:"text-xs text-muted-foreground",children:[r," активных"]})]})]}),r>0&&e.jsxs(Le,{variant:"outline",size:"sm",onClick:n,className:"h-7 px-2 text-xs gap-1 hover:bg-destructive/10 hover:text-destructive hover:border-destructive/30",children:[e.jsx(qe,{className:"h-3 w-3"}),"Очистить"]})]}),r>0&&e.jsx(K,{variant:"glass",className:"border-primary/20 bg-primary/5",children:e.jsxs($,{className:"p-3",children:[e.jsx("div",{className:"flex items-center gap-2 mb-2",children:e.jsx("span",{className:"text-xs font-medium",children:"Активные фильтры"})}),e.jsxs("div",{className:"flex flex-wrap gap-1",children:[t.categoryIds.length>0&&e.jsxs(R,{variant:"secondary",className:"text-xs h-5",children:["Категории: ",t.categoryIds.length]}),t.brandIds.length>0&&e.jsxs(R,{variant:"secondary",className:"text-xs h-5",children:["Бренды: ",t.brandIds.length]}),t.accuracyLevels&&t.accuracyLevels.length>0&&e.jsxs(R,{variant:"secondary",className:"text-xs h-5",children:["Точность: ",t.accuracyLevels.length]}),t.attributeFilters&&Object.keys(t.attributeFilters).length>0&&e.jsxs(R,{variant:"secondary",className:"text-xs h-5",children:["Параметры: ",Object.keys(t.attributeFilters).length]}),t.isOemOnly&&e.jsx(R,{variant:"secondary",className:"text-xs h-5",children:"Только OEM"})]})]})}),e.jsx(K,{variant:"default",children:e.jsxs(Y,{open:x.categories,onOpenChange:()=>j("categories"),children:[e.jsx(X,{asChild:!0,children:e.jsx(q,{className:"cursor-pointer p-3 hover:bg-accent/5 transition-colors",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(U,{className:"text-sm",children:"Категории"}),t.categoryIds.length>0&&e.jsx(R,{variant:"default",className:"h-4 w-4 rounded-full p-0 text-xs",children:t.categoryIds.length})]}),x.categories?e.jsx(H,{className:"h-3 w-3 text-muted-foreground"}):e.jsx(V,{className:"h-3 w-3 text-muted-foreground"})]})})}),e.jsx(G,{children:e.jsxs($,{className:"p-3 pt-0 space-y-2",children:[o.length>5&&e.jsxs("div",{className:"relative",children:[e.jsx(J,{className:"absolute left-2 top-1/2 h-3 w-3 -translate-y-1/2 text-muted-foreground"}),e.jsx(Q,{placeholder:"Поиск категорий...",value:p,onChange:i=>b(i.target.value),className:"pl-7 h-7 text-xs",variant:"ghost"})]}),E.map(i=>e.jsxs("div",{className:"flex items-center space-x-2 group",children:[e.jsx(z,{id:`category-${i.id}`,checked:t.categoryIds.includes(i.id),onCheckedChange:()=>v(i.id,t.categoryIds,S=>s({...t,categoryIds:S})),className:"data-[state=checked]:bg-primary data-[state=checked]:border-primary h-3 w-3"}),e.jsx(O,{htmlFor:`category-${i.id}`,className:"text-xs cursor-pointer flex-1 group-hover:text-primary transition-colors",children:i.name})]},i.id))]})})]})}),e.jsx(K,{variant:"default",children:e.jsxs(Y,{open:x.brands,onOpenChange:()=>j("brands"),children:[e.jsx(X,{asChild:!0,children:e.jsx(q,{className:"cursor-pointer p-3 hover:bg-accent/5 transition-colors",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(U,{className:"text-sm",children:"Производители"}),(t.brandIds.length>0||t.isOemOnly)&&e.jsx(R,{variant:"default",className:"h-4 w-4 rounded-full p-0 text-xs",children:t.brandIds.length+(t.isOemOnly?1:0)})]}),x.brands?e.jsx(H,{className:"h-3 w-3 text-muted-foreground"}):e.jsx(V,{className:"h-3 w-3 text-muted-foreground"})]})})}),e.jsx(G,{children:e.jsxs($,{className:"p-3 pt-0 space-y-2",children:[e.jsxs("div",{className:"flex items-center space-x-2 p-2 rounded bg-accent/5 border border-border/30",children:[e.jsx(z,{id:"oem-only",checked:t.isOemOnly,onCheckedChange:i=>s({...t,isOemOnly:!!i}),className:"data-[state=checked]:bg-primary data-[state=checked]:border-primary h-3 w-3"}),e.jsx(O,{htmlFor:"oem-only",className:"text-xs cursor-pointer font-medium",children:"Только OEM производители"})]}),e.jsx(Ve,{className:"bg-border/30"}),l.length>5&&e.jsxs("div",{className:"relative",children:[e.jsx(J,{className:"absolute left-2 top-1/2 h-3 w-3 -translate-y-1/2 text-muted-foreground"}),e.jsx(Q,{placeholder:"Поиск брендов...",value:N,onChange:i=>m(i.target.value),className:"pl-7 h-7 text-xs",variant:"ghost"})]}),L.map(i=>e.jsxs("div",{className:"flex items-center space-x-2 group",children:[e.jsx(z,{id:`brand-${i.id}`,checked:t.brandIds.includes(i.id),onCheckedChange:()=>v(i.id,t.brandIds,S=>s({...t,brandIds:S})),className:"data-[state=checked]:bg-primary data-[state=checked]:border-primary h-3 w-3"}),e.jsxs(O,{htmlFor:`brand-${i.id}`,className:"text-xs cursor-pointer flex-1 group-hover:text-primary transition-colors flex items-center gap-1",children:[i.name,i.isOem&&e.jsx(R,{variant:"outline",className:"text-xs h-4",children:"OEM"})]})]},i.id))]})})]})}),e.jsx(K,{variant:"default",children:e.jsxs(Y,{open:x.accuracy,onOpenChange:()=>j("accuracy"),children:[e.jsx(X,{asChild:!0,children:e.jsx(q,{className:"cursor-pointer p-3 hover:bg-accent/5 transition-colors",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(U,{className:"text-sm",children:"Точность применимости"}),t.accuracyLevels&&t.accuracyLevels.length>0&&e.jsx(R,{variant:"default",className:"h-4 w-4 rounded-full p-0 text-xs",children:t.accuracyLevels.length})]}),x.accuracy?e.jsx(H,{className:"h-3 w-3 text-muted-foreground"}):e.jsx(V,{className:"h-3 w-3 text-muted-foreground"})]})})}),e.jsx(G,{children:e.jsxs($,{className:"p-3 pt-0 space-y-2",children:[e.jsxs("div",{className:"relative",children:[e.jsx(J,{className:"absolute left-2 top-1/2 h-3 w-3 -translate-y-1/2 text-muted-foreground"}),e.jsx(Q,{placeholder:"Поиск по точности...",value:y,onChange:i=>g(i.target.value),className:"pl-7 h-7 text-xs",variant:"ghost"})]}),F.map(i=>e.jsxs("div",{className:"flex items-center space-x-2 group",children:[e.jsx(z,{id:`accuracy-${i.value}`,checked:!!t.accuracyLevels?.includes(i.value),onCheckedChange:()=>v(i.value,t.accuracyLevels||[],S=>s({...t,accuracyLevels:S})),className:"data-[state=checked]:bg-primary data-[state=checked]:border-primary h-3 w-3"}),e.jsx(O,{htmlFor:`accuracy-${i.value}`,className:"text-xs cursor-pointer flex-1 group-hover:text-primary transition-colors",children:e.jsx(Ge,{status:i.value,size:"sm",className:"border-0 bg-transparent p-0 text-xs font-normal"})})]},i.value))]})})]})}),e.jsx(K,{variant:"default",children:e.jsxs(Y,{open:h,onOpenChange:f,children:[e.jsx(X,{asChild:!0,children:e.jsx(q,{className:"cursor-pointer p-3 hover:bg-accent/5 transition-colors",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(tt,{className:"h-3 w-3 text-primary"}),e.jsx(U,{className:"text-sm",children:"Технические параметры"}),t.attributeFilters&&Object.keys(t.attributeFilters).length>0&&e.jsx(R,{variant:"default",className:"h-4 w-4 rounded-full p-0 text-xs",children:Object.keys(t.attributeFilters).length})]}),h?e.jsx(H,{className:"h-3 w-3 text-muted-foreground"}):e.jsx(V,{className:"h-3 w-3 text-muted-foreground"})]})})}),e.jsx(G,{children:e.jsx($,{className:"p-3 pt-0 space-y-3",children:a.map(i=>e.jsx(Mt,{template:i,selectedValues:t.attributeFilters?.[i.id]?.values||[],numericRange:t.attributeFilters?.[i.id]?.numericRange,onValuesChange:S=>{s({...t,attributeFilters:{...t.attributeFilters||{},[i.id]:{...t.attributeFilters?.[i.id]||{},values:S}}})},onRangeChange:S=>{s({...t,attributeFilters:{...t.attributeFilters||{},[i.id]:{...t.attributeFilters?.[i.id]||{},numericRange:S}}})},availableValues:d.values[i.id]||[],numericStats:d.numericStats[i.id]},i.id))})})]})})]})})})}function Pt(t){const{data:s,isLoading:r,error:n}=le({queryKey:["site.catalog.categories",!1,200],queryFn:()=>oe.site.catalog.categories.query({rootOnly:!1,take:200}),staleTime:6e5,gcTime:36e5});return{categories:c.useMemo(()=>(Array.isArray(s)?s:[]).map(a=>({id:a.id,name:a.name,slug:a.slug,level:a.level,path:a.path})),[s]),isLoading:r,error:n}}function It(t){const{data:s,isLoading:r,error:n}=le({queryKey:["site.catalog.brands",100],queryFn:()=>oe.site.catalog.brands.query({take:100}),staleTime:6e5,gcTime:36e5});return{brands:c.useMemo(()=>(Array.isArray(s)?s:[]).map(a=>({id:a.id,name:a.name,slug:a.slug,isOem:a.isOem,country:a.country})),[s]),isLoading:r,error:n}}function At(t){const{data:s,isLoading:r,error:n}=le({queryKey:["site.attributes.templates",100],queryFn:()=>oe.site.attributes.templates.query({take:100}),staleTime:9e5,gcTime:36e5});return{templates:c.useMemo(()=>(Array.isArray(s)?s:[]).map(a=>({id:a.id,name:a.name,title:a.title,dataType:a.dataType,unit:a.unit,isRequired:a.isRequired,allowedValues:a.allowedValues,tolerance:a.tolerance})),[s]),isLoading:r,error:n}}function _t(){const t=Pt(),s=It(),r=At(),n=t.isLoading||s.isLoading||r.isLoading,o=t.error||s.error||r.error;return{categories:t.categories,brands:s.brands,templates:r.templates,isLoading:n,hasError:o,errors:{categories:t.error,brands:s.error,templates:r.error}}}function rs(){return e.jsx(Fe,{children:e.jsx(Tt,{})})}function Tt(){const{filters:t,setFilters:s,clearFilters:r,availableAttributeValues:n}=We(),{categories:o,brands:l,templates:a}=_t(),d=c.useMemo(()=>t.categoryIds.length+t.brandIds.length+Object.keys(t.attributeFilters).length+t.accuracyLevels.length+(t.isOemOnly?1:0),[t]);return e.jsx(Et,{filters:t,setFilters:s,activeFiltersCount:d,onClearAll:r,mockCategories:o,mockBrands:l,mockAttributeTemplates:a,availableAttributeValues:n})}export{rs as default};
