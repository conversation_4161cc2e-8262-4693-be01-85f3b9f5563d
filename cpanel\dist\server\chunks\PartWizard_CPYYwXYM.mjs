import { createElementBlock, openBlock, mergeProps, renderSlot, defineComponent, useSSRContext, ref, withCtx, resolveDirective, withDirectives, createCommentVNode, createElementVNode, createBlock, resolveDynamicComponent, normalizeClass, computed, watch, createTextVNode, toDisplayString, createVNode, resolveComponent, Fragment, renderList, createSlots, onMounted } from 'vue';
import BaseComponent from '@primevue/core/basecomponent';
import { style } from '@primeuix/styles/tabs';
import BaseStyle from '@primevue/core/base/style';
import { p as ptViewMerge, _ as _export_sfc, R as Ripple } from './ClientRouter_B8Zzhk9G.mjs';
import { ssrRenderComponent, ssrRenderSlot, ssrInterpolate, ssrRenderAttrs, ssrRenderList, ssrGetDirectiveProps, ssrRenderAttr } from 'vue/server-renderer';
import { cn } from '@primeuix/utils';
import { getWidth, getHeight, findSingle, getOuterHeight, getOffset, getOuterWidth, isRTL, focus, getAttribute } from '@primeuix/utils/dom';
import ChevronLeftIcon from '@primevue/icons/chevronleft';
import ChevronRightIcon from '@primevue/icons/chevronright';
import { equals, isNotEmpty, resolveFieldData } from '@primeuix/utils/object';
import { u as useTrpc } from './useTrpc_B-zNiBto.mjs';
import { V as VCard } from './Card_BWxK5e93.mjs';
import { V as VButton } from './Button_CuwpNmer.mjs';
import { I as InputText } from './InputText_CtReD0EA.mjs';
import { V as VAutoComplete } from './AutoComplete_CPZ3fUb-.mjs';
import { M as Message } from './Message_aZfC-dtu.mjs';
import { D as Dialog } from './Dialog_g9jHgXMZ.mjs';
import { V as VTextarea } from './Textarea_DrL1j52W.mjs';
import { Q as QuickCreateBrand, A as AttributeManager } from './QuickCreateBrand_CQ6NwQoR.mjs';
import BaseEditableHolder from '@primevue/core/baseeditableholder';
import { style as style$1 } from '@primeuix/styles/togglebutton';
import { style as style$2 } from '@primeuix/styles/selectbutton';
import { PlusIcon } from 'lucide-vue-next';
import { T as Tag } from './Tag_DnSNoHBb.mjs';
import { S as Select } from './Select_Bk34xTKM.mjs';
import { I as Icon } from './AdminLayout_b20tykPC.mjs';
import { r as resolveMediaUrl, f as fileToBase64 } from './utils_C3NgBSzL.mjs';

var classes$4 = {
  root: function root(_ref) {
    var props = _ref.props;
    return ['p-tabs p-component', {
      'p-tabs-scrollable': props.scrollable
    }];
  }
};
var TabsStyle = BaseStyle.extend({
  name: 'tabs',
  style: style,
  classes: classes$4
});

var script$1$4 = {
  name: 'BaseTabs',
  "extends": BaseComponent,
  props: {
    value: {
      type: [String, Number],
      "default": undefined
    },
    lazy: {
      type: Boolean,
      "default": false
    },
    scrollable: {
      type: Boolean,
      "default": false
    },
    showNavigators: {
      type: Boolean,
      "default": true
    },
    tabindex: {
      type: Number,
      "default": 0
    },
    selectOnFocus: {
      type: Boolean,
      "default": false
    }
  },
  style: TabsStyle,
  provide: function provide() {
    return {
      $pcTabs: this,
      $parentInstance: this
    };
  }
};

var script$5 = {
  name: 'Tabs',
  "extends": script$1$4,
  inheritAttrs: false,
  emits: ['update:value'],
  data: function data() {
    return {
      d_value: this.value
    };
  },
  watch: {
    value: function value(newValue) {
      this.d_value = newValue;
    }
  },
  methods: {
    updateValue: function updateValue(newValue) {
      if (this.d_value !== newValue) {
        this.d_value = newValue;
        this.$emit('update:value', newValue);
      }
    },
    isVertical: function isVertical() {
      return this.orientation === 'vertical';
    }
  }
};

function render$4(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createElementBlock("div", mergeProps({
    "class": _ctx.cx('root')
  }, _ctx.ptmi('root')), [renderSlot(_ctx.$slots, "default")], 16);
}

script$5.render = render$4;

const _sfc_main$7 = /* @__PURE__ */ defineComponent({
  __name: "Tabs",
  setup(__props, { expose: __expose }) {
    __expose();
    const props = __props;
    const theme = ref({
      root: `flex flex-col`
    });
    const __returned__ = { props, theme, get Tabs() {
      return script$5;
    }, get ptViewMerge() {
      return ptViewMerge;
    } };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$7(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(ssrRenderComponent($setup["Tabs"], mergeProps({
    value: $setup.props.value,
    unstyled: "",
    pt: $setup.theme,
    ptOptions: {
      mergeProps: $setup.ptViewMerge
    }
  }, _attrs), {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        ssrRenderSlot(_ctx.$slots, "default", {}, null, _push2, _parent2, _scopeId);
      } else {
        return [
          renderSlot(_ctx.$slots, "default")
        ];
      }
    }),
    _: 3
  }, _parent));
}
const _sfc_setup$7 = _sfc_main$7.setup;
_sfc_main$7.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/volt/Tabs.vue");
  return _sfc_setup$7 ? _sfc_setup$7(props, ctx) : void 0;
};
const VTabs = /* @__PURE__ */ _export_sfc(_sfc_main$7, [["ssrRender", _sfc_ssrRender$7]]);

var classes$3 = {
  root: 'p-tablist',
  content: function content(_ref) {
    var instance = _ref.instance;
    return ['p-tablist-content', {
      'p-tablist-viewport': instance.$pcTabs.scrollable
    }];
  },
  tabList: 'p-tablist-tab-list',
  activeBar: 'p-tablist-active-bar',
  prevButton: 'p-tablist-prev-button p-tablist-nav-button',
  nextButton: 'p-tablist-next-button p-tablist-nav-button'
};
var TabListStyle = BaseStyle.extend({
  name: 'tablist',
  classes: classes$3
});

var script$1$3 = {
  name: 'BaseTabList',
  "extends": BaseComponent,
  props: {},
  style: TabListStyle,
  provide: function provide() {
    return {
      $pcTabList: this,
      $parentInstance: this
    };
  }
};

var script$4 = {
  name: 'TabList',
  "extends": script$1$3,
  inheritAttrs: false,
  inject: ['$pcTabs'],
  data: function data() {
    return {
      isPrevButtonEnabled: false,
      isNextButtonEnabled: true
    };
  },
  resizeObserver: undefined,
  watch: {
    showNavigators: function showNavigators(newValue) {
      newValue ? this.bindResizeObserver() : this.unbindResizeObserver();
    },
    activeValue: {
      flush: 'post',
      handler: function handler() {
        this.updateInkBar();
      }
    }
  },
  mounted: function mounted() {
    var _this = this;
    setTimeout(function () {
      _this.updateInkBar();
    }, 150);
    if (this.showNavigators) {
      this.updateButtonState();
      this.bindResizeObserver();
    }
  },
  updated: function updated() {
    this.showNavigators && this.updateButtonState();
  },
  beforeUnmount: function beforeUnmount() {
    this.unbindResizeObserver();
  },
  methods: {
    onScroll: function onScroll(event) {
      this.showNavigators && this.updateButtonState();
      event.preventDefault();
    },
    onPrevButtonClick: function onPrevButtonClick() {
      var content = this.$refs.content;
      var buttonWidths = this.getVisibleButtonWidths();
      var width = getWidth(content) - buttonWidths;
      var currentScrollLeft = Math.abs(content.scrollLeft);
      var scrollStep = width * 0.8;
      var targetScrollLeft = currentScrollLeft - scrollStep;
      var scrollLeft = Math.max(targetScrollLeft, 0);
      content.scrollLeft = isRTL(content) ? -1 * scrollLeft : scrollLeft;
    },
    onNextButtonClick: function onNextButtonClick() {
      var content = this.$refs.content;
      var buttonWidths = this.getVisibleButtonWidths();
      var width = getWidth(content) - buttonWidths;
      var currentScrollLeft = Math.abs(content.scrollLeft);
      var scrollStep = width * 0.8;
      var targetScrollLeft = currentScrollLeft + scrollStep;
      var maxScrollLeft = content.scrollWidth - width;
      var scrollLeft = Math.min(targetScrollLeft, maxScrollLeft);
      content.scrollLeft = isRTL(content) ? -1 * scrollLeft : scrollLeft;
    },
    bindResizeObserver: function bindResizeObserver() {
      var _this2 = this;
      this.resizeObserver = new ResizeObserver(function () {
        return _this2.updateButtonState();
      });
      this.resizeObserver.observe(this.$refs.list);
    },
    unbindResizeObserver: function unbindResizeObserver() {
      var _this$resizeObserver;
      (_this$resizeObserver = this.resizeObserver) === null || _this$resizeObserver === void 0 || _this$resizeObserver.unobserve(this.$refs.list);
      this.resizeObserver = undefined;
    },
    updateInkBar: function updateInkBar() {
      var _this$$refs = this.$refs,
        content = _this$$refs.content,
        inkbar = _this$$refs.inkbar,
        tabs = _this$$refs.tabs;
      if (!inkbar) return;
      var activeTab = findSingle(content, '[data-pc-name="tab"][data-p-active="true"]');
      if (this.$pcTabs.isVertical()) {
        inkbar.style.height = getOuterHeight(activeTab) + 'px';
        inkbar.style.top = getOffset(activeTab).top - getOffset(tabs).top + 'px';
      } else {
        inkbar.style.width = getOuterWidth(activeTab) + 'px';
        inkbar.style.left = getOffset(activeTab).left - getOffset(tabs).left + 'px';
      }
    },
    updateButtonState: function updateButtonState() {
      var _this$$refs2 = this.$refs,
        list = _this$$refs2.list,
        content = _this$$refs2.content;
      var scrollTop = content.scrollTop,
        scrollWidth = content.scrollWidth,
        scrollHeight = content.scrollHeight,
        offsetWidth = content.offsetWidth,
        offsetHeight = content.offsetHeight;
      var scrollLeft = Math.abs(content.scrollLeft);
      var _ref = [getWidth(content), getHeight(content)],
        width = _ref[0],
        height = _ref[1];
      if (this.$pcTabs.isVertical()) {
        this.isPrevButtonEnabled = scrollTop !== 0;
        this.isNextButtonEnabled = list.offsetHeight >= offsetHeight && parseInt(scrollTop) !== scrollHeight - height;
      } else {
        this.isPrevButtonEnabled = scrollLeft !== 0;
        this.isNextButtonEnabled = list.offsetWidth >= offsetWidth && parseInt(scrollLeft) !== scrollWidth - width;
      }
    },
    getVisibleButtonWidths: function getVisibleButtonWidths() {
      var _this$$refs3 = this.$refs,
        prevButton = _this$$refs3.prevButton,
        nextButton = _this$$refs3.nextButton;
      var width = 0;
      if (this.showNavigators) {
        width = ((prevButton === null || prevButton === void 0 ? void 0 : prevButton.offsetWidth) || 0) + ((nextButton === null || nextButton === void 0 ? void 0 : nextButton.offsetWidth) || 0);
      }
      return width;
    }
  },
  computed: {
    templates: function templates() {
      return this.$pcTabs.$slots;
    },
    activeValue: function activeValue() {
      return this.$pcTabs.d_value;
    },
    showNavigators: function showNavigators() {
      return this.$pcTabs.scrollable && this.$pcTabs.showNavigators;
    },
    prevButtonAriaLabel: function prevButtonAriaLabel() {
      return this.$primevue.config.locale.aria ? this.$primevue.config.locale.aria.previous : undefined;
    },
    nextButtonAriaLabel: function nextButtonAriaLabel() {
      return this.$primevue.config.locale.aria ? this.$primevue.config.locale.aria.next : undefined;
    },
    dataP: function dataP() {
      return cn({
        scrollable: this.$pcTabs.scrollable
      });
    }
  },
  components: {
    ChevronLeftIcon: ChevronLeftIcon,
    ChevronRightIcon: ChevronRightIcon
  },
  directives: {
    ripple: Ripple
  }
};

var _hoisted_1$2 = ["data-p"];
var _hoisted_2$1 = ["aria-label", "tabindex"];
var _hoisted_3 = ["data-p"];
var _hoisted_4 = ["aria-orientation"];
var _hoisted_5 = ["aria-label", "tabindex"];
function render$3(_ctx, _cache, $props, $setup, $data, $options) {
  var _directive_ripple = resolveDirective("ripple");
  return openBlock(), createElementBlock("div", mergeProps({
    ref: "list",
    "class": _ctx.cx('root'),
    "data-p": $options.dataP
  }, _ctx.ptmi('root')), [$options.showNavigators && $data.isPrevButtonEnabled ? withDirectives((openBlock(), createElementBlock("button", mergeProps({
    key: 0,
    ref: "prevButton",
    type: "button",
    "class": _ctx.cx('prevButton'),
    "aria-label": $options.prevButtonAriaLabel,
    tabindex: $options.$pcTabs.tabindex,
    onClick: _cache[0] || (_cache[0] = function () {
      return $options.onPrevButtonClick && $options.onPrevButtonClick.apply($options, arguments);
    })
  }, _ctx.ptm('prevButton'), {
    "data-pc-group-section": "navigator"
  }), [(openBlock(), createBlock(resolveDynamicComponent($options.templates.previcon || 'ChevronLeftIcon'), mergeProps({
    "aria-hidden": "true"
  }, _ctx.ptm('prevIcon')), null, 16))], 16, _hoisted_2$1)), [[_directive_ripple]]) : createCommentVNode("", true), createElementVNode("div", mergeProps({
    ref: "content",
    "class": _ctx.cx('content'),
    onScroll: _cache[1] || (_cache[1] = function () {
      return $options.onScroll && $options.onScroll.apply($options, arguments);
    }),
    "data-p": $options.dataP
  }, _ctx.ptm('content')), [createElementVNode("div", mergeProps({
    ref: "tabs",
    "class": _ctx.cx('tabList'),
    role: "tablist",
    "aria-orientation": $options.$pcTabs.orientation || 'horizontal'
  }, _ctx.ptm('tabList')), [renderSlot(_ctx.$slots, "default"), createElementVNode("span", mergeProps({
    ref: "inkbar",
    "class": _ctx.cx('activeBar'),
    role: "presentation",
    "aria-hidden": "true"
  }, _ctx.ptm('activeBar')), null, 16)], 16, _hoisted_4)], 16, _hoisted_3), $options.showNavigators && $data.isNextButtonEnabled ? withDirectives((openBlock(), createElementBlock("button", mergeProps({
    key: 1,
    ref: "nextButton",
    type: "button",
    "class": _ctx.cx('nextButton'),
    "aria-label": $options.nextButtonAriaLabel,
    tabindex: $options.$pcTabs.tabindex,
    onClick: _cache[2] || (_cache[2] = function () {
      return $options.onNextButtonClick && $options.onNextButtonClick.apply($options, arguments);
    })
  }, _ctx.ptm('nextButton'), {
    "data-pc-group-section": "navigator"
  }), [(openBlock(), createBlock(resolveDynamicComponent($options.templates.nexticon || 'ChevronRightIcon'), mergeProps({
    "aria-hidden": "true"
  }, _ctx.ptm('nextIcon')), null, 16))], 16, _hoisted_5)), [[_directive_ripple]]) : createCommentVNode("", true)], 16, _hoisted_1$2);
}

script$4.render = render$3;

const navButton = `!absolute flex-shrink-0 top-0 z-20 h-full flex items-center justify-center cursor-pointer
        bg-surface-0 dark:bg-surface-900 text-surface-500 dark:text-surface-400 hover:text-surface-700 dark:hover:text-surface-0 w-10
        shadow-[0px_0px_10px_50px_rgba(255,255,255,0.6)] dark:shadow-[0px_0px_10px_50px] dark:shadow-surface-900/50
        focus-visible:z-10 focus-visible:outline focus-visible:outline-1 focus-visible:outline-offset-[-1px] focus-visible:outline-primary
        transition-colors duration-200`;
const _sfc_main$6 = /* @__PURE__ */ defineComponent({
  __name: "TabList",
  setup(__props, { expose: __expose }) {
    __expose();
    const theme = ref({
      root: `flex relative`,
      prevButton: navButton + ` start-0`,
      nextButton: navButton + ` end-0`,
      content: `flex-grow
        p-scrollable:overflow-x-auto p-scrollable:overflow-y-hidden p-scrollable:overscroll-y-contain p-scrollable:overscroll-x-auto
        scroll-smooth [scrollbar-width:none]`,
      tabList: `relative flex bg-surface-0 dark:bg-surface-900 border-b border-surface-200 dark:border-surface-700
        p-scrollable:overflow-hidden`,
      activeBar: `z-10 block absolute -bottom-px h-px bg-primary transition-[left] duration-200 ease-[cubic-bezier(0.35,0,0.25,1)]`
    });
    const __returned__ = { navButton, theme, get TabList() {
      return script$4;
    }, get ptViewMerge() {
      return ptViewMerge;
    } };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$6(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(ssrRenderComponent($setup["TabList"], mergeProps({
    unstyled: "",
    pt: $setup.theme,
    ptOptions: {
      mergeProps: $setup.ptViewMerge
    }
  }, _attrs), {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        ssrRenderSlot(_ctx.$slots, "default", {}, null, _push2, _parent2, _scopeId);
      } else {
        return [
          renderSlot(_ctx.$slots, "default")
        ];
      }
    }),
    _: 3
  }, _parent));
}
const _sfc_setup$6 = _sfc_main$6.setup;
_sfc_main$6.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/volt/TabList.vue");
  return _sfc_setup$6 ? _sfc_setup$6(props, ctx) : void 0;
};
const VTabList = /* @__PURE__ */ _export_sfc(_sfc_main$6, [["ssrRender", _sfc_ssrRender$6]]);

var classes$2 = {
  root: function root(_ref) {
    var instance = _ref.instance,
      props = _ref.props;
    return ['p-tab', {
      'p-tab-active': instance.active,
      'p-disabled': props.disabled
    }];
  }
};
var TabStyle = BaseStyle.extend({
  name: 'tab',
  classes: classes$2
});

var script$1$2 = {
  name: 'BaseTab',
  "extends": BaseComponent,
  props: {
    value: {
      type: [String, Number],
      "default": undefined
    },
    disabled: {
      type: Boolean,
      "default": false
    },
    as: {
      type: [String, Object],
      "default": 'BUTTON'
    },
    asChild: {
      type: Boolean,
      "default": false
    }
  },
  style: TabStyle,
  provide: function provide() {
    return {
      $pcTab: this,
      $parentInstance: this
    };
  }
};

var script$3 = {
  name: 'Tab',
  "extends": script$1$2,
  inheritAttrs: false,
  inject: ['$pcTabs', '$pcTabList'],
  methods: {
    onFocus: function onFocus() {
      this.$pcTabs.selectOnFocus && this.changeActiveValue();
    },
    onClick: function onClick() {
      this.changeActiveValue();
    },
    onKeydown: function onKeydown(event) {
      switch (event.code) {
        case 'ArrowRight':
          this.onArrowRightKey(event);
          break;
        case 'ArrowLeft':
          this.onArrowLeftKey(event);
          break;
        case 'Home':
          this.onHomeKey(event);
          break;
        case 'End':
          this.onEndKey(event);
          break;
        case 'PageDown':
          this.onPageDownKey(event);
          break;
        case 'PageUp':
          this.onPageUpKey(event);
          break;
        case 'Enter':
        case 'NumpadEnter':
        case 'Space':
          this.onEnterKey(event);
          break;
      }
    },
    onArrowRightKey: function onArrowRightKey(event) {
      var nextTab = this.findNextTab(event.currentTarget);
      nextTab ? this.changeFocusedTab(event, nextTab) : this.onHomeKey(event);
      event.preventDefault();
    },
    onArrowLeftKey: function onArrowLeftKey(event) {
      var prevTab = this.findPrevTab(event.currentTarget);
      prevTab ? this.changeFocusedTab(event, prevTab) : this.onEndKey(event);
      event.preventDefault();
    },
    onHomeKey: function onHomeKey(event) {
      var firstTab = this.findFirstTab();
      this.changeFocusedTab(event, firstTab);
      event.preventDefault();
    },
    onEndKey: function onEndKey(event) {
      var lastTab = this.findLastTab();
      this.changeFocusedTab(event, lastTab);
      event.preventDefault();
    },
    onPageDownKey: function onPageDownKey(event) {
      this.scrollInView(this.findLastTab());
      event.preventDefault();
    },
    onPageUpKey: function onPageUpKey(event) {
      this.scrollInView(this.findFirstTab());
      event.preventDefault();
    },
    onEnterKey: function onEnterKey(event) {
      this.changeActiveValue();
      event.preventDefault();
    },
    findNextTab: function findNextTab(tabElement) {
      var selfCheck = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
      var element = selfCheck ? tabElement : tabElement.nextElementSibling;
      return element ? getAttribute(element, 'data-p-disabled') || getAttribute(element, 'data-pc-section') === 'activebar' ? this.findNextTab(element) : findSingle(element, '[data-pc-name="tab"]') : null;
    },
    findPrevTab: function findPrevTab(tabElement) {
      var selfCheck = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
      var element = selfCheck ? tabElement : tabElement.previousElementSibling;
      return element ? getAttribute(element, 'data-p-disabled') || getAttribute(element, 'data-pc-section') === 'activebar' ? this.findPrevTab(element) : findSingle(element, '[data-pc-name="tab"]') : null;
    },
    findFirstTab: function findFirstTab() {
      return this.findNextTab(this.$pcTabList.$refs.tabs.firstElementChild, true);
    },
    findLastTab: function findLastTab() {
      return this.findPrevTab(this.$pcTabList.$refs.tabs.lastElementChild, true);
    },
    changeActiveValue: function changeActiveValue() {
      this.$pcTabs.updateValue(this.value);
    },
    changeFocusedTab: function changeFocusedTab(event, element) {
      focus(element);
      this.scrollInView(element);
    },
    scrollInView: function scrollInView(element) {
      var _element$scrollIntoVi;
      element === null || element === void 0 || (_element$scrollIntoVi = element.scrollIntoView) === null || _element$scrollIntoVi === void 0 || _element$scrollIntoVi.call(element, {
        block: 'nearest'
      });
    }
  },
  computed: {
    active: function active() {
      var _this$$pcTabs;
      return equals((_this$$pcTabs = this.$pcTabs) === null || _this$$pcTabs === void 0 ? void 0 : _this$$pcTabs.d_value, this.value);
    },
    id: function id() {
      var _this$$pcTabs2;
      return "".concat((_this$$pcTabs2 = this.$pcTabs) === null || _this$$pcTabs2 === void 0 ? void 0 : _this$$pcTabs2.$id, "_tab_").concat(this.value);
    },
    ariaControls: function ariaControls() {
      var _this$$pcTabs3;
      return "".concat((_this$$pcTabs3 = this.$pcTabs) === null || _this$$pcTabs3 === void 0 ? void 0 : _this$$pcTabs3.$id, "_tabpanel_").concat(this.value);
    },
    attrs: function attrs() {
      return mergeProps(this.asAttrs, this.a11yAttrs, this.ptmi('root', this.ptParams));
    },
    asAttrs: function asAttrs() {
      return this.as === 'BUTTON' ? {
        type: 'button',
        disabled: this.disabled
      } : undefined;
    },
    a11yAttrs: function a11yAttrs() {
      return {
        id: this.id,
        tabindex: this.active ? this.$pcTabs.tabindex : -1,
        role: 'tab',
        'aria-selected': this.active,
        'aria-controls': this.ariaControls,
        'data-pc-name': 'tab',
        'data-p-disabled': this.disabled,
        'data-p-active': this.active,
        onFocus: this.onFocus,
        onKeydown: this.onKeydown
      };
    },
    ptParams: function ptParams() {
      return {
        context: {
          active: this.active
        }
      };
    },
    dataP: function dataP() {
      return cn({
        active: this.active
      });
    }
  },
  directives: {
    ripple: Ripple
  }
};

function render$2(_ctx, _cache, $props, $setup, $data, $options) {
  var _directive_ripple = resolveDirective("ripple");
  return !_ctx.asChild ? withDirectives((openBlock(), createBlock(resolveDynamicComponent(_ctx.as), mergeProps({
    key: 0,
    "class": _ctx.cx('root'),
    "data-p": $options.dataP,
    onClick: $options.onClick
  }, $options.attrs), {
    "default": withCtx(function () {
      return [renderSlot(_ctx.$slots, "default")];
    }),
    _: 3
  }, 16, ["class", "data-p", "onClick"])), [[_directive_ripple]]) : renderSlot(_ctx.$slots, "default", {
    key: 1,
    dataP: $options.dataP,
    "class": normalizeClass(_ctx.cx('root')),
    active: $options.active,
    a11yAttrs: $options.a11yAttrs,
    onClick: $options.onClick
  });
}

script$3.render = render$2;

const _sfc_main$5 = /* @__PURE__ */ defineComponent({
  __name: "Tab",
  setup(__props, { expose: __expose }) {
    __expose();
    const props = __props;
    const theme = ref({
      root: `flex-shrink-0 cursor-pointer select-none relative whitespace-nowrap py-4 px-[1.125rem]
        border-b border-surface-200 dark:border-surface-700 font-semibold
        text-surface-500 dark:text-surface-400
        transition-colors duration-200 -mb-px
        not-p-active:enabled:hover:text-surface-700 dark:not-p-active:enabled:hover:text-surface-0
        p-active:border-primary p-active:text-primary
        disabled:pointer-events-none disabled:opacity-60
        focus-visible:z-10 focus-visible:outline focus-visible:outline-1 focus-visible:outline-offset-[-1px] focus-visible:outline-primary`
    });
    const __returned__ = { props, theme, get Tab() {
      return script$3;
    }, get ptViewMerge() {
      return ptViewMerge;
    } };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$5(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(ssrRenderComponent($setup["Tab"], mergeProps({
    value: $setup.props.value,
    unstyled: "",
    pt: $setup.theme,
    ptOptions: {
      mergeProps: $setup.ptViewMerge
    }
  }, _attrs), {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        ssrRenderSlot(_ctx.$slots, "default", {}, null, _push2, _parent2, _scopeId);
      } else {
        return [
          renderSlot(_ctx.$slots, "default")
        ];
      }
    }),
    _: 3
  }, _parent));
}
const _sfc_setup$5 = _sfc_main$5.setup;
_sfc_main$5.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/volt/Tab.vue");
  return _sfc_setup$5 ? _sfc_setup$5(props, ctx) : void 0;
};
const VTab = /* @__PURE__ */ _export_sfc(_sfc_main$5, [["ssrRender", _sfc_ssrRender$5]]);

const _sfc_main$4 = /* @__PURE__ */ defineComponent({
  __name: "QuickCreateCategory",
  props: {
    visible: { type: Boolean }
  },
  emits: ["update:visible", "created"],
  setup(__props, { expose: __expose, emit: __emit }) {
    __expose();
    const props = __props;
    const emit = __emit;
    const { loading, error, clearError, partCategories } = useTrpc();
    const visible = computed({
      get: () => props.visible,
      set: (value) => emit("update:visible", value)
    });
    const formData = ref({
      name: "",
      description: "",
      parent: null
    });
    const errors = ref({
      name: ""
    });
    const parentSuggestions = ref([]);
    const canCreate = computed(() => {
      return formData.value.name.trim().length > 0 && !loading.value;
    });
    const searchParents = async (event) => {
      const query = event.query.toLowerCase();
      const categories = await partCategories.findMany({
        where: {
          name: {
            contains: query,
            mode: "insensitive"
          }
        },
        take: 10
      });
      if (categories) {
        parentSuggestions.value = categories;
      }
    };
    const createCategory = async () => {
      clearError();
      errors.value = { name: "" };
      if (!formData.value.name.trim()) {
        errors.value.name = "\u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435 \u043E\u0431\u044F\u0437\u0430\u0442\u0435\u043B\u044C\u043D\u043E";
        return;
      }
      try {
        const slug = formData.value.name.toLowerCase().replace(/[^a-zа-я0-9\s-]/g, "").replace(/\s+/g, "-").replace(/-+/g, "-").trim();
        const categoryData = {
          name: formData.value.name.trim(),
          slug,
          description: formData.value.description?.trim() || void 0,
          level: formData.value.parent ? formData.value.parent.level + 1 : 0,
          path: formData.value.parent ? `${formData.value.parent.path}/${slug}` : `/${slug}`,
          parentId: formData.value.parent?.id || void 0
        };
        const result = await partCategories.create({
          data: categoryData
        });
        if (result) {
          emit("created", result);
          resetForm();
          visible.value = false;
        }
      } catch (err) {
        console.error("\u041E\u0448\u0438\u0431\u043A\u0430 \u0441\u043E\u0437\u0434\u0430\u043D\u0438\u044F \u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438:", err);
      }
    };
    const resetForm = () => {
      formData.value = {
        name: "",
        description: "",
        parent: null
      };
      errors.value = { name: "" };
      clearError();
    };
    watch(visible, (newValue) => {
      if (!newValue) {
        resetForm();
      }
    });
    const __returned__ = { props, emit, loading, error, clearError, partCategories, visible, formData, errors, parentSuggestions, canCreate, searchParents, createCategory, resetForm, VDialog: Dialog, VButton, VInputText: InputText, VTextarea, VAutoComplete, VMessage: Message };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$4(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(ssrRenderComponent($setup["VDialog"], mergeProps({
    visible: $setup.visible,
    "onUpdate:visible": ($event) => $setup.visible = $event,
    modal: "",
    header: "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u043D\u043E\u0432\u0443\u044E \u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u044E",
    class: "w-96"
  }, _attrs), {
    footer: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="flex justify-end gap-3"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["VButton"], {
          onClick: ($event) => $setup.visible = false,
          severity: "secondary",
          outlined: "",
          disabled: $setup.loading
        }, {
          default: withCtx((_2, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(` \u041E\u0442\u043C\u0435\u043D\u0430 `);
            } else {
              return [
                createTextVNode(" \u041E\u0442\u043C\u0435\u043D\u0430 ")
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["VButton"], {
          onClick: $setup.createCategory,
          loading: $setup.loading,
          disabled: !$setup.canCreate
        }, {
          default: withCtx((_2, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(` \u0421\u043E\u0437\u0434\u0430\u0442\u044C `);
            } else {
              return [
                createTextVNode(" \u0421\u043E\u0437\u0434\u0430\u0442\u044C ")
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(`</div>`);
      } else {
        return [
          createVNode("div", { class: "flex justify-end gap-3" }, [
            createVNode($setup["VButton"], {
              onClick: ($event) => $setup.visible = false,
              severity: "secondary",
              outlined: "",
              disabled: $setup.loading
            }, {
              default: withCtx(() => [
                createTextVNode(" \u041E\u0442\u043C\u0435\u043D\u0430 ")
              ]),
              _: 1
            }, 8, ["onClick", "disabled"]),
            createVNode($setup["VButton"], {
              onClick: $setup.createCategory,
              loading: $setup.loading,
              disabled: !$setup.canCreate
            }, {
              default: withCtx(() => [
                createTextVNode(" \u0421\u043E\u0437\u0434\u0430\u0442\u044C ")
              ]),
              _: 1
            }, 8, ["loading", "disabled"])
          ])
        ];
      }
    }),
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="space-y-4"${_scopeId}><div${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}> \u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435 \u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438 * </label>`);
        _push2(ssrRenderComponent($setup["VInputText"], {
          modelValue: $setup.formData.name,
          "onUpdate:modelValue": ($event) => $setup.formData.name = $event,
          placeholder: "\u041D\u0430\u043F\u0440\u0438\u043C\u0435\u0440: \u0424\u0438\u043B\u044C\u0442\u0440\u044B \u043C\u0430\u0441\u043B\u044F\u043D\u044B\u0435",
          class: ["w-full", { "p-invalid": $setup.errors.name }]
        }, null, _parent2, _scopeId));
        if ($setup.errors.name) {
          _push2(`<small class="text-red-500"${_scopeId}>${ssrInterpolate($setup.errors.name)}</small>`);
        } else {
          _push2(`<!---->`);
        }
        _push2(`</div><div${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}> \u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435 </label>`);
        _push2(ssrRenderComponent($setup["VTextarea"], {
          modelValue: $setup.formData.description,
          "onUpdate:modelValue": ($event) => $setup.formData.description = $event,
          placeholder: "\u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435 \u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438",
          rows: "3",
          class: "w-full"
        }, null, _parent2, _scopeId));
        _push2(`</div><div${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}> \u0420\u043E\u0434\u0438\u0442\u0435\u043B\u044C\u0441\u043A\u0430\u044F \u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u044F </label>`);
        _push2(ssrRenderComponent($setup["VAutoComplete"], {
          modelValue: $setup.formData.parent,
          "onUpdate:modelValue": ($event) => $setup.formData.parent = $event,
          suggestions: $setup.parentSuggestions,
          onComplete: $setup.searchParents,
          "option-label": "name",
          placeholder: "\u041F\u043E\u0438\u0441\u043A \u0440\u043E\u0434\u0438\u0442\u0435\u043B\u044C\u0441\u043A\u043E\u0439 \u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438",
          class: "w-full"
        }, null, _parent2, _scopeId));
        _push2(`</div></div>`);
        if ($setup.error) {
          _push2(ssrRenderComponent($setup["VMessage"], {
            severity: "error",
            class: "mt-4"
          }, {
            default: withCtx((_2, _push3, _parent3, _scopeId2) => {
              if (_push3) {
                _push3(`${ssrInterpolate($setup.error)}`);
              } else {
                return [
                  createTextVNode(toDisplayString($setup.error), 1)
                ];
              }
            }),
            _: 1
          }, _parent2, _scopeId));
        } else {
          _push2(`<!---->`);
        }
      } else {
        return [
          createVNode("div", { class: "space-y-4" }, [
            createVNode("div", null, [
              createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435 \u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438 * "),
              createVNode($setup["VInputText"], {
                modelValue: $setup.formData.name,
                "onUpdate:modelValue": ($event) => $setup.formData.name = $event,
                placeholder: "\u041D\u0430\u043F\u0440\u0438\u043C\u0435\u0440: \u0424\u0438\u043B\u044C\u0442\u0440\u044B \u043C\u0430\u0441\u043B\u044F\u043D\u044B\u0435",
                class: ["w-full", { "p-invalid": $setup.errors.name }]
              }, null, 8, ["modelValue", "onUpdate:modelValue", "class"]),
              $setup.errors.name ? (openBlock(), createBlock("small", {
                key: 0,
                class: "text-red-500"
              }, toDisplayString($setup.errors.name), 1)) : createCommentVNode("", true)
            ]),
            createVNode("div", null, [
              createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435 "),
              createVNode($setup["VTextarea"], {
                modelValue: $setup.formData.description,
                "onUpdate:modelValue": ($event) => $setup.formData.description = $event,
                placeholder: "\u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435 \u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438",
                rows: "3",
                class: "w-full"
              }, null, 8, ["modelValue", "onUpdate:modelValue"])
            ]),
            createVNode("div", null, [
              createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u0420\u043E\u0434\u0438\u0442\u0435\u043B\u044C\u0441\u043A\u0430\u044F \u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u044F "),
              createVNode($setup["VAutoComplete"], {
                modelValue: $setup.formData.parent,
                "onUpdate:modelValue": ($event) => $setup.formData.parent = $event,
                suggestions: $setup.parentSuggestions,
                onComplete: $setup.searchParents,
                "option-label": "name",
                placeholder: "\u041F\u043E\u0438\u0441\u043A \u0440\u043E\u0434\u0438\u0442\u0435\u043B\u044C\u0441\u043A\u043E\u0439 \u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438",
                class: "w-full"
              }, null, 8, ["modelValue", "onUpdate:modelValue", "suggestions"])
            ])
          ]),
          $setup.error ? (openBlock(), createBlock($setup["VMessage"], {
            key: 0,
            severity: "error",
            class: "mt-4"
          }, {
            default: withCtx(() => [
              createTextVNode(toDisplayString($setup.error), 1)
            ]),
            _: 1
          })) : createCommentVNode("", true)
        ];
      }
    }),
    _: 1
  }, _parent));
}
const _sfc_setup$4 = _sfc_main$4.setup;
_sfc_main$4.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/parts/QuickCreateCategory.vue");
  return _sfc_setup$4 ? _sfc_setup$4(props, ctx) : void 0;
};
const QuickCreateCategory = /* @__PURE__ */ _export_sfc(_sfc_main$4, [["ssrRender", _sfc_ssrRender$4]]);

var classes$1 = {
  root: function root(_ref) {
    var instance = _ref.instance,
      props = _ref.props;
    return ['p-togglebutton p-component', {
      'p-togglebutton-checked': instance.active,
      'p-invalid': instance.$invalid,
      'p-togglebutton-fluid': props.fluid,
      'p-togglebutton-sm p-inputfield-sm': props.size === 'small',
      'p-togglebutton-lg p-inputfield-lg': props.size === 'large'
    }];
  },
  content: 'p-togglebutton-content',
  icon: 'p-togglebutton-icon',
  label: 'p-togglebutton-label'
};
var ToggleButtonStyle = BaseStyle.extend({
  name: 'togglebutton',
  style: style$1,
  classes: classes$1
});

var script$1$1 = {
  name: 'BaseToggleButton',
  "extends": BaseEditableHolder,
  props: {
    onIcon: String,
    offIcon: String,
    onLabel: {
      type: String,
      "default": 'Yes'
    },
    offLabel: {
      type: String,
      "default": 'No'
    },
    iconPos: {
      type: String,
      "default": 'left'
    },
    readonly: {
      type: Boolean,
      "default": false
    },
    tabindex: {
      type: Number,
      "default": null
    },
    ariaLabelledby: {
      type: String,
      "default": null
    },
    ariaLabel: {
      type: String,
      "default": null
    },
    size: {
      type: String,
      "default": null
    },
    fluid: {
      type: Boolean,
      "default": null
    }
  },
  style: ToggleButtonStyle,
  provide: function provide() {
    return {
      $pcToggleButton: this,
      $parentInstance: this
    };
  }
};

function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: true, configurable: true, writable: true }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
var script$2 = {
  name: 'ToggleButton',
  "extends": script$1$1,
  inheritAttrs: false,
  emits: ['change'],
  methods: {
    getPTOptions: function getPTOptions(key) {
      var _ptm = key === 'root' ? this.ptmi : this.ptm;
      return _ptm(key, {
        context: {
          active: this.active,
          disabled: this.disabled
        }
      });
    },
    onChange: function onChange(event) {
      if (!this.disabled && !this.readonly) {
        this.writeValue(!this.d_value, event);
        this.$emit('change', event);
      }
    },
    onBlur: function onBlur(event) {
      var _this$formField$onBlu, _this$formField;
      (_this$formField$onBlu = (_this$formField = this.formField).onBlur) === null || _this$formField$onBlu === void 0 || _this$formField$onBlu.call(_this$formField, event);
    }
  },
  computed: {
    active: function active() {
      return this.d_value === true;
    },
    hasLabel: function hasLabel() {
      return isNotEmpty(this.onLabel) && isNotEmpty(this.offLabel);
    },
    label: function label() {
      return this.hasLabel ? this.d_value ? this.onLabel : this.offLabel : "\xA0";
    },
    dataP: function dataP() {
      return cn(_defineProperty({
        checked: this.active,
        invalid: this.$invalid
      }, this.size, this.size));
    }
  },
  directives: {
    ripple: Ripple
  }
};

var _hoisted_1$1 = ["tabindex", "disabled", "aria-pressed", "aria-label", "aria-labelledby", "data-p-checked", "data-p-disabled", "data-p"];
var _hoisted_2 = ["data-p"];
function render$1(_ctx, _cache, $props, $setup, $data, $options) {
  var _directive_ripple = resolveDirective("ripple");
  return withDirectives((openBlock(), createElementBlock("button", mergeProps({
    type: "button",
    "class": _ctx.cx('root'),
    tabindex: _ctx.tabindex,
    disabled: _ctx.disabled,
    "aria-pressed": _ctx.d_value,
    onClick: _cache[0] || (_cache[0] = function () {
      return $options.onChange && $options.onChange.apply($options, arguments);
    }),
    onBlur: _cache[1] || (_cache[1] = function () {
      return $options.onBlur && $options.onBlur.apply($options, arguments);
    })
  }, $options.getPTOptions('root'), {
    "aria-label": _ctx.ariaLabel,
    "aria-labelledby": _ctx.ariaLabelledby,
    "data-p-checked": $options.active,
    "data-p-disabled": _ctx.disabled,
    "data-p": $options.dataP
  }), [createElementVNode("span", mergeProps({
    "class": _ctx.cx('content')
  }, $options.getPTOptions('content'), {
    "data-p": $options.dataP
  }), [renderSlot(_ctx.$slots, "default", {}, function () {
    return [renderSlot(_ctx.$slots, "icon", {
      value: _ctx.d_value,
      "class": normalizeClass(_ctx.cx('icon'))
    }, function () {
      return [_ctx.onIcon || _ctx.offIcon ? (openBlock(), createElementBlock("span", mergeProps({
        key: 0,
        "class": [_ctx.cx('icon'), _ctx.d_value ? _ctx.onIcon : _ctx.offIcon]
      }, $options.getPTOptions('icon')), null, 16)) : createCommentVNode("", true)];
    }), createElementVNode("span", mergeProps({
      "class": _ctx.cx('label')
    }, $options.getPTOptions('label')), toDisplayString($options.label), 17)];
  })], 16, _hoisted_2)], 16, _hoisted_1$1)), [[_directive_ripple]]);
}

script$2.render = render$1;

var classes = {
  root: function root(_ref) {
    var props = _ref.props,
      instance = _ref.instance;
    return ['p-selectbutton p-component', {
      'p-invalid': instance.$invalid,
      // @todo: check
      'p-selectbutton-fluid': props.fluid
    }];
  }
};
var SelectButtonStyle = BaseStyle.extend({
  name: 'selectbutton',
  style: style$2,
  classes: classes
});

var script$1 = {
  name: 'BaseSelectButton',
  "extends": BaseEditableHolder,
  props: {
    options: Array,
    optionLabel: null,
    optionValue: null,
    optionDisabled: null,
    multiple: Boolean,
    allowEmpty: {
      type: Boolean,
      "default": true
    },
    dataKey: null,
    ariaLabelledby: {
      type: String,
      "default": null
    },
    size: {
      type: String,
      "default": null
    },
    fluid: {
      type: Boolean,
      "default": null
    }
  },
  style: SelectButtonStyle,
  provide: function provide() {
    return {
      $pcSelectButton: this,
      $parentInstance: this
    };
  }
};

function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: true } : { done: false, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = true, u = false; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = true, o = r; }, f: function f() { try { a || null == t["return"] || t["return"](); } finally { if (u) throw o; } } }; }
function _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }
function _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _iterableToArray(r) { if ("undefined" != typeof Symbol && null != r[Symbol.iterator] || null != r["@@iterator"]) return Array.from(r); }
function _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
var script = {
  name: 'SelectButton',
  "extends": script$1,
  inheritAttrs: false,
  emits: ['change'],
  methods: {
    getOptionLabel: function getOptionLabel(option) {
      return this.optionLabel ? resolveFieldData(option, this.optionLabel) : option;
    },
    getOptionValue: function getOptionValue(option) {
      return this.optionValue ? resolveFieldData(option, this.optionValue) : option;
    },
    getOptionRenderKey: function getOptionRenderKey(option) {
      return this.dataKey ? resolveFieldData(option, this.dataKey) : this.getOptionLabel(option);
    },
    isOptionDisabled: function isOptionDisabled(option) {
      return this.optionDisabled ? resolveFieldData(option, this.optionDisabled) : false;
    },
    isOptionReadonly: function isOptionReadonly(option) {
      if (this.allowEmpty) return false;
      var selected = this.isSelected(option);
      if (this.multiple) {
        return selected && this.d_value.length === 1;
      } else {
        return selected;
      }
    },
    onOptionSelect: function onOptionSelect(event, option, index) {
      var _this = this;
      if (this.disabled || this.isOptionDisabled(option) || this.isOptionReadonly(option)) {
        return;
      }
      var selected = this.isSelected(option);
      var optionValue = this.getOptionValue(option);
      var newValue;
      if (this.multiple) {
        if (selected) {
          newValue = this.d_value.filter(function (val) {
            return !equals(val, optionValue, _this.equalityKey);
          });
          if (!this.allowEmpty && newValue.length === 0) return;
        } else {
          newValue = this.d_value ? [].concat(_toConsumableArray(this.d_value), [optionValue]) : [optionValue];
        }
      } else {
        if (selected && !this.allowEmpty) return;
        newValue = selected ? null : optionValue;
      }
      this.writeValue(newValue, event);
      this.$emit('change', {
        event: event,
        value: newValue
      });
    },
    isSelected: function isSelected(option) {
      var selected = false;
      var optionValue = this.getOptionValue(option);
      if (this.multiple) {
        if (this.d_value) {
          var _iterator = _createForOfIteratorHelper(this.d_value),
            _step;
          try {
            for (_iterator.s(); !(_step = _iterator.n()).done;) {
              var val = _step.value;
              if (equals(val, optionValue, this.equalityKey)) {
                selected = true;
                break;
              }
            }
          } catch (err) {
            _iterator.e(err);
          } finally {
            _iterator.f();
          }
        }
      } else {
        selected = equals(this.d_value, optionValue, this.equalityKey);
      }
      return selected;
    }
  },
  computed: {
    equalityKey: function equalityKey() {
      return this.optionValue ? null : this.dataKey;
    },
    dataP: function dataP() {
      return cn({
        invalid: this.$invalid
      });
    }
  },
  directives: {
    ripple: Ripple
  },
  components: {
    ToggleButton: script$2
  }
};

var _hoisted_1 = ["aria-labelledby", "data-p"];
function render(_ctx, _cache, $props, $setup, $data, $options) {
  var _component_ToggleButton = resolveComponent("ToggleButton");
  return openBlock(), createElementBlock("div", mergeProps({
    "class": _ctx.cx('root'),
    role: "group",
    "aria-labelledby": _ctx.ariaLabelledby
  }, _ctx.ptmi('root'), {
    "data-p": $options.dataP
  }), [(openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.options, function (option, index) {
    return openBlock(), createBlock(_component_ToggleButton, {
      key: $options.getOptionRenderKey(option),
      modelValue: $options.isSelected(option),
      onLabel: $options.getOptionLabel(option),
      offLabel: $options.getOptionLabel(option),
      disabled: _ctx.disabled || $options.isOptionDisabled(option),
      unstyled: _ctx.unstyled,
      size: _ctx.size,
      readonly: $options.isOptionReadonly(option),
      onChange: function onChange($event) {
        return $options.onOptionSelect($event, option, index);
      },
      pt: _ctx.ptm('pcToggleButton')
    }, createSlots({
      _: 2
    }, [_ctx.$slots.option ? {
      name: "default",
      fn: withCtx(function () {
        return [renderSlot(_ctx.$slots, "option", {
          option: option,
          index: index
        }, function () {
          return [createElementVNode("span", mergeProps({
            ref_for: true
          }, _ctx.ptm('pcToggleButton')['label']), toDisplayString($options.getOptionLabel(option)), 17)];
        })];
      }),
      key: "0"
    } : undefined]), 1032, ["modelValue", "onLabel", "offLabel", "disabled", "unstyled", "size", "readonly", "onChange", "pt"]);
  }), 128))], 16, _hoisted_1);
}

script.render = render;

const _sfc_main$3 = /* @__PURE__ */ defineComponent({
  __name: "SelectButton",
  setup(__props, { expose: __expose }) {
    __expose();
    const theme = ref({
      root: `inline-flex select-none rounded-md
        p-invalid:outline p-invalid:outline-offset-0 p-invalid:outline-red-400 dark:p-invalid:outline-red-300`,
      pcToggleButton: {
        root: `inline-flex items-center justify-center overflow-hidden relative cursor-pointer select-none grow
            border border-surface-100 dark:border-surface-950
            rounded-none first:rounded-s-md last:rounded-e-md
            bg-surface-100 dark:bg-surface-950
            text-surface-500 dark:text-surface-400
            p-checked:text-surface-700 dark:p-checked:text-surface-0
            text-base font-medium
            focus-visible:outline focus-visible:outline-1 focus-visible:outline-offset-2 focus-visible:outline-primary focus-visible:relative focus-visible:z-10
            disabled:cursor-default
            disabled:bg-surface-200 disabled:border-surface-200 disabled:text-surface-500
            disabled:dark:bg-surface-700 disabled:dark:border-surface-700 disabled:dark:text-surface-400
            p-invalid:border-red-400 dark:p-invalid:border-red-300
            transition-colors duration-200
            p-1 p-small:text-sm p-large:text-lg
        `,
        content: `relative flex-auto inline-flex items-center justify-center gap-2 py-1 px-3
            rounded-md transition-colors duration-200
            p-checked:bg-surface-0 dark:p-checked:bg-surface-800 p-checked:shadow-[0px_1px_2px_0px_rgba(0,0,0,0.02),0px_1px_2px_0px_rgba(0,0,0,0.04)]`,
        icon: ``,
        label: ``
      }
    });
    const __returned__ = { theme, get SelectButton() {
      return script;
    }, get ptViewMerge() {
      return ptViewMerge;
    } };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$3(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(ssrRenderComponent($setup["SelectButton"], mergeProps({
    unstyled: "",
    pt: $setup.theme,
    ptOptions: {
      mergeProps: $setup.ptViewMerge
    }
  }, _attrs), createSlots({ _: 2 }, [
    renderList(_ctx.$slots, (_, slotName) => {
      return {
        name: slotName,
        fn: withCtx((slotProps, _push2, _parent2, _scopeId) => {
          if (_push2) {
            ssrRenderSlot(_ctx.$slots, slotName, slotProps ?? {}, null, _push2, _parent2, _scopeId);
          } else {
            return [
              renderSlot(_ctx.$slots, slotName, slotProps ?? {})
            ];
          }
        })
      };
    })
  ]), _parent));
}
const _sfc_setup$3 = _sfc_main$3.setup;
_sfc_main$3.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/volt/SelectButton.vue");
  return _sfc_setup$3 ? _sfc_setup$3(props, ctx) : void 0;
};
const VSelectButton = /* @__PURE__ */ _export_sfc(_sfc_main$3, [["ssrRender", _sfc_ssrRender$3]]);

const _sfc_main$2 = /* @__PURE__ */ defineComponent({
  __name: "EquipmentSelector",
  props: {
    modelValue: {}
  },
  emits: ["update:modelValue"],
  setup(__props, { expose: __expose, emit: __emit }) {
    __expose();
    const props = __props;
    const emit = __emit;
    const { equipmentModels, brands } = useTrpc();
    const equipmentTypeOptions = [
      { label: "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u043D\u043E\u0432\u0443\u044E \u043C\u043E\u0434\u0435\u043B\u044C", value: false },
      { label: "\u041D\u0430\u0439\u0442\u0438 \u0441\u0443\u0449\u0435\u0441\u0442\u0432\u0443\u044E\u0449\u0443\u044E", value: true }
    ];
    const equipmentSuggestions = ref([]);
    const brandSuggestions = ref([]);
    const modelValue = computed({
      get: () => props.modelValue,
      set: (value) => emit("update:modelValue", value)
    });
    const searchEquipmentModels = async (event, _itemIndex) => {
      const query = event.query.toLowerCase();
      const equipmentResult = await equipmentModels.findMany({
        where: {
          name: {
            contains: query,
            mode: "insensitive"
          }
        },
        include: {
          brand: true
        },
        take: 10
      });
      if (equipmentResult && Array.isArray(equipmentResult)) {
        equipmentSuggestions.value = equipmentResult;
      }
    };
    const searchBrands = async (event) => {
      const query = event.query.toLowerCase();
      const brandsResult = await brands.findMany({
        where: {
          name: {
            contains: query,
            mode: "insensitive"
          }
        },
        take: 10
      });
      if (brandsResult && Array.isArray(brandsResult)) {
        brandSuggestions.value = brandsResult;
      }
    };
    const addEquipment = () => {
      const newEquipment = {
        name: "",
        selectedBrand: null,
        isExisting: false,
        existingEquipmentModel: null,
        notes: ""
      };
      modelValue.value = [...modelValue.value, newEquipment];
    };
    const removeEquipment = (index) => {
      const newValue = [...modelValue.value];
      newValue.splice(index, 1);
      modelValue.value = newValue;
    };
    const __returned__ = { props, emit, equipmentModels, brands, equipmentTypeOptions, equipmentSuggestions, brandSuggestions, modelValue, searchEquipmentModels, searchBrands, addEquipment, removeEquipment, VButton, VInputText: InputText, VAutoComplete, VSelectButton, get PlusIcon() {
      return PlusIcon;
    } };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$2(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "equipment-selector" }, _attrs))}><div class="flex items-center justify-between mb-4"><h3 class="text-lg font-medium text-surface-900 dark:text-surface-0"> \u041F\u0440\u0438\u043C\u0435\u043D\u0438\u043C\u043E\u0441\u0442\u044C \u043A \u0442\u0435\u0445\u043D\u0438\u043A\u0435 </h3>`);
  _push(ssrRenderComponent($setup["VButton"], {
    onClick: $setup.addEquipment,
    outlined: "",
    size: "small"
  }, {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(` \u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0442\u0435\u0445\u043D\u0438\u043A\u0443 `);
        _push2(ssrRenderComponent($setup["PlusIcon"], { class: "h-4 w-4" }, null, _parent2, _scopeId));
      } else {
        return [
          createTextVNode(" \u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0442\u0435\u0445\u043D\u0438\u043A\u0443 "),
          createVNode($setup["PlusIcon"], { class: "h-4 w-4" })
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</div>`);
  if ($setup.modelValue.length === 0) {
    _push(`<div class="text-center py-8 text-surface-500"> \u0414\u043E\u0431\u0430\u0432\u044C\u0442\u0435 \u043C\u043E\u0434\u0435\u043B\u0438 \u0442\u0435\u0445\u043D\u0438\u043A\u0438, \u043A \u043A\u043E\u0442\u043E\u0440\u044B\u043C \u043F\u0440\u0438\u043C\u0435\u043D\u0438\u043C\u0430 \u044D\u0442\u0430 \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u044C </div>`);
  } else {
    _push(`<!---->`);
  }
  _push(`<!--[-->`);
  ssrRenderList($setup.modelValue, (item, index) => {
    _push(`<div class="border border-surface-200 dark:border-surface-700 rounded-lg p-4 mb-4"><div class="flex items-center justify-between mb-4"><h4 class="font-medium text-surface-900 dark:text-surface-0"> \u0422\u0435\u0445\u043D\u0438\u043A\u0430 ${ssrInterpolate(index + 1)}</h4>`);
    _push(ssrRenderComponent($setup["VButton"], {
      onClick: ($event) => $setup.removeEquipment(index),
      severity: "danger",
      size: "small",
      text: ""
    }, {
      default: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(` \u0423\u0434\u0430\u043B\u0438\u0442\u044C `);
        } else {
          return [
            createTextVNode(" \u0423\u0434\u0430\u043B\u0438\u0442\u044C ")
          ];
        }
      }),
      _: 2
    }, _parent));
    _push(`</div><div class="mb-4"><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"> \u0422\u0438\u043F \u0434\u043E\u0431\u0430\u0432\u043B\u0435\u043D\u0438\u044F </label>`);
    _push(ssrRenderComponent($setup["VSelectButton"], {
      modelValue: item.isExisting,
      "onUpdate:modelValue": ($event) => item.isExisting = $event,
      options: $setup.equipmentTypeOptions,
      "option-label": "label",
      "option-value": "value",
      class: "w-full"
    }, null, _parent));
    _push(`</div>`);
    if (item.isExisting) {
      _push(`<div class="mb-4"><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"> \u041F\u043E\u0438\u0441\u043A \u043C\u043E\u0434\u0435\u043B\u0438 \u0442\u0435\u0445\u043D\u0438\u043A\u0438 * </label>`);
      _push(ssrRenderComponent($setup["VAutoComplete"], {
        modelValue: item.existingEquipmentModel,
        "onUpdate:modelValue": ($event) => item.existingEquipmentModel = $event,
        suggestions: $setup.equipmentSuggestions,
        onComplete: (event) => $setup.searchEquipmentModels(event, index),
        "option-label": "name",
        placeholder: "\u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u0435 \u043C\u043E\u0434\u0435\u043B\u0438 \u0434\u043B\u044F \u043F\u043E\u0438\u0441\u043A\u0430...",
        class: "w-full",
        dropdown: ""
      }, {
        option: withCtx(({ option }, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<div class="flex items-center gap-2"${_scopeId}><div class="flex-1"${_scopeId}><div class="font-medium"${_scopeId}>${ssrInterpolate(option.name)}</div>`);
            if (option.brand) {
              _push2(`<div class="text-sm text-surface-600"${_scopeId}>${ssrInterpolate(option.brand.name)}</div>`);
            } else {
              _push2(`<!---->`);
            }
            _push2(`</div></div>`);
          } else {
            return [
              createVNode("div", { class: "flex items-center gap-2" }, [
                createVNode("div", { class: "flex-1" }, [
                  createVNode("div", { class: "font-medium" }, toDisplayString(option.name), 1),
                  option.brand ? (openBlock(), createBlock("div", {
                    key: 0,
                    class: "text-sm text-surface-600"
                  }, toDisplayString(option.brand.name), 1)) : createCommentVNode("", true)
                ])
              ])
            ];
          }
        }),
        _: 2
      }, _parent));
      _push(`</div>`);
    } else {
      _push(`<div class="space-y-4"><div class="grid grid-cols-1 md:grid-cols-2 gap-4"><div><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"> \u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435 \u043C\u043E\u0434\u0435\u043B\u0438 * </label>`);
      _push(ssrRenderComponent($setup["VInputText"], {
        modelValue: item.name,
        "onUpdate:modelValue": ($event) => item.name = $event,
        placeholder: "\u041D\u0430\u043F\u0440\u0438\u043C\u0435\u0440: CAT 320D",
        class: "w-full p-3"
      }, null, _parent));
      _push(`</div><div><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"> \u0411\u0440\u0435\u043D\u0434 </label>`);
      _push(ssrRenderComponent($setup["VAutoComplete"], {
        modelValue: item.selectedBrand,
        "onUpdate:modelValue": ($event) => item.selectedBrand = $event,
        suggestions: $setup.brandSuggestions,
        onComplete: $setup.searchBrands,
        "option-label": "name",
        placeholder: "\u041F\u043E\u0438\u0441\u043A \u0431\u0440\u0435\u043D\u0434\u0430...",
        class: "w-full",
        dropdown: ""
      }, null, _parent));
      _push(`</div></div></div>`);
    }
    _push(`<div class="mt-4"><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"> \u041F\u0440\u0438\u043C\u0435\u0447\u0430\u043D\u0438\u044F </label>`);
    _push(ssrRenderComponent($setup["VInputText"], {
      modelValue: item.notes,
      "onUpdate:modelValue": ($event) => item.notes = $event,
      placeholder: "\u0414\u043E\u043F\u043E\u043B\u043D\u0438\u0442\u0435\u043B\u044C\u043D\u0430\u044F \u0438\u043D\u0444\u043E\u0440\u043C\u0430\u0446\u0438\u044F \u043E \u043F\u0440\u0438\u043C\u0435\u043D\u0438\u043C\u043E\u0441\u0442\u0438 \u043A \u0434\u0430\u043D\u043D\u043E\u0439 \u0442\u0435\u0445\u043D\u0438\u043A\u0435",
      class: "w-full p-3"
    }, null, _parent));
    _push(`</div></div>`);
  });
  _push(`<!--]--></div>`);
}
const _sfc_setup$2 = _sfc_main$2.setup;
_sfc_main$2.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/parts/EquipmentSelector.vue");
  return _sfc_setup$2 ? _sfc_setup$2(props, ctx) : void 0;
};
const EquipmentSelector = /* @__PURE__ */ _export_sfc(_sfc_main$2, [["ssrRender", _sfc_ssrRender$2]]);

const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "CatalogItemWizardEditor",
  props: {
    modelValue: {}
  },
  emits: ["update:modelValue"],
  setup(__props, { expose: __expose, emit: __emit }) {
    __expose();
    const props = __props;
    const emit = __emit;
    const { catalogItems, brands } = useTrpc();
    const catalogItemSuggestions = ref([]);
    const brandSuggestions = ref([]);
    const showCreateBrand = ref(false);
    const accuracyOptions = [
      { label: "\u0422\u043E\u0447\u043D\u043E\u0435 \u0441\u043E\u0432\u043F\u0430\u0434\u0435\u043D\u0438\u0435", value: "EXACT_MATCH" },
      { label: "\u0421\u043E\u0432\u043F\u0430\u0434\u0435\u043D\u0438\u0435 \u0441 \u043F\u0440\u0438\u043C\u0435\u0447\u0430\u043D\u0438\u044F\u043C\u0438", value: "MATCH_WITH_NOTES" },
      { label: "\u0422\u0440\u0435\u0431\u0443\u0435\u0442 \u0434\u043E\u0440\u0430\u0431\u043E\u0442\u043A\u0438", value: "REQUIRES_MODIFICATION" },
      { label: "\u0427\u0430\u0441\u0442\u0438\u0447\u043D\u043E\u0435 \u0441\u043E\u0432\u043F\u0430\u0434\u0435\u043D\u0438\u0435", value: "PARTIAL_MATCH" }
    ];
    const itemTypeOptions = [
      { label: "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u043D\u043E\u0432\u0443\u044E \u043F\u043E\u0437\u0438\u0446\u0438\u044E", value: false },
      { label: "\u0412\u044B\u0431\u0440\u0430\u0442\u044C \u0441\u0443\u0449\u0435\u0441\u0442\u0432\u0443\u044E\u0449\u0443\u044E", value: true }
    ];
    const addCatalogItem = () => {
      const newItem = {
        // Для создания новой позиции
        sku: "",
        brandId: "",
        selectedBrand: null,
        description: "",
        // Для поиска существующей позиции
        isExisting: false,
        existingCatalogItem: null,
        // Уровень точности применимости
        accuracy: "EXACT_MATCH",
        notes: ""
      };
      const updatedValue = [...props.modelValue, newItem];
      emit("update:modelValue", updatedValue);
    };
    const removeCatalogItem = (index) => {
      const updatedValue = props.modelValue.filter((_, i) => i !== index);
      emit("update:modelValue", updatedValue);
    };
    const getDisplayLabel = (item) => {
      if (!item) return "";
      return typeof item === "object" ? `${item.sku} (${item.brand?.name || "\u0411\u0435\u0437 \u0431\u0440\u0435\u043D\u0434\u0430"})` : item;
    };
    const onItemSelect = (index, selectedItem) => {
      const updatedValue = [...props.modelValue];
      if (typeof selectedItem === "object") {
        updatedValue[index].existingCatalogItem = selectedItem;
      }
      emit("update:modelValue", updatedValue);
    };
    const searchCatalogItems = async (event) => {
      try {
        const query = event.query.toLowerCase();
        const result = await catalogItems.findMany({
          where: {
            OR: [
              { sku: { contains: query, mode: "insensitive" } },
              { brand: { name: { contains: query, mode: "insensitive" } } }
            ]
          },
          include: {
            brand: true
          },
          take: 10
        });
        if (result) {
          catalogItemSuggestions.value = result.map((item) => ({
            ...item,
            displayLabel: `${item.sku} (${item.brand?.name || "\u0411\u0435\u0437 \u0431\u0440\u0435\u043D\u0434\u0430"})`
          }));
        }
      } catch (err) {
        console.error("\u041E\u0448\u0438\u0431\u043A\u0430 \u043F\u043E\u0438\u0441\u043A\u0430 \u043A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u044B\u0445 \u043F\u043E\u0437\u0438\u0446\u0438\u0439:", err);
      }
    };
    const searchBrands = async (event) => {
      try {
        const query = event.query.toLowerCase();
        const result = await brands.findMany({
          where: {
            name: { contains: query, mode: "insensitive" }
          },
          take: 10
        });
        if (result) {
          brandSuggestions.value = result;
        }
      } catch (err) {
        console.error("\u041E\u0448\u0438\u0431\u043A\u0430 \u043F\u043E\u0438\u0441\u043A\u0430 \u0431\u0440\u0435\u043D\u0434\u043E\u0432:", err);
      }
    };
    const onBrandCreated = (brand) => {
      brandSuggestions.value = [brand, ...brandSuggestions.value];
    };
    const __returned__ = { props, emit, catalogItems, brands, catalogItemSuggestions, brandSuggestions, showCreateBrand, accuracyOptions, itemTypeOptions, addCatalogItem, removeCatalogItem, getDisplayLabel, onItemSelect, searchCatalogItems, searchBrands, onBrandCreated, VCard, VButton, VTag: Tag, VSelectButton, VAutoComplete, VInputText: InputText, VTextarea, VSelect: Select, QuickCreateBrand, Icon, get PlusIcon() {
      return PlusIcon;
    } };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$1(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  const _directive_tooltip = resolveDirective("tooltip");
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "catalog-item-wizard-editor" }, _attrs))}><div class="flex items-center justify-between mb-4"><h3 class="text-lg font-medium text-surface-900 dark:text-surface-0"> \u041A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u044B\u0435 \u043F\u043E\u0437\u0438\u0446\u0438\u0438 </h3>`);
  _push(ssrRenderComponent($setup["VButton"], {
    onClick: $setup.addCatalogItem,
    outlined: "",
    size: "small"
  }, {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(` \u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C `);
        _push2(ssrRenderComponent($setup["PlusIcon"], null, null, _parent2, _scopeId));
      } else {
        return [
          createTextVNode(" \u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C "),
          createVNode($setup["PlusIcon"])
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</div>`);
  if ($props.modelValue.length === 0) {
    _push(`<div class="text-center py-10"><div class="text-surface-600 dark:text-surface-400 mb-4"> \u0412\u044B \u043C\u043E\u0436\u0435\u0442\u0435 \u0434\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u043A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u044B\u0435 \u043F\u043E\u0437\u0438\u0446\u0438\u0438 \u0441\u0435\u0439\u0447\u0430\u0441 \u0438\u043B\u0438 \u043F\u0440\u043E\u043F\u0443\u0441\u0442\u0438\u0442\u044C \u044D\u0442\u043E\u0442 \u0448\u0430\u0433. </div>`);
    _push(ssrRenderComponent($setup["VButton"], {
      size: "small",
      onClick: $setup.addCatalogItem,
      outlined: ""
    }, {
      default: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(` \u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C `);
          _push2(ssrRenderComponent($setup["PlusIcon"], null, null, _parent2, _scopeId));
        } else {
          return [
            createTextVNode(" \u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C "),
            createVNode($setup["PlusIcon"])
          ];
        }
      }),
      _: 1
    }, _parent));
    _push(`</div>`);
  } else {
    _push(`<div class="space-y-4"><!--[-->`);
    ssrRenderList($props.modelValue, (item, index) => {
      _push(ssrRenderComponent($setup["VCard"], {
        key: index,
        class: "border border-surface-200 dark:border-surface-700"
      }, {
        content: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<div class="p-4"${_scopeId}><div class="flex items-center justify-between mb-4"${_scopeId}><div class="flex items-center gap-3"${_scopeId}>`);
            _push2(ssrRenderComponent($setup["VTag"], {
              value: `\u041F\u043E\u0437\u0438\u0446\u0438\u044F ${index + 1}`,
              severity: "secondary",
              size: "small"
            }, null, _parent2, _scopeId));
            if (item.isExisting) {
              _push2(ssrRenderComponent($setup["VTag"], {
                value: "\u0421\u0443\u0449\u0435\u0441\u0442\u0432\u0443\u044E\u0449\u0430\u044F",
                severity: "info",
                size: "small"
              }, null, _parent2, _scopeId));
            } else {
              _push2(ssrRenderComponent($setup["VTag"], {
                value: "\u041D\u043E\u0432\u0430\u044F",
                severity: "success",
                size: "small"
              }, null, _parent2, _scopeId));
            }
            _push2(`</div>`);
            _push2(ssrRenderComponent($setup["VButton"], {
              onClick: ($event) => $setup.removeCatalogItem(index),
              severity: "danger",
              size: "small",
              text: ""
            }, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(ssrRenderComponent($setup["Icon"], {
                    name: "pi pi-trash",
                    class: "w-5 h-5"
                  }, null, _parent3, _scopeId2));
                } else {
                  return [
                    createVNode($setup["Icon"], {
                      name: "pi pi-trash",
                      class: "w-5 h-5"
                    })
                  ];
                }
              }),
              _: 2
            }, _parent2, _scopeId));
            _push2(`</div><div class="mb-4"${_scopeId}>`);
            _push2(ssrRenderComponent($setup["VSelectButton"], {
              modelValue: item.isExisting,
              "onUpdate:modelValue": ($event) => item.isExisting = $event,
              options: $setup.itemTypeOptions,
              optionLabel: "label",
              optionValue: "value",
              class: "w-full md:w-auto"
            }, null, _parent2, _scopeId));
            _push2(`</div>`);
            if (item.isExisting) {
              _push2(`<div class="mb-4"${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}> \u041F\u043E\u0438\u0441\u043A \u043A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u043E\u0439 \u043F\u043E\u0437\u0438\u0446\u0438\u0438 * </label>`);
              _push2(ssrRenderComponent($setup["VAutoComplete"], {
                "model-value": $setup.getDisplayLabel(item.existingCatalogItem),
                "onUpdate:modelValue": ($event) => $setup.onItemSelect(index, $event),
                suggestions: $setup.catalogItemSuggestions,
                onComplete: $setup.searchCatalogItems,
                field: "displayLabel",
                placeholder: "\u041F\u043E\u0438\u0441\u043A \u043F\u043E \u0430\u0440\u0442\u0438\u043A\u0443\u043B\u0443 \u0438\u043B\u0438 \u0431\u0440\u0435\u043D\u0434\u0443...",
                class: "w-full",
                dropdown: ""
              }, {
                option: withCtx(({ option }, _push3, _parent3, _scopeId2) => {
                  if (_push3) {
                    _push3(`<div class="flex items-center gap-2"${_scopeId2}><span class="font-mono font-medium"${_scopeId2}>${ssrInterpolate(option.sku)}</span>`);
                    _push3(ssrRenderComponent($setup["VTag"], {
                      value: option.brand?.name,
                      severity: "secondary",
                      size: "small"
                    }, null, _parent3, _scopeId2));
                    _push3(`</div>`);
                  } else {
                    return [
                      createVNode("div", { class: "flex items-center gap-2" }, [
                        createVNode("span", { class: "font-mono font-medium" }, toDisplayString(option.sku), 1),
                        createVNode($setup["VTag"], {
                          value: option.brand?.name,
                          severity: "secondary",
                          size: "small"
                        }, null, 8, ["value"])
                      ])
                    ];
                  }
                }),
                _: 2
              }, _parent2, _scopeId));
              _push2(`</div>`);
            } else {
              _push2(`<div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4"${_scopeId}><div${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}> \u0410\u0440\u0442\u0438\u043A\u0443\u043B (SKU) * </label>`);
              _push2(ssrRenderComponent($setup["VInputText"], {
                modelValue: item.sku,
                "onUpdate:modelValue": ($event) => item.sku = $event,
                placeholder: "\u041D\u0430\u043F\u0440\u0438\u043C\u0435\u0440: 12345-ABC",
                class: "w-full"
              }, null, _parent2, _scopeId));
              _push2(`</div><div${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}> \u0411\u0440\u0435\u043D\u0434 * </label><div class="flex gap-2"${_scopeId}>`);
              _push2(ssrRenderComponent($setup["VAutoComplete"], {
                modelValue: item.selectedBrand,
                "onUpdate:modelValue": ($event) => item.selectedBrand = $event,
                suggestions: $setup.brandSuggestions,
                onComplete: $setup.searchBrands,
                "option-label": "name",
                placeholder: "\u041F\u043E\u0438\u0441\u043A \u0431\u0440\u0435\u043D\u0434\u0430...",
                class: "flex-1",
                dropdown: ""
              }, null, _parent2, _scopeId));
              _push2(ssrRenderComponent($setup["VButton"], mergeProps({
                onClick: ($event) => $setup.showCreateBrand = true,
                severity: "secondary",
                outlined: "",
                size: "small"
              }, ssrGetDirectiveProps(_ctx, _directive_tooltip, "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u043D\u043E\u0432\u044B\u0439 \u0431\u0440\u0435\u043D\u0434")), {
                default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                  if (_push3) {
                    _push3(ssrRenderComponent($setup["Icon"], {
                      name: "pi pi-plus",
                      class: "w-5 h-5"
                    }, null, _parent3, _scopeId2));
                  } else {
                    return [
                      createVNode($setup["Icon"], {
                        name: "pi pi-plus",
                        class: "w-5 h-5"
                      })
                    ];
                  }
                }),
                _: 2
              }, _parent2, _scopeId));
              _push2(`</div></div><div class="md:col-span-2"${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}> \u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435 </label>`);
              _push2(ssrRenderComponent($setup["VInputText"], {
                modelValue: item.description,
                "onUpdate:modelValue": ($event) => item.description = $event,
                placeholder: "\u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435 \u043A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u043E\u0439 \u043F\u043E\u0437\u0438\u0446\u0438\u0438...",
                class: "w-full"
              }, null, _parent2, _scopeId));
              _push2(`</div></div>`);
            }
            _push2(`<div class="grid grid-cols-1 md:grid-cols-2 gap-4"${_scopeId}><div${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}> \u0422\u043E\u0447\u043D\u043E\u0441\u0442\u044C \u043F\u0440\u0438\u043C\u0435\u043D\u0438\u043C\u043E\u0441\u0442\u0438 * </label>`);
            _push2(ssrRenderComponent($setup["VSelect"], {
              modelValue: item.accuracy,
              "onUpdate:modelValue": ($event) => item.accuracy = $event,
              options: $setup.accuracyOptions,
              "option-label": "label",
              "option-value": "value",
              placeholder: "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0442\u043E\u0447\u043D\u043E\u0441\u0442\u044C",
              class: "w-full"
            }, null, _parent2, _scopeId));
            _push2(`</div><div${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}> \u041F\u0440\u0438\u043C\u0435\u0447\u0430\u043D\u0438\u044F </label>`);
            _push2(ssrRenderComponent($setup["VTextarea"], {
              modelValue: item.notes,
              "onUpdate:modelValue": ($event) => item.notes = $event,
              "auto-resize": true,
              rows: "2",
              placeholder: "\u0414\u043E\u043F\u043E\u043B\u043D\u0438\u0442\u0435\u043B\u044C\u043D\u044B\u0435 \u043F\u0440\u0438\u043C\u0435\u0447\u0430\u043D\u0438\u044F...",
              class: "w-full"
            }, null, _parent2, _scopeId));
            _push2(`</div></div></div>`);
          } else {
            return [
              createVNode("div", { class: "p-4" }, [
                createVNode("div", { class: "flex items-center justify-between mb-4" }, [
                  createVNode("div", { class: "flex items-center gap-3" }, [
                    createVNode($setup["VTag"], {
                      value: `\u041F\u043E\u0437\u0438\u0446\u0438\u044F ${index + 1}`,
                      severity: "secondary",
                      size: "small"
                    }, null, 8, ["value"]),
                    item.isExisting ? (openBlock(), createBlock($setup["VTag"], {
                      key: 0,
                      value: "\u0421\u0443\u0449\u0435\u0441\u0442\u0432\u0443\u044E\u0449\u0430\u044F",
                      severity: "info",
                      size: "small"
                    })) : (openBlock(), createBlock($setup["VTag"], {
                      key: 1,
                      value: "\u041D\u043E\u0432\u0430\u044F",
                      severity: "success",
                      size: "small"
                    }))
                  ]),
                  createVNode($setup["VButton"], {
                    onClick: ($event) => $setup.removeCatalogItem(index),
                    severity: "danger",
                    size: "small",
                    text: ""
                  }, {
                    default: withCtx(() => [
                      createVNode($setup["Icon"], {
                        name: "pi pi-trash",
                        class: "w-5 h-5"
                      })
                    ]),
                    _: 2
                  }, 1032, ["onClick"])
                ]),
                createVNode("div", { class: "mb-4" }, [
                  createVNode($setup["VSelectButton"], {
                    modelValue: item.isExisting,
                    "onUpdate:modelValue": ($event) => item.isExisting = $event,
                    options: $setup.itemTypeOptions,
                    optionLabel: "label",
                    optionValue: "value",
                    class: "w-full md:w-auto"
                  }, null, 8, ["modelValue", "onUpdate:modelValue"])
                ]),
                item.isExisting ? (openBlock(), createBlock("div", {
                  key: 0,
                  class: "mb-4"
                }, [
                  createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u041F\u043E\u0438\u0441\u043A \u043A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u043E\u0439 \u043F\u043E\u0437\u0438\u0446\u0438\u0438 * "),
                  createVNode($setup["VAutoComplete"], {
                    "model-value": $setup.getDisplayLabel(item.existingCatalogItem),
                    "onUpdate:modelValue": ($event) => $setup.onItemSelect(index, $event),
                    suggestions: $setup.catalogItemSuggestions,
                    onComplete: $setup.searchCatalogItems,
                    field: "displayLabel",
                    placeholder: "\u041F\u043E\u0438\u0441\u043A \u043F\u043E \u0430\u0440\u0442\u0438\u043A\u0443\u043B\u0443 \u0438\u043B\u0438 \u0431\u0440\u0435\u043D\u0434\u0443...",
                    class: "w-full",
                    dropdown: ""
                  }, {
                    option: withCtx(({ option }) => [
                      createVNode("div", { class: "flex items-center gap-2" }, [
                        createVNode("span", { class: "font-mono font-medium" }, toDisplayString(option.sku), 1),
                        createVNode($setup["VTag"], {
                          value: option.brand?.name,
                          severity: "secondary",
                          size: "small"
                        }, null, 8, ["value"])
                      ])
                    ]),
                    _: 2
                  }, 1032, ["model-value", "onUpdate:modelValue", "suggestions"])
                ])) : (openBlock(), createBlock("div", {
                  key: 1,
                  class: "grid grid-cols-1 md:grid-cols-2 gap-4 mb-4"
                }, [
                  createVNode("div", null, [
                    createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u0410\u0440\u0442\u0438\u043A\u0443\u043B (SKU) * "),
                    createVNode($setup["VInputText"], {
                      modelValue: item.sku,
                      "onUpdate:modelValue": ($event) => item.sku = $event,
                      placeholder: "\u041D\u0430\u043F\u0440\u0438\u043C\u0435\u0440: 12345-ABC",
                      class: "w-full"
                    }, null, 8, ["modelValue", "onUpdate:modelValue"])
                  ]),
                  createVNode("div", null, [
                    createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u0411\u0440\u0435\u043D\u0434 * "),
                    createVNode("div", { class: "flex gap-2" }, [
                      createVNode($setup["VAutoComplete"], {
                        modelValue: item.selectedBrand,
                        "onUpdate:modelValue": ($event) => item.selectedBrand = $event,
                        suggestions: $setup.brandSuggestions,
                        onComplete: $setup.searchBrands,
                        "option-label": "name",
                        placeholder: "\u041F\u043E\u0438\u0441\u043A \u0431\u0440\u0435\u043D\u0434\u0430...",
                        class: "flex-1",
                        dropdown: ""
                      }, null, 8, ["modelValue", "onUpdate:modelValue", "suggestions"]),
                      withDirectives((openBlock(), createBlock($setup["VButton"], {
                        onClick: ($event) => $setup.showCreateBrand = true,
                        severity: "secondary",
                        outlined: "",
                        size: "small"
                      }, {
                        default: withCtx(() => [
                          createVNode($setup["Icon"], {
                            name: "pi pi-plus",
                            class: "w-5 h-5"
                          })
                        ]),
                        _: 1
                      }, 8, ["onClick"])), [
                        [_directive_tooltip, "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u043D\u043E\u0432\u044B\u0439 \u0431\u0440\u0435\u043D\u0434"]
                      ])
                    ])
                  ]),
                  createVNode("div", { class: "md:col-span-2" }, [
                    createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435 "),
                    createVNode($setup["VInputText"], {
                      modelValue: item.description,
                      "onUpdate:modelValue": ($event) => item.description = $event,
                      placeholder: "\u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435 \u043A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u043E\u0439 \u043F\u043E\u0437\u0438\u0446\u0438\u0438...",
                      class: "w-full"
                    }, null, 8, ["modelValue", "onUpdate:modelValue"])
                  ])
                ])),
                createVNode("div", { class: "grid grid-cols-1 md:grid-cols-2 gap-4" }, [
                  createVNode("div", null, [
                    createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u0422\u043E\u0447\u043D\u043E\u0441\u0442\u044C \u043F\u0440\u0438\u043C\u0435\u043D\u0438\u043C\u043E\u0441\u0442\u0438 * "),
                    createVNode($setup["VSelect"], {
                      modelValue: item.accuracy,
                      "onUpdate:modelValue": ($event) => item.accuracy = $event,
                      options: $setup.accuracyOptions,
                      "option-label": "label",
                      "option-value": "value",
                      placeholder: "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0442\u043E\u0447\u043D\u043E\u0441\u0442\u044C",
                      class: "w-full"
                    }, null, 8, ["modelValue", "onUpdate:modelValue"])
                  ]),
                  createVNode("div", null, [
                    createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u041F\u0440\u0438\u043C\u0435\u0447\u0430\u043D\u0438\u044F "),
                    createVNode($setup["VTextarea"], {
                      modelValue: item.notes,
                      "onUpdate:modelValue": ($event) => item.notes = $event,
                      "auto-resize": true,
                      rows: "2",
                      placeholder: "\u0414\u043E\u043F\u043E\u043B\u043D\u0438\u0442\u0435\u043B\u044C\u043D\u044B\u0435 \u043F\u0440\u0438\u043C\u0435\u0447\u0430\u043D\u0438\u044F...",
                      class: "w-full"
                    }, null, 8, ["modelValue", "onUpdate:modelValue"])
                  ])
                ])
              ])
            ];
          }
        }),
        _: 2
      }, _parent));
    });
    _push(`<!--]--></div>`);
  }
  _push(ssrRenderComponent($setup["QuickCreateBrand"], {
    visible: $setup.showCreateBrand,
    "onUpdate:visible": ($event) => $setup.showCreateBrand = $event,
    onCreated: $setup.onBrandCreated
  }, null, _parent));
  _push(`</div>`);
}
const _sfc_setup$1 = _sfc_main$1.setup;
_sfc_main$1.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/catalogitems/CatalogItemWizardEditor.vue");
  return _sfc_setup$1 ? _sfc_setup$1(props, ctx) : void 0;
};
const CatalogItemWizardEditor = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["ssrRender", _sfc_ssrRender$1]]);

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "PartWizard",
  props: {
    part: { default: null },
    mode: { default: "create" }
  },
  emits: ["created", "updated"],
  setup(__props, { expose: __expose, emit: __emit }) {
    __expose();
    const props = __props;
    const emit = __emit;
    const steps = [
      { title: "\u041E\u0441\u043D\u043E\u0432\u043D\u0430\u044F \u0438\u043D\u0444\u043E\u0440\u043C\u0430\u0446\u0438\u044F", key: "basic" },
      { title: "\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B", key: "attributes" },
      { title: "\u041A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u044B\u0435 \u043F\u043E\u0437\u0438\u0446\u0438\u0438", key: "catalog" },
      { title: "\u041F\u0440\u0438\u043C\u0435\u043D\u0438\u043C\u043E\u0441\u0442\u044C \u043A \u0442\u0435\u0445\u043D\u0438\u043A\u0435", key: "equipment" },
      { title: "\u041F\u043E\u0434\u0442\u0432\u0435\u0440\u0436\u0434\u0435\u043D\u0438\u0435", key: "confirm" }
    ];
    const totalSteps = steps.length;
    const currentStep = ref(1);
    const activeStepKey = computed({
      get: () => steps[currentStep.value - 1]?.key,
      set: (val) => {
        const index = steps.findIndex((s) => s.key === val);
        if (index >= 0) currentStep.value = index + 1;
      }
    });
    const formData = ref({
      name: "",
      attributes: [],
      catalogItems: [],
      equipmentApplicabilities: []
    });
    const {
      loading,
      error,
      clearError,
      partCategories,
      parts,
      catalogItems,
      equipmentModels,
      partAttributes,
      client,
      partApplicability,
      equipmentApplicability,
      media
    } = useTrpc();
    const selectedCategory = ref(null);
    const categorySuggestions = ref([]);
    const showCreateCategory = ref(false);
    const showCreateBrand = ref(false);
    const saving = ref(false);
    const saveError = ref(null);
    const currentPart = ref(props.part || null);
    const partImageUrl = computed(() => currentPart.value?.image?.url || null);
    const selectedFile = ref(null);
    const uploading = ref(false);
    const onSelectPartImage = (e) => {
      const input = e.target;
      if (input.files && input.files[0]) {
        selectedFile.value = input.files[0];
      }
    };
    const uploadPartImage = async () => {
      if (!props.part?.id || !selectedFile.value) return;
      try {
        uploading.value = true;
        const dataUrl = await fileToBase64(selectedFile.value);
        await media.uploadPartImage({
          partId: props.part.id,
          fileName: selectedFile.value.name,
          fileData: dataUrl,
          mimeType: selectedFile.value.type || "image/png"
        });
        await refreshCurrentPart();
        selectedFile.value = null;
      } finally {
        uploading.value = false;
      }
    };
    const removePartImage = async () => {
      if (!props.part?.id) return;
      try {
        uploading.value = true;
        await media.deletePartImage({ partId: props.part.id });
        await refreshCurrentPart();
      } finally {
        uploading.value = false;
      }
    };
    const selectedMediaFiles = ref(null);
    const onSelectPartMedia = (e) => {
      const input = e.target;
      selectedMediaFiles.value = input.files || null;
    };
    const uploadingMedia = ref(false);
    const uploadPartMedia = async () => {
      if (!props.part?.id || !selectedMediaFiles.value || selectedMediaFiles.value.length === 0) return;
      try {
        uploadingMedia.value = true;
        for (const f of Array.from(selectedMediaFiles.value)) {
          const dataUrl = await fileToBase64(f);
          await media.uploadPartMedia({
            partId: props.part.id,
            fileName: f.name,
            fileData: dataUrl,
            mimeType: f.type || "application/octet-stream"
          });
        }
        await refreshCurrentPart();
        selectedMediaFiles.value = null;
      } finally {
        uploadingMedia.value = false;
      }
    };
    const removePartMedia = async (mediaId) => {
      if (!props.part?.id) return;
      await media.removePartMedia({ partId: props.part.id, mediaId });
      await refreshCurrentPart();
    };
    const refreshCurrentPart = async () => {
      if (!props.part?.id) return;
      const p = await client.crud.part.findUnique.query({ where: { id: props.part.id }, include: { image: true, mediaAssets: true, partCategory: true } });
      currentPart.value = p;
    };
    const onCategoryCreated = (category) => {
      selectedCategory.value = category;
      categorySuggestions.value = [category, ...categorySuggestions.value];
    };
    const searchCategories = async (event) => {
      const query = event.query.toLowerCase();
      const categories = await partCategories.findMany({
        where: {
          name: {
            contains: query,
            mode: "insensitive"
          }
        },
        take: 10
      });
      if (categories && Array.isArray(categories)) {
        categorySuggestions.value = categories;
      }
    };
    const canProceed = computed(() => {
      switch (currentStep.value) {
        case 1:
          return formData.value.name.trim() && selectedCategory.value;
        case 2:
          return formData.value.attributes.every((attribute) => {
            const isRequired = Boolean(attribute.template?.isRequired || attribute.isRequired);
            if (!isRequired) return true;
            const dataType = attribute.template?.dataType || attribute.templateDataType;
            const value = attribute.value;
            if (dataType === "BOOLEAN") {
              return value !== null && value !== void 0;
            }
            return String(value ?? "").trim().length > 0;
          });
        case 3:
          return formData.value.catalogItems.every((item) => {
            if (item.isExisting) {
              return item.existingCatalogItem && item.accuracy;
            } else {
              return item.sku.trim() && item.selectedBrand && item.accuracy;
            }
          });
        case 4:
          return formData.value.equipmentApplicabilities.every((equipment) => {
            if (equipment.isExisting) {
              return equipment.existingEquipmentModel;
            } else {
              return equipment.name.trim();
            }
          });
        case 5:
          return true;
        default:
          return false;
      }
    });
    const canFinish = computed(() => canProceed.value && currentStep.value === totalSteps);
    const nextStep = () => {
      if (currentStep.value < totalSteps && canProceed.value) {
        currentStep.value++;
        clearError();
      }
    };
    const previousStep = () => {
      if (currentStep.value > 1) {
        currentStep.value--;
        clearError();
      }
    };
    const onTabChange = (nextKey) => {
      const nextIndex = steps.findIndex((s) => s.key === nextKey) + 1;
      if (nextIndex === currentStep.value) return;
      if (nextIndex > currentStep.value && !canProceed.value) return;
      currentStep.value = nextIndex;
      clearError();
    };
    const savePart = async () => {
      if (!canProceed.value) return;
      try {
        saving.value = true;
        saveError.value = null;
        clearError();
        const partData = {
          name: formData.value.name,
          partCategoryId: typeof selectedCategory.value === "object" ? selectedCategory.value.id : selectedCategory.value,
          level: props.part?.level || 0,
          path: props.part?.path || "/"
        };
        let partResult = null;
        if (props.mode === "edit" && props.part) {
          partResult = await parts.update({ where: { id: props.part.id }, data: partData });
        } else {
          partResult = await parts.create({ data: partData });
        }
        if (!partResult || !partResult.id) {
          throw new Error(`\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C ${props.mode === "edit" ? "\u043E\u0431\u043D\u043E\u0432\u0438\u0442\u044C" : "\u0441\u043E\u0437\u0434\u0430\u0442\u044C"} \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u044C`);
        }
        const partId = partResult.id;
        const attributeByTemplate = /* @__PURE__ */ new Map();
        for (const attribute of formData.value.attributes) {
          if (!attribute.templateId) continue;
          attributeByTemplate.set(attribute.templateId, attribute);
        }
        const attributesToCreate = [];
        const attributesToUpdate = [];
        attributeByTemplate.forEach((attribute) => {
          const raw = attribute.value;
          const hasValue = !(raw === null || raw === void 0 || String(raw).trim() === "");
          if (!hasValue) return;
          if (attribute.id) {
            attributesToUpdate.push({ id: attribute.id, value: String(raw).trim() });
          } else {
            attributesToCreate.push({ templateId: attribute.templateId, value: String(raw).trim() });
          }
        });
        const attributeOps = [];
        if (attributesToCreate.length > 0) {
          attributeOps.push(partAttributes.bulkCreate({ partId, attributes: attributesToCreate }));
        }
        if (attributesToUpdate.length > 0) {
          attributeOps.push(Promise.all(attributesToUpdate.map((a) => partAttributes.update({ id: a.id, value: a.value }))));
        }
        if (attributeOps.length > 0) {
          await Promise.all(attributeOps);
        }
        const catalogItemIds = [];
        const createCatalogErrors = [];
        for (const item of formData.value.catalogItems) {
          if (item.isExisting && item.existingCatalogItem) {
            catalogItemIds.push({ id: item.existingCatalogItem.id, accuracy: item.accuracy, notes: item.notes || void 0 });
          }
        }
        const newItems = formData.value.catalogItems.filter((i) => !i.isExisting);
        const newCreates = newItems.map(async (item) => {
          let brandId = null;
          if (typeof item.selectedBrand === "object" && item.selectedBrand?.id) brandId = item.selectedBrand.id;
          else if (typeof item.selectedBrand === "number") brandId = item.selectedBrand;
          else if (typeof item.brandId === "number") brandId = item.brandId;
          if (!brandId) {
            createCatalogErrors.push(`\u041D\u0435 \u0432\u044B\u0431\u0440\u0430\u043D \u0431\u0440\u0435\u043D\u0434 \u0434\u043B\u044F \u043A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u043E\u0439 \u043F\u043E\u0437\u0438\u0446\u0438\u0438 ${item.sku}`);
            return null;
          }
          const catalogResult = await catalogItems.create({
            data: {
              sku: item.sku.toUpperCase().trim(),
              brandId,
              description: item.description || void 0,
              isPublic: true
            }
          });
          if (!catalogResult || !catalogResult.id) {
            createCatalogErrors.push(`\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0441\u043E\u0437\u0434\u0430\u0442\u044C \u043A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u0443\u044E \u043F\u043E\u0437\u0438\u0446\u0438\u044E ${item.sku}`);
            return null;
          }
          return { id: catalogResult.id, accuracy: item.accuracy, notes: item.notes || void 0 };
        });
        const createdCatalog = await Promise.all(newCreates);
        for (const c of createdCatalog) {
          if (c) catalogItemIds.push(c);
        }
        const applicabilityErrors = [];
        const applicabilityOps = catalogItemIds.map(
          (c) => partApplicability.upsert({
            where: { partId_catalogItemId: { partId, catalogItemId: c.id } },
            update: { accuracy: c.accuracy, notes: c.notes },
            create: { partId, catalogItemId: c.id, accuracy: c.accuracy, notes: c.notes }
          })
        );
        const applicabilityResults = await Promise.all(applicabilityOps);
        applicabilityResults.forEach((res, idx) => {
          if (!res) applicabilityErrors.push(`\u0421\u0432\u044F\u0437\u044C \u0441 \u043A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u043E\u0439 \u043F\u043E\u0437\u0438\u0446\u0438\u0435\u0439 #${catalogItemIds[idx].id} \u043D\u0435 \u0441\u043E\u0445\u0440\u0430\u043D\u0435\u043D\u0430`);
        });
        const equipmentIds = [];
        const createEquipmentErrors = [];
        for (const eq of formData.value.equipmentApplicabilities) {
          if (eq.isExisting && eq.existingEquipmentModel) {
            equipmentIds.push({ id: eq.existingEquipmentModel.id, notes: eq.notes || void 0 });
          }
        }
        const newEquipments = formData.value.equipmentApplicabilities.filter((e) => !e.isExisting);
        const newEquipCreates = newEquipments.map(async (e) => {
          const data = { name: e.name.trim() };
          if (e.selectedBrand) data.brandId = typeof e.selectedBrand === "object" ? e.selectedBrand.id : e.selectedBrand;
          const equipmentResult = await equipmentModels.create({ data });
          if (!equipmentResult || !equipmentResult.id) {
            createEquipmentErrors.push(`\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0441\u043E\u0437\u0434\u0430\u0442\u044C \u043C\u043E\u0434\u0435\u043B\u044C \u0442\u0435\u0445\u043D\u0438\u043A\u0438 ${e.name}`);
            return null;
          }
          return { id: equipmentResult.id, notes: e.notes || void 0 };
        });
        const createdEquip = await Promise.all(newEquipCreates);
        for (const e of createdEquip) {
          if (e) equipmentIds.push(e);
        }
        const equipApplicabilityErrors = [];
        const equipApplicabilityOps = equipmentIds.map(
          (e) => equipmentApplicability.upsert({
            where: { partId_equipmentModelId: { partId, equipmentModelId: e.id } },
            update: { notes: e.notes },
            create: { partId, equipmentModelId: e.id, notes: e.notes }
          })
        );
        const equipApplicabilityResults = await Promise.all(equipApplicabilityOps);
        equipApplicabilityResults.forEach((res, idx) => {
          if (!res) equipApplicabilityErrors.push(`\u0421\u0432\u044F\u0437\u044C \u0441 \u0442\u0435\u0445\u043D\u0438\u043A\u043E\u0439 #${equipmentIds[idx].id} \u043D\u0435 \u0441\u043E\u0445\u0440\u0430\u043D\u0435\u043D\u0430`);
        });
        const allErrors = [
          ...createCatalogErrors,
          ...applicabilityErrors,
          ...createEquipmentErrors,
          ...equipApplicabilityErrors
        ].filter(Boolean);
        if (allErrors.length > 0) {
          saveError.value = allErrors.join("; ");
        }
        if (props.mode === "edit") emit("updated", partResult);
        else emit("created", partResult);
        if (props.mode === "create") resetForm();
      } catch (err) {
        console.error(`\u041E\u0448\u0438\u0431\u043A\u0430 ${props.mode === "edit" ? "\u043E\u0431\u043D\u043E\u0432\u043B\u0435\u043D\u0438\u044F" : "\u0441\u043E\u0437\u0434\u0430\u043D\u0438\u044F"} \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0438:`, err);
        saveError.value = err?.message || "\u041F\u0440\u043E\u0438\u0437\u043E\u0448\u043B\u0430 \u043E\u0448\u0438\u0431\u043A\u0430 \u043F\u0440\u0438 \u0441\u043E\u0445\u0440\u0430\u043D\u0435\u043D\u0438\u0438 \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0438";
      } finally {
        saving.value = false;
      }
    };
    const resetForm = () => {
      formData.value = {
        name: "",
        attributes: [],
        catalogItems: [],
        equipmentApplicabilities: []
      };
      selectedCategory.value = null;
      currentStep.value = 1;
    };
    const getDataTypeLabel = (dataType) => {
      if (!dataType) return "";
      const labels = {
        STRING: "\u0421\u0442\u0440\u043E\u043A\u0430",
        NUMBER: "\u0427\u0438\u0441\u043B\u043E",
        BOOLEAN: "\u041B\u043E\u0433\u0438\u0447\u0435\u0441\u043A\u043E\u0435",
        DATE: "\u0414\u0430\u0442\u0430",
        JSON: "JSON"
      };
      return labels[dataType] || dataType;
    };
    const getUnitLabel = (unit) => {
      if (!unit) return "";
      const labels = {
        MM: "\u043C\u043C",
        INCH: "\u0434\u044E\u0439\u043C\u044B",
        FT: "\u0444\u0443\u0442\u044B",
        G: "\u0433",
        KG: "\u043A\u0433",
        T: "\u0442",
        LB: "\u0444\u0443\u043D\u0442\u044B",
        ML: "\u043C\u043B",
        L: "\u043B",
        GAL: "\u0433\u0430\u043B\u043B\u043E\u043D\u044B",
        PCS: "\u0448\u0442",
        SET: "\u043A\u043E\u043C\u043F\u043B\u0435\u043A\u0442",
        PAIR: "\u043F\u0430\u0440\u0430",
        BAR: "\u0431\u0430\u0440",
        PSI: "PSI",
        KW: "\u043A\u0412\u0442",
        HP: "\u043B.\u0441.",
        NM: "\u041D\u22C5\u043C",
        RPM: "\u043E\u0431/\u043C\u0438\u043D",
        C: "\xB0C",
        F: "\xB0F",
        PERCENT: "%"
      };
      return labels[unit] || unit;
    };
    const getAccuracyLabel = (accuracy) => {
      const labels = {
        EXACT_MATCH: "\u0422\u043E\u0447\u043D\u043E\u0435 \u0441\u043E\u0432\u043F\u0430\u0434\u0435\u043D\u0438\u0435",
        MATCH_WITH_NOTES: "\u0421 \u043F\u0440\u0438\u043C\u0435\u0447\u0430\u043D\u0438\u044F\u043C\u0438",
        REQUIRES_MODIFICATION: "\u0422\u0440\u0435\u0431\u0443\u0435\u0442 \u0434\u043E\u0440\u0430\u0431\u043E\u0442\u043A\u0438",
        PARTIAL_MATCH: "\u0427\u0430\u0441\u0442\u0438\u0447\u043D\u043E\u0435 \u0441\u043E\u0432\u043F\u0430\u0434\u0435\u043D\u0438\u0435"
      };
      return labels[accuracy] || accuracy;
    };
    const loadExistingAttributes = async (partId) => {
      try {
        const attributes = await partAttributes.findByPartId({ partId });
        if (attributes && Array.isArray(attributes)) {
          formData.value.attributes = attributes.map((attr) => ({
            id: attr.id,
            templateId: attr.templateId,
            value: attr.value,
            template: attr.template,
            // Для отображения
            templateTitle: attr.template?.title,
            templateDataType: attr.template?.dataType,
            templateUnit: attr.template?.unit,
            templateGroup: attr.template?.group?.name,
            templateDescription: attr.template?.description
          }));
        }
      } catch (error2) {
        console.warn("\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0437\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u044C \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0438:", error2);
      }
    };
    const loadExistingCatalogItems = async (partId) => {
      try {
        const applicabilities = await client.crud.partApplicability.findMany.query({
          where: { partId },
          include: {
            catalogItem: {
              include: {
                brand: true
              }
            }
          }
        });
        if (applicabilities) {
          formData.value.catalogItems = applicabilities.map((app) => ({
            isExisting: true,
            existingCatalogItem: app.catalogItem,
            accuracy: app.accuracy,
            notes: app.notes || "",
            // Поля для новых позиций (пустые)
            sku: "",
            brandId: "",
            selectedBrand: null,
            description: ""
          }));
        }
      } catch (error2) {
        console.warn("\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0437\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u044C \u043A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u044B\u0435 \u043F\u043E\u0437\u0438\u0446\u0438\u0438:", error2);
      }
    };
    const loadExistingEquipmentApplicabilities = async (partId) => {
      try {
        const applicabilities = await client.crud.equipmentApplicability.findMany.query({
          where: { partId },
          include: {
            equipmentModel: {
              include: {
                brand: true
              }
            }
          }
        });
        if (applicabilities) {
          formData.value.equipmentApplicabilities = applicabilities.map((app) => ({
            isExisting: true,
            existingEquipmentModel: app.equipmentModel,
            notes: app.notes || "",
            // Поля для новых моделей (пустые)
            name: "",
            selectedBrand: null
          }));
        }
      } catch (error2) {
        console.warn("\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0437\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u044C \u043F\u0440\u0438\u043C\u0435\u043D\u0438\u043C\u043E\u0441\u0442\u044C \u043A \u0442\u0435\u0445\u043D\u0438\u043A\u0435:", error2);
      }
    };
    const loadPartData = async () => {
      if (props.part && props.mode === "edit") {
        formData.value.name = props.part.name || "";
        if (props.part.partCategory) {
          selectedCategory.value = props.part.partCategory;
        }
        if (props.part.id) {
          await Promise.all([
            loadExistingAttributes(props.part.id),
            loadExistingCatalogItems(props.part.id),
            loadExistingEquipmentApplicabilities(props.part.id)
          ]);
        }
      }
    };
    watch(
      () => props.part,
      async () => {
        if (props.mode === "edit") {
          await loadPartData();
        }
      },
      { immediate: true }
    );
    onMounted(async () => {
      if (props.mode === "edit" && props.part) {
        await loadPartData();
      }
    });
    const __returned__ = { props, emit, steps, totalSteps, currentStep, activeStepKey, formData, loading, error, clearError, partCategories, parts, catalogItems, equipmentModels, partAttributes, client, partApplicability, equipmentApplicability, media, selectedCategory, categorySuggestions, showCreateCategory, showCreateBrand, saving, saveError, currentPart, partImageUrl, selectedFile, uploading, onSelectPartImage, uploadPartImage, removePartImage, selectedMediaFiles, onSelectPartMedia, uploadingMedia, uploadPartMedia, removePartMedia, refreshCurrentPart, onCategoryCreated, searchCategories, canProceed, canFinish, nextStep, previousStep, onTabChange, savePart, resetForm, getDataTypeLabel, getUnitLabel, getAccuracyLabel, loadExistingAttributes, loadExistingCatalogItems, loadExistingEquipmentApplicabilities, loadPartData, VTabs, VTabList, VTab, VCard, VButton, VInputText: InputText, VAutoComplete, VMessage: Message, QuickCreateCategory, QuickCreateBrand, AttributeManager, EquipmentSelector, CatalogItemWizardEditor, get resolveMediaUrl() {
      return resolveMediaUrl;
    }, Icon };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "part-wizard" }, _attrs))}>`);
  _push(ssrRenderComponent($setup["VCard"], null, {
    header: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="flex items-center justify-between p-6 border-b border-surface-200 dark:border-surface-700"${_scopeId}><h2 class="text-xl font-semibold text-surface-900 dark:text-surface-0"${_scopeId}> \u041C\u0430\u0441\u0442\u0435\u0440 \u0441\u043E\u0437\u0434\u0430\u043D\u0438\u044F \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0438 </h2><div class="flex items-center gap-2 text-sm text-surface-600 dark:text-surface-400"${_scopeId}> \u0428\u0430\u0433 ${ssrInterpolate($setup.currentStep)} \u0438\u0437 ${ssrInterpolate($setup.totalSteps)}</div></div>`);
      } else {
        return [
          createVNode("div", { class: "flex items-center justify-between p-6 border-b border-surface-200 dark:border-surface-700" }, [
            createVNode("h2", { class: "text-xl font-semibold text-surface-900 dark:text-surface-0" }, " \u041C\u0430\u0441\u0442\u0435\u0440 \u0441\u043E\u0437\u0434\u0430\u043D\u0438\u044F \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0438 "),
            createVNode("div", { class: "flex items-center gap-2 text-sm text-surface-600 dark:text-surface-400" }, " \u0428\u0430\u0433 " + toDisplayString($setup.currentStep) + " \u0438\u0437 " + toDisplayString($setup.totalSteps), 1)
          ])
        ];
      }
    }),
    content: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="p-6"${_scopeId}><div class="mb-4"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["VTabs"], {
          value: $setup.activeStepKey,
          "onUpdate:value": $setup.onTabChange
        }, {
          default: withCtx((_2, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(ssrRenderComponent($setup["VTabList"], null, {
                default: withCtx((_3, _push4, _parent4, _scopeId3) => {
                  if (_push4) {
                    _push4(`<!--[-->`);
                    ssrRenderList($setup.steps, (step, index) => {
                      _push4(ssrRenderComponent($setup["VTab"], {
                        key: step.key,
                        value: step.key
                      }, {
                        default: withCtx((_4, _push5, _parent5, _scopeId4) => {
                          if (_push5) {
                            _push5(`<span class="text-sm"${_scopeId4}>${ssrInterpolate(index + 1)}. ${ssrInterpolate(step.title)}</span>`);
                          } else {
                            return [
                              createVNode("span", { class: "text-sm" }, toDisplayString(index + 1) + ". " + toDisplayString(step.title), 1)
                            ];
                          }
                        }),
                        _: 2
                      }, _parent4, _scopeId3));
                    });
                    _push4(`<!--]-->`);
                  } else {
                    return [
                      (openBlock(), createBlock(Fragment, null, renderList($setup.steps, (step, index) => {
                        return createVNode($setup["VTab"], {
                          key: step.key,
                          value: step.key
                        }, {
                          default: withCtx(() => [
                            createVNode("span", { class: "text-sm" }, toDisplayString(index + 1) + ". " + toDisplayString(step.title), 1)
                          ]),
                          _: 2
                        }, 1032, ["value"]);
                      }), 64))
                    ];
                  }
                }),
                _: 1
              }, _parent3, _scopeId2));
            } else {
              return [
                createVNode($setup["VTabList"], null, {
                  default: withCtx(() => [
                    (openBlock(), createBlock(Fragment, null, renderList($setup.steps, (step, index) => {
                      return createVNode($setup["VTab"], {
                        key: step.key,
                        value: step.key
                      }, {
                        default: withCtx(() => [
                          createVNode("span", { class: "text-sm" }, toDisplayString(index + 1) + ". " + toDisplayString(step.title), 1)
                        ]),
                        _: 2
                      }, 1032, ["value"]);
                    }), 64))
                  ]),
                  _: 1
                })
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(`</div><div class="min-h-96"${_scopeId}>`);
        if ($setup.currentStep === 1) {
          _push2(`<div class="space-y-6"${_scopeId}><h3 class="text-lg font-medium text-surface-900 dark:text-surface-0 mb-4"${_scopeId}> \u041E\u0441\u043D\u043E\u0432\u043D\u0430\u044F \u0438\u043D\u0444\u043E\u0440\u043C\u0430\u0446\u0438\u044F \u043E \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0438 </h3><div class="grid grid-cols-1 md:grid-cols-2 gap-6"${_scopeId}><div${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}> \u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435 \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0438 * </label>`);
          _push2(ssrRenderComponent($setup["VInputText"], {
            modelValue: $setup.formData.name,
            "onUpdate:modelValue": ($event) => $setup.formData.name = $event,
            placeholder: "\u041D\u0430\u043F\u0440\u0438\u043C\u0435\u0440: \u0421\u0430\u043B\u044C\u043D\u0438\u043A \u043A\u043E\u043B\u0435\u043D\u0432\u0430\u043B\u0430 \u043F\u0435\u0440\u0435\u0434\u043D\u0438\u0439",
            class: "w-full p-3"
          }, null, _parent2, _scopeId));
          _push2(`</div><div${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}> \u041A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u044F * </label><div class="flex gap-2"${_scopeId}>`);
          _push2(ssrRenderComponent($setup["VAutoComplete"], {
            modelValue: $setup.selectedCategory,
            "onUpdate:modelValue": ($event) => $setup.selectedCategory = $event,
            suggestions: $setup.categorySuggestions,
            onComplete: $setup.searchCategories,
            "option-label": "name",
            placeholder: "\u041F\u043E\u0438\u0441\u043A \u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438...",
            class: "flex-1",
            dropdown: ""
          }, null, _parent2, _scopeId));
          _push2(ssrRenderComponent($setup["VButton"], {
            onClick: ($event) => $setup.showCreateCategory = true,
            severity: "secondary",
            outlined: "",
            size: "small",
            label: "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u043D\u043E\u0432\u0443\u044E \u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u044E"
          }, {
            default: withCtx((_2, _push3, _parent3, _scopeId2) => {
              if (_push3) {
                _push3(` + `);
              } else {
                return [
                  createTextVNode(" + ")
                ];
              }
            }),
            _: 1
          }, _parent2, _scopeId));
          _push2(`</div></div></div>`);
          if ($props.mode === "edit" && $setup.props.part) {
            _push2(`<div class="mt-4 space-y-4"${_scopeId}><div${_scopeId}><h4 class="text-surface-900 dark:text-surface-0 mb-2 font-medium"${_scopeId}>\u041E\u0441\u043D\u043E\u0432\u043D\u043E\u0435 \u0438\u0437\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u0435</h4><div class="flex items-center gap-4"${_scopeId}><div class="w-32 h-32 border rounded bg-surface-50 dark:bg-surface-900 flex items-center justify-center overflow-hidden"${_scopeId}>`);
            if ($setup.partImageUrl) {
              _push2(`<img${ssrRenderAttr("src", $setup.resolveMediaUrl($setup.partImageUrl))} alt="\u0418\u0437\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u0435 \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0438" class="object-cover w-full h-full"${_scopeId}>`);
            } else {
              _push2(`<span class="text-surface-500 text-sm"${_scopeId}>\u041D\u0435\u0442 \u0438\u0437\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u044F</span>`);
            }
            _push2(`</div><div class="flex flex-col gap-2"${_scopeId}><input type="file" accept="image/*"${_scopeId}><div class="flex gap-2"${_scopeId}>`);
            _push2(ssrRenderComponent($setup["VButton"], {
              size: "small",
              disabled: !$setup.selectedFile || $setup.uploading,
              onClick: $setup.uploadPartImage
            }, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(`\u0417\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u044C`);
                } else {
                  return [
                    createTextVNode("\u0417\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u044C")
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
            _push2(ssrRenderComponent($setup["VButton"], {
              size: "small",
              severity: "danger",
              outlined: "",
              disabled: !$setup.partImageUrl || $setup.uploading,
              onClick: $setup.removePartImage
            }, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(`\u0423\u0434\u0430\u043B\u0438\u0442\u044C`);
                } else {
                  return [
                    createTextVNode("\u0423\u0434\u0430\u043B\u0438\u0442\u044C")
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
            _push2(`</div>`);
            if ($setup.uploading) {
              _push2(`<div class="text-surface-500 text-xs"${_scopeId}>\u0417\u0430\u0433\u0440\u0443\u0437\u043A\u0430...</div>`);
            } else {
              _push2(`<!---->`);
            }
            _push2(`</div></div></div><div${_scopeId}><h4 class="text-surface-900 dark:text-surface-0 mb-2 font-medium"${_scopeId}>\u0414\u043E\u043F\u043E\u043B\u043D\u0438\u0442\u0435\u043B\u044C\u043D\u044B\u0435 \u043C\u0435\u0434\u0438\u0430 (\u0438\u0437\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u044F, PDF)</h4><div class="flex items-center gap-4"${_scopeId}><div class="flex-1 grid grid-cols-2 gap-3"${_scopeId}><!--[-->`);
            ssrRenderList($setup.currentPart?.mediaAssets || [], (m) => {
              _push2(`<div class="border rounded overflow-hidden"${_scopeId}>`);
              if (m.mimeType?.startsWith("image/")) {
                _push2(`<img${ssrRenderAttr("src", $setup.resolveMediaUrl(m.url))}${ssrRenderAttr("alt", m.fileName)} class="w-full h-32 object-cover"${_scopeId}>`);
              } else {
                _push2(`<a${ssrRenderAttr("href", $setup.resolveMediaUrl(m.url))} target="_blank" class="flex items-center gap-2 p-2 hover:bg-surface-50 dark:hover:bg-surface-900"${_scopeId}>`);
                _push2(ssrRenderComponent($setup["Icon"], {
                  name: "pi pi-file-pdf",
                  class: "text-red-600 w-4 h-4"
                }, null, _parent2, _scopeId));
                _push2(`<span class="text-sm truncate"${_scopeId}>${ssrInterpolate(m.fileName)}</span></a>`);
              }
              _push2(`<div class="p-2 border-t flex justify-end"${_scopeId}>`);
              _push2(ssrRenderComponent($setup["VButton"], {
                size: "small",
                severity: "danger",
                text: "",
                onClick: ($event) => $setup.removePartMedia(m.id)
              }, {
                default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                  if (_push3) {
                    _push3(`\u0423\u0434\u0430\u043B\u0438\u0442\u044C`);
                  } else {
                    return [
                      createTextVNode("\u0423\u0434\u0430\u043B\u0438\u0442\u044C")
                    ];
                  }
                }),
                _: 2
              }, _parent2, _scopeId));
              _push2(`</div></div>`);
            });
            _push2(`<!--]--></div><div class="flex flex-col gap-2 w-64"${_scopeId}><input type="file" multiple accept="image/*,application/pdf"${_scopeId}>`);
            _push2(ssrRenderComponent($setup["VButton"], {
              size: "small",
              disabled: !$setup.selectedMediaFiles || $setup.uploadingMedia,
              onClick: $setup.uploadPartMedia
            }, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(`\u0417\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u044C \u0432\u044B\u0431\u0440\u0430\u043D\u043D\u044B\u0435`);
                } else {
                  return [
                    createTextVNode("\u0417\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u044C \u0432\u044B\u0431\u0440\u0430\u043D\u043D\u044B\u0435")
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
            if ($setup.uploadingMedia) {
              _push2(`<div class="text-surface-500 text-xs"${_scopeId}>\u0417\u0430\u0433\u0440\u0443\u0437\u043A\u0430...</div>`);
            } else {
              _push2(`<!---->`);
            }
            _push2(`</div></div></div></div>`);
          } else {
            _push2(`<!---->`);
          }
          _push2(`</div>`);
        } else {
          _push2(`<!---->`);
        }
        if ($setup.currentStep === 2) {
          _push2(`<div class="space-y-6"${_scopeId}>`);
          _push2(ssrRenderComponent($setup["AttributeManager"], {
            modelValue: $setup.formData.attributes,
            "onUpdate:modelValue": ($event) => $setup.formData.attributes = $event
          }, null, _parent2, _scopeId));
          _push2(`</div>`);
        } else {
          _push2(`<!---->`);
        }
        if ($setup.currentStep === 3) {
          _push2(`<div class="space-y-6"${_scopeId}>`);
          _push2(ssrRenderComponent($setup["CatalogItemWizardEditor"], {
            modelValue: $setup.formData.catalogItems,
            "onUpdate:modelValue": ($event) => $setup.formData.catalogItems = $event
          }, null, _parent2, _scopeId));
          _push2(`</div>`);
        } else {
          _push2(`<!---->`);
        }
        if ($setup.currentStep === 4) {
          _push2(`<div class="space-y-6"${_scopeId}>`);
          _push2(ssrRenderComponent($setup["EquipmentSelector"], {
            modelValue: $setup.formData.equipmentApplicabilities,
            "onUpdate:modelValue": ($event) => $setup.formData.equipmentApplicabilities = $event
          }, null, _parent2, _scopeId));
          _push2(`</div>`);
        } else {
          _push2(`<!---->`);
        }
        if ($setup.currentStep === 5) {
          _push2(`<div class="space-y-6"${_scopeId}><h3 class="text-lg font-medium text-surface-900 dark:text-surface-0 mb-4"${_scopeId}> \u041F\u043E\u0434\u0442\u0432\u0435\u0440\u0436\u0434\u0435\u043D\u0438\u0435 \u0441\u043E\u0437\u0434\u0430\u043D\u0438\u044F </h3><div class="bg-surface-50 dark:bg-surface-900 rounded-lg p-6"${_scopeId}><h4 class="font-medium text-surface-900 dark:text-surface-0 mb-4"${_scopeId}> \u041E\u0441\u043D\u043E\u0432\u043D\u0430\u044F \u0438\u043D\u0444\u043E\u0440\u043C\u0430\u0446\u0438\u044F: </h4><dl class="space-y-2"${_scopeId}><div class="flex"${_scopeId}><dt class="w-32 text-surface-600 dark:text-surface-400"${_scopeId}> \u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435: </dt><dd class="text-surface-900 dark:text-surface-0"${_scopeId}>${ssrInterpolate($setup.formData.name)}</dd></div><div class="flex"${_scopeId}><dt class="w-32 text-surface-600 dark:text-surface-400"${_scopeId}> \u041A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u044F: </dt><dd class="text-surface-900 dark:text-surface-0"${_scopeId}>${ssrInterpolate($setup.selectedCategory?.name)}</dd></div></dl></div>`);
          if ($setup.formData.attributes.length > 0) {
            _push2(`<div class="bg-surface-50 dark:bg-surface-900 rounded-lg p-6"${_scopeId}><h4 class="font-medium text-surface-900 dark:text-surface-0 mb-4"${_scopeId}> \u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B (${ssrInterpolate($setup.formData.attributes.length)}): </h4><div class="space-y-3"${_scopeId}><!--[-->`);
            ssrRenderList($setup.formData.attributes, (attribute, index) => {
              _push2(`<div class="flex items-center justify-between p-3 bg-surface-0 dark:bg-surface-800 rounded border"${_scopeId}><div class="flex-1"${_scopeId}><div class="font-medium text-surface-900 dark:text-surface-0"${_scopeId}>${ssrInterpolate(attribute.template?.title || attribute.templateTitle)} `);
              if (attribute.template?.isRequired) {
                _push2(`<span class="text-red-500 ml-1"${_scopeId}>*</span>`);
              } else {
                _push2(`<!---->`);
              }
              _push2(`</div><div class="text-sm text-surface-600 dark:text-surface-400"${_scopeId}>${ssrInterpolate(attribute.value)}${ssrInterpolate(attribute.template?.unit || attribute.templateUnit ? `
                        ${$setup.getUnitLabel(
                attribute.template?.unit || attribute.templateUnit
              )}` : "")} `);
              if (attribute.template?.group?.name || attribute.templateGroup) {
                _push2(`<span class="ml-2 px-2 py-1 bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 rounded text-xs"${_scopeId}>${ssrInterpolate(attribute.template?.group?.name || attribute.templateGroup)}</span>`);
              } else {
                _push2(`<!---->`);
              }
              _push2(`</div></div><div class="text-xs text-surface-500 dark:text-surface-400"${_scopeId}>${ssrInterpolate($setup.getDataTypeLabel(
                attribute.template?.dataType || attribute.templateDataType
              ))}</div></div>`);
            });
            _push2(`<!--]--></div></div>`);
          } else {
            _push2(`<!---->`);
          }
          _push2(`<div class="bg-surface-50 dark:bg-surface-900 rounded-lg p-6"${_scopeId}><h4 class="font-medium text-surface-900 dark:text-surface-0 mb-4"${_scopeId}> \u041A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u044B\u0435 \u043F\u043E\u0437\u0438\u0446\u0438\u0438 (${ssrInterpolate($setup.formData.catalogItems.length)}): </h4><div class="space-y-3"${_scopeId}><!--[-->`);
          ssrRenderList($setup.formData.catalogItems, (item, index) => {
            _push2(`<div class="flex items-center justify-between p-3 bg-surface-0 dark:bg-surface-950 rounded border"${_scopeId}><div class="flex-1"${_scopeId}><div class="flex items-center gap-2"${_scopeId}><span class="font-medium"${_scopeId}>${ssrInterpolate(item.isExisting ? item.existingCatalogItem?.sku : item.sku)}</span><span class="text-surface-600 dark:text-surface-400"${_scopeId}> (${ssrInterpolate(item.isExisting ? item.existingCatalogItem?.brand?.name : item.selectedBrand?.name)}) </span>`);
            if (item.isExisting) {
              _push2(`<span class="px-2 py-1 bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded text-xs"${_scopeId}> \u0421\u0443\u0449\u0435\u0441\u0442\u0432\u0443\u044E\u0449\u0430\u044F </span>`);
            } else {
              _push2(`<span class="px-2 py-1 bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded text-xs"${_scopeId}> \u041D\u043E\u0432\u0430\u044F </span>`);
            }
            _push2(`</div><div class="text-sm text-surface-600 dark:text-surface-400 mt-1"${_scopeId}> \u0422\u043E\u0447\u043D\u043E\u0441\u0442\u044C: ${ssrInterpolate($setup.getAccuracyLabel(item.accuracy))} `);
            if (item.notes) {
              _push2(`<span class="ml-2"${_scopeId}>\u2022 ${ssrInterpolate(item.notes)}</span>`);
            } else {
              _push2(`<!---->`);
            }
            _push2(`</div></div></div>`);
          });
          _push2(`<!--]--></div></div>`);
          if ($setup.formData.equipmentApplicabilities.length > 0) {
            _push2(`<div class="bg-surface-50 dark:bg-surface-900 rounded-lg p-6"${_scopeId}><h4 class="font-medium text-surface-900 dark:text-surface-0 mb-4"${_scopeId}> \u041F\u0440\u0438\u043C\u0435\u043D\u0438\u043C\u043E\u0441\u0442\u044C \u043A \u0442\u0435\u0445\u043D\u0438\u043A\u0435 (${ssrInterpolate($setup.formData.equipmentApplicabilities.length)}): </h4><div class="space-y-3"${_scopeId}><!--[-->`);
            ssrRenderList($setup.formData.equipmentApplicabilities, (equipment, index) => {
              _push2(`<div class="flex items-center justify-between p-3 bg-surface-0 dark:bg-surface-950 rounded border"${_scopeId}><div class="flex-1"${_scopeId}><div class="flex items-center gap-2"${_scopeId}><span class="font-medium"${_scopeId}>${ssrInterpolate(equipment.isExisting ? equipment.existingEquipmentModel?.name : equipment.name)}</span>`);
              if (equipment.selectedBrand || equipment.existingEquipmentModel?.brand) {
                _push2(`<span class="text-surface-600 dark:text-surface-400"${_scopeId}> (${ssrInterpolate(equipment.selectedBrand?.name || equipment.existingEquipmentModel?.brand?.name)}) </span>`);
              } else {
                _push2(`<!---->`);
              }
              if (equipment.isExisting) {
                _push2(`<span class="px-2 py-1 bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded text-xs"${_scopeId}> \u0421\u0443\u0449\u0435\u0441\u0442\u0432\u0443\u044E\u0449\u0430\u044F </span>`);
              } else {
                _push2(`<span class="px-2 py-1 bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded text-xs"${_scopeId}> \u041D\u043E\u0432\u0430\u044F </span>`);
              }
              _push2(`</div>`);
              if (equipment.notes) {
                _push2(`<div class="text-sm text-surface-600 dark:text-surface-400 mt-1"${_scopeId}>${ssrInterpolate(equipment.notes)}</div>`);
              } else {
                _push2(`<!---->`);
              }
              _push2(`</div></div>`);
            });
            _push2(`<!--]--></div></div>`);
          } else {
            _push2(`<!---->`);
          }
          _push2(`</div>`);
        } else {
          _push2(`<!---->`);
        }
        _push2(`</div><div class="flex items-center justify-between mt-8 pt-6 border-t border-surface-200 dark:border-surface-700"${_scopeId}>`);
        if ($setup.currentStep > 1) {
          _push2(ssrRenderComponent($setup["VButton"], {
            onClick: $setup.previousStep,
            severity: "secondary",
            outlined: ""
          }, {
            default: withCtx((_2, _push3, _parent3, _scopeId2) => {
              if (_push3) {
                _push3(` \u041D\u0430\u0437\u0430\u0434 `);
              } else {
                return [
                  createTextVNode(" \u041D\u0430\u0437\u0430\u0434 ")
                ];
              }
            }),
            _: 1
          }, _parent2, _scopeId));
        } else {
          _push2(`<div${_scopeId}></div>`);
        }
        _push2(`<div class="flex gap-3"${_scopeId}>`);
        if ($setup.currentStep < $setup.totalSteps) {
          _push2(ssrRenderComponent($setup["VButton"], {
            onClick: $setup.nextStep,
            disabled: !$setup.canProceed,
            label: "\u0414\u0430\u043B\u0435\u0435",
            outlined: ""
          }, null, _parent2, _scopeId));
        } else {
          _push2(ssrRenderComponent($setup["VButton"], {
            label: $props.mode === "edit" ? "\u0421\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C \u0438\u0437\u043C\u0435\u043D\u0435\u043D\u0438\u044F" : "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u044C",
            onClick: $setup.savePart,
            loading: $setup.loading || $setup.saving,
            disabled: !$setup.canFinish || $setup.saving
          }, null, _parent2, _scopeId));
        }
        _push2(`</div></div></div>`);
      } else {
        return [
          createVNode("div", { class: "p-6" }, [
            createVNode("div", { class: "mb-4" }, [
              createVNode($setup["VTabs"], {
                value: $setup.activeStepKey,
                "onUpdate:value": $setup.onTabChange
              }, {
                default: withCtx(() => [
                  createVNode($setup["VTabList"], null, {
                    default: withCtx(() => [
                      (openBlock(), createBlock(Fragment, null, renderList($setup.steps, (step, index) => {
                        return createVNode($setup["VTab"], {
                          key: step.key,
                          value: step.key
                        }, {
                          default: withCtx(() => [
                            createVNode("span", { class: "text-sm" }, toDisplayString(index + 1) + ". " + toDisplayString(step.title), 1)
                          ]),
                          _: 2
                        }, 1032, ["value"]);
                      }), 64))
                    ]),
                    _: 1
                  })
                ]),
                _: 1
              }, 8, ["value"])
            ]),
            createVNode("div", { class: "min-h-96" }, [
              $setup.currentStep === 1 ? (openBlock(), createBlock("div", {
                key: 0,
                class: "space-y-6"
              }, [
                createVNode("h3", { class: "text-lg font-medium text-surface-900 dark:text-surface-0 mb-4" }, " \u041E\u0441\u043D\u043E\u0432\u043D\u0430\u044F \u0438\u043D\u0444\u043E\u0440\u043C\u0430\u0446\u0438\u044F \u043E \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0438 "),
                createVNode("div", { class: "grid grid-cols-1 md:grid-cols-2 gap-6" }, [
                  createVNode("div", null, [
                    createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435 \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0438 * "),
                    createVNode($setup["VInputText"], {
                      modelValue: $setup.formData.name,
                      "onUpdate:modelValue": ($event) => $setup.formData.name = $event,
                      placeholder: "\u041D\u0430\u043F\u0440\u0438\u043C\u0435\u0440: \u0421\u0430\u043B\u044C\u043D\u0438\u043A \u043A\u043E\u043B\u0435\u043D\u0432\u0430\u043B\u0430 \u043F\u0435\u0440\u0435\u0434\u043D\u0438\u0439",
                      class: "w-full p-3"
                    }, null, 8, ["modelValue", "onUpdate:modelValue"])
                  ]),
                  createVNode("div", null, [
                    createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u041A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u044F * "),
                    createVNode("div", { class: "flex gap-2" }, [
                      createVNode($setup["VAutoComplete"], {
                        modelValue: $setup.selectedCategory,
                        "onUpdate:modelValue": ($event) => $setup.selectedCategory = $event,
                        suggestions: $setup.categorySuggestions,
                        onComplete: $setup.searchCategories,
                        "option-label": "name",
                        placeholder: "\u041F\u043E\u0438\u0441\u043A \u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438...",
                        class: "flex-1",
                        dropdown: ""
                      }, null, 8, ["modelValue", "onUpdate:modelValue", "suggestions"]),
                      createVNode($setup["VButton"], {
                        onClick: ($event) => $setup.showCreateCategory = true,
                        severity: "secondary",
                        outlined: "",
                        size: "small",
                        label: "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u043D\u043E\u0432\u0443\u044E \u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u044E"
                      }, {
                        default: withCtx(() => [
                          createTextVNode(" + ")
                        ]),
                        _: 1
                      }, 8, ["onClick"])
                    ])
                  ])
                ]),
                $props.mode === "edit" && $setup.props.part ? (openBlock(), createBlock("div", {
                  key: 0,
                  class: "mt-4 space-y-4"
                }, [
                  createVNode("div", null, [
                    createVNode("h4", { class: "text-surface-900 dark:text-surface-0 mb-2 font-medium" }, "\u041E\u0441\u043D\u043E\u0432\u043D\u043E\u0435 \u0438\u0437\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u0435"),
                    createVNode("div", { class: "flex items-center gap-4" }, [
                      createVNode("div", { class: "w-32 h-32 border rounded bg-surface-50 dark:bg-surface-900 flex items-center justify-center overflow-hidden" }, [
                        $setup.partImageUrl ? (openBlock(), createBlock("img", {
                          key: 0,
                          src: $setup.resolveMediaUrl($setup.partImageUrl),
                          alt: "\u0418\u0437\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u0435 \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0438",
                          class: "object-cover w-full h-full"
                        }, null, 8, ["src"])) : (openBlock(), createBlock("span", {
                          key: 1,
                          class: "text-surface-500 text-sm"
                        }, "\u041D\u0435\u0442 \u0438\u0437\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u044F"))
                      ]),
                      createVNode("div", { class: "flex flex-col gap-2" }, [
                        createVNode("input", {
                          type: "file",
                          accept: "image/*",
                          onChange: $setup.onSelectPartImage
                        }, null, 32),
                        createVNode("div", { class: "flex gap-2" }, [
                          createVNode($setup["VButton"], {
                            size: "small",
                            disabled: !$setup.selectedFile || $setup.uploading,
                            onClick: $setup.uploadPartImage
                          }, {
                            default: withCtx(() => [
                              createTextVNode("\u0417\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u044C")
                            ]),
                            _: 1
                          }, 8, ["disabled"]),
                          createVNode($setup["VButton"], {
                            size: "small",
                            severity: "danger",
                            outlined: "",
                            disabled: !$setup.partImageUrl || $setup.uploading,
                            onClick: $setup.removePartImage
                          }, {
                            default: withCtx(() => [
                              createTextVNode("\u0423\u0434\u0430\u043B\u0438\u0442\u044C")
                            ]),
                            _: 1
                          }, 8, ["disabled"])
                        ]),
                        $setup.uploading ? (openBlock(), createBlock("div", {
                          key: 0,
                          class: "text-surface-500 text-xs"
                        }, "\u0417\u0430\u0433\u0440\u0443\u0437\u043A\u0430...")) : createCommentVNode("", true)
                      ])
                    ])
                  ]),
                  createVNode("div", null, [
                    createVNode("h4", { class: "text-surface-900 dark:text-surface-0 mb-2 font-medium" }, "\u0414\u043E\u043F\u043E\u043B\u043D\u0438\u0442\u0435\u043B\u044C\u043D\u044B\u0435 \u043C\u0435\u0434\u0438\u0430 (\u0438\u0437\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u044F, PDF)"),
                    createVNode("div", { class: "flex items-center gap-4" }, [
                      createVNode("div", { class: "flex-1 grid grid-cols-2 gap-3" }, [
                        (openBlock(true), createBlock(Fragment, null, renderList($setup.currentPart?.mediaAssets || [], (m) => {
                          return openBlock(), createBlock("div", {
                            key: m.id,
                            class: "border rounded overflow-hidden"
                          }, [
                            m.mimeType?.startsWith("image/") ? (openBlock(), createBlock("img", {
                              key: 0,
                              src: $setup.resolveMediaUrl(m.url),
                              alt: m.fileName,
                              class: "w-full h-32 object-cover"
                            }, null, 8, ["src", "alt"])) : (openBlock(), createBlock("a", {
                              key: 1,
                              href: $setup.resolveMediaUrl(m.url),
                              target: "_blank",
                              class: "flex items-center gap-2 p-2 hover:bg-surface-50 dark:hover:bg-surface-900"
                            }, [
                              createVNode($setup["Icon"], {
                                name: "pi pi-file-pdf",
                                class: "text-red-600 w-4 h-4"
                              }),
                              createVNode("span", { class: "text-sm truncate" }, toDisplayString(m.fileName), 1)
                            ], 8, ["href"])),
                            createVNode("div", { class: "p-2 border-t flex justify-end" }, [
                              createVNode($setup["VButton"], {
                                size: "small",
                                severity: "danger",
                                text: "",
                                onClick: ($event) => $setup.removePartMedia(m.id)
                              }, {
                                default: withCtx(() => [
                                  createTextVNode("\u0423\u0434\u0430\u043B\u0438\u0442\u044C")
                                ]),
                                _: 2
                              }, 1032, ["onClick"])
                            ])
                          ]);
                        }), 128))
                      ]),
                      createVNode("div", { class: "flex flex-col gap-2 w-64" }, [
                        createVNode("input", {
                          type: "file",
                          multiple: "",
                          accept: "image/*,application/pdf",
                          onChange: $setup.onSelectPartMedia
                        }, null, 32),
                        createVNode($setup["VButton"], {
                          size: "small",
                          disabled: !$setup.selectedMediaFiles || $setup.uploadingMedia,
                          onClick: $setup.uploadPartMedia
                        }, {
                          default: withCtx(() => [
                            createTextVNode("\u0417\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u044C \u0432\u044B\u0431\u0440\u0430\u043D\u043D\u044B\u0435")
                          ]),
                          _: 1
                        }, 8, ["disabled"]),
                        $setup.uploadingMedia ? (openBlock(), createBlock("div", {
                          key: 0,
                          class: "text-surface-500 text-xs"
                        }, "\u0417\u0430\u0433\u0440\u0443\u0437\u043A\u0430...")) : createCommentVNode("", true)
                      ])
                    ])
                  ])
                ])) : createCommentVNode("", true)
              ])) : createCommentVNode("", true),
              $setup.currentStep === 2 ? (openBlock(), createBlock("div", {
                key: 1,
                class: "space-y-6"
              }, [
                createVNode($setup["AttributeManager"], {
                  modelValue: $setup.formData.attributes,
                  "onUpdate:modelValue": ($event) => $setup.formData.attributes = $event
                }, null, 8, ["modelValue", "onUpdate:modelValue"])
              ])) : createCommentVNode("", true),
              $setup.currentStep === 3 ? (openBlock(), createBlock("div", {
                key: 2,
                class: "space-y-6"
              }, [
                createVNode($setup["CatalogItemWizardEditor"], {
                  modelValue: $setup.formData.catalogItems,
                  "onUpdate:modelValue": ($event) => $setup.formData.catalogItems = $event
                }, null, 8, ["modelValue", "onUpdate:modelValue"])
              ])) : createCommentVNode("", true),
              $setup.currentStep === 4 ? (openBlock(), createBlock("div", {
                key: 3,
                class: "space-y-6"
              }, [
                createVNode($setup["EquipmentSelector"], {
                  modelValue: $setup.formData.equipmentApplicabilities,
                  "onUpdate:modelValue": ($event) => $setup.formData.equipmentApplicabilities = $event
                }, null, 8, ["modelValue", "onUpdate:modelValue"])
              ])) : createCommentVNode("", true),
              $setup.currentStep === 5 ? (openBlock(), createBlock("div", {
                key: 4,
                class: "space-y-6"
              }, [
                createVNode("h3", { class: "text-lg font-medium text-surface-900 dark:text-surface-0 mb-4" }, " \u041F\u043E\u0434\u0442\u0432\u0435\u0440\u0436\u0434\u0435\u043D\u0438\u0435 \u0441\u043E\u0437\u0434\u0430\u043D\u0438\u044F "),
                createVNode("div", { class: "bg-surface-50 dark:bg-surface-900 rounded-lg p-6" }, [
                  createVNode("h4", { class: "font-medium text-surface-900 dark:text-surface-0 mb-4" }, " \u041E\u0441\u043D\u043E\u0432\u043D\u0430\u044F \u0438\u043D\u0444\u043E\u0440\u043C\u0430\u0446\u0438\u044F: "),
                  createVNode("dl", { class: "space-y-2" }, [
                    createVNode("div", { class: "flex" }, [
                      createVNode("dt", { class: "w-32 text-surface-600 dark:text-surface-400" }, " \u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435: "),
                      createVNode("dd", { class: "text-surface-900 dark:text-surface-0" }, toDisplayString($setup.formData.name), 1)
                    ]),
                    createVNode("div", { class: "flex" }, [
                      createVNode("dt", { class: "w-32 text-surface-600 dark:text-surface-400" }, " \u041A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u044F: "),
                      createVNode("dd", { class: "text-surface-900 dark:text-surface-0" }, toDisplayString($setup.selectedCategory?.name), 1)
                    ])
                  ])
                ]),
                $setup.formData.attributes.length > 0 ? (openBlock(), createBlock("div", {
                  key: 0,
                  class: "bg-surface-50 dark:bg-surface-900 rounded-lg p-6"
                }, [
                  createVNode("h4", { class: "font-medium text-surface-900 dark:text-surface-0 mb-4" }, " \u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B (" + toDisplayString($setup.formData.attributes.length) + "): ", 1),
                  createVNode("div", { class: "space-y-3" }, [
                    (openBlock(true), createBlock(Fragment, null, renderList($setup.formData.attributes, (attribute, index) => {
                      return openBlock(), createBlock("div", {
                        key: index,
                        class: "flex items-center justify-between p-3 bg-surface-0 dark:bg-surface-800 rounded border"
                      }, [
                        createVNode("div", { class: "flex-1" }, [
                          createVNode("div", { class: "font-medium text-surface-900 dark:text-surface-0" }, [
                            createTextVNode(toDisplayString(attribute.template?.title || attribute.templateTitle) + " ", 1),
                            attribute.template?.isRequired ? (openBlock(), createBlock("span", {
                              key: 0,
                              class: "text-red-500 ml-1"
                            }, "*")) : createCommentVNode("", true)
                          ]),
                          createVNode("div", { class: "text-sm text-surface-600 dark:text-surface-400" }, [
                            createTextVNode(toDisplayString(attribute.value) + toDisplayString(attribute.template?.unit || attribute.templateUnit ? `
                        ${$setup.getUnitLabel(
                              attribute.template?.unit || attribute.templateUnit
                            )}` : "") + " ", 1),
                            attribute.template?.group?.name || attribute.templateGroup ? (openBlock(), createBlock("span", {
                              key: 0,
                              class: "ml-2 px-2 py-1 bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 rounded text-xs"
                            }, toDisplayString(attribute.template?.group?.name || attribute.templateGroup), 1)) : createCommentVNode("", true)
                          ])
                        ]),
                        createVNode("div", { class: "text-xs text-surface-500 dark:text-surface-400" }, toDisplayString($setup.getDataTypeLabel(
                          attribute.template?.dataType || attribute.templateDataType
                        )), 1)
                      ]);
                    }), 128))
                  ])
                ])) : createCommentVNode("", true),
                createVNode("div", { class: "bg-surface-50 dark:bg-surface-900 rounded-lg p-6" }, [
                  createVNode("h4", { class: "font-medium text-surface-900 dark:text-surface-0 mb-4" }, " \u041A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u044B\u0435 \u043F\u043E\u0437\u0438\u0446\u0438\u0438 (" + toDisplayString($setup.formData.catalogItems.length) + "): ", 1),
                  createVNode("div", { class: "space-y-3" }, [
                    (openBlock(true), createBlock(Fragment, null, renderList($setup.formData.catalogItems, (item, index) => {
                      return openBlock(), createBlock("div", {
                        key: index,
                        class: "flex items-center justify-between p-3 bg-surface-0 dark:bg-surface-950 rounded border"
                      }, [
                        createVNode("div", { class: "flex-1" }, [
                          createVNode("div", { class: "flex items-center gap-2" }, [
                            createVNode("span", { class: "font-medium" }, toDisplayString(item.isExisting ? item.existingCatalogItem?.sku : item.sku), 1),
                            createVNode("span", { class: "text-surface-600 dark:text-surface-400" }, " (" + toDisplayString(item.isExisting ? item.existingCatalogItem?.brand?.name : item.selectedBrand?.name) + ") ", 1),
                            item.isExisting ? (openBlock(), createBlock("span", {
                              key: 0,
                              class: "px-2 py-1 bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded text-xs"
                            }, " \u0421\u0443\u0449\u0435\u0441\u0442\u0432\u0443\u044E\u0449\u0430\u044F ")) : (openBlock(), createBlock("span", {
                              key: 1,
                              class: "px-2 py-1 bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded text-xs"
                            }, " \u041D\u043E\u0432\u0430\u044F "))
                          ]),
                          createVNode("div", { class: "text-sm text-surface-600 dark:text-surface-400 mt-1" }, [
                            createTextVNode(" \u0422\u043E\u0447\u043D\u043E\u0441\u0442\u044C: " + toDisplayString($setup.getAccuracyLabel(item.accuracy)) + " ", 1),
                            item.notes ? (openBlock(), createBlock("span", {
                              key: 0,
                              class: "ml-2"
                            }, "\u2022 " + toDisplayString(item.notes), 1)) : createCommentVNode("", true)
                          ])
                        ])
                      ]);
                    }), 128))
                  ])
                ]),
                $setup.formData.equipmentApplicabilities.length > 0 ? (openBlock(), createBlock("div", {
                  key: 1,
                  class: "bg-surface-50 dark:bg-surface-900 rounded-lg p-6"
                }, [
                  createVNode("h4", { class: "font-medium text-surface-900 dark:text-surface-0 mb-4" }, " \u041F\u0440\u0438\u043C\u0435\u043D\u0438\u043C\u043E\u0441\u0442\u044C \u043A \u0442\u0435\u0445\u043D\u0438\u043A\u0435 (" + toDisplayString($setup.formData.equipmentApplicabilities.length) + "): ", 1),
                  createVNode("div", { class: "space-y-3" }, [
                    (openBlock(true), createBlock(Fragment, null, renderList($setup.formData.equipmentApplicabilities, (equipment, index) => {
                      return openBlock(), createBlock("div", {
                        key: index,
                        class: "flex items-center justify-between p-3 bg-surface-0 dark:bg-surface-950 rounded border"
                      }, [
                        createVNode("div", { class: "flex-1" }, [
                          createVNode("div", { class: "flex items-center gap-2" }, [
                            createVNode("span", { class: "font-medium" }, toDisplayString(equipment.isExisting ? equipment.existingEquipmentModel?.name : equipment.name), 1),
                            equipment.selectedBrand || equipment.existingEquipmentModel?.brand ? (openBlock(), createBlock("span", {
                              key: 0,
                              class: "text-surface-600 dark:text-surface-400"
                            }, " (" + toDisplayString(equipment.selectedBrand?.name || equipment.existingEquipmentModel?.brand?.name) + ") ", 1)) : createCommentVNode("", true),
                            equipment.isExisting ? (openBlock(), createBlock("span", {
                              key: 1,
                              class: "px-2 py-1 bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded text-xs"
                            }, " \u0421\u0443\u0449\u0435\u0441\u0442\u0432\u0443\u044E\u0449\u0430\u044F ")) : (openBlock(), createBlock("span", {
                              key: 2,
                              class: "px-2 py-1 bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded text-xs"
                            }, " \u041D\u043E\u0432\u0430\u044F "))
                          ]),
                          equipment.notes ? (openBlock(), createBlock("div", {
                            key: 0,
                            class: "text-sm text-surface-600 dark:text-surface-400 mt-1"
                          }, toDisplayString(equipment.notes), 1)) : createCommentVNode("", true)
                        ])
                      ]);
                    }), 128))
                  ])
                ])) : createCommentVNode("", true)
              ])) : createCommentVNode("", true)
            ]),
            createVNode("div", { class: "flex items-center justify-between mt-8 pt-6 border-t border-surface-200 dark:border-surface-700" }, [
              $setup.currentStep > 1 ? (openBlock(), createBlock($setup["VButton"], {
                key: 0,
                onClick: $setup.previousStep,
                severity: "secondary",
                outlined: ""
              }, {
                default: withCtx(() => [
                  createTextVNode(" \u041D\u0430\u0437\u0430\u0434 ")
                ]),
                _: 1
              })) : (openBlock(), createBlock("div", { key: 1 })),
              createVNode("div", { class: "flex gap-3" }, [
                $setup.currentStep < $setup.totalSteps ? (openBlock(), createBlock($setup["VButton"], {
                  key: 0,
                  onClick: $setup.nextStep,
                  disabled: !$setup.canProceed,
                  label: "\u0414\u0430\u043B\u0435\u0435",
                  outlined: ""
                }, null, 8, ["disabled"])) : (openBlock(), createBlock($setup["VButton"], {
                  key: 1,
                  label: $props.mode === "edit" ? "\u0421\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C \u0438\u0437\u043C\u0435\u043D\u0435\u043D\u0438\u044F" : "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u044C",
                  onClick: $setup.savePart,
                  loading: $setup.loading || $setup.saving,
                  disabled: !$setup.canFinish || $setup.saving
                }, null, 8, ["label", "loading", "disabled"]))
              ])
            ])
          ])
        ];
      }
    }),
    _: 1
  }, _parent));
  if ($setup.error) {
    _push(ssrRenderComponent($setup["VMessage"], {
      severity: "error",
      class: "mt-4"
    }, {
      default: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`${ssrInterpolate($setup.error)}`);
        } else {
          return [
            createTextVNode(toDisplayString($setup.error), 1)
          ];
        }
      }),
      _: 1
    }, _parent));
  } else {
    _push(`<!---->`);
  }
  if ($setup.saveError) {
    _push(ssrRenderComponent($setup["VMessage"], {
      severity: "error",
      class: "mt-4"
    }, {
      default: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`${ssrInterpolate($setup.saveError)}`);
        } else {
          return [
            createTextVNode(toDisplayString($setup.saveError), 1)
          ];
        }
      }),
      _: 1
    }, _parent));
  } else {
    _push(`<!---->`);
  }
  _push(ssrRenderComponent($setup["QuickCreateCategory"], {
    visible: $setup.showCreateCategory,
    "onUpdate:visible": ($event) => $setup.showCreateCategory = $event,
    onCreated: $setup.onCategoryCreated
  }, null, _parent));
  _push(ssrRenderComponent($setup["QuickCreateBrand"], {
    visible: $setup.showCreateBrand,
    "onUpdate:visible": ($event) => $setup.showCreateBrand = $event
  }, null, _parent));
  _push(`</div>`);
}
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/parts/PartWizard.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const PartWizard = /* @__PURE__ */ _export_sfc(_sfc_main, [["ssrRender", _sfc_ssrRender]]);

export { PartWizard as P };
