import { describe, it, expect, beforeEach, vi } from 'vitest'
import { CatalogStateManager } from '@/lib/catalog-state'

// Мокаем localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}

// Мокаем window
const windowMock = {
  localStorage: localStorageMock,
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  dispatchEvent: vi.fn(),
}

Object.defineProperty(global, 'window', {
  value: windowMock,
  writable: true,
})

describe('CatalogStateManager', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should return default filters when localStorage is empty', () => {
    localStorageMock.getItem.mockReturnValue(null)
    
    const filters = CatalogStateManager.getFilters()
    
    expect(filters).toEqual({
      query: "",
      categoryIds: [],
      brandIds: [],
      attributeFilters: {},
      accuracyLevels: [],
      isOemOnly: false,
    })
  })

  it('should save filters to localStorage', () => {
    const testFilters = {
      query: "test query",
      categoryIds: [1, 2],
      brandIds: [3],
      attributeFilters: {},
      accuracyLevels: [],
      isOemOnly: true,
    }

    CatalogStateManager.setFilters(testFilters)

    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      'catalog-search-filters',
      JSON.stringify(testFilters)
    )
    expect(windowMock.dispatchEvent).toHaveBeenCalled()
  })

  it('should update filters partially', () => {
    const initialFilters = {
      query: "",
      categoryIds: [],
      brandIds: [],
      attributeFilters: {},
      accuracyLevels: [],
      isOemOnly: false,
    }
    
    localStorageMock.getItem.mockReturnValue(JSON.stringify(initialFilters))

    CatalogStateManager.updateFilters({ query: "new query", isOemOnly: true })

    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      'catalog-search-filters',
      JSON.stringify({
        ...initialFilters,
        query: "new query",
        isOemOnly: true,
      })
    )
  })

  it('should clear filters', () => {
    CatalogStateManager.clearFilters()

    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      'catalog-search-filters',
      JSON.stringify({
        query: "",
        categoryIds: [],
        brandIds: [],
        attributeFilters: {},
        accuracyLevels: [],
        isOemOnly: false,
      })
    )
  })

  it('should handle localStorage errors gracefully', () => {
    localStorageMock.getItem.mockImplementation(() => {
      throw new Error('localStorage error')
    })

    const filters = CatalogStateManager.getFilters()

    expect(filters).toEqual({
      query: "",
      categoryIds: [],
      brandIds: [],
      attributeFilters: {},
      accuracyLevels: [],
      isOemOnly: false,
    })
  })
})
