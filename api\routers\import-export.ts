import { z } from 'zod'
import { router, expertProcedure } from '../trpc'
import { ExcelExportService } from '../services/excel/excel-export.service'

export const importExportRouter = router({
  exportXlsx: expertProcedure
    .input(z.object({
      include: z.object({
        Brand: z.boolean().optional(),
        PartCategory: z.boolean().optional(),
        AttributeGroup: z.boolean().optional(),
        AttributeTemplate: z.boolean().optional(),
        AttributeSynonymGroup: z.boolean().optional(),
        AttributeSynonym: z.boolean().optional(),
        Part: z.boolean().optional(),
        CatalogItem: z.boolean().optional(),
        EquipmentModel: z.boolean().optional(),
        PartAttribute: z.boolean().optional(),
        CatalogItemAttribute: z.boolean().optional(),
        EquipmentModelAttribute: z.boolean().optional(),
        PartApplicability: z.boolean().optional(),
        EquipmentApplicability: z.boolean().optional(),
      }),
      filters: z.object({
        brandSlugs: z.array(z.string()).optional(),
        partCategorySlugs: z.array(z.string()).optional(),
      }).optional(),
      meta: z.object({
        createMissingRefs: z.boolean().default(false),
        onConflict: z.enum(['upsert','update_only','skip','error']).default('upsert'),
        scope: z.object({
          brandSlug: z.string().optional(),
          categorySlug: z.string().optional(),
        }).optional(),
      }).optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      const service = new ExcelExportService(ctx.db)
      const { buffer, fileName } = await service.buildWorkbook(input)
      // Возвращаем как base64 чтобы было удобно скачать из cpanel
      return { fileName, base64: buffer.toString('base64') }
    }),

  exportTemplate: expertProcedure
    .input(z.object({
      include: z.object({
        Brand: z.boolean().optional(),
        PartCategory: z.boolean().optional(),
        AttributeGroup: z.boolean().optional(),
        AttributeTemplate: z.boolean().optional(),
        AttributeSynonymGroup: z.boolean().optional(),
        AttributeSynonym: z.boolean().optional(),
        Part: z.boolean().optional(),
        CatalogItem: z.boolean().optional(),
        EquipmentModel: z.boolean().optional(),
        PartAttribute: z.boolean().optional(),
        CatalogItemAttribute: z.boolean().optional(),
        EquipmentModelAttribute: z.boolean().optional(),
        PartApplicability: z.boolean().optional(),
        EquipmentApplicability: z.boolean().optional(),
      }).optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      const service = new ExcelExportService(ctx.db)
      const { buffer, fileName } = await service.buildTemplateWorkbook(input.include || {})
      return { fileName, base64: buffer.toString('base64') }
    }),
})

export type ImportExportRouter = typeof importExportRouter

