// @ts-check
import { defineConfig } from "astro/config";

import tailwindcss from "@tailwindcss/vite";
import react from "@astrojs/react";
import node from "@astrojs/node";

// https://astro.build/config
export default defineConfig({
  server: {
    port: 4323,
  },
  output: 'server',
  vite: {
    plugins: [tailwindcss()],
  },

  integrations: [react()],

  adapter: node({
    mode: "standalone",
  }),
});
