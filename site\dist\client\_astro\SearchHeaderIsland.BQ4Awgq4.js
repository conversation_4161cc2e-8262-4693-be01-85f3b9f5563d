import{j as e}from"./jsx-runtime.D_zvdyIk.js";import{B as d}from"./badge.CrhVNNKw.js";import{M as t,T as h}from"./TrpcBoundary.g7wmOpBw.js";import{M as m}from"./modern-input.Cd5D-6fr.js";import{c as n}from"./createLucideIcon.DdiNmGRb.js";import{S as x}from"./search.ooeMVQga.js";import{D as p}from"./download.D1J5CNLA.js";import{B as u}from"./bot.zF5v5Aoc.js";import{u as j}from"./useCatalogSearch.3hHK2Zsb.js";import{P as v}from"./package.CV5wAU3g.js";import"./index.0yr9KlQE.js";import"./index.3rXK4OIH.js";import"./utils.CBfrqCZ4.js";import"./index.BWNh2K4G.js";import"./trpc.9HS4D061.js";/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const y=[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]],f=n("bell",y);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const N=[["path",{d:"M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z",key:"yt0hxn"}],["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}]],g=n("bolt",N);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const w=[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]],b=n("calculator",w);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const k=[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]],M=n("save",k);function z({totalCount:r,filteredCount:l,searchQuery:s,onSearchChange:a,onOpenAI:i}){return e.jsx("header",{className:"sticky top-0 z-50 w-full border-b border-border/40 bg-background/80 backdrop-blur-xl",children:e.jsxs("div",{className:"container flex h-16 max-w-screen-2xl items-center justify-between px-6",children:[e.jsx("div",{className:"flex items-center gap-4",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"flex bg-zinc-900 h-10 w-10 items-center justify-center rounded-xl shadow-lg",children:e.jsx(g,{className:"h-5 w-5 text-primary-foreground"})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-xl font-bold tracking-tight",children:"Каталог"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Профессиональная система каталогизации"})]})]})}),e.jsx("div",{className:"flex flex-1 items-center justify-center px-8",children:e.jsxs("div",{className:"relative w-full max-w-lg",children:[e.jsx(x,{className:"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground"}),e.jsx(m,{placeholder:"Поиск по артикулу, описанию, бренду...",value:s,onChange:c=>a(c.target.value),className:"pl-10 pr-4",variant:"ghost"})]})}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs(d,{variant:"outline",className:"hidden sm:flex",children:[l," / ",r]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(t,{variant:"ghost",size:"icon",className:"relative",children:[e.jsx(f,{className:"h-4 w-4"}),e.jsx("span",{className:"absolute -top-1 -right-1 h-2 w-2 rounded-full bg-destructive"})]}),e.jsxs(t,{variant:"outline",size:"sm",children:[e.jsx(M,{className:"h-4 w-4"}),e.jsx("span",{className:"hidden sm:inline",children:"Сохранить"})]}),e.jsxs(t,{variant:"outline",size:"sm",children:[e.jsx(b,{className:"h-4 w-4"}),e.jsx("span",{className:"hidden sm:inline",children:"Калькулятор"})]}),e.jsxs(t,{variant:"outline",size:"sm",children:[e.jsx(p,{className:"h-4 w-4"}),e.jsx("span",{className:"hidden sm:inline",children:"Экспорт"})]}),e.jsxs(t,{variant:"gradient",size:"sm",onClick:i,className:"gap-2",children:[e.jsx(u,{className:"h-4 w-4"}),e.jsx("span",{className:"hidden sm:inline",children:"AI Помощник"})]})]})]})]})})}function L(){return e.jsx(h,{children:e.jsx(C,{})})}function C(){const{filters:r,updateFilters:l,totalCount:s,filteredCount:a}=j(),i=o=>{l({query:o})},c=()=>{const o=new CustomEvent("openAIAssistant");window.dispatchEvent(o)};return e.jsxs("div",{className:"bg-background border-b border-border/40",children:[e.jsx(z,{totalCount:s,filteredCount:a,searchQuery:r.query,onSearchChange:i,onOpenAI:c}),e.jsx("div",{className:"container max-w-none px-4 py-2",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(d,{variant:"secondary",className:"px-2 py-1 font-medium text-xs",children:[e.jsx(v,{className:"h-3 w-3 mr-1"}),a," позиций"]}),a!==s&&e.jsxs(d,{variant:"outline",className:"px-2 py-1 text-xs",children:["из ",s," общих"]})]})})]})}export{L as default};
