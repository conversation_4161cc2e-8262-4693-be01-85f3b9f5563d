import{u as w}from"./index.CzaMXvxd.js";const L=()=>{let e=null;try{e=w()}catch{e=null}const i=o=>{typeof window<"u"&&window.dispatchEvent(new CustomEvent("app:toast",{detail:o}))},n=o=>{const r={severity:o.severity||"info",summary:o.summary,detail:o.detail,life:o.life||5e3,closable:o.closable!==!1,group:o.group};e&&typeof e.add=="function"?e.add(r):i(r)},c=(o,r,s)=>{n({severity:"success",summary:o,detail:r,life:s})},l=(o,r,s)=>{n({severity:"info",summary:o,detail:r,life:s})},a=(o,r,s)=>{n({severity:"warn",summary:o,detail:r,life:s})},t=(o,r,s)=>{n({severity:"error",summary:o,detail:r,life:s||8e3})};return{show:n,success:c,info:l,warn:a,error:t,clear:o=>{e.removeAllGroups()},remove:o=>{e.remove(o)},showSaveSuccess:(o="Запись")=>{c("Сохранено",`${o} успешно сохранена`)},showDeleteSuccess:(o="Запись")=>{c("Удалено",`${o} успешно удалена`)},showSaveError:(o="Запись",r)=>{t("Ошибка сохранения",r||`Не удалось сохранить ${o.toLowerCase()}`)},showDeleteError:(o="Запись",r)=>{t("Ошибка удаления",r||`Не удалось удалить ${o.toLowerCase()}`)},showLoadError:(o="Данные",r)=>{t("Ошибка загрузки",r||`Не удалось загрузить ${o.toLowerCase()}`)},showValidationError:(o="Проверьте правильность заполнения полей")=>{a("Ошибка валидации",o)},showNetworkError:()=>{t("Ошибка сети","Проверьте подключение к интернету и попробуйте снова")},showUnauthorizedError:()=>{t("Нет доступа","У вас недостаточно прав для выполнения этого действия")}}};export{L as u};
