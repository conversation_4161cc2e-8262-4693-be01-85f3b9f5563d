import { defineComponent, useSSRContext, ref, computed, onMounted, watch, mergeProps, withCtx, createVNode, toDisplayString, createTextVNode, createBlock, createCommentVNode, openBlock, Fragment, renderList, withDirectives, vModelRadio } from 'vue';
import { u as useTrpc } from './useTrpc_B-zNiBto.mjs';
import { V as VCard } from './Card_BWxK5e93.mjs';
import { V as VButton } from './Button_CuwpNmer.mjs';
import { V as VAutoComplete } from './AutoComplete_CPZ3fUb-.mjs';
import { T as Tag } from './Tag_DnSNoHBb.mjs';
import { D as Dialog } from './Dialog_g9jHgXMZ.mjs';
import { A as AttributeValueInput } from './AttributeValueInput_tN0JsC7m.mjs';
import { I as Icon } from './AdminLayout_b20tykPC.mjs';
import { D as DangerButton } from './DangerButton_CkG9WtM3.mjs';
import { PencilIcon, TrashIcon } from 'lucide-vue-next';
import { ssrRenderAttrs, ssrInterpolate, ssrRenderComponent, ssrRenderList, ssrRenderClass, ssrRenderAttr, ssrIncludeBooleanAttr, ssrLooseEqual } from 'vue/server-renderer';
import { _ as _export_sfc } from './ClientRouter_B8Zzhk9G.mjs';
import { I as InputText } from './InputText_CtReD0EA.mjs';
import { M as Message } from './Message_aZfC-dtu.mjs';

const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "AttributeManager",
  props: {
    modelValue: {},
    entityId: {},
    title: { default: "\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0438" },
    description: { default: "" },
    showGroupSelector: { type: Boolean, default: true },
    groupByTemplate: { type: Boolean, default: false },
    cardMode: { default: "detailed" }
  },
  emits: ["update:modelValue"],
  setup(__props, { expose: __expose, emit: __emit }) {
    __expose();
    const props = __props;
    const emit = __emit;
    const { client, partAttributes, attributeTemplates } = useTrpc();
    const selectedTemplateGroup = ref(null);
    const selectedTemplate = ref(null);
    const groupSuggestions = ref([]);
    const templateSuggestions = ref([]);
    const loadingTemplates = ref(false);
    const showAddDialog = ref(false);
    const showGroupDialog = ref(false);
    const editingAttribute = ref(null);
    const newAttributeValue = ref("");
    const valueInputRef = ref(null);
    const templateGroups = ref([]);
    const modelValue = computed({
      get: () => props.modelValue,
      set: (value) => emit("update:modelValue", value)
    });
    const filledAttributesCount = computed(() => {
      return modelValue.value.filter((attr) => attr.value && String(attr.value).trim()).length;
    });
    const groupedAttributes = computed(() => {
      if (!props.groupByTemplate) return {};
      return modelValue.value.reduce((groups, attribute) => {
        const groupName = attribute.template?.group?.name || attribute.templateGroup || "undefined";
        if (!groups[groupName]) {
          groups[groupName] = [];
        }
        groups[groupName].push(attribute);
        return groups;
      }, {});
    });
    const canSave = computed(() => {
      if (!selectedTemplate.value && !editingAttribute.value) return false;
      const template = selectedTemplate.value || editingAttribute.value?.template;
      if (!template) return false;
      const value = newAttributeValue.value;
      if (template.dataType === "BOOLEAN") {
        return value !== null && value !== void 0;
      }
      if (value === null || value === void 0 || value === "") return false;
      if (template.dataType === "STRING" && value.toString().trim() === "") return false;
      return true;
    });
    const updateAttributeValue = (index, value) => {
      const newValue = [...props.modelValue];
      newValue[index].value = value;
      emit("update:modelValue", newValue);
    };
    const removeAttribute = async (index) => {
      const attribute = props.modelValue[index];
      if (attribute.id) {
        try {
          const result = await partAttributes.delete({ id: attribute.id });
          console.log("\u0410\u0442\u0440\u0438\u0431\u0443\u0442 \u0443\u0441\u043F\u0435\u0448\u043D\u043E \u0443\u0434\u0430\u043B\u0435\u043D \u0438\u0437 \u0411\u0414:", attribute.id, result);
        } catch (error) {
          console.error("\u041E\u0448\u0438\u0431\u043A\u0430 \u0443\u0434\u0430\u043B\u0435\u043D\u0438\u044F \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u0430:", error);
          return;
        }
      }
      const newValue = [...props.modelValue];
      newValue.splice(index, 1);
      emit("update:modelValue", newValue);
    };
    const getTemplateForInput = (attribute) => {
      if (!attribute) {
        return {
          id: 0,
          name: "",
          title: "",
          description: "",
          dataType: "STRING",
          unit: void 0,
          isRequired: false,
          minValue: void 0,
          maxValue: void 0,
          allowedValues: []
        };
      }
      return {
        id: attribute.templateId,
        name: attribute.template?.name || "",
        title: attribute.templateTitle || "",
        description: attribute.templateDescription || "",
        dataType: attribute.templateDataType || "STRING",
        unit: attribute.templateUnit,
        isRequired: attribute.template?.isRequired || false,
        minValue: attribute.template?.minValue,
        maxValue: attribute.template?.maxValue,
        allowedValues: attribute.template?.allowedValues || [],
        group: attribute.template?.group
      };
    };
    const addSingleTemplate = () => {
      if (!selectedTemplate.value) return;
      const template = selectedTemplate.value;
      const attribute = {
        templateId: template.id,
        value: "",
        template,
        templateTitle: template.title,
        templateDataType: template.dataType,
        templateUnit: template.unit,
        templateGroup: template.group?.name,
        templateDescription: template.description
      };
      emit("update:modelValue", [...props.modelValue, attribute]);
      selectedTemplate.value = null;
    };
    const filterGroups = async (event) => {
      const query = event.query.toLowerCase();
      try {
        const groups = await attributeTemplates.findAllGroups();
        if (groups && Array.isArray(groups)) {
          const filteredGroups = groups.filter(
            (group) => group.name.toLowerCase().includes(query)
          ).slice(0, 10);
          groupSuggestions.value = filteredGroups;
        }
      } catch (error) {
        console.error("\u041E\u0448\u0438\u0431\u043A\u0430 \u043F\u043E\u0438\u0441\u043A\u0430 \u0433\u0440\u0443\u043F\u043F:", error);
        groupSuggestions.value = [];
      }
    };
    const loadSelectedGroupTemplates = async () => {
      if (!selectedTemplateGroup.value) return;
      loadingTemplates.value = true;
      try {
        const groupId = selectedTemplateGroup.value.id || selectedTemplateGroup.value;
        const result = await attributeTemplates.findMany({ groupId });
        if (result && typeof result === "object" && result.templates) {
          const templates = result.templates;
          const newAttributes = templates.map((template) => ({
            templateId: template.id,
            value: "",
            template,
            templateTitle: template.title,
            templateDataType: template.dataType,
            templateUnit: template.unit,
            templateGroup: template.group?.name,
            templateDescription: template.description
          }));
          emit("update:modelValue", [...props.modelValue, ...newAttributes]);
          selectedTemplateGroup.value = null;
        }
      } catch (error) {
        console.error("\u041E\u0448\u0438\u0431\u043A\u0430 \u0437\u0430\u0433\u0440\u0443\u0437\u043A\u0438 \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u0432:", error);
      } finally {
        loadingTemplates.value = false;
      }
    };
    const filterTemplates = async (event) => {
      const query = event.query.toLowerCase();
      try {
        const result = await attributeTemplates.findMany({
          search: query,
          limit: 10
        });
        if (result && Array.isArray(result.templates)) {
          templateSuggestions.value = result.templates;
        } else {
          templateSuggestions.value = [];
        }
      } catch (error) {
        console.error("\u041E\u0448\u0438\u0431\u043A\u0430 \u043F\u043E\u0438\u0441\u043A\u0430 \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u0432:", error);
        templateSuggestions.value = [];
      }
    };
    const getDataTypeIcon = (dataType) => {
      const icons = {
        STRING: "pi pi-font",
        NUMBER: "pi pi-hashtag",
        BOOLEAN: "pi pi-check-square",
        DATE: "pi pi-calendar",
        JSON: "pi pi-code"
      };
      return icons[dataType || ""] || "pi pi-question";
    };
    const getDataTypeLabel = (dataType) => {
      if (!dataType) return "";
      const labels = {
        STRING: "\u0421\u0442\u0440\u043E\u043A\u0430",
        NUMBER: "\u0427\u0438\u0441\u043B\u043E",
        BOOLEAN: "\u041B\u043E\u0433\u0438\u0447\u0435\u0441\u043A\u043E\u0435",
        DATE: "\u0414\u0430\u0442\u0430",
        JSON: "JSON"
      };
      return labels[dataType] || dataType;
    };
    const getUnitLabel = (unit) => {
      if (!unit) return "";
      const labels = {
        MM: "\u043C\u043C",
        INCH: "\u0434\u044E\u0439\u043C\u044B",
        FT: "\u0444\u0443\u0442\u044B",
        G: "\u0433",
        KG: "\u043A\u0433",
        T: "\u0442",
        LB: "\u0444\u0443\u043D\u0442\u044B",
        ML: "\u043C\u043B",
        L: "\u043B",
        GAL: "\u0433\u0430\u043B\u043B\u043E\u043D\u044B",
        PCS: "\u0448\u0442",
        SET: "\u043A\u043E\u043C\u043F\u043B\u0435\u043A\u0442",
        PAIR: "\u043F\u0430\u0440\u0430",
        BAR: "\u0431\u0430\u0440",
        PSI: "PSI",
        KW: "\u043A\u0412\u0442",
        HP: "\u043B.\u0441.",
        NM: "\u041D\u22C5\u043C",
        RPM: "\u043E\u0431/\u043C\u0438\u043D",
        C: "\xB0C",
        F: "\xB0F",
        PERCENT: "%"
      };
      return labels[unit] || unit;
    };
    const getPlaceholder = (attribute) => {
      const dataType = attribute.templateDataType;
      const unit = attribute.templateUnit;
      switch (dataType) {
        case "STRING":
          return "\u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u0442\u0435\u043A\u0441\u0442...";
        case "NUMBER":
          return unit ? `\u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u0447\u0438\u0441\u043B\u043E (${getUnitLabel(unit)})...` : "\u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u0447\u0438\u0441\u043B\u043E...";
        case "BOOLEAN":
          return "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0435...";
        case "DATE":
          return "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0434\u0430\u0442\u0443...";
        default:
          return "\u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0435...";
      }
    };
    const editAttribute = (attribute) => {
      editingAttribute.value = attribute;
      newAttributeValue.value = attribute.value || "";
      showAddDialog.value = true;
    };
    const closeAddDialog = () => {
      showAddDialog.value = false;
      selectedTemplate.value = null;
      newAttributeValue.value = "";
      editingAttribute.value = null;
    };
    const handleValueChange = (value) => {
      newAttributeValue.value = value;
    };
    const getAttributeIndex = (attribute) => {
      return props.modelValue.findIndex((attr) => {
        if (attr.id && attribute.id) {
          return attr.id === attribute.id;
        }
        return attr.templateId === attribute.templateId;
      });
    };
    const removeAttributeByObject = async (attribute) => {
      const index = getAttributeIndex(attribute);
      if (index !== -1) {
        await removeAttribute(index);
      }
    };
    const saveAttribute = async () => {
      const template = selectedTemplate.value || editingAttribute.value?.template;
      if (!template) return;
      const value = newAttributeValue.value;
      if (template.dataType === "BOOLEAN") {
        if (value === null || value === void 0) return;
      } else {
        if (value === null || value === void 0 || value === "") return;
        if (template.dataType === "STRING" && value.toString().trim() === "") return;
      }
      if (valueInputRef.value && !valueInputRef.value.validate()) {
        return;
      }
      if (editingAttribute.value) {
        if (editingAttribute.value.id && props.entityId) {
          try {
            await partAttributes.update({
              id: editingAttribute.value.id,
              value: newAttributeValue.value
            });
          } catch (error) {
            console.error("\u041E\u0448\u0438\u0431\u043A\u0430 \u043E\u0431\u043D\u043E\u0432\u043B\u0435\u043D\u0438\u044F \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u0430:", error);
            return;
          }
        }
        editingAttribute.value.value = newAttributeValue.value;
      } else if (selectedTemplate.value) {
        const templateId = typeof selectedTemplate.value === "object" ? selectedTemplate.value.id : selectedTemplate.value;
        const existingAttribute = modelValue.value.find(
          (attr) => attr.templateId === templateId
        );
        if (existingAttribute) {
          if (existingAttribute.id && props.entityId) {
            try {
              await partAttributes.update({
                id: existingAttribute.id,
                value: newAttributeValue.value
              });
            } catch (error) {
              console.error("\u041E\u0448\u0438\u0431\u043A\u0430 \u043E\u0431\u043D\u043E\u0432\u043B\u0435\u043D\u0438\u044F \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u0430:", error);
              return;
            }
          }
          existingAttribute.value = newAttributeValue.value;
        } else {
          const newAttribute = {
            templateId,
            value: newAttributeValue.value,
            template: selectedTemplate.value
          };
          if (props.entityId) {
            try {
              const savedAttribute = await partAttributes.create({
                partId: props.entityId,
                templateId,
                value: newAttributeValue.value
              });
              if (savedAttribute && typeof savedAttribute === "object" && "id" in savedAttribute) {
                newAttribute.id = savedAttribute.id;
              }
            } catch (error) {
              console.error("\u041E\u0448\u0438\u0431\u043A\u0430 \u0441\u043E\u0437\u0434\u0430\u043D\u0438\u044F \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u0430:", error);
              return;
            }
          }
          emit("update:modelValue", [...props.modelValue, newAttribute]);
        }
      }
      closeAddDialog();
    };
    const loadTemplateGroups = async () => {
      try {
        const groups = await attributeTemplates.findAllGroups();
        if (groups && Array.isArray(groups)) {
          groupSuggestions.value = groups;
          templateGroups.value = groups;
        }
      } catch (error) {
        console.error("\u041E\u0448\u0438\u0431\u043A\u0430 \u0437\u0430\u0433\u0440\u0443\u0437\u043A\u0438 \u0433\u0440\u0443\u043F\u043F \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u0432:", error);
      }
    };
    const loadExistingAttributes = async () => {
      if (!props.entityId) return;
      try {
        const existingAttributes = await partAttributes.findByPartId({ partId: props.entityId });
        if (existingAttributes && Array.isArray(existingAttributes)) {
          const formattedAttributes = existingAttributes.map((attr) => ({
            id: attr.id,
            templateId: attr.templateId,
            value: attr.value,
            template: attr.template,
            templateTitle: attr.template?.title,
            templateDataType: attr.template?.dataType,
            templateUnit: attr.template?.unit,
            templateGroup: attr.template?.group?.name,
            templateDescription: attr.template?.description
          }));
          const existingTemplateIds = new Set(props.modelValue.map((a) => a.templateId));
          const newAttributes = formattedAttributes.filter((attr) => !existingTemplateIds.has(attr.templateId));
          emit("update:modelValue", [...props.modelValue, ...newAttributes]);
        }
      } catch (error) {
        console.error("\u041E\u0448\u0438\u0431\u043A\u0430 \u0437\u0430\u0433\u0440\u0443\u0437\u043A\u0438 \u0441\u0443\u0449\u0435\u0441\u0442\u0432\u0443\u044E\u0449\u0438\u0445 \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432:", error);
      }
    };
    const loadTemplatesByGroupId = async (groupId) => {
      try {
        loadingTemplates.value = true;
        const result = await attributeTemplates.findMany({
          groupId,
          limit: 100
        });
        if (result && typeof result === "object") {
          const templates = result.templates || [];
          const newAttributes = templates.filter((template) => !props.modelValue.some((attr) => attr.templateId === template.id)).map((template) => ({
            templateId: template.id,
            value: "",
            template,
            templateTitle: template.title,
            templateDataType: template.dataType,
            templateUnit: template.unit,
            templateGroup: template.group?.name,
            templateDescription: template.description
          }));
          emit("update:modelValue", [...props.modelValue, ...newAttributes]);
          console.log(`\u0417\u0430\u0433\u0440\u0443\u0436\u0435\u043D\u043E ${newAttributes.length} \u043D\u043E\u0432\u044B\u0445 \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u0432 \u0438\u0437 \u0433\u0440\u0443\u043F\u043F\u044B`);
          showGroupDialog.value = false;
        }
      } catch (error) {
        console.error("\u041E\u0448\u0438\u0431\u043A\u0430 \u0437\u0430\u0433\u0440\u0443\u0437\u043A\u0438 \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u0432 \u0433\u0440\u0443\u043F\u043F\u044B:", error);
      } finally {
        loadingTemplates.value = false;
      }
    };
    onMounted(() => {
      loadTemplateGroups();
      loadExistingAttributes();
    });
    watch(() => props.entityId, () => {
      if (props.entityId) {
        loadExistingAttributes();
      }
    });
    watch(showAddDialog, (newValue) => {
      if (!newValue) {
        selectedTemplate.value = null;
        newAttributeValue.value = "";
        editingAttribute.value = null;
      }
    });
    watch(newAttributeValue, (newValue) => {
    });
    const __returned__ = { props, emit, client, partAttributes, attributeTemplates, selectedTemplateGroup, selectedTemplate, groupSuggestions, templateSuggestions, loadingTemplates, showAddDialog, showGroupDialog, editingAttribute, newAttributeValue, valueInputRef, templateGroups, modelValue, filledAttributesCount, groupedAttributes, canSave, updateAttributeValue, removeAttribute, getTemplateForInput, addSingleTemplate, filterGroups, loadSelectedGroupTemplates, filterTemplates, getDataTypeIcon, getDataTypeLabel, getUnitLabel, getPlaceholder, editAttribute, closeAddDialog, handleValueChange, getAttributeIndex, removeAttributeByObject, saveAttribute, loadTemplateGroups, loadExistingAttributes, loadTemplatesByGroupId, VCard, VButton, VAutoComplete, VTag: Tag, VDialog: Dialog, AttributeValueInput, Icon, DangerButton, get TrashIcon() {
      return TrashIcon;
    }, get PencilIcon() {
      return PencilIcon;
    } };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$1(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "simple-attribute-manager" }, _attrs))}><div class="flex items-center justify-between mb-4"><div class="flex items-center gap-3"><h3 class="text-lg font-medium text-surface-900 dark:text-surface-0">${ssrInterpolate($props.title)}</h3>`);
  if ($setup.modelValue.length > 0) {
    _push(ssrRenderComponent($setup["VTag"], {
      value: `${$setup.filledAttributesCount}/${$setup.modelValue.length} \u0437\u0430\u043F\u043E\u043B\u043D\u0435\u043D\u043E`,
      severity: $setup.filledAttributesCount === $setup.modelValue.length ? "success" : "warn",
      size: "small"
    }, null, _parent));
  } else {
    _push(`<!---->`);
  }
  _push(`</div><div class="flex gap-2">`);
  if ($props.showGroupSelector) {
    _push(ssrRenderComponent($setup["VButton"], {
      onClick: ($event) => $setup.showGroupDialog = true,
      severity: "secondary",
      outlined: "",
      size: "small",
      label: "\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0433\u0440\u0443\u043F\u043F\u0443"
    }, {
      icon: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(ssrRenderComponent($setup["Icon"], {
            name: "pi pi-tags",
            class: "w-5 h-5"
          }, null, _parent2, _scopeId));
        } else {
          return [
            createVNode($setup["Icon"], {
              name: "pi pi-tags",
              class: "w-5 h-5"
            })
          ];
        }
      }),
      _: 1
    }, _parent));
  } else {
    _push(`<!---->`);
  }
  _push(ssrRenderComponent($setup["VButton"], {
    onClick: ($event) => $setup.showAddDialog = true,
    size: "small",
    outlined: "",
    label: "\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0430\u0442\u0440\u0438\u0431\u0443\u0442"
  }, null, _parent));
  _push(`</div></div>`);
  _push(ssrRenderComponent($setup["VCard"], { class: "mb-4" }, {
    content: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="p-4"${_scopeId}><div class="grid grid-cols-1 md:grid-cols-3 gap-4"${_scopeId}><div${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}> \u0413\u0440\u0443\u043F\u043F\u0430 \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u0432 </label>`);
        _push2(ssrRenderComponent($setup["VAutoComplete"], {
          modelValue: $setup.selectedTemplateGroup,
          "onUpdate:modelValue": ($event) => $setup.selectedTemplateGroup = $event,
          suggestions: $setup.groupSuggestions,
          onComplete: $setup.filterGroups,
          "option-label": "name",
          placeholder: "\u041F\u043E\u0438\u0441\u043A \u0433\u0440\u0443\u043F\u043F\u044B...",
          class: "w-full",
          dropdown: ""
        }, null, _parent2, _scopeId));
        _push2(`</div><div${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}> \u0418\u043B\u0438 \u043E\u0442\u0434\u0435\u043B\u044C\u043D\u044B\u0439 \u0448\u0430\u0431\u043B\u043E\u043D </label>`);
        _push2(ssrRenderComponent($setup["VAutoComplete"], {
          modelValue: $setup.selectedTemplate,
          "onUpdate:modelValue": ($event) => $setup.selectedTemplate = $event,
          suggestions: $setup.templateSuggestions,
          onComplete: $setup.filterTemplates,
          "option-label": "title",
          placeholder: "\u041F\u043E\u0438\u0441\u043A \u0448\u0430\u0431\u043B\u043E\u043D\u0430...",
          class: "w-full",
          dropdown: ""
        }, {
          option: withCtx(({ option }, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(`<div class="flex items-center gap-2"${_scopeId2}>`);
              _push3(ssrRenderComponent($setup["Icon"], {
                name: $setup.getDataTypeIcon(option.dataType),
                class: "text-primary w-4 h-4"
              }, null, _parent3, _scopeId2));
              _push3(`<div class="flex-1"${_scopeId2}><div class="font-medium"${_scopeId2}>${ssrInterpolate(option.title)}</div><div class="text-sm text-surface-600"${_scopeId2}>${ssrInterpolate(option.group?.name)} \u2022 ${ssrInterpolate($setup.getDataTypeLabel(option.dataType))}</div></div></div>`);
            } else {
              return [
                createVNode("div", { class: "flex items-center gap-2" }, [
                  createVNode($setup["Icon"], {
                    name: $setup.getDataTypeIcon(option.dataType),
                    class: "text-primary w-4 h-4"
                  }, null, 8, ["name"]),
                  createVNode("div", { class: "flex-1" }, [
                    createVNode("div", { class: "font-medium" }, toDisplayString(option.title), 1),
                    createVNode("div", { class: "text-sm text-surface-600" }, toDisplayString(option.group?.name) + " \u2022 " + toDisplayString($setup.getDataTypeLabel(option.dataType)), 1)
                  ])
                ])
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(`</div><div class="flex items-end gap-2"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["VButton"], {
          onClick: $setup.loadSelectedGroupTemplates,
          size: "small",
          outlined: "",
          disabled: !$setup.selectedTemplateGroup || $setup.loadingTemplates,
          loading: $setup.loadingTemplates,
          label: "\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0433\u0440\u0443\u043F\u043F\u0443",
          class: "flex-1"
        }, null, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["VButton"], {
          onClick: $setup.addSingleTemplate,
          size: "small",
          outlined: "",
          disabled: !$setup.selectedTemplate,
          label: "\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C",
          class: "flex-1"
        }, null, _parent2, _scopeId));
        _push2(`</div></div></div>`);
      } else {
        return [
          createVNode("div", { class: "p-4" }, [
            createVNode("div", { class: "grid grid-cols-1 md:grid-cols-3 gap-4" }, [
              createVNode("div", null, [
                createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u0413\u0440\u0443\u043F\u043F\u0430 \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u0432 "),
                createVNode($setup["VAutoComplete"], {
                  modelValue: $setup.selectedTemplateGroup,
                  "onUpdate:modelValue": ($event) => $setup.selectedTemplateGroup = $event,
                  suggestions: $setup.groupSuggestions,
                  onComplete: $setup.filterGroups,
                  "option-label": "name",
                  placeholder: "\u041F\u043E\u0438\u0441\u043A \u0433\u0440\u0443\u043F\u043F\u044B...",
                  class: "w-full",
                  dropdown: ""
                }, null, 8, ["modelValue", "onUpdate:modelValue", "suggestions"])
              ]),
              createVNode("div", null, [
                createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u0418\u043B\u0438 \u043E\u0442\u0434\u0435\u043B\u044C\u043D\u044B\u0439 \u0448\u0430\u0431\u043B\u043E\u043D "),
                createVNode($setup["VAutoComplete"], {
                  modelValue: $setup.selectedTemplate,
                  "onUpdate:modelValue": ($event) => $setup.selectedTemplate = $event,
                  suggestions: $setup.templateSuggestions,
                  onComplete: $setup.filterTemplates,
                  "option-label": "title",
                  placeholder: "\u041F\u043E\u0438\u0441\u043A \u0448\u0430\u0431\u043B\u043E\u043D\u0430...",
                  class: "w-full",
                  dropdown: ""
                }, {
                  option: withCtx(({ option }) => [
                    createVNode("div", { class: "flex items-center gap-2" }, [
                      createVNode($setup["Icon"], {
                        name: $setup.getDataTypeIcon(option.dataType),
                        class: "text-primary w-4 h-4"
                      }, null, 8, ["name"]),
                      createVNode("div", { class: "flex-1" }, [
                        createVNode("div", { class: "font-medium" }, toDisplayString(option.title), 1),
                        createVNode("div", { class: "text-sm text-surface-600" }, toDisplayString(option.group?.name) + " \u2022 " + toDisplayString($setup.getDataTypeLabel(option.dataType)), 1)
                      ])
                    ])
                  ]),
                  _: 1
                }, 8, ["modelValue", "onUpdate:modelValue", "suggestions"])
              ]),
              createVNode("div", { class: "flex items-end gap-2" }, [
                createVNode($setup["VButton"], {
                  onClick: $setup.loadSelectedGroupTemplates,
                  size: "small",
                  outlined: "",
                  disabled: !$setup.selectedTemplateGroup || $setup.loadingTemplates,
                  loading: $setup.loadingTemplates,
                  label: "\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0433\u0440\u0443\u043F\u043F\u0443",
                  class: "flex-1"
                }, null, 8, ["disabled", "loading"]),
                createVNode($setup["VButton"], {
                  onClick: $setup.addSingleTemplate,
                  size: "small",
                  outlined: "",
                  disabled: !$setup.selectedTemplate,
                  label: "\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C",
                  class: "flex-1"
                }, null, 8, ["disabled"])
              ])
            ])
          ])
        ];
      }
    }),
    _: 1
  }, _parent));
  if ($setup.modelValue.length > 0) {
    _push(`<div class="space-y-4">`);
    if ($props.groupByTemplate) {
      _push(`<!--[-->`);
      ssrRenderList($setup.groupedAttributes, (group, groupName) => {
        _push(`<div class="space-y-3"><div class="flex items-center gap-2 mb-3">`);
        _push(ssrRenderComponent($setup["Icon"], {
          name: "pi pi-folder",
          class: "text-blue-600 w-4 h-4"
        }, null, _parent));
        _push(`<h4 class="font-medium text-surface-900 dark:text-surface-0">${ssrInterpolate(groupName === "undefined" ? "\u0411\u0435\u0437 \u0433\u0440\u0443\u043F\u043F\u044B" : groupName)}</h4>`);
        _push(ssrRenderComponent($setup["VTag"], {
          value: `${group.length} \u0430\u0442\u0440.`,
          severity: "secondary",
          size: "small"
        }, null, _parent));
        _push(`</div><div class="space-y-3 ml-6"><!--[-->`);
        ssrRenderList(group, (attribute, index) => {
          _push(`<div><div class="${ssrRenderClass([[
            attribute.value ? "border-green-200 dark:border-green-700 bg-green-50 dark:bg-green-900/10" : "border-orange-200 dark:border-orange-700 bg-orange-50 dark:bg-orange-900/10"
          ], "flex items-center gap-3 p-4 border rounded-lg transition-all duration-200 hover:shadow-sm"])}"><div class="flex-shrink-0 relative">`);
          _push(ssrRenderComponent($setup["Icon"], {
            name: $setup.getDataTypeIcon(attribute.templateDataType),
            class: "text-lg text-primary w-5 h-5"
          }, null, _parent));
          _push(`<div class="${ssrRenderClass([attribute.value ? "bg-green-500" : "bg-orange-500", "absolute -top-1 -right-1 w-3 h-3 rounded-full border-2 border-white dark:border-surface-900"])}"${ssrRenderAttr("title", attribute.value ? "\u0417\u0430\u043F\u043E\u043B\u043D\u0435\u043D\u043E" : "\u041D\u0435 \u0437\u0430\u043F\u043E\u043B\u043D\u0435\u043D\u043E")}></div></div><div class="flex-shrink-0 w-48"><div class="font-medium text-surface-900 dark:text-surface-0 text-sm">${ssrInterpolate(attribute.templateTitle || "\u0411\u0435\u0437 \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u044F")}</div>`);
          if (attribute.templateGroup) {
            _push(ssrRenderComponent($setup["VTag"], {
              value: attribute.templateGroup,
              severity: "secondary",
              size: "small",
              class: "mt-1"
            }, null, _parent));
          } else {
            _push(`<!---->`);
          }
          _push(`</div><div class="flex-1">`);
          _push(ssrRenderComponent($setup["AttributeValueInput"], {
            template: $setup.getTemplateForInput(attribute),
            "model-value": attribute.value,
            "onUpdate:modelValue": ($event) => $setup.updateAttributeValue($setup.getAttributeIndex(attribute), $event),
            class: "w-full",
            placeholder: $setup.getPlaceholder(attribute)
          }, null, _parent));
          _push(`</div><div class="flex-shrink-0 w-16 text-center">`);
          if (attribute.templateUnit) {
            _push(`<span class="text-sm text-surface-500 font-medium">${ssrInterpolate($setup.getUnitLabel(attribute.templateUnit))}</span>`);
          } else {
            _push(`<!---->`);
          }
          _push(`</div><div class="flex-shrink-0 flex gap-2">`);
          _push(ssrRenderComponent($setup["VButton"], {
            onClick: ($event) => $setup.editAttribute(attribute),
            size: "small",
            outlined: ""
          }, {
            default: withCtx((_, _push2, _parent2, _scopeId) => {
              if (_push2) {
                _push2(ssrRenderComponent($setup["PencilIcon"], { class: "h-4 w-4" }, null, _parent2, _scopeId));
              } else {
                return [
                  createVNode($setup["PencilIcon"], { class: "h-4 w-4" })
                ];
              }
            }),
            _: 2
          }, _parent));
          _push(ssrRenderComponent($setup["DangerButton"], {
            onClick: ($event) => $setup.removeAttributeByObject(attribute),
            size: "small",
            outlined: ""
          }, {
            default: withCtx((_, _push2, _parent2, _scopeId) => {
              if (_push2) {
                _push2(ssrRenderComponent($setup["TrashIcon"], { class: "h-4 w-4" }, null, _parent2, _scopeId));
              } else {
                return [
                  createVNode($setup["TrashIcon"], { class: "h-4 w-4" })
                ];
              }
            }),
            _: 2
          }, _parent));
          _push(`</div></div></div>`);
        });
        _push(`<!--]--></div></div>`);
      });
      _push(`<!--]-->`);
    } else {
      _push(`<div class="space-y-3"><!--[-->`);
      ssrRenderList($setup.modelValue, (attribute, index) => {
        _push(`<div><div class="${ssrRenderClass([[
          attribute.value ? "border-green-200 dark:border-green-700 bg-green-50 dark:bg-green-900/10" : "border-orange-200 dark:border-orange-700 bg-orange-50 dark:bg-orange-900/10"
        ], "flex items-center gap-3 p-4 border rounded-lg transition-all duration-200 hover:shadow-sm"])}"><div class="flex-shrink-0 relative">`);
        _push(ssrRenderComponent($setup["Icon"], {
          name: $setup.getDataTypeIcon(attribute.templateDataType),
          class: "text-lg text-primary w-5 h-5"
        }, null, _parent));
        _push(`<div class="${ssrRenderClass([attribute.value ? "bg-green-500" : "bg-orange-500", "absolute -top-1 -right-1 w-3 h-3 rounded-full border-2 border-white dark:border-surface-900"])}"${ssrRenderAttr("title", attribute.value ? "\u0417\u0430\u043F\u043E\u043B\u043D\u0435\u043D\u043E" : "\u041D\u0435 \u0437\u0430\u043F\u043E\u043B\u043D\u0435\u043D\u043E")}></div></div><div class="flex-shrink-0 w-48"><div class="font-medium text-surface-900 dark:text-surface-0 text-sm">${ssrInterpolate(attribute.templateTitle || "\u0411\u0435\u0437 \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u044F")}</div>`);
        if (attribute.templateGroup) {
          _push(ssrRenderComponent($setup["VTag"], {
            value: attribute.templateGroup,
            severity: "secondary",
            size: "small",
            class: "mt-1"
          }, null, _parent));
        } else {
          _push(`<!---->`);
        }
        _push(`</div><div class="flex-1">`);
        _push(ssrRenderComponent($setup["AttributeValueInput"], {
          template: $setup.getTemplateForInput(attribute),
          "model-value": attribute.value,
          "onUpdate:modelValue": ($event) => $setup.updateAttributeValue(index, $event),
          class: "w-full",
          placeholder: $setup.getPlaceholder(attribute)
        }, null, _parent));
        _push(`</div><div class="flex-shrink-0 w-16 text-center">`);
        if (attribute.templateUnit) {
          _push(`<span class="text-sm text-surface-500 font-medium">${ssrInterpolate($setup.getUnitLabel(attribute.templateUnit))}</span>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div><div class="flex-shrink-0 flex gap-2">`);
        _push(ssrRenderComponent($setup["DangerButton"], {
          onClick: ($event) => $setup.removeAttribute(index),
          severity: "danger",
          size: "small",
          outlined: "",
          class: "hover:bg-red-50 dark:hover:bg-red-900/20"
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(ssrRenderComponent($setup["TrashIcon"], { class: "h-4 w-4" }, null, _parent2, _scopeId));
            } else {
              return [
                createVNode($setup["TrashIcon"], { class: "h-4 w-4" })
              ];
            }
          }),
          _: 2
        }, _parent));
        _push(`</div></div></div>`);
      });
      _push(`<!--]--></div>`);
    }
    _push(`</div>`);
  } else {
    _push(`<div class="text-center py-8 text-surface-500 border-2 border-dashed border-surface-200 dark:border-surface-700 rounded-lg"><i class="pi pi-plus-circle text-3xl mb-2 block"></i><p>\u0414\u043E\u0431\u0430\u0432\u044C\u0442\u0435 \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u0434\u043B\u044F \u043E\u043F\u0438\u0441\u0430\u043D\u0438\u044F \u0445\u0430\u0440\u0430\u043A\u0442\u0435\u0440\u0438\u0441\u0442\u0438\u043A \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0438</p><p class="text-sm mt-1">\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0433\u0440\u0443\u043F\u043F\u0443 \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u0432 \u0438\u043B\u0438 \u043E\u0442\u0434\u0435\u043B\u044C\u043D\u044B\u0439 \u0448\u0430\u0431\u043B\u043E\u043D \u0432\u044B\u0448\u0435</p></div>`);
  }
  _push(ssrRenderComponent($setup["VDialog"], {
    visible: $setup.showAddDialog,
    "onUpdate:visible": ($event) => $setup.showAddDialog = $event,
    modal: "",
    header: $setup.editingAttribute ? "\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0430\u0442\u0440\u0438\u0431\u0443\u0442" : "\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0430\u0442\u0440\u0438\u0431\u0443\u0442",
    style: { width: "50rem" },
    breakpoints: { "1199px": "75vw", "575px": "90vw" }
  }, {
    footer: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["VButton"], {
          label: "\u041E\u0442\u043C\u0435\u043D\u0430",
          severity: "secondary",
          onClick: $setup.closeAddDialog
        }, null, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["VButton"], {
          label: $setup.editingAttribute ? "\u0421\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C" : "\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C",
          onClick: $setup.saveAttribute,
          disabled: !$setup.canSave
        }, null, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["VButton"], {
            label: "\u041E\u0442\u043C\u0435\u043D\u0430",
            severity: "secondary",
            onClick: $setup.closeAddDialog
          }),
          createVNode($setup["VButton"], {
            label: $setup.editingAttribute ? "\u0421\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C" : "\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C",
            onClick: $setup.saveAttribute,
            disabled: !$setup.canSave
          }, null, 8, ["label", "disabled"])
        ];
      }
    }),
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="space-y-4"${_scopeId}>`);
        if (!$setup.editingAttribute) {
          _push2(`<div${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}> \u0428\u0430\u0431\u043B\u043E\u043D \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u0430 * </label>`);
          _push2(ssrRenderComponent($setup["VAutoComplete"], {
            modelValue: $setup.selectedTemplate,
            "onUpdate:modelValue": ($event) => $setup.selectedTemplate = $event,
            suggestions: $setup.templateSuggestions,
            onComplete: $setup.filterTemplates,
            "option-label": "title",
            placeholder: "\u041F\u043E\u0438\u0441\u043A \u0448\u0430\u0431\u043B\u043E\u043D\u0430...",
            class: "w-full",
            dropdown: ""
          }, {
            option: withCtx((slotProps, _push3, _parent3, _scopeId2) => {
              if (_push3) {
                _push3(`<div class="flex items-center gap-3"${_scopeId2}><i class="${ssrRenderClass([$setup.getDataTypeIcon(slotProps.option.dataType), "text-primary"])}"${_scopeId2}></i><div class="flex-1"${_scopeId2}><div class="font-medium"${_scopeId2}>${ssrInterpolate(slotProps.option.title)}</div><div class="text-sm text-surface-600 dark:text-surface-400"${_scopeId2}>${ssrInterpolate(slotProps.option.name)} `);
                if (slotProps.option.group?.name) {
                  _push3(`<span class="ml-2 px-2 py-1 bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 rounded text-xs"${_scopeId2}>${ssrInterpolate(slotProps.option.group.name)}</span>`);
                } else {
                  _push3(`<!---->`);
                }
                _push3(`</div></div>`);
                _push3(ssrRenderComponent($setup["VTag"], {
                  value: $setup.getDataTypeLabel(slotProps.option.dataType),
                  severity: "info",
                  size: "small"
                }, null, _parent3, _scopeId2));
                _push3(`</div>`);
              } else {
                return [
                  createVNode("div", { class: "flex items-center gap-3" }, [
                    createVNode("i", {
                      class: [$setup.getDataTypeIcon(slotProps.option.dataType), "text-primary"]
                    }, null, 2),
                    createVNode("div", { class: "flex-1" }, [
                      createVNode("div", { class: "font-medium" }, toDisplayString(slotProps.option.title), 1),
                      createVNode("div", { class: "text-sm text-surface-600 dark:text-surface-400" }, [
                        createTextVNode(toDisplayString(slotProps.option.name) + " ", 1),
                        slotProps.option.group?.name ? (openBlock(), createBlock("span", {
                          key: 0,
                          class: "ml-2 px-2 py-1 bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 rounded text-xs"
                        }, toDisplayString(slotProps.option.group.name), 1)) : createCommentVNode("", true)
                      ])
                    ]),
                    createVNode($setup["VTag"], {
                      value: $setup.getDataTypeLabel(slotProps.option.dataType),
                      severity: "info",
                      size: "small"
                    }, null, 8, ["value"])
                  ])
                ];
              }
            }),
            _: 1
          }, _parent2, _scopeId));
          _push2(`</div>`);
        } else {
          _push2(`<div class="p-4 bg-surface-50 dark:bg-surface-900 rounded-lg"${_scopeId}><div class="flex items-center gap-2 mb-2"${_scopeId}><i class="${ssrRenderClass([$setup.getDataTypeIcon($setup.editingAttribute.template?.dataType), "text-primary"])}"${_scopeId}></i><span class="font-medium"${_scopeId}>${ssrInterpolate($setup.editingAttribute.template?.title)}</span>`);
          _push2(ssrRenderComponent($setup["VTag"], {
            value: $setup.getDataTypeLabel($setup.editingAttribute.template?.dataType),
            severity: "info",
            size: "small"
          }, null, _parent2, _scopeId));
          _push2(`</div><div class="text-sm text-surface-600 dark:text-surface-400"${_scopeId}>${ssrInterpolate($setup.editingAttribute.template?.description)}</div></div>`);
        }
        if ($setup.selectedTemplate || $setup.editingAttribute) {
          _push2(`<div${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}> \u0417\u043D\u0430\u0447\u0435\u043D\u0438\u0435 * </label>`);
          _push2(ssrRenderComponent($setup["AttributeValueInput"], {
            modelValue: $setup.newAttributeValue,
            "onUpdate:modelValue": ($event) => $setup.newAttributeValue = $event,
            template: $setup.selectedTemplate || $setup.editingAttribute?.template,
            ref: "valueInputRef",
            onChange: $setup.handleValueChange
          }, null, _parent2, _scopeId));
          _push2(`</div>`);
        } else {
          _push2(`<!---->`);
        }
        _push2(`</div>`);
      } else {
        return [
          createVNode("div", { class: "space-y-4" }, [
            !$setup.editingAttribute ? (openBlock(), createBlock("div", { key: 0 }, [
              createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u0428\u0430\u0431\u043B\u043E\u043D \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u0430 * "),
              createVNode($setup["VAutoComplete"], {
                modelValue: $setup.selectedTemplate,
                "onUpdate:modelValue": ($event) => $setup.selectedTemplate = $event,
                suggestions: $setup.templateSuggestions,
                onComplete: $setup.filterTemplates,
                "option-label": "title",
                placeholder: "\u041F\u043E\u0438\u0441\u043A \u0448\u0430\u0431\u043B\u043E\u043D\u0430...",
                class: "w-full",
                dropdown: ""
              }, {
                option: withCtx((slotProps) => [
                  createVNode("div", { class: "flex items-center gap-3" }, [
                    createVNode("i", {
                      class: [$setup.getDataTypeIcon(slotProps.option.dataType), "text-primary"]
                    }, null, 2),
                    createVNode("div", { class: "flex-1" }, [
                      createVNode("div", { class: "font-medium" }, toDisplayString(slotProps.option.title), 1),
                      createVNode("div", { class: "text-sm text-surface-600 dark:text-surface-400" }, [
                        createTextVNode(toDisplayString(slotProps.option.name) + " ", 1),
                        slotProps.option.group?.name ? (openBlock(), createBlock("span", {
                          key: 0,
                          class: "ml-2 px-2 py-1 bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 rounded text-xs"
                        }, toDisplayString(slotProps.option.group.name), 1)) : createCommentVNode("", true)
                      ])
                    ]),
                    createVNode($setup["VTag"], {
                      value: $setup.getDataTypeLabel(slotProps.option.dataType),
                      severity: "info",
                      size: "small"
                    }, null, 8, ["value"])
                  ])
                ]),
                _: 1
              }, 8, ["modelValue", "onUpdate:modelValue", "suggestions"])
            ])) : (openBlock(), createBlock("div", {
              key: 1,
              class: "p-4 bg-surface-50 dark:bg-surface-900 rounded-lg"
            }, [
              createVNode("div", { class: "flex items-center gap-2 mb-2" }, [
                createVNode("i", {
                  class: [$setup.getDataTypeIcon($setup.editingAttribute.template?.dataType), "text-primary"]
                }, null, 2),
                createVNode("span", { class: "font-medium" }, toDisplayString($setup.editingAttribute.template?.title), 1),
                createVNode($setup["VTag"], {
                  value: $setup.getDataTypeLabel($setup.editingAttribute.template?.dataType),
                  severity: "info",
                  size: "small"
                }, null, 8, ["value"])
              ]),
              createVNode("div", { class: "text-sm text-surface-600 dark:text-surface-400" }, toDisplayString($setup.editingAttribute.template?.description), 1)
            ])),
            $setup.selectedTemplate || $setup.editingAttribute ? (openBlock(), createBlock("div", { key: 2 }, [
              createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u0417\u043D\u0430\u0447\u0435\u043D\u0438\u0435 * "),
              createVNode($setup["AttributeValueInput"], {
                modelValue: $setup.newAttributeValue,
                "onUpdate:modelValue": ($event) => $setup.newAttributeValue = $event,
                template: $setup.selectedTemplate || $setup.editingAttribute?.template,
                ref: "valueInputRef",
                onChange: $setup.handleValueChange
              }, null, 8, ["modelValue", "onUpdate:modelValue", "template"])
            ])) : createCommentVNode("", true)
          ])
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(ssrRenderComponent($setup["VDialog"], {
    visible: $setup.showGroupDialog,
    "onUpdate:visible": ($event) => $setup.showGroupDialog = $event,
    modal: "",
    header: "\u0412\u044B\u0431\u043E\u0440 \u0433\u0440\u0443\u043F\u043F\u044B \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u0432",
    style: { width: "40rem" }
  }, {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="space-y-4"${_scopeId}><!--[-->`);
        ssrRenderList($setup.templateGroups, (group) => {
          _push2(`<div class="border border-surface-200 dark:border-surface-700 rounded-lg p-4"${_scopeId}><div class="flex items-center justify-between"${_scopeId}><div${_scopeId}><h4 class="font-medium text-surface-900 dark:text-surface-0"${_scopeId}>${ssrInterpolate(group.name)}</h4><p class="text-sm text-surface-600 dark:text-surface-400"${_scopeId}>${ssrInterpolate(group.description)}</p><small class="text-surface-500"${_scopeId}>${ssrInterpolate(group._count?.templates || 0)} \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u0432</small></div>`);
          _push2(ssrRenderComponent($setup["VButton"], {
            onClick: ($event) => $setup.loadTemplatesByGroupId(group.id),
            size: "small",
            loading: $setup.loadingTemplates
          }, {
            default: withCtx((_2, _push3, _parent3, _scopeId2) => {
              if (_push3) {
                _push3(` \u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0432\u0441\u0435 `);
              } else {
                return [
                  createTextVNode(" \u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0432\u0441\u0435 ")
                ];
              }
            }),
            _: 2
          }, _parent2, _scopeId));
          _push2(`</div></div>`);
        });
        _push2(`<!--]--></div>`);
      } else {
        return [
          createVNode("div", { class: "space-y-4" }, [
            (openBlock(true), createBlock(Fragment, null, renderList($setup.templateGroups, (group) => {
              return openBlock(), createBlock("div", {
                key: group.id,
                class: "border border-surface-200 dark:border-surface-700 rounded-lg p-4"
              }, [
                createVNode("div", { class: "flex items-center justify-between" }, [
                  createVNode("div", null, [
                    createVNode("h4", { class: "font-medium text-surface-900 dark:text-surface-0" }, toDisplayString(group.name), 1),
                    createVNode("p", { class: "text-sm text-surface-600 dark:text-surface-400" }, toDisplayString(group.description), 1),
                    createVNode("small", { class: "text-surface-500" }, toDisplayString(group._count?.templates || 0) + " \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u0432", 1)
                  ]),
                  createVNode($setup["VButton"], {
                    onClick: ($event) => $setup.loadTemplatesByGroupId(group.id),
                    size: "small",
                    loading: $setup.loadingTemplates
                  }, {
                    default: withCtx(() => [
                      createTextVNode(" \u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0432\u0441\u0435 ")
                    ]),
                    _: 2
                  }, 1032, ["onClick", "loading"])
                ])
              ]);
            }), 128))
          ])
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</div>`);
}
const _sfc_setup$1 = _sfc_main$1.setup;
_sfc_main$1.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/attributes/AttributeManager.vue");
  return _sfc_setup$1 ? _sfc_setup$1(props, ctx) : void 0;
};
const AttributeManager = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["ssrRender", _sfc_ssrRender$1]]);

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "QuickCreateBrand",
  props: {
    visible: { type: Boolean }
  },
  emits: ["update:visible", "created"],
  setup(__props, { expose: __expose, emit: __emit }) {
    __expose();
    const props = __props;
    const emit = __emit;
    const { loading, error, clearError, brands } = useTrpc();
    const visible = computed({
      get: () => props.visible,
      set: (value) => emit("update:visible", value)
    });
    const formData = ref({
      name: "",
      country: "",
      isOem: false
    });
    const errors = ref({
      name: ""
    });
    const canCreate = computed(() => {
      return formData.value.name.trim().length > 0 && !loading.value;
    });
    const createBrand = async () => {
      clearError();
      errors.value = { name: "" };
      if (!formData.value.name.trim()) {
        errors.value.name = "\u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435 \u043E\u0431\u044F\u0437\u0430\u0442\u0435\u043B\u044C\u043D\u043E";
        return;
      }
      try {
        const slug = formData.value.name.toLowerCase().replace(/[^a-z0-9\s-]/g, "").replace(/\s+/g, "-").replace(/-+/g, "-").trim();
        const brandData = {
          name: formData.value.name.trim(),
          slug,
          country: formData.value.country?.trim() || void 0,
          isOem: formData.value.isOem
        };
        const result = await brands.create({
          data: brandData
        });
        if (result) {
          emit("created", result);
          resetForm();
          visible.value = false;
        }
      } catch (err) {
        console.error("\u041E\u0448\u0438\u0431\u043A\u0430 \u0441\u043E\u0437\u0434\u0430\u043D\u0438\u044F \u0431\u0440\u0435\u043D\u0434\u0430:", err);
      }
    };
    const resetForm = () => {
      formData.value = {
        name: "",
        country: "",
        isOem: false
      };
      errors.value = { name: "" };
      clearError();
    };
    watch(visible, (newValue) => {
      if (!newValue) {
        resetForm();
      }
    });
    const __returned__ = { props, emit, loading, error, clearError, brands, visible, formData, errors, canCreate, createBrand, resetForm, VDialog: Dialog, VButton, VInputText: InputText, VMessage: Message };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(ssrRenderComponent($setup["VDialog"], mergeProps({
    visible: $setup.visible,
    "onUpdate:visible": ($event) => $setup.visible = $event,
    modal: "",
    header: "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u043D\u043E\u0432\u044B\u0439 \u0431\u0440\u0435\u043D\u0434",
    class: "w-96"
  }, _attrs), {
    footer: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="flex justify-end gap-3"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["VButton"], {
          onClick: ($event) => $setup.visible = false,
          severity: "secondary",
          outlined: "",
          disabled: $setup.loading
        }, {
          default: withCtx((_2, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(` \u041E\u0442\u043C\u0435\u043D\u0430 `);
            } else {
              return [
                createTextVNode(" \u041E\u0442\u043C\u0435\u043D\u0430 ")
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["VButton"], {
          onClick: $setup.createBrand,
          loading: $setup.loading,
          disabled: !$setup.canCreate
        }, {
          default: withCtx((_2, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(` \u0421\u043E\u0437\u0434\u0430\u0442\u044C `);
            } else {
              return [
                createTextVNode(" \u0421\u043E\u0437\u0434\u0430\u0442\u044C ")
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(`</div>`);
      } else {
        return [
          createVNode("div", { class: "flex justify-end gap-3" }, [
            createVNode($setup["VButton"], {
              onClick: ($event) => $setup.visible = false,
              severity: "secondary",
              outlined: "",
              disabled: $setup.loading
            }, {
              default: withCtx(() => [
                createTextVNode(" \u041E\u0442\u043C\u0435\u043D\u0430 ")
              ]),
              _: 1
            }, 8, ["onClick", "disabled"]),
            createVNode($setup["VButton"], {
              onClick: $setup.createBrand,
              loading: $setup.loading,
              disabled: !$setup.canCreate
            }, {
              default: withCtx(() => [
                createTextVNode(" \u0421\u043E\u0437\u0434\u0430\u0442\u044C ")
              ]),
              _: 1
            }, 8, ["loading", "disabled"])
          ])
        ];
      }
    }),
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="space-y-4"${_scopeId}><div${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}> \u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435 \u0431\u0440\u0435\u043D\u0434\u0430 * </label>`);
        _push2(ssrRenderComponent($setup["VInputText"], {
          modelValue: $setup.formData.name,
          "onUpdate:modelValue": ($event) => $setup.formData.name = $event,
          placeholder: "\u041D\u0430\u043F\u0440\u0438\u043C\u0435\u0440: SKF",
          class: ["w-full", { "p-invalid": $setup.errors.name }]
        }, null, _parent2, _scopeId));
        if ($setup.errors.name) {
          _push2(`<small class="text-red-500"${_scopeId}>${ssrInterpolate($setup.errors.name)}</small>`);
        } else {
          _push2(`<!---->`);
        }
        _push2(`</div><div${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}> \u0421\u0442\u0440\u0430\u043D\u0430 </label>`);
        _push2(ssrRenderComponent($setup["VInputText"], {
          modelValue: $setup.formData.country,
          "onUpdate:modelValue": ($event) => $setup.formData.country = $event,
          placeholder: "\u041D\u0430\u043F\u0440\u0438\u043C\u0435\u0440: \u0428\u0432\u0435\u0446\u0438\u044F",
          class: "w-full"
        }, null, _parent2, _scopeId));
        _push2(`</div><div${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}> \u0422\u0438\u043F \u043F\u0440\u043E\u0438\u0437\u0432\u043E\u0434\u0438\u0442\u0435\u043B\u044F </label><div class="flex gap-4"${_scopeId}><label class="flex items-center"${_scopeId}><input type="radio"${ssrIncludeBooleanAttr(ssrLooseEqual($setup.formData.isOem, false)) ? " checked" : ""}${ssrRenderAttr("value", false)} class="mr-2"${_scopeId}> Aftermarket </label><label class="flex items-center"${_scopeId}><input type="radio"${ssrIncludeBooleanAttr(ssrLooseEqual($setup.formData.isOem, true)) ? " checked" : ""}${ssrRenderAttr("value", true)} class="mr-2"${_scopeId}> OEM </label></div></div></div>`);
        if ($setup.error) {
          _push2(ssrRenderComponent($setup["VMessage"], {
            severity: "error",
            class: "mt-4"
          }, {
            default: withCtx((_2, _push3, _parent3, _scopeId2) => {
              if (_push3) {
                _push3(`${ssrInterpolate($setup.error)}`);
              } else {
                return [
                  createTextVNode(toDisplayString($setup.error), 1)
                ];
              }
            }),
            _: 1
          }, _parent2, _scopeId));
        } else {
          _push2(`<!---->`);
        }
      } else {
        return [
          createVNode("div", { class: "space-y-4" }, [
            createVNode("div", null, [
              createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435 \u0431\u0440\u0435\u043D\u0434\u0430 * "),
              createVNode($setup["VInputText"], {
                modelValue: $setup.formData.name,
                "onUpdate:modelValue": ($event) => $setup.formData.name = $event,
                placeholder: "\u041D\u0430\u043F\u0440\u0438\u043C\u0435\u0440: SKF",
                class: ["w-full", { "p-invalid": $setup.errors.name }]
              }, null, 8, ["modelValue", "onUpdate:modelValue", "class"]),
              $setup.errors.name ? (openBlock(), createBlock("small", {
                key: 0,
                class: "text-red-500"
              }, toDisplayString($setup.errors.name), 1)) : createCommentVNode("", true)
            ]),
            createVNode("div", null, [
              createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u0421\u0442\u0440\u0430\u043D\u0430 "),
              createVNode($setup["VInputText"], {
                modelValue: $setup.formData.country,
                "onUpdate:modelValue": ($event) => $setup.formData.country = $event,
                placeholder: "\u041D\u0430\u043F\u0440\u0438\u043C\u0435\u0440: \u0428\u0432\u0435\u0446\u0438\u044F",
                class: "w-full"
              }, null, 8, ["modelValue", "onUpdate:modelValue"])
            ]),
            createVNode("div", null, [
              createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u0422\u0438\u043F \u043F\u0440\u043E\u0438\u0437\u0432\u043E\u0434\u0438\u0442\u0435\u043B\u044F "),
              createVNode("div", { class: "flex gap-4" }, [
                createVNode("label", { class: "flex items-center" }, [
                  withDirectives(createVNode("input", {
                    type: "radio",
                    "onUpdate:modelValue": ($event) => $setup.formData.isOem = $event,
                    value: false,
                    class: "mr-2"
                  }, null, 8, ["onUpdate:modelValue"]), [
                    [vModelRadio, $setup.formData.isOem]
                  ]),
                  createTextVNode(" Aftermarket ")
                ]),
                createVNode("label", { class: "flex items-center" }, [
                  withDirectives(createVNode("input", {
                    type: "radio",
                    "onUpdate:modelValue": ($event) => $setup.formData.isOem = $event,
                    value: true,
                    class: "mr-2"
                  }, null, 8, ["onUpdate:modelValue"]), [
                    [vModelRadio, $setup.formData.isOem]
                  ]),
                  createTextVNode(" OEM ")
                ])
              ])
            ])
          ]),
          $setup.error ? (openBlock(), createBlock($setup["VMessage"], {
            key: 0,
            severity: "error",
            class: "mt-4"
          }, {
            default: withCtx(() => [
              createTextVNode(toDisplayString($setup.error), 1)
            ]),
            _: 1
          })) : createCommentVNode("", true)
        ];
      }
    }),
    _: 1
  }, _parent));
}
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/parts/QuickCreateBrand.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const QuickCreateBrand = /* @__PURE__ */ _export_sfc(_sfc_main, [["ssrRender", _sfc_ssrRender]]);

export { AttributeManager as A, QuickCreateBrand as Q };
