// Общие типы каталога для фронтенда
// Цель: дать IDE-подсказки без протягивания серверных зависимостей (Prisma/@trpc/server)

export type SortOrder = 'asc' | 'desc'

// Минимальный набор полей, который реально используется в UI
export interface PartCategoryLite {
  id: number
  name: string
}

export interface AttributeTemplateLite {
  id: number
  name: string
  title?: string | null
  dataType: 'TEXT' | 'NUMBER' | 'STRING'
  unit?: string | null | { symbol?: string | null; name?: string | null }
}

export interface PartAttributeLite {
  id: number
  value: string
  numericValue?: number | null
  template: AttributeTemplateLite
}

export interface MediaAssetLite {
  id: number
  url?: string | null
}

export interface PartListItem {
  id: number
  name?: string | null
  path?: string | null
  partCategory: PartCategoryLite
  image?: MediaAssetLite | null
  attributes: PartAttributeLite[]
  applicabilities?: { id: number }[]
  equipmentApplicabilities?: { id: number }[]
  updatedAt?: string | Date
}

export interface BrandLite {
  id: number
  name: string
}

// Тип запроса, который мы реально формируем на странице /catalog
export interface PartListQuery {
  where?: unknown
  include?: {
    partCategory?: boolean
    image?: boolean
    attributes?: { include?: { template?: boolean } }
  }
  take?: number
  skip?: number
  orderBy?: Record<string, SortOrder>
}

export interface CategoryListQuery {
  where?: { level?: number }
  orderBy?: { name?: SortOrder }
}

export type BrandListQuery = Record<string, unknown>

