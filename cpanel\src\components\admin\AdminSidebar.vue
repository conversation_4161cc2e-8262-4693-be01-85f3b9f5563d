<template>
  <aside class="bg-surface-section border-surface-border min-h-full w-64 border-r shadow-sm">
    <div class="p-6">
      <!-- Основное -->
      <div class="mb-6">
        <h3 class="text-surface-500 mb-3 text-xs font-semibold tracking-wider uppercase">Основное</h3>
        <div class="space-y-1">
          <SecondaryButton label="Дашборд" text :aria-current="isActiveExact('/admin') ? 'page' : undefined"
            :class="{ 'bg-primary-50 text-primary-600': isActiveExact('/admin') }" @click="navigateTo('/admin')" />
        </div>
      </div>

      <!-- Каталог -->
      <div class="mb-6">
        <h3 class="text-surface-500 mb-3 text-xs font-semibold tracking-wider uppercase">Каталог</h3>
        <div class="flex flex-col items-start  space-y-1">
          <SecondaryButton label="Запчасти" text :aria-current="isActive('/admin/parts') ? 'page' : undefined"
            :class="{ 'bg-primary-50 text-primary-600': isActive('/admin/parts') }"
            @click="navigateTo('/admin/parts')" />
          <SecondaryButton label="Каталожные позиции" text
            :aria-current="isActive('/admin/catalogitems') ? 'page' : undefined"
            :class="{ 'bg-primary-50 text-primary-600': isActive('/admin/catalogitems') }"
            @click="navigateTo('/admin/catalogitems')" />
          <SecondaryButton label="Подбор эквивалентов" text
            :aria-current="isActive('/admin/proposals') ? 'page' : undefined"
            :class="{ 'bg-primary-50 text-primary-600': isActive('/admin/proposals') }"
            @click="navigateTo('/admin/proposals')" />
        </div>
      </div>

      <!-- Справочники -->
      <div class="mb-6">
        <h3 class="text-surface-500 mb-3 text-xs font-semibold tracking-wider uppercase">Справочники</h3>
        <div class="flex flex-col items-start space-y-1">
          <SecondaryButton label="Атрибуты" text :aria-current="isActive('/admin/attributes') ? 'page' : undefined"
            :class="{ 'bg-primary-50 text-primary-600': isActive('/admin/attributes') }"
            @click="navigateTo('/admin/attributes')" />
          <SecondaryButton label="Бренды" text :aria-current="isActive('/admin/brands') ? 'page' : undefined"
            :class="{ 'bg-primary-50 text-primary-600': isActive('/admin/brands') }"
            @click="navigateTo('/admin/brands')" />
          <SecondaryButton label="Категории" text :aria-current="isActive('/admin/categories') ? 'page' : undefined"
            :class="{ 'bg-primary-50 text-primary-600': isActive('/admin/categories') }"
            @click="navigateTo('/admin/categories')" />
          <SecondaryButton label="Модели техники" text :aria-current="isActive('/admin/equipment') ? 'page' : undefined"
            :class="{ 'bg-primary-50 text-primary-600': isActive('/admin/equipment') }"
            @click="navigateTo('/admin/equipment')" />
        </div>
      </div>

      <!-- Система -->
      <div>
        <h3 class="text-surface-500 mb-3 text-xs font-semibold tracking-wider uppercase">Система</h3>
        <div class="space-y-1">
          <SecondaryButton label="Импорт / Экспорт" text
            :aria-current="isActive('/admin/import-export') ? 'page' : undefined"
            :class="{ 'bg-primary-50 text-primary-600': isActive('/admin/import-export') }"
            @click="navigateTo('/admin/import-export')" />
          <SecondaryButton label="Пользователи" text :aria-current="isActive('/admin/users') ? 'page' : undefined"
            :class="{ 'bg-primary-50 text-primary-600': isActive('/admin/users') }"
            @click="navigateTo('/admin/users')" />
        </div>
      </div>
    </div>
  </aside>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import SecondaryButton from '@/volt/SecondaryButton.vue'
import { navigate } from 'astro:transitions/client'

// Получаем текущий путь
const currentPath = computed(() => {
  if (typeof window !== 'undefined') {
    return window.location.pathname
  }
  return ''
})

// Методы
const navigateTo = (path: string) => {
  // window.location.href = path
  navigate(path)
}

const isActive = (path: string): boolean => {
  return currentPath.value.startsWith(path)
}

const isActiveExact = (path: string): boolean => currentPath.value === path
</script>
