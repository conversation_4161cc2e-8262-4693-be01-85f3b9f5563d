# Multi-stage build для оптимизации размера образа
FROM oven/bun:1-alpine AS base

# Устанавливаем рабочую директорию
WORKDIR /app

# Устанавливаем системные зависимости для Prisma
RUN apk add --no-cache openssl

# Копируем package.json и bun.lockb для кэширования зависимостей
COPY package.json bun.lockb* ./

# Устанавливаем зависимости
RUN bun install --frozen-lockfile

# Копируем исходный код
COPY . .

# Генерируем Prisma клиент и ZenStack
RUN bunx prisma generate
RUN bunx zenstack generate

# Production stage
FROM oven/bun:1-alpine AS production

WORKDIR /app

# Устанавливаем только runtime зависимости
RUN apk add --no-cache openssl

# Копируем node_modules и сгенерированные файлы
COPY --from=base /app/node_modules ./node_modules
COPY --from=base /app/package.json ./package.json

# Копируем исходный код
COPY . .

# Создаем пользователя для безопасности
RUN addgroup -g 1001 -S nodejs
RUN adduser -S bun -u 1001

# Создаем директорию для загрузок и устанавливаем права
RUN mkdir -p uploads && chown -R bun:nodejs uploads

# Переключаемся на непривилегированного пользователя
USER bun

# Открываем порт
EXPOSE 3000

# Устанавливаем переменные окружения
ENV NODE_ENV=production

# Запускаем приложение
CMD ["bun", "run", "index.ts"]
