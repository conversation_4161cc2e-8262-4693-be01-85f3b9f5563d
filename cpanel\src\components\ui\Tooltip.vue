<template>
  <div class="relative inline-block" @mouseenter="show = true" @mouseleave="show = false">
    <slot />
    
    <Teleport to="body">
      <Motion
        v-if="show"
        :initial="{ opacity: 0, scale: 0.8, y: 10 }"
        :animate="{ opacity: 1, scale: 1, y: 0 }"
        :exit="{ opacity: 0, scale: 0.8, y: 10 }"
        :transition="{ duration: 0.2 }"
        class="absolute z-50 px-3 py-2 text-sm text-[--color-foreground] bg-[--color-card] border border-[--color-border] rounded-[--radius-sm] [box-shadow:var(--shadow-md)] backdrop-blur-xl"
        :style="tooltipStyle"
      >
        {{ content }}
        <div class="absolute w-2 h-2 bg-[--color-card] border-l border-b border-[--color-border] transform rotate-45 -bottom-1 left-1/2 -translate-x-1/2" />
      </Motion>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { Motion } from 'motion-v'

interface Props {
  content: string
  placement?: 'top' | 'bottom' | 'left' | 'right'
}

const props = withDefaults(defineProps<Props>(), {
  placement: 'top'
})

const show = ref(false)
const triggerRef = ref<HTMLElement>()

const tooltipStyle = computed(() => {
  // Simple positioning for now
  return {
    position: 'fixed',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    pointerEvents: 'none',
    color: 'var(--p-text-color)'
  }
})
</script>