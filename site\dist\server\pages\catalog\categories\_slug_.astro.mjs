import { e as createComponent, f as createAstro, k as renderComponent, r as renderTemplate, m as maybeRenderHead, h as addAttribute } from '../../../chunks/astro/server_D7mwM5eH.mjs';
import 'kleur/colors';
import { $ as $$MainLayout } from '../../../chunks/MainLayout_M77LEN4m.mjs';
import { t as trpcClient } from '../../../chunks/badge_BlauDB1K.mjs';
import { T as TrpcProvider } from '../../../chunks/TrpcProvider_CX1isq_a.mjs';
import { P as PartCard } from '../../../chunks/PartCard_BoNOaBW6.mjs';
import { C as CategoryCard } from '../../../chunks/CategoryCard_Cjcin2cY.mjs';
export { renderers } from '../../../renderers.mjs';

const $$Astro = createAstro();
const $$slug = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$slug;
  const { slug } = Astro2.params;
  if (!slug) {
    return Astro2.redirect("/categories");
  }
  let category = null;
  let parts = [];
  let subcategories = [];
  try {
    const categoryResponse = await trpcClient.crud.partCategory.findUnique.query({
      where: { slug },
      include: {
        image: true,
        parent: true,
        children: {
          include: {
            image: true,
            _count: { select: { parts: true } }
          },
          orderBy: { name: "asc" }
        },
        _count: { select: { parts: true } }
      }
    });
    if (!categoryResponse) {
      return Astro2.redirect("/categories");
    }
    category = categoryResponse;
    subcategories = category.children || [];
    const partsResponse = await trpcClient.crud.part.findMany.query({
      where: { partCategoryId: category.id },
      include: {
        partCategory: true,
        image: true,
        attributes: {
          include: { template: true },
          take: 3
        }
      },
      take: 20,
      orderBy: { updatedAt: "desc" }
    });
    parts = partsResponse || [];
  } catch (error) {
    console.error("Error loading category data:", error);
    return Astro2.redirect("/categories");
  }
  return renderTemplate`${renderComponent($$result, "MainLayout", $$MainLayout, { "title": category.name, "description": category.description || `\u0417\u0430\u043F\u0447\u0430\u0441\u0442\u0438 \u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438 ${category.name}` }, { "default": async ($$result2) => renderTemplate` ${renderComponent($$result2, "TrpcProvider", TrpcProvider, { "client:load": true, "client:component-hydration": "load", "client:component-path": "@/components/providers/TrpcProvider", "client:component-export": "TrpcProvider" }, { "default": async ($$result3) => renderTemplate` ${maybeRenderHead()}<div class="container mx-auto px-4 py-8"> <!-- Хлебные крошки --> <nav class="flex mb-8" aria-label="Breadcrumb"> <ol class="inline-flex items-center space-x-1 md:space-x-3"> <li class="inline-flex items-center"> <a href="/" class="inline-flex items-center text-sm font-medium text-muted-foreground hover:text-foreground">
Главная
</a> </li> <li> <div class="flex items-center"> <span class="mx-2 text-muted-foreground">/</span> <a href="/categories" class="text-sm font-medium text-muted-foreground hover:text-foreground">Категории</a> </div> </li> ${category.parent && renderTemplate`<li> <div class="flex items-center"> <span class="mx-2 text-muted-foreground">/</span> <a${addAttribute(`/catalog/categories/${category.parent.slug}`, "href")} class="text-sm font-medium text-muted-foreground hover:text-foreground"> ${category.parent.name} </a> </div> </li>`} <li aria-current="page"> <div class="flex items-center"> <span class="mx-2 text-muted-foreground">/</span> <span class="text-sm font-medium text-foreground">${category.name}</span> </div> </li> </ol> </nav> <!-- Заголовок категории --> <div class="mb-8"> <div class="flex items-start gap-4"> ${category.image && renderTemplate`<img${addAttribute(category.image.url, "src")}${addAttribute(category.name, "alt")} class="w-20 h-20 object-cover rounded-lg">`} <div class="flex-1"> <h1 class="text-3xl font-bold tracking-tight mb-2 flex items-center gap-2"> ${category.icon && renderTemplate`<span class="text-3xl">${category.icon}</span>`} ${category.name} </h1> ${category.description && renderTemplate`<p class="text-muted-foreground mb-4">${category.description}</p>`} <div class="flex items-center gap-4 text-sm text-muted-foreground"> <span>Уровень: ${category.level}</span> <span>Запчастей: ${category._count.parts}</span> ${subcategories.length > 0 && renderTemplate`<span>Подкатегорий: ${subcategories.length}</span>`} </div> </div> </div> </div> <!-- Подкатегории --> ${subcategories.length > 0 && renderTemplate`<div class="mb-12"> <h2 class="text-2xl font-semibold mb-6">Подкатегории</h2> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"> ${subcategories.map((subcategory) => renderTemplate`${renderComponent($$result3, "CategoryCard", CategoryCard, { "category": subcategory, "client:load": true, "client:component-hydration": "load", "client:component-path": "@/components/catalog/CategoryCard", "client:component-export": "CategoryCard" })}`)} </div> </div>`} <!-- Запчасти категории --> <div> <div class="flex items-center justify-between mb-6"> <h2 class="text-2xl font-semibold">
Запчасти ${category.name.toLowerCase()} </h2> ${parts.length === 20 && renderTemplate`<a${addAttribute(`/catalog?category=${category.id}`, "href")} class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2">
Показать все
</a>`} </div> ${parts.length > 0 ? renderTemplate`<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"> ${parts.map((part) => renderTemplate`${renderComponent($$result3, "PartCard", PartCard, { "part": part, "client:load": true, "client:component-hydration": "load", "client:component-path": "@/components/catalog/PartCard", "client:component-export": "PartCard" })}`)} </div>` : renderTemplate`<div class="text-center py-12"> <h3 class="text-lg font-semibold mb-2">Запчасти не найдены</h3> <p class="text-muted-foreground mb-4">
В этой категории пока нет запчастей
</p> ${subcategories.length > 0 ? renderTemplate`<p class="text-sm text-muted-foreground">
Попробуйте поискать в подкатегориях выше
</p>` : renderTemplate`<a href="/catalog" class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground shadow hover:bg-primary/90 h-9 px-4 py-2">
Перейти к каталогу
</a>`} </div>`} </div> </div> ` })} ` })}`;
}, "D:/Dev/parttec/site/src/pages/catalog/categories/[slug].astro", void 0);

const $$file = "D:/Dev/parttec/site/src/pages/catalog/categories/[slug].astro";
const $$url = "/catalog/categories/[slug]";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$slug,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
