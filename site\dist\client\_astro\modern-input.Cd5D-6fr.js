import{j as s}from"./jsx-runtime.D_zvdyIk.js";import{r as d}from"./index.0yr9KlQE.js";import{c as n}from"./utils.CBfrqCZ4.js";const l=d.forwardRef(({className:e,type:o,variant:r="default",...i},t)=>s.jsx("input",{type:o,className:n("flex h-11 w-full rounded-lg border border-input bg-input px-4 py-2 text-sm transition-all duration-200","file:border-0 file:bg-transparent file:text-sm file:font-medium","placeholder:text-muted-foreground","focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-0","focus-visible:border-primary/50 focus-visible:bg-background/50","disabled:cursor-not-allowed disabled:opacity-50","hover:border-primary/30 hover:bg-background/30",{"border-transparent bg-muted/50":r==="ghost","bg-muted border-muted":r==="filled"},e),ref:t,...i}));l.displayName="ModernInput";export{l as M};
