<template>
  <div class="simple-attribute-manager">
    <div class="flex items-center justify-between mb-4">
      <div class="flex items-center gap-3">
        <h3 class="text-lg font-medium text-surface-900 dark:text-surface-0">
          {{ title }}
        </h3>
        <VTag
          v-if="modelValue.length > 0"
          :value="`${filledAttributesCount}/${modelValue.length} заполнено`"
          :severity="filledAttributesCount === modelValue.length ? 'success' : 'warn'"
          size="small"
        />
      </div>
      <div class="flex gap-2">
        <VButton
          v-if="showGroupSelector"
          @click="showGroupDialog = true"
          severity="secondary"
          outlined
          size="small"
          label="Добавить группу"
        >
          <template #icon>
            <Icon name="pi pi-tags" class="w-5 h-5" />
          </template>
        </VButton>
        <VButton
          @click="showAddDialog = true"
          size="small"
          outlined
          label="Добавить атрибут"
        />
      </div>
    </div>

    <!-- Быстрое добавление атрибутов по группе -->
    <VCard class="mb-4">
      <template #content>
        <div class="p-4">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
                Группа шаблонов
              </label>
              <VAutoComplete
                v-model="selectedTemplateGroup"
                :suggestions="groupSuggestions"
                @complete="filterGroups"
                option-label="name"
                placeholder="Поиск группы..."
                class="w-full"
                dropdown
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
                Или отдельный шаблон
              </label>
              <VAutoComplete
                v-model="selectedTemplate"
                :suggestions="templateSuggestions"
                @complete="filterTemplates"
                option-label="title"
                placeholder="Поиск шаблона..."
                class="w-full"
                dropdown
              >
                <template #option="{ option }">
                  <div class="flex items-center gap-2">
                    <Icon :name="getDataTypeIcon(option.dataType)" class="text-primary w-4 h-4" />
                    <div class="flex-1">
                      <div class="font-medium">{{ option.title }}</div>
                      <div class="text-sm text-surface-600">
                        {{ option.group?.name }} • {{ getDataTypeLabel(option.dataType) }}
                      </div>
                    </div>
                  </div>
                </template>
              </VAutoComplete>
            </div>
              <div class="flex items-end gap-2">
                <VButton
                  @click="loadSelectedGroupTemplates"
                  size="small"
                  outlined
                  :disabled="!selectedTemplateGroup || loadingTemplates"
                  :loading="loadingTemplates"
                  label="Добавить группу"
                  class="flex-1"
                />
                <VButton
                  @click="addSingleTemplate"
                  size="small"
                  outlined
                  :disabled="!selectedTemplate"
                  label="Добавить"
                  class="flex-1"
                />
              </div>
          </div>
        </div>
      </template>
    </VCard>

    <!-- Список атрибутов с поддержкой группировки -->
    <div v-if="modelValue.length > 0" class="space-y-4">
      <!-- Группировка по группам шаблонов -->
      <div v-if="groupByTemplate" v-for="(group, groupName) in groupedAttributes" :key="groupName" class="space-y-3">
        <div class="flex items-center gap-2 mb-3">
          <Icon name="pi pi-folder" class="text-blue-600 w-4 h-4" />
          <h4 class="font-medium text-surface-900 dark:text-surface-0">
            {{ groupName === 'undefined' ? 'Без группы' : groupName }}
          </h4>
          <VTag :value="`${group.length} атр.`" severity="secondary" size="small" />
        </div>

        <div class="space-y-3 ml-6">
          <div v-for="(attribute, index) in group" :key="attribute.id || `new-${index}`">
            <div class="flex items-center gap-3 p-4 border rounded-lg transition-all duration-200 hover:shadow-sm"
                 :class="[
                   attribute.value
                     ? 'border-green-200 dark:border-green-700 bg-green-50 dark:bg-green-900/10'
                     : 'border-orange-200 dark:border-orange-700 bg-orange-50 dark:bg-orange-900/10'
                 ]">

              <!-- Иконка и статус -->
              <div class="flex-shrink-0 relative">
                <Icon :name="getDataTypeIcon(attribute.templateDataType)" class="text-lg text-primary w-5 h-5" />
                <div class="absolute -top-1 -right-1 w-3 h-3 rounded-full border-2 border-white dark:border-surface-900"
                     :class="attribute.value ? 'bg-green-500' : 'bg-orange-500'"
                     :title="attribute.value ? 'Заполнено' : 'Не заполнено'">
                </div>
              </div>

              <!-- Название и группа -->
              <div class="flex-shrink-0 w-48">
                <div class="font-medium text-surface-900 dark:text-surface-0 text-sm">
                  {{ attribute.templateTitle || 'Без названия' }}
                </div>
                <VTag
                  v-if="attribute.templateGroup"
                  :value="attribute.templateGroup"
                  severity="secondary"
                  size="small"
                  class="mt-1"
                />
              </div>

              <!-- Поле ввода значения -->
              <div class="flex-1">
                <AttributeValueInput
                  :template="getTemplateForInput(attribute)"
                  :model-value="attribute.value"
                  @update:model-value="updateAttributeValue(getAttributeIndex(attribute), $event)"
                  class="w-full"
                  :placeholder="getPlaceholder(attribute)"
                />
              </div>

              <!-- Единица измерения -->
              <div class="flex-shrink-0 w-16 text-center">
                <span v-if="attribute.templateUnit" class="text-sm text-surface-500 font-medium">
                  {{ getUnitLabel(attribute.templateUnit) }}
                </span>
              </div>

              <!-- Кнопки действий -->
              <div class="flex-shrink-0 flex gap-2">
                <VButton
                  @click="editAttribute(attribute)"
                  size="small"
                  outlined
                >
                <PencilIcon class="h-4 w-4" />
                </VButton>
                <DangerButton
                  @click="removeAttributeByObject(attribute)"
                  size="small"
                  outlined
                >
                <TrashIcon class="h-4 w-4" />
                </DangerButton>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Обычный список без группировки -->
      <div v-else class="space-y-3">
        <div v-for="(attribute, index) in modelValue" :key="attribute.id || `new-${index}`">
        <div class="flex items-center gap-3 p-4 border rounded-lg transition-all duration-200 hover:shadow-sm"
             :class="[
               attribute.value
                 ? 'border-green-200 dark:border-green-700 bg-green-50 dark:bg-green-900/10'
                 : 'border-orange-200 dark:border-orange-700 bg-orange-50 dark:bg-orange-900/10'
             ]">
          
          <!-- Иконка и статус -->
          <div class="flex-shrink-0 relative">
            <Icon :name="getDataTypeIcon(attribute.templateDataType)" class="text-lg text-primary w-5 h-5" />
            <div class="absolute -top-1 -right-1 w-3 h-3 rounded-full border-2 border-white dark:border-surface-900"
                 :class="attribute.value ? 'bg-green-500' : 'bg-orange-500'"
                 :title="attribute.value ? 'Заполнено' : 'Не заполнено'">
            </div>
          </div>
          
          <!-- Название и группа -->
          <div class="flex-shrink-0 w-48">
            <div class="font-medium text-surface-900 dark:text-surface-0 text-sm">
              {{ attribute.templateTitle || 'Без названия' }}
            </div>
            <VTag 
              v-if="attribute.templateGroup" 
              :value="attribute.templateGroup" 
              severity="secondary" 
              size="small" 
              class="mt-1"
            />
          </div>
          
          <!-- Поле ввода значения -->
          <div class="flex-1">
            <AttributeValueInput
              :template="getTemplateForInput(attribute)"
              :model-value="attribute.value"
              @update:model-value="updateAttributeValue(index, $event)"
              class="w-full"
              :placeholder="getPlaceholder(attribute)"
            />
          </div>
          
          <!-- Единица измерения -->
          <div class="flex-shrink-0 w-16 text-center">
            <span v-if="attribute.templateUnit" class="text-sm text-surface-500 font-medium">
              {{ getUnitLabel(attribute.templateUnit) }}
            </span>
          </div>
          
          <!-- Кнопки действий -->
          <div class="flex-shrink-0 flex gap-2">
            <!-- <VButton
              @click="editAttribute(attribute)"
              severity="secondary"
              size="small"
              outlined
            >
              <PencilIcon class="h-4 w-4" />
            </VButton> -->
            <DangerButton
              @click="removeAttribute(index)"
              severity="danger"
              size="small"
              outlined
              class="hover:bg-red-50 dark:hover:bg-red-900/20"
            >
              <TrashIcon class="h-4 w-4" />
            </DangerButton>
          </div>
        </div>
        </div>
      </div>
    </div>

    <div v-else class="text-center py-8 text-surface-500 border-2 border-dashed border-surface-200 dark:border-surface-700 rounded-lg">
      <i class="pi pi-plus-circle text-3xl mb-2 block"></i>
      <p>Добавьте атрибуты для описания характеристик запчасти</p>
      <p class="text-sm mt-1">Выберите группу шаблонов или отдельный шаблон выше</p>
    </div>

    <!-- Диалог добавления/редактирования атрибута -->
    <VDialog
      v-model:visible="showAddDialog"
      modal
      :header="editingAttribute ? 'Редактировать атрибут' : 'Добавить атрибут'"
      :style="{ width: '50rem' }"
      :breakpoints="{ '1199px': '75vw', '575px': '90vw' }"
    >
      <div class="space-y-4">
        <!-- Поиск шаблона (только при добавлении) -->
        <div v-if="!editingAttribute">
          <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
            Шаблон атрибута *
          </label>
          <VAutoComplete
            v-model="selectedTemplate"
            :suggestions="templateSuggestions"
            @complete="filterTemplates"
            option-label="title"
            placeholder="Поиск шаблона..."
            class="w-full"
            dropdown
          >
            <template #option="slotProps">
              <div class="flex items-center gap-3">
                <i :class="getDataTypeIcon(slotProps.option.dataType)" class="text-primary"></i>
                <div class="flex-1">
                  <div class="font-medium">{{ slotProps.option.title }}</div>
                  <div class="text-sm text-surface-600 dark:text-surface-400">
                    {{ slotProps.option.name }}
                    <span v-if="slotProps.option.group?.name" class="ml-2 px-2 py-1 bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 rounded text-xs">
                      {{ slotProps.option.group.name }}
                    </span>
                  </div>
                </div>
                <VTag :value="getDataTypeLabel(slotProps.option.dataType)" severity="info" size="small" />
              </div>
            </template>
          </VAutoComplete>
        </div>

        <!-- Информация о шаблоне при редактировании -->
        <div v-else class="p-4 bg-surface-50 dark:bg-surface-900 rounded-lg">
          <div class="flex items-center gap-2 mb-2">
            <i :class="getDataTypeIcon(editingAttribute.template?.dataType)" class="text-primary"></i>
            <span class="font-medium">{{ editingAttribute.template?.title }}</span>
            <VTag :value="getDataTypeLabel(editingAttribute.template?.dataType)" severity="info" size="small" />
          </div>
          <div class="text-sm text-surface-600 dark:text-surface-400">
            {{ editingAttribute.template?.description }}
          </div>
        </div>

        <!-- Ввод значения -->
        <div v-if="selectedTemplate || editingAttribute">
          <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
            Значение *
          </label>
          <AttributeValueInput
            v-model="newAttributeValue"
            :template="selectedTemplate || editingAttribute?.template"
            ref="valueInputRef"
            @change="handleValueChange"
          />
        </div>
      </div>

      <template #footer>
        <VButton
          label="Отмена"
          severity="secondary"
          @click="closeAddDialog"
        />
        <VButton
          :label="editingAttribute ? 'Сохранить' : 'Добавить'"
          @click="saveAttribute"
          :disabled="!canSave"
        />
      </template>
    </VDialog>

    <!-- Диалог выбора группы шаблонов -->
    <VDialog
      v-model:visible="showGroupDialog"
      modal
      header="Выбор группы шаблонов"
      :style="{ width: '40rem' }"
    >
      <div class="space-y-4">
        <div v-for="group in templateGroups" :key="group.id" class="border border-surface-200 dark:border-surface-700 rounded-lg p-4">
          <div class="flex items-center justify-between">
            <div>
              <h4 class="font-medium text-surface-900 dark:text-surface-0">{{ group.name }}</h4>
              <p class="text-sm text-surface-600 dark:text-surface-400">{{ group.description }}</p>
              <small class="text-surface-500">{{ group._count?.templates || 0 }} шаблонов</small>
            </div>
            <VButton
              @click="loadTemplatesByGroupId(group.id)"
              size="small"
              :loading="loadingTemplates"
            >
              Добавить все
            </VButton>
          </div>
        </div>
      </div>
    </VDialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useTrpc } from '@/composables/useTrpc';
import VCard from '@/volt/Card.vue';
import VButton from '@/volt/Button.vue';
import VAutoComplete from '@/volt/AutoComplete.vue';
import VTag from '@/volt/Tag.vue';
import VDialog from '@/volt/Dialog.vue';
import AttributeValueInput from '@/components/admin/attributes/AttributeValueInput.vue';
import Icon from '@/components/ui/Icon.vue';
import DangerButton from '@/volt/DangerButton.vue';
import { TrashIcon } from 'lucide-vue-next';
import { PencilIcon } from 'lucide-vue-next';

// Интерфейсы
interface AttributeTemplate {
  id: number;
  name: string;
  title: string;
  description?: string;
  dataType: 'STRING' | 'NUMBER' | 'BOOLEAN' | 'DATE' | 'JSON';
  unit?: string;
  isRequired: boolean;
  minValue?: number;
  maxValue?: number;
  allowedValues?: string[];
  group?: {
    id: number;
    name: string;
  };
}
interface AttributeForm {
  id?: number;
  templateId: number;
  value: string;
  template?: any;
  templateTitle?: string;
  templateDataType?: string;
  templateUnit?: string;
  templateGroup?: string;
  templateDescription?: string;
}

// Props
interface Props {
  modelValue: AttributeForm[];
  entityId?: number; // ID сущности для загрузки существующих атрибутов
  title?: string;
  description?: string;
  showGroupSelector?: boolean;
  groupByTemplate?: boolean;
  cardMode?: 'compact' | 'detailed';
}

interface Emits {
  (e: 'update:modelValue', value: AttributeForm[]): void;
}

const props = withDefaults(defineProps<Props>(), {
  title: 'Атрибуты запчасти',
  description: '',
  showGroupSelector: true,
  groupByTemplate: false,
  cardMode: 'detailed'
});
const emit = defineEmits<Emits>();

// tRPC клиент
const { client, partAttributes, attributeTemplates } = useTrpc();

// Локальное состояние
const selectedTemplateGroup = ref<any>(null);
const selectedTemplate = ref<any>(null);
const groupSuggestions = ref<any[]>([]);
const templateSuggestions = ref<any[]>([]);
const loadingTemplates = ref(false);

// Новые состояния для диалогов
const showAddDialog = ref(false);
const showGroupDialog = ref(false);
const editingAttribute = ref<AttributeForm | null>(null);
const newAttributeValue = ref('');
const valueInputRef = ref<any>(null);
const templateGroups = ref<any[]>([]);

// Computed для v-model
const modelValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

// Подсчет заполненных атрибутов
const filledAttributesCount = computed(() => {
  return modelValue.value.filter(attr => attr.value && String(attr.value).trim()).length;
});

// Группировка атрибутов по группам шаблонов
const groupedAttributes = computed(() => {
  if (!props.groupByTemplate) return {};

  return modelValue.value.reduce((groups: Record<string, AttributeForm[]>, attribute) => {
    const groupName = attribute.template?.group?.name || attribute.templateGroup || 'undefined';
    if (!groups[groupName]) {
      groups[groupName] = [];
    }
    groups[groupName].push(attribute);
    return groups;
  }, {});
});

// Проверка возможности сохранения
const canSave = computed(() => {
  if (!selectedTemplate.value && !editingAttribute.value) return false;

  // Проверяем значение в зависимости от типа данных
  const template = selectedTemplate.value || editingAttribute.value?.template;
  if (!template) return false;

  const value = newAttributeValue.value;

  // Для булевых значений разрешаем false
  if (template.dataType === 'BOOLEAN') {
    return value !== null && value !== undefined;
  }

  // Для остальных типов проверяем, что значение не пустое
  if (value === null || value === undefined || value === '') return false;

  // Для строк проверяем, что не только пробелы
  if (template.dataType === 'STRING' && value.toString().trim() === '') return false;

  return true;
});

// Методы
const updateAttributeValue = (index: number, value: any) => {
  const newValue = [...props.modelValue];
  newValue[index].value = value;
  emit('update:modelValue', newValue);
};

const removeAttribute = async (index: number) => {
  const attribute = props.modelValue[index];

  // Если атрибут уже сохранен в БД (имеет id), удаляем его через API
  if (attribute.id) {
    try {
      const result = await partAttributes.delete({ id: attribute.id });
      console.log('Атрибут успешно удален из БД:', attribute.id, result);
    } catch (error) {
      console.error('Ошибка удаления атрибута:', error);
      // Можно показать уведомление об ошибке
      return;
    }
  }

  // Удаляем из локального состояния
  const newValue = [...props.modelValue];
  newValue.splice(index, 1);
  emit('update:modelValue', newValue);
};

// Создает объект template для AttributeValueInput
const getTemplateForInput = (attribute: AttributeForm | null): AttributeTemplate => {
  if (!attribute) {
    return {
      id: 0,
      name: '',
      title: '',
      description: '',
      dataType: 'STRING',
      unit: undefined,
      isRequired: false,
      minValue: undefined,
      maxValue: undefined,
      allowedValues: [],
    };
  }

  return {
    id: attribute.templateId,
    name: attribute.template?.name || '',
    title: attribute.templateTitle || '',
    description: attribute.templateDescription || '',
    dataType: (attribute.templateDataType as 'STRING' | 'NUMBER' | 'BOOLEAN' | 'DATE' | 'JSON') || 'STRING',
    unit: attribute.templateUnit,
    isRequired: attribute.template?.isRequired || false,
    minValue: attribute.template?.minValue,
    maxValue: attribute.template?.maxValue,
    allowedValues: attribute.template?.allowedValues || [],
    group: attribute.template?.group
  };
};

// Добавление одного шаблона
const addSingleTemplate = () => {
  if (!selectedTemplate.value) return;

  const template = selectedTemplate.value;
  const attribute: AttributeForm = {
    templateId: template.id,
    value: "",
    template: template,
    templateTitle: template.title,
    templateDataType: template.dataType,
    templateUnit: template.unit,
    templateGroup: template.group?.name,
    templateDescription: template.description,
  };

  emit('update:modelValue', [...props.modelValue, attribute]);
  selectedTemplate.value = null;
};

// Поиск групп шаблонов
const filterGroups = async (event: any) => {
  const query = event.query.toLowerCase();
  try {
    const groups = await attributeTemplates.findAllGroups();

    if (groups && Array.isArray(groups)) {
      // Фильтруем группы по запросу
      const filteredGroups = groups.filter((group: any) =>
        group.name.toLowerCase().includes(query)
      ).slice(0, 10);
      groupSuggestions.value = filteredGroups;
    }
  } catch (error) {
    console.error('Ошибка поиска групп:', error);
    groupSuggestions.value = [];
  }
};

// Загрузка шаблонов выбранной группы
const loadSelectedGroupTemplates = async () => {
  if (!selectedTemplateGroup.value) return;

  loadingTemplates.value = true;
  try {
    const groupId = selectedTemplateGroup.value.id || selectedTemplateGroup.value;
    const result = await attributeTemplates.findMany({ groupId });

    if (result && typeof result === 'object' && (result as any).templates) {
      const templates = (result as any).templates;
      // Добавляем шаблоны как новые атрибуты
      const newAttributes = templates.map((template: any) => ({
        templateId: template.id,
        value: "",
        template: template,
        templateTitle: template.title,
        templateDataType: template.dataType,
        templateUnit: template.unit,
        templateGroup: template.group?.name,
        templateDescription: template.description,
      }));

      emit('update:modelValue', [...props.modelValue, ...newAttributes]);
      selectedTemplateGroup.value = null;
    }
  } catch (error) {
    console.error('Ошибка загрузки шаблонов:', error);
  } finally {
    loadingTemplates.value = false;
  }
};

// Поиск шаблонов
const filterTemplates = async (event: any) => {
  const query = event.query.toLowerCase();
  try {
    const result = await attributeTemplates.findMany({
      search: query,
      limit: 10
    });

    if (result && Array.isArray((result as any).templates)) {
      templateSuggestions.value = (result as any).templates;
    } else {
      templateSuggestions.value = [];
    }
  } catch (error) {
    console.error('Ошибка поиска шаблонов:', error);
    templateSuggestions.value = [];
  }
};

// Утилиты
const getDataTypeIcon = (dataType: string | undefined) => {
  const icons: Record<string, string> = {
    STRING: 'pi pi-font',
    NUMBER: 'pi pi-hashtag',
    BOOLEAN: 'pi pi-check-square',
    DATE: 'pi pi-calendar',
    JSON: 'pi pi-code',
  };
  return icons[dataType || ''] || 'pi pi-question';
};

const getDataTypeLabel = (dataType: string | undefined) => {
  if (!dataType) return "";
  const labels: Record<string, string> = {
    STRING: "Строка",
    NUMBER: "Число",
    BOOLEAN: "Логическое",
    DATE: "Дата",
    JSON: "JSON",
  };
  return labels[dataType] || dataType;
};

const getUnitLabel = (unit: string | undefined) => {
  if (!unit) return "";
  const labels: Record<string, string> = {
    MM: "мм",
    INCH: "дюймы",
    FT: "футы",
    G: "г",
    KG: "кг",
    T: "т",
    LB: "фунты",
    ML: "мл",
    L: "л",
    GAL: "галлоны",
    PCS: "шт",
    SET: "комплект",
    PAIR: "пара",
    BAR: "бар",
    PSI: "PSI",
    KW: "кВт",
    HP: "л.с.",
    NM: "Н⋅м",
    RPM: "об/мин",
    C: "°C",
    F: "°F",
    PERCENT: "%",
  };
  return labels[unit] || unit;
};

const getPlaceholder = (attribute: AttributeForm) => {
  const dataType = attribute.templateDataType;
  const unit = attribute.templateUnit;

  switch (dataType) {
    case 'STRING':
      return 'Введите текст...';
    case 'NUMBER':
      return unit ? `Введите число (${getUnitLabel(unit)})...` : 'Введите число...';
    case 'BOOLEAN':
      return 'Выберите значение...';
    case 'DATE':
      return 'Выберите дату...';
    default:
      return 'Введите значение...';
  }
};

// Новые функции для работы с диалогами
const editAttribute = (attribute: AttributeForm) => {
  editingAttribute.value = attribute;
  newAttributeValue.value = attribute.value || '';
  showAddDialog.value = true;
};

const closeAddDialog = () => {
  showAddDialog.value = false;
  selectedTemplate.value = null;
  newAttributeValue.value = '';
  editingAttribute.value = null;
};

const handleValueChange = (value: any) => {
  newAttributeValue.value = value;
};

// Получение индекса атрибута в массиве
const getAttributeIndex = (attribute: AttributeForm) => {
  return props.modelValue.findIndex(attr => {
    if (attr.id && attribute.id) {
      return attr.id === attribute.id;
    }
    return attr.templateId === attribute.templateId;
  });
};

// Удаление атрибута по объекту
const removeAttributeByObject = async (attribute: AttributeForm) => {
  const index = getAttributeIndex(attribute);
  if (index !== -1) {
    await removeAttribute(index);
  }
};

// Сохранение атрибута
const saveAttribute = async () => {
  // Проверяем, что есть значение в зависимости от типа данных
  const template = selectedTemplate.value || editingAttribute.value?.template;
  if (!template) return;

  const value = newAttributeValue.value;

  // Для булевых значений разрешаем false
  if (template.dataType === 'BOOLEAN') {
    if (value === null || value === undefined) return;
  } else {
    // Для остальных типов проверяем, что значение не пустое
    if (value === null || value === undefined || value === '') return;

    // Для строк проверяем, что не только пробелы
    if (template.dataType === 'STRING' && value.toString().trim() === '') return;
  }

  // Валидация через компонент ввода
  if (valueInputRef.value && !valueInputRef.value.validate()) {
    return;
  }

  if (editingAttribute.value) {
    // Обновляем существующий атрибут
    if (editingAttribute.value.id && props.entityId) {
      // Сохраняем в базу данных
      try {
        await partAttributes.update({
          id: editingAttribute.value.id,
          value: newAttributeValue.value
        });
      } catch (error) {
        console.error('Ошибка обновления атрибута:', error);
        return;
      }
    }
    editingAttribute.value.value = newAttributeValue.value;
  } else if (selectedTemplate.value) {
    // Извлекаем ID из selectedTemplate
    const templateId = typeof selectedTemplate.value === 'object'
      ? selectedTemplate.value.id
      : selectedTemplate.value;

    // Проверяем, есть ли уже атрибут с таким шаблоном
    const existingAttribute = modelValue.value.find(
      attr => attr.templateId === templateId
    );

    if (existingAttribute) {
      // Обновляем существующий атрибут
      if (existingAttribute.id && props.entityId) {
        try {
          await partAttributes.update({
            id: existingAttribute.id,
            value: newAttributeValue.value
          });
        } catch (error) {
          console.error('Ошибка обновления атрибута:', error);
          return;
        }
      }
      existingAttribute.value = newAttributeValue.value;
    } else {
      // Добавляем новый атрибут
      const newAttribute: AttributeForm = {
        templateId: templateId,
        value: newAttributeValue.value,
        template: selectedTemplate.value
      };

      // Сохраняем в базу данных если есть entityId
      if (props.entityId) {
        try {
          const savedAttribute = await partAttributes.create({
            partId: props.entityId,
            templateId: templateId,
            value: newAttributeValue.value
          });

          if (savedAttribute && typeof savedAttribute === 'object' && 'id' in savedAttribute) {
            newAttribute.id = (savedAttribute as any).id;
          }
        } catch (error) {
          console.error('Ошибка создания атрибута:', error);
          return;
        }
      }

      emit('update:modelValue', [...props.modelValue, newAttribute]);
    }
  }

  closeAddDialog();
};

// Загрузка групп шаблонов
const loadTemplateGroups = async () => {
  try {
    const groups = await attributeTemplates.findAllGroups();
    if (groups && Array.isArray(groups)) {
      groupSuggestions.value = groups;
      templateGroups.value = groups;
    }
  } catch (error) {
    console.error('Ошибка загрузки групп шаблонов:', error);
  }
};

// Загрузка существующих атрибутов для сущности
const loadExistingAttributes = async () => {
  if (!props.entityId) return;

  try {
    const existingAttributes = await partAttributes.findByPartId({ partId: props.entityId });
    if (existingAttributes && Array.isArray(existingAttributes)) {
      // Преобразуем существующие атрибуты в формат компонента
      const formattedAttributes = existingAttributes.map((attr: any) => ({
        id: attr.id,
        templateId: attr.templateId,
        value: attr.value,
        template: attr.template,
        templateTitle: attr.template?.title,
        templateDataType: attr.template?.dataType,
        templateUnit: attr.template?.unit,
        templateGroup: attr.template?.group?.name,
        templateDescription: attr.template?.description,
      }));

      // Объединяем с уже существующими атрибутами (если есть)
      const existingTemplateIds = new Set(props.modelValue.map(a => a.templateId));
      const newAttributes = formattedAttributes.filter(attr => !existingTemplateIds.has(attr.templateId));

      emit('update:modelValue', [...props.modelValue, ...newAttributes]);
    }
  } catch (error) {
    console.error('Ошибка загрузки существующих атрибутов:', error);
  }
};

// Загрузка шаблонов по ID группы
const loadTemplatesByGroupId = async (groupId: number) => {
  try {
    loadingTemplates.value = true;
    const result = await attributeTemplates.findMany({
      groupId: groupId,
      limit: 100
    });

    if (result && typeof result === "object") {
      // API возвращает объект { templates: [...], total: number }
      const templates = (result as any).templates || [];

      // Добавляем шаблоны из группы как новые атрибуты
      const newAttributes = templates
        .filter((template: any) => !props.modelValue.some(attr => attr.templateId === template.id))
        .map((template: any) => ({
          templateId: template.id,
          value: '',
          template: template,
          templateTitle: template.title,
          templateDataType: template.dataType,
          templateUnit: template.unit,
          templateGroup: template.group?.name,
          templateDescription: template.description,
        }));

      emit('update:modelValue', [...props.modelValue, ...newAttributes]);

      console.log(`Загружено ${newAttributes.length} новых шаблонов из группы`);

      // Закрываем диалог после успешной загрузки
      showGroupDialog.value = false;
    }
  } catch (error) {
    console.error('Ошибка загрузки шаблонов группы:', error);
  } finally {
    loadingTemplates.value = false;
  }
};

// Инициализация при монтировании
onMounted(() => {
  loadTemplateGroups();
  loadExistingAttributes();
});

// Перезагрузка атрибутов при изменении entityId
watch(() => props.entityId, () => {
  if (props.entityId) {
    loadExistingAttributes();
  }
});

// Сброс диалога при закрытии
watch(showAddDialog, (newValue) => {
  if (!newValue) {
    selectedTemplate.value = null;
    newAttributeValue.value = '';
    editingAttribute.value = null;
  }
});

// Отслеживаем изменения значения для обновления состояния кнопки
watch(newAttributeValue, (newValue) => {
  // Принудительно обновляем реактивность
  if (newValue !== undefined) {
    // Это заставит Vue пересчитать computed свойство canSave
  }
});
</script>
