<template>
  <VCard>
    <template #content>
      <VDataTable
        :value="items"
        :loading="loading"
        :paginator="true"
        :rows="rows"
        :total-records="totalRecords"
        :first="first"
        :lazy="true"
        :sortable="true"
        paginator-template="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
        current-page-report-template="Показано {first} - {last} из {totalRecords} записей"
        :rows-per-page-options="[10, 20, 50]"
        @page="$emit('page', $event)"
        @sort="$emit('sort', $event)"
      >
        <!-- Колонка SKU -->
        <VColumn field="sku" header="Артикул" :sortable="true" class="min-w-32">
          <template #body="{ data }">
            <div class="flex items-center gap-2">
              <span class="font-mono font-medium">{{ data.sku }}</span>
              <VTag v-if="!data.isPublic" value="Приватная" severity="warning" size="small" />
            </div>
          </template>
        </VColumn>

        <!-- Колонка Бренд -->
        <VColumn field="brand.name" header="Бренд" :sortable="true" class="min-w-32">
          <template #body="{ data }">
            <div class="flex items-center gap-2">
              <span class="font-medium">{{ data.brand?.name || 'Не указан' }}</span>
              <VTag v-if="data.brand?.isOem" value="OEM" severity="info" size="small" />
            </div>
          </template>
        </VColumn>

        <!-- Колонка Описание -->
        <VColumn field="description" header="Описание" class="min-w-48">
          <template #body="{ data }">
            <div class="max-w-xs">
              <p v-if="data.description" class="text-surface-600 dark:text-surface-400 truncate text-sm" :title="data.description">
                {{ data.description }}
              </p>
              <span v-else class="text-surface-400 italic">Нет описания</span>
            </div>
          </template>
        </VColumn>

        <!-- Колонка Атрибуты -->
        <VColumn header="Атрибуты" class="min-w-32">
          <template #body="{ data }">
            <div class="flex flex-wrap gap-1">
              <VTag
                v-for="attr in data.attributes?.slice(0, 3)"
                :key="attr.id"
                :value="`${attr.template?.title}: ${attr.value}`"
                severity="secondary"
                size="small"
                class="text-xs"
              />
              <VTag v-if="data.attributes?.length > 3" :value="`+${data.attributes.length - 3}`" severity="secondary" size="small" class="text-xs" />
            </div>
          </template>
        </VColumn>

        <!-- Колонка Применимость -->
        <VColumn header="Применимость" class="min-w-24">
          <template #body="{ data }">
            <div class="text-center">
              <VTag v-if="data.applicabilities?.length > 0" :value="`${data.applicabilities.length} групп`" severity="success" size="small" />
              <span v-else class="text-surface-400 text-sm">Не назначена</span>
            </div>
          </template>
        </VColumn>

        <!-- Колонка Источник -->
        <VColumn field="source" header="Источник" class="min-w-32">
          <template #body="{ data }">
            <span v-if="data.source" class="text-surface-600 dark:text-surface-400 text-sm">
              {{ data.source }}
            </span>
            <span v-else class="text-surface-400 italic">Не указан</span>
          </template>
        </VColumn>

        <!-- Колонка ID -->
        <VColumn field="id" header="ID" :sortable="true" class="min-w-20">
          <template #body="{ data }">
            <span class="text-surface-600 dark:text-surface-400 font-mono text-sm"> #{{ data.id }} </span>
          </template>
        </VColumn>

        <!-- Колонка Действия -->
        <VColumn header="Действия" class="min-w-32">
          <template #body="{ data }">
            <div class="flex gap-1">
              <VButton @click="$emit('match', data)" severity="secondary" size="small" text v-tooltip="'Найти группу взаимозаменяемости'">
                <LinkIcon class="w-5 h-5" />
              </VButton>
              <VButton @click="$emit('view-details', data)" severity="secondary" size="small" text>
                <ScanEyeIcon class="w-5 h-5" />
              </VButton>
              <VButton @click="$emit('edit', data)" severity="secondary" size="small" text>
                <PencilIcon class="w-5 h-5" />
              </VButton>
              <VButton @click="$emit('delete', data)" severity="danger" size="small" text>
                <TrashIcon class="w-5 h-5" />
              </VButton>
            </div>
          </template>
        </VColumn>

        <!-- Пустое состояние -->
        <template #empty>
          <div class="py-8 text-center">
            <Icon name="pi pi-inbox" class="text-surface-300 dark:text-surface-600 mb-4 inline-block text-4xl" />
            <p class="text-surface-500 dark:text-surface-400 mb-2 text-lg">Каталожные позиции не найдены</p>
            <p class="text-surface-400 dark:text-surface-500 text-sm">Попробуйте изменить параметры поиска или создайте новую позицию</p>
          </div>
        </template>

        <!-- Состояние загрузки -->
        <template #loading>
          <div class="py-8 text-center">
            <VProgressSpinner size="50" />
            <p class="text-surface-500 dark:text-surface-400 mt-4">Загрузка каталожных позиций...</p>
          </div>
        </template>
      </VDataTable>
    </template>
  </VCard>
</template>

<script setup lang="ts">
import VCard from '@/volt/Card.vue'
import VDataTable from '@/volt/DataTable.vue'
import VColumn from 'primevue/column'
import VButton from '@/volt/Button.vue'
import VTag from '@/volt/Tag.vue'
import VProgressSpinner from 'primevue/progressspinner'
import { PencilIcon, ScanEyeIcon, TrashIcon } from 'lucide-vue-next'
import Icon from '@/components/ui/Icon.vue'
import { LinkIcon } from 'lucide-vue-next'

// Props
interface Props {
  items: any[]
  loading?: boolean
  totalRecords: number
  rows: number
  first: number
}

defineProps<Props>()

// Emits
interface Emits {
  (e: 'page', event: any): void
  (e: 'sort', event: any): void
  (e: 'edit', item: any): void
  (e: 'delete', item: any): void
  (e: 'view-details', item: any): void
  (e: 'match', item: any): void
}

defineEmits<Emits>()

// Методы
const formatDate = (dateString: string) => {
  if (!dateString) return ''

  const date = new Date(dateString)
  return new Intl.DateTimeFormat('ru-RU', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}
</script>
