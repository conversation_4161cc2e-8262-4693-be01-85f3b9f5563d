import{j as i}from"./jsx-runtime.D_zvdyIk.js";import{r as s}from"./index.0yr9KlQE.js";import{u as z}from"./index.BWNh2K4G.js";import{d as U,c as G,b as K,P as y,u as W,a as N}from"./index.Di12BYGB.js";import{c as j}from"./utils.CBfrqCZ4.js";import{c as X}from"./createLucideIcon.DdiNmGRb.js";/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],V=X("check",$);function J(e){const r=s.useRef({value:e,previous:e});return s.useMemo(()=>(r.current.value!==e&&(r.current.previous=r.current.value,r.current.value=e),r.current.previous),[e])}function Q(e){const[r,o]=s.useState(void 0);return U(()=>{if(e){o({width:e.offsetWidth,height:e.offsetHeight});const a=new ResizeObserver(n=>{if(!Array.isArray(n)||!n.length)return;const t=n[0];let u,c;if("borderBoxSize"in t){const d=t.borderBoxSize,p=Array.isArray(d)?d[0]:d;u=p.inlineSize,c=p.blockSize}else u=e.offsetWidth,c=e.offsetHeight;o({width:u,height:c})});return a.observe(e,{box:"border-box"}),()=>a.unobserve(e)}else o(void 0)},[e]),r}var E="Checkbox",[Y,fe]=G(E),[Z,I]=Y(E);function ee(e){const{__scopeCheckbox:r,checked:o,children:a,defaultChecked:n,disabled:t,form:u,name:c,onCheckedChange:d,required:p,value:x="on",internal_do_not_use_render:h}=e,[b,C]=W({prop:o,defaultProp:n??!1,onChange:d,caller:E}),[v,m]=s.useState(null),[g,l]=s.useState(null),f=s.useRef(!1),S=v?!!u||!!v.closest("form"):!0,R={checked:b,disabled:t,setChecked:C,control:v,setControl:m,name:c,form:u,value:x,hasConsumerStoppedPropagationRef:f,required:p,defaultChecked:k(n)?!1:n,isFormControl:S,bubbleInput:g,setBubbleInput:l};return i.jsx(Z,{scope:r,...R,children:te(h)?h(R):a})}var w="CheckboxTrigger",A=s.forwardRef(({__scopeCheckbox:e,onKeyDown:r,onClick:o,...a},n)=>{const{control:t,value:u,disabled:c,checked:d,required:p,setControl:x,setChecked:h,hasConsumerStoppedPropagationRef:b,isFormControl:C,bubbleInput:v}=I(w,e),m=z(n,x),g=s.useRef(d);return s.useEffect(()=>{const l=t?.form;if(l){const f=()=>h(g.current);return l.addEventListener("reset",f),()=>l.removeEventListener("reset",f)}},[t,h]),i.jsx(y.button,{type:"button",role:"checkbox","aria-checked":k(d)?"mixed":d,"aria-required":p,"data-state":q(d),"data-disabled":c?"":void 0,disabled:c,value:u,...a,ref:m,onKeyDown:N(r,l=>{l.key==="Enter"&&l.preventDefault()}),onClick:N(o,l=>{h(f=>k(f)?!0:!f),v&&C&&(b.current=l.isPropagationStopped(),b.current||l.stopPropagation())})})});A.displayName=w;var O=s.forwardRef((e,r)=>{const{__scopeCheckbox:o,name:a,checked:n,defaultChecked:t,required:u,disabled:c,value:d,onCheckedChange:p,form:x,...h}=e;return i.jsx(ee,{__scopeCheckbox:o,checked:n,defaultChecked:t,disabled:c,required:u,onCheckedChange:p,name:a,form:x,value:d,internal_do_not_use_render:({isFormControl:b})=>i.jsxs(i.Fragment,{children:[i.jsx(A,{...h,ref:r,__scopeCheckbox:o}),b&&i.jsx(L,{__scopeCheckbox:o})]})})});O.displayName=E;var T="CheckboxIndicator",B=s.forwardRef((e,r)=>{const{__scopeCheckbox:o,forceMount:a,...n}=e,t=I(T,o);return i.jsx(K,{present:a||k(t.checked)||t.checked===!0,children:i.jsx(y.span,{"data-state":q(t.checked),"data-disabled":t.disabled?"":void 0,...n,ref:r,style:{pointerEvents:"none",...e.style}})})});B.displayName=T;var M="CheckboxBubbleInput",L=s.forwardRef(({__scopeCheckbox:e,...r},o)=>{const{control:a,hasConsumerStoppedPropagationRef:n,checked:t,defaultChecked:u,required:c,disabled:d,name:p,value:x,form:h,bubbleInput:b,setBubbleInput:C}=I(M,e),v=z(o,C),m=J(t),g=Q(a);s.useEffect(()=>{const f=b;if(!f)return;const S=window.HTMLInputElement.prototype,_=Object.getOwnPropertyDescriptor(S,"checked").set,F=!n.current;if(m!==t&&_){const H=new Event("click",{bubbles:F});f.indeterminate=k(t),_.call(f,k(t)?!1:t),f.dispatchEvent(H)}},[b,m,t,n]);const l=s.useRef(k(t)?!1:t);return i.jsx(y.input,{type:"checkbox","aria-hidden":!0,defaultChecked:u??l.current,required:c,disabled:d,name:p,value:x,form:h,...r,tabIndex:-1,ref:v,style:{...r.style,...g,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});L.displayName=M;function te(e){return typeof e=="function"}function k(e){return e==="indeterminate"}function q(e){return k(e)?"indeterminate":e?"checked":"unchecked"}function pe({className:e,...r}){return i.jsx(O,{"data-slot":"checkbox",className:j("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...r,children:i.jsx(B,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:i.jsx(V,{className:"size-3.5"})})})}var re="Separator",P="horizontal",oe=["horizontal","vertical"],D=s.forwardRef((e,r)=>{const{decorative:o,orientation:a=P,...n}=e,t=ne(a)?a:P,c=o?{role:"none"}:{"aria-orientation":t==="vertical"?t:void 0,role:"separator"};return i.jsx(y.div,{"data-orientation":t,...c,...n,ref:r})});D.displayName=re;function ne(e){return oe.includes(e)}var ae=D;function he({className:e,orientation:r="horizontal",decorative:o=!0,...a}){return i.jsx(ae,{"data-slot":"separator",decorative:o,orientation:r,className:j("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",e),...a})}export{pe as C,he as S,J as a,Q as u};
