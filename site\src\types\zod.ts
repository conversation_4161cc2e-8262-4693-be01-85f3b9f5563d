import { z } from 'zod';

// Generated Zod schemas from backend (ZenStack)
export { ApplicabilityAccuracySchema } from '../../../api/generated/zod/enums/ApplicabilityAccuracy.schema';
export { AttributeDataTypeSchema } from '../../../api/generated/zod/enums/AttributeDataType.schema';
export { AttributeUnitSchema } from '../../../api/generated/zod/enums/AttributeUnit.schema';

export { PartSchema } from '../../../api/generated/zod/models/Part.schema';
export { PartCategorySchema } from '../../../api/generated/zod/models/PartCategory.schema';
export { BrandSchema } from '../../../api/generated/zod/models/Brand.schema';
export { CatalogItemSchema } from '../../../api/generated/zod/models/CatalogItem.schema';
export { EquipmentModelSchema } from '../../../api/generated/zod/models/EquipmentModel.schema';

// Strong TS types inferred from schemas
export type Part = z.infer<typeof PartSchema>;
export type PartCategory = z.infer<typeof PartCategorySchema>;
export type Brand = z.infer<typeof BrandSchema>;
export type CatalogItem = z.infer<typeof CatalogItemSchema>;
export type EquipmentModel = z.infer<typeof EquipmentModelSchema>;

export type ApplicabilityAccuracy = z.infer<typeof ApplicabilityAccuracySchema>;
export type AttributeDataType = z.infer<typeof AttributeDataTypeSchema>;
export type AttributeUnit = z.infer<typeof AttributeUnitSchema>;

