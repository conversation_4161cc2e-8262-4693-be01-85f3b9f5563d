# Документ требований

## Введение

PartTec3 - это профессиональная система каталогизации запчастей, предназначенная для создания базы данных взаимозаменяемых деталей от различных производителей. Клиентский интерфейс должен предоставлять полную функциональность для различных ролей пользователей (GUEST, USER, SHOP, ADMIN) для поиска, просмотра и управления данными о запчастях. Система должна использовать существующую архитектуру Vue Islands с компонентами Volt UI и поддерживать принципы чистого кода без дублирования.

## Требования

### Требование 1: Публичный интерфейс каталога запчастей

**Пользовательская история:** Как гость или зарегистрированный пользователь, я хочу просматривать и искать в каталоге запчастей, чтобы найти совместимые детали для моего оборудования.

#### Критерии приемки

1. КОГДА пользователь посещает главную страницу каталога ТО система ДОЛЖНА отображать список запчастей с возможностью поиска и фильтрации
2. КОГДА пользователь ищет запчасти ТО система ДОЛЖНА предоставлять фильтры по категориям, брендам, атрибутам и совместимости с оборудованием
3. КОГДА пользователь просматривает страницу детали запчасти ТО система ДОЛЖНА показывать всю информацию о детали, атрибуты, совместимые каталожные позиции и применимость к оборудованию
4. КОГДА пользователь просматривает атрибуты детали ТО система ДОЛЖНА отображать их в организованных группах с правильными единицами измерения и форматированием
5. ЕСЛИ у детали есть изображение ТО система ДОЛЖНА отображать его на видном месте на странице детали
6. КОГДА пользователь просматривает взаимозаменяемые детали ТО система ДОЛЖНА показывать уровни точности (EXACT_MATCH, MATCH_WITH_NOTES и т.д.) с соответствующими визуальными индикаторами

### Требование 2: Расширенная система поиска и фильтрации

**Пользовательская история:** Как пользователь, я хочу расширенные возможности поиска с множественными фильтрами, чтобы быстро найти именно те детали, которые мне нужны.

#### Критерии приемки

1. КОГДА пользователь обращается к интерфейсу поиска ТО система ДОЛЖНА предоставлять текстовый поиск, фильтрацию по атрибутам и навигацию по категориям
2. КОГДА пользователь применяет несколько фильтров ТО система ДОЛЖНА логически их объединять и показывать количество результатов в реальном времени
3. КОГДА пользователь ищет по атрибутам деталей ТО система ДОЛЖНА поддерживать диапазонные запросы для числовых значений и сопоставление синонимов для текстовых значений
4. КОГДА пользователь ищет по модели оборудования ТО система ДОЛЖНА показывать все применимые детали с примечаниями о совместимости
5. КОГДА отображаются результаты поиска ТО система ДОЛЖНА поддерживать сортировку по релевантности, названию, бренду и другим критериям
6. ЕСЛИ результаты не найдены ТО система ДОЛЖНА предлагать альтернативные поисковые термины или более широкие критерии

### Требование 3: Интерфейс совместимости с оборудованием

**Пользовательская история:** Как пользователь, я хочу находить детали по модели оборудования, чтобы обеспечить совместимость с моей конкретной техникой.

#### Критерии приемки

1. КОГДА пользователь просматривает модели оборудования ТО система ДОЛЖНА отображать их, организованные по брендам и категориям
2. КОГДА пользователь выбирает модель оборудования ТО система ДОЛЖНА показывать все применимые детали с деталями совместимости
3. КОГДА просматривается применимость к оборудованию ТО система ДОЛЖНА отображать любые примечания или условия использования деталей
4. КОГДА пользователь просматривает атрибуты оборудования ТО система ДОЛЖНА показывать технические характеристики в организованных группах
5. ЕСЛИ у оборудования есть связанные модели ТО система ДОЛЖНА предлагать похожее или связанное оборудование

### Требование 4: Интерактивная визуализация схем

**Пользовательская история:** Как пользователь, я хочу просматривать интерактивные диаграммы узлов оборудования, чтобы понимать взаимосвязи и расположение деталей.

#### Критерии приемки

1. КОГДА пользователь просматривает схему агрегата ТО система ДОЛЖНА отображать интерактивную SVG-диаграмму
2. КОГДА пользователь кликает на деталь в схеме ТО система ДОЛЖНА выделить её и показать детали детали
3. КОГДА просматриваются позиции схемы ТО система ДОЛЖНА четко показывать аннотации и номера деталей
4. КОГДА пользователь наводит курсор на элементы схемы ТО система ДОЛЖНА предоставлять всплывающие подсказки с информацией о деталях
5. ЕСЛИ у схемы есть несколько видов ТО система ДОЛЖНА позволять переключение между различными перспективами диаграммы

### Требование 5: Управление учетными записями пользователей

**Пользовательская история:** Как зарегистрированный пользователь, я хочу управлять своей учетной записью и настройками, чтобы настроить свой опыт и получить доступ к пользовательским функциям.

#### Критерии приемки

1. КОГДА пользователь регистрируется ТО система ДОЛЖНА собрать необходимую информацию и назначить соответствующую роль (USER/SHOP)
2. КОГДА пользователь входит в систему ТО система ДОЛЖНА аутентифицировать его и предоставить доступ к функциям на основе роли
3. КОГДА пользователь обращается к своему профилю ТО система ДОЛЖНА позволить редактирование личной информации и настроек
4. КОГДА пользователь имеет роль SHOP ТО система ДОЛЖНА предоставлять дополнительные функции для управления каталожными позициями
5. ЕСЛИ пользователь забыл пароль ТО система ДОЛЖНА предоставить безопасную функцию сброса пароля

### Требование 6: Адаптивный дизайн и доступность

**Пользовательская история:** Как пользователь на любом устройстве, я хочу, чтобы интерфейс работал безупречно, чтобы я мог получать доступ к информации о деталях с настольного компьютера, планшета или мобильного устройства.

#### Критерии приемки

1. КОГДА пользователь обращается к сайту с любого устройства ТО система ДОЛЖНА предоставлять адаптивный макет, который адаптируется к размеру экрана
2. КОГДА используются сенсорные устройства ТО система ДОЛЖНА предоставлять соответствующие сенсорные цели и жесты
3. КОГДА у пользователя есть потребности в доступности ТО система ДОЛЖНА соответствовать стандартам WCAG 2.1 AA
4. КОГДА загружается контент ТО система ДОЛЖНА предоставлять соответствующие состояния загрузки и обработку ошибок
5. КОГДА пользователь офлайн или с плохим соединением ТО система ДОЛЖНА корректно обрабатывать проблемы с сетью

### Требование 7: Интерфейс управления данными для авторизованных пользователей

**Пользовательская история:** Как пользователь SHOP, я хочу управлять каталожными позициями и предлагать соответствия деталей, чтобы вносить вклад в базу данных деталей.

#### Критерии приемки

1. КОГДА пользователь SHOP обращается к управлению данными ТО система ДОЛЖНА предоставлять интерфейсы для создания и редактирования каталожных позиций
2. КОГДА пользователь SHOP предлагает соответствия деталей ТО система ДОЛЖНА создавать предложения соответствий для рассмотрения администратором
3. КОГДА управляются каталожные позиции ТО система ДОЛЖНА валидировать данные против шаблонов атрибутов и бизнес-правил
4. КОГДА просматриваются предложения ТО система ДОЛЖНА показывать статус предложения и обратную связь администратора
5. ЕСЛИ валидация данных не удается ТО система ДОЛЖНА предоставлять четкие сообщения об ошибках и руководство

### Требование 8: Производительность и кэширование

**Пользовательская история:** Как пользователь, я хочу быстрой загрузки страниц и отзывчивых взаимодействий, чтобы эффективно просматривать и искать в каталоге.

#### Критерии приемки

1. КОГДА пользователь навигирует по сайту ТО система ДОЛЖНА загружать страницы в течение 2 секунд при стандартных соединениях
2. КОГДА отображаются большие наборы данных ТО система ДОЛЖНА реализовывать пагинацию или виртуальную прокрутку
3. КОГДА пользователи выполняют повторные поиски ТО система ДОЛЖНА соответствующим образом кэшировать результаты
4. КОГДА загружаются изображения ТО система ДОЛЖНА реализовывать ленивую загрузку и оптимизировать размеры изображений
5. КОГДА используется интерфейс поиска ТО система ДОЛЖНА использовать debounce для ввода и предоставлять мгновенную обратную связь

### Требование 9: Генерация форм и валидация

**Пользовательская история:** Как разработчик, я хочу автоматическую генерацию форм из Zod схем, чтобы формы оставались согласованными с моделями данных и уменьшали дублирование кода.

#### Критерии приемки

1. КОГДА создаются формы для ввода данных ТО система ДОЛЖНА автоматически генерировать поля форм из Zod схем
2. КОГДА валидируются данные форм ТО система ДОЛЖНА использовать те же Zod схемы для клиентской и серверной валидации
3. КОГДА отображаются ошибки валидации ТО система ДОЛЖНА показывать четкие, понятные пользователю сообщения об ошибках
4. КОГДА формы имеют сложные структуры атрибутов ТО система ДОЛЖНА обрабатывать динамические шаблоны атрибутов
5. ЕСЛИ шаблоны атрибутов изменяются ТО система ДОЛЖНА автоматически обновлять формы без изменений кода

### Требование 10: Поддержка тем и интернационализации

**Пользовательская история:** Как пользователь, я хочу использовать интерфейс на предпочитаемом языке и в предпочитаемой теме, чтобы иметь комфортный и доступный опыт.

#### Критерии приемки

1. КОГДА пользователь обращается к сайту ТО система ДОЛЖНА поддерживать светлую и темную темы с правильным контрастом
2. КОГДА переключаются темы ТО система ДОЛЖНА сохранять предпочтение и применять его последовательно
3. КОГДА отображается текст ТО система ДОЛЖНА поддерживать русский язык
4. КОГДА форматируются числа и даты ТО система ДОЛЖНА использовать соответствующее локально-специфичное форматирование
5. ЕСЛИ добавляются новые языки ТО система ДОЛЖНА поддерживать их без изменений кода компонентов

#### Правила
1. Категорически нельзя использовать @apply атрибут в стилях.
2. Vue компоненты импортируются как Astro islands  компоненты с атрибутом client:load или client:only="vue" 
3. Не запускай  "Open in Browser", предлагай проверить мне.
4. Следуй кристально чистой архитектуре проекта.
5. Главный паттерн Vue Islands с client:load директивой.
6. Volt UI как библиотека компонентов.
7. МАКСИМАЛЬНО ПРОСТОЙ КОД, без лишнего функционала.
8. Мы делаем максимально гибкую систему, которая будет легко расширяться и поддерживаться. 
9. По возможности реализовывай автогенерацию форм на базе Zod схем.
10. Используй по максимуму где это возможно уже сгенерированные Zenstack:  Zod схемы и tRPC роуты.
11. Мы не создаем демо код, тестовый код! Мы сразу приступаем к реальному функционалу!
12. Учти что категорически запрещено дублировать функционал, везде и всегда должны быть единые компоненты! 
13. Категорически запрещено нарушать принцип DRY 
14. Lucide-vue иконки
15. Volt UI (unstyled primevue) + Tailwind 4
16. React компоненты импортируются как Astro islands  компоненты с атрибутом client:load или client:only="react" 

17. Следуй кристально чистой архитектуре проекта.

18. МАКСИМАЛЬНО ПРОСТОЙ КОД, без лишнего функционала.

19. По возможности реализовывай автогенерацию форм на базе Zod схем.

20. Используй по максимуму где это возможно уже сгенерированные Zenstack:  Zod схемы и tRPC роуты.

21. Мы не создаем демо код, тестовый код! Мы сразу приступаем к реальному функционалу!

22. Учти что категорически запрещено дублировать функционал, везде и всегда должны быть единые компоненты! 

23. Категорически запрещено нарушать принцип DRY 

24. Категорически запрещено нарушать принцип SOLID

25. Lucide react иконки

26. shadcn/ui + tailwind 4 для /site (клиентская часть)

27. Роутинг и навигация Astro View Transition (метод navigate(path:string)) https://docs.astro.build/en/reference/modules/astro-transitions/#navigate