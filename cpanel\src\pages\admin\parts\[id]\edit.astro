---
import AdminLayout from "../../../../layouts/AdminLayout.astro";
import { trpc } from "@/lib/trpc";
import Icon from "@/components/ui/Icon.vue";
import PartWizard from "@/components/admin/parts/PartWizard.vue";

const { id } = Astro.params;

if (!id) {
  return Astro.redirect('/admin/parts');
}

let part;
try {
  part = await trpc.crud.part.findUnique.query({
    where: { id: Number(id) },
    include: {
      image: true,
      mediaAssets: true,
      partCategory: true,
    }
  });

  if (!part) {
    return Astro.redirect('/admin/parts');
  }
} catch (error) {
  console.error('Error loading part:', error);
  return Astro.redirect('/admin/parts');
}
---

<AdminLayout>
  <div class="max-w-6xl mx-auto">
    <div class="mb-6">
      <div class="flex items-center gap-2 text-sm text-surface-600 dark:text-surface-400 mb-2">
        <a href="/admin/parts" class="hover:text-primary">Запчасти</a>
        <span>/</span>
        <a href={`/admin/parts/${part.id}`} class="hover:text-primary">#{part.id}</a>
        <span>/</span>
        <span>Редактирование</span>
      </div>
      <h1 class="text-3xl font-bold text-surface-900 dark:text-surface-0">
        Редактирование: {part.name || 'Без названия'}
      </h1>
    </div>

    <PartWizard client:load part={part} mode="edit" />

    <div class="mt-6 flex gap-3">
      <a href={`/admin/parts/${part.id}`} class="inline-flex items-center px-4 py-2 bg-primary text-primary-contrast rounded-lg hover:bg-primary-600 transition-colors">
        <Icon name="pi pi-eye" class="mr-2 w-4 h-4 inline-block" />
        Просмотр запчасти
      </a>
      <a href="/admin/parts" class="inline-flex items-center px-4 py-2 bg-surface-200 dark:bg-surface-700 text-surface-700 dark:text-surface-300 rounded-lg hover:bg-surface-300 dark:hover:bg-surface-600 transition-colors">
        <Icon name="pi pi-arrow-left" class="mr-2 w-4 h-4 inline-block" />
        Назад к списку
      </a>
    </div>
  </div>
</AdminLayout>