import { createAccessControl } from "better-auth/plugins/access";
import { defaultStatements, adminAc } from "better-auth/plugins/admin/access";
import fs from 'fs';
import path from 'path';
import { Prisma } from '@prisma/client';

// Базовые ресурсы и действия (можно расширять под домен)
export const statement = {
  ...defaultStatements,
  user: ["ban", "delete", "update", "read"],
  session: ["revoke"],
  project: ["create", "update", "delete", "share"],
} as const;

export const ac = createAccessControl(statement);

// Роли для admin-плагина
export const user = ac.newRole({
  project: ["create"],
});

export const admin = ac.newRole({
  ...adminAc.statements,
  user: ["ban", "delete", "update", "read"],
  session: ["revoke"],
  project: ["create", "update", "delete", "share"],
});

// Рантайм-каталог ресурсов/действий для UI
const DEFAULT_ACTIONS = ['read', 'create', 'update', 'delete'] as const;

function listZodModelNames(): string[] {
  try {
    // Надёжный путь до сгенерированных Zod моделей относительно корня проекта
    const modelsDir = path.resolve(process.cwd(), 'api', 'generated', 'zod', 'models');
    if (!fs.existsSync(modelsDir)) {
      return [];
    }
    const files = fs.readdirSync(modelsDir);
    // Имена файлов формата Model.schema.(ts|js)
    return files
      .map((f) => {
        const match = f.match(/^(.*)\.schema\.(t|j)sx?$/i);
        return match ? match[1] : null;
      })
      .filter((v): v is string => Boolean(v));
  } catch {
    return [];
  }
}

export function getAccessCatalog(): Record<string, string[]> {
  // 1) Предпочтительно: собрать ресурсы из Prisma DMMF (реальные модели)
  try {
    const modelNames = Prisma.dmmf.datamodel.models.map((m) => m.name);
    if (modelNames.length > 0) {
      const catalog: Record<string, string[]> = {};
      for (const m of modelNames) catalog[m] = [...DEFAULT_ACTIONS];
      return catalog;
    }
  } catch {}

  // 2) Пытаемся собрать ресурсы из Zod моделей ZenStack
  const models = listZodModelNames();
  if (models.length > 0) {
    const catalog: Record<string, string[]> = {};
    for (const m of models) {
      catalog[m] = [...DEFAULT_ACTIONS];
    }
    return catalog;
  }

  // 3) Фолбэк: берём из объявленного statement
  const catalogFromStatement: Record<string, string[]> = Object.fromEntries(
    Object.entries(statement).flatMap(([key, value]) => {
      if (Array.isArray(value) && value.every((v) => typeof v === 'string')) {
        return [[key, value as string[]]];
      }
      return [];
    })
  );
  return catalogFromStatement;
}


