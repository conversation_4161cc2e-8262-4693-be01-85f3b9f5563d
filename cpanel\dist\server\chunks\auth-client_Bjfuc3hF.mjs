import { createAuthClient } from 'better-auth/vue';
import { adminClient, phoneNumberClient, usernameClient, anonymousClient } from 'better-auth/client/plugins';
import { z } from 'zod';

z.object({
  id: z.string(),
  email: z.string(),
  emailVerified: z.boolean(),
  name: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
  image: z.string().nullable().optional(),
  role: z.string()
});
z.enum(["USER", "ADMIN", "SHOP"]);
const config = {
  // Используем API сервер напрямую или через прокси в Astro
  baseURL: "http://localhost:3000",
  fetchOptions: {
    timeout: 1e4,
    retries: 3
  }
};
const authClient = createAuthClient({
  baseURL: config.baseURL,
  plugins: [
    adminClient(),
    phoneNumberClient(),
    usernameClient(),
    anonymousClient()
  ],
  fetchOptions: {
    timeout: config.fetchOptions?.timeout,
    retry: config.fetchOptions?.retries,
    onRequest: (ctx) => {
      if (process.env.NODE_ENV === "development") {
        console.log("🔄 Auth request:", ctx.url);
      }
    },
    onResponse: (ctx) => {
      if (process.env.NODE_ENV === "development") {
        console.log("✅ Auth response:", ctx.response.status);
      }
    },
    onError: (ctx) => {
      console.error("❌ Auth error:", ctx.error);
      if (ctx.response?.status === 401) {
        console.warn("🔒 Unauthorized - redirecting to login");
        if (typeof window !== "undefined" && !window.location.pathname.includes("/login")) ;
      } else if (ctx.response?.status === 403) {
        console.warn("🚫 Forbidden - insufficient permissions");
      } else if (ctx.response?.status >= 500) {
        console.error("🔥 Server error - check API status");
      }
    }
  }
});
const {
  signIn,
  signUp,
  signOut,
  useSession,
  getSession,
  $ERROR_CODES
} = authClient;
authClient.admin;
const hasRole = (user, role) => {
  return user?.role === role;
};
const isAdmin = (user) => {
  return hasRole(user, "ADMIN");
};
const isShopOwner = (user) => {
  return hasRole(user, "SHOP");
};
const canAccessAdmin = (user) => {
  return isAdmin(user) || isShopOwner(user);
};
const getErrorMessage = (error, fallback = "Произошла ошибка") => {
  if (typeof error === "string") return error;
  if (error?.message) return error.message;
  if (error?.error?.message) return error.error.message;
  return fallback;
};

export { authClient as a, isShopOwner as b, canAccessAdmin as c, getErrorMessage as g, hasRole as h, isAdmin as i, useSession as u };
