<template>
  <div :class="containerClasses">
    <Motion
      :animate="{ rotate: 360 }"
      :transition="{ duration: 1, repeat: Infinity, ease: 'linear' }"
      :class="spinnerClasses"
    >
      <svg viewBox="0 0 24 24" fill="none">
        <circle
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-dasharray="60 40"
          class="opacity-25"
        />
        <circle
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-dasharray="15 85"
          class="opacity-75"
        />
      </svg>
    </Motion>
    <div v-if="label" :class="labelClasses">
      {{ label }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Motion } from 'motion-v'

interface Props {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  variant?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'
  label?: string
  centered?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  variant: 'default',
  centered: false
})

const containerClasses = computed(() => {
  const base = 'flex items-center gap-2'
  const centered = props.centered ? 'justify-center' : ''
  const direction = props.label ? 'flex-col' : ''
  
  return `${base} ${centered} ${direction}`.trim()
})

const spinnerClasses = computed(() => {
  const sizes = {
    xs: 'w-3 h-3',
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  }
  
  const variants = {
    default: 'text-[--color-foreground]',
    primary: 'text-[--color-primary]',
    secondary: 'text-[--color-secondary]',
    success: 'text-[--color-success]',
    warning: 'text-[--color-warning]',
    danger: 'text-[--color-danger]'
  }
  
  return `${sizes[props.size]} ${variants[props.variant]}`
})

const labelClasses = computed(() => {
  const sizes = {
    xs: 'text-xs',
    sm: 'text-sm',
    md: 'text-sm',
    lg: 'text-base',
    xl: 'text-lg'
  }
  
  return `${sizes[props.size]} text-[--color-muted] font-medium`
})
</script>