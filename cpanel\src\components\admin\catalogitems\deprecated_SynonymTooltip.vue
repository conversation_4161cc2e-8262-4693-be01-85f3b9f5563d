<template>
  <div class="relative inline-block">
    <!-- Trigger element -->
    <div
      ref="triggerRef"
      @mouseenter="handleMouseEnter"
      @mouseleave="handleMouseLeave"
      @click="handleClick"
      class="cursor-help inline-flex items-center gap-1"
    >
      <slot />
      <Info class="w-3 h-3 text-surface-400 hover:text-surface-600" />
    </div>

    <!-- Tooltip content -->
    <Teleport to="body">
      <div
        v-if="showTooltip && synonymsData"
        ref="tooltipRef"
        class="fixed z-50 bg-white dark:bg-surface-800 border border-surface-200 dark:border-surface-700 rounded-lg shadow-lg p-3 max-w-sm"
        :style="tooltipStyle"
        @mouseenter="onTooltipEnter"
        @mouseleave="onTooltipLeave"
      >
        <div class="space-y-2">
          <!-- Group header -->
          <div class="border-b border-surface-200 dark:border-surface-700 pb-2">
            <div class="flex items-center justify-between">
              <h4 class="text-sm font-medium text-surface-900 dark:text-surface-100">
                {{ synonymsData.group.name }}
              </h4>
              <VTag 
                size="small" 
                :value="synonymsData.group.compatibilityLevel" 
                :severity="getCompatibilitySeverity(synonymsData.group.compatibilityLevel)" 
              />
            </div>
            <p v-if="synonymsData.group.description" class="text-xs text-surface-500 mt-1">
              {{ synonymsData.group.description }}
            </p>
          </div>

          <!-- Synonyms list -->
          <div>
            <div class="text-xs text-surface-600 dark:text-surface-400 mb-1">
              Синонимы ({{ synonymsData.synonyms.length }}):
            </div>
            <div class="grid grid-cols-1 gap-1 max-h-32 overflow-y-auto">
              <div 
                v-for="synonym in synonymsData.synonyms" 
                :key="synonym.id"
                class="flex items-center justify-between text-xs p-1 rounded"
                :class="synonym.id === synonymsData.currentSynonymId ? 'bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300' : 'text-surface-700 dark:text-surface-300'"
              >
                <span class="font-mono">{{ synonym.value }}</span>
                <div class="flex items-center gap-1">
                  <span v-if="synonym.brand" class="text-xs text-surface-500">
                    {{ synonym.brand.name }}
                  </span>
                  <VTag 
                    v-if="synonym.compatibilityLevel && synonym.compatibilityLevel !== synonymsData.group.compatibilityLevel"
                    size="small" 
                    :value="synonym.compatibilityLevel" 
                    :severity="getCompatibilitySeverity(synonym.compatibilityLevel)" 
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- Canonical value -->
          <div v-if="synonymsData.group.canonicalValue" class="border-t border-surface-200 dark:border-surface-700 pt-2">
            <div class="text-xs text-surface-600 dark:text-surface-400">
              Каноническое значение:
            </div>
            <div class="text-xs font-mono text-surface-800 dark:text-surface-200">
              {{ synonymsData.group.canonicalValue }}
            </div>
          </div>

          <!-- Notes -->
          <div v-if="synonymsData.group.notes" class="border-t border-surface-200 dark:border-surface-700 pt-2">
            <div class="text-xs text-surface-600 dark:text-surface-400">
              Примечания:
            </div>
            <div class="text-xs text-surface-700 dark:text-surface-300">
              {{ synonymsData.group.notes }}
            </div>
          </div>

          <!-- Actions -->
          <div class="border-t border-surface-200 dark:border-surface-700 pt-2 flex justify-end">
            <VButton 
              size="small" 
              severity="secondary" 
              outlined 
              @click="openDetailModal"
              class="text-xs"
            >
              Подробнее
            </VButton>
          </div>
        </div>
      </div>
    </Teleport>

    <!-- Loading state -->
    <Teleport to="body">
      <div
        v-if="showTooltip && loading"
        ref="loadingTooltipRef"
        class="fixed z-50 bg-white dark:bg-surface-800 border border-surface-200 dark:border-surface-700 rounded-lg shadow-lg p-3"
        :style="tooltipStyle"
      >
        <div class="flex items-center gap-2 text-xs text-surface-600">
          <Loader2 class="w-4 h-4 animate-spin" />
          Загрузка синонимов...
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue'
import VTag from '@/volt/Tag.vue'
import VButton from '@/volt/Button.vue'
import { Info, Loader2 } from 'lucide-vue-next'
import { useTrpc } from '@/composables/useTrpc'

interface Props {
  templateId: number
  value: string
  showOnHover?: boolean
  showOnClick?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showOnHover: true,
  showOnClick: true
})

const emit = defineEmits<{
  openDetail: [data: any]
}>()

const { attributeSynonyms } = useTrpc()

const triggerRef = ref<HTMLElement>()
const tooltipRef = ref<HTMLElement>()
const loadingTooltipRef = ref<HTMLElement>()
const showTooltip = ref(false)
const loading = ref(false)
const synonymsData = ref<any>(null)
const tooltipPosition = ref({ x: 0, y: 0 })

let hoverTimeout: NodeJS.Timeout | null = null

const getCompatibilitySeverity = (level: string) => {
  switch (level) {
    case 'EXACT': return 'success'
    case 'NEAR': return 'info'
    case 'LEGACY': return 'warning'
    default: return 'secondary'
  }
}

const tooltipStyle = computed(() => ({
  left: `${tooltipPosition.value.x}px`,
  top: `${tooltipPosition.value.y}px`,
}))

const updateTooltipPosition = () => {
  if (!triggerRef.value) return
  
  const rect = triggerRef.value.getBoundingClientRect()
  const scrollX = window.pageXOffset || document.documentElement.scrollLeft
  const scrollY = window.pageYOffset || document.documentElement.scrollTop
  
  tooltipPosition.value = {
    x: rect.left + scrollX,
    y: rect.bottom + scrollY + 5
  }
}

const loadSynonyms = async () => {
  if (synonymsData.value) return // уже загружали
  loading.value = true
  try {
    const result = await attributeSynonyms.utils.findGroupSynonymsByValue({
      templateId: props.templateId,
      value: props.value
    })
    synonymsData.value = result
  } catch (error) {
    console.error('Ошибка загрузки синонимов:', error)
  } finally {
    loading.value = false
  }
}

const handleMouseEnter = () => {
  if (!props.showOnHover) return
  if (hoverTimeout) { clearTimeout(hoverTimeout) }
  updateTooltipPosition()
  showTooltip.value = true
  // запускаем загрузку без await, чтобы сразу показать спиннер
  void loadSynonyms()
}

const handleMouseLeave = () => {
  if (hoverTimeout) {
    clearTimeout(hoverTimeout)
    hoverTimeout = null
  }
  // небольшая задержка, чтобы успеть навести на сам tooltip
  setTimeout(() => {
    showTooltip.value = false
  }, 150)
}

const onTooltipEnter = () => {
  // когда курсор над тултипом — не скрываем
  if (hoverTimeout) { clearTimeout(hoverTimeout); hoverTimeout = null }
}

const onTooltipLeave = () => {
  showTooltip.value = false
}

const handleClick = () => {
  if (!props.showOnClick) return
  if (showTooltip.value) {
    showTooltip.value = false
    return
  }
  updateTooltipPosition()
  showTooltip.value = true
  void loadSynonyms()
}

const openDetailModal = () => {
  if (synonymsData.value) emit('openDetail', synonymsData.value)
  showTooltip.value = false
}
</script>
