"use client"

import { useState, useEffect } from "react"
import { AIAssistantDrawer } from "../pro/AIAssistantDrawer"
import { useCatalogSearch } from "../pro/useCatalogSearch"

import { TrpcBoundary } from "@/components/providers/TrpcBoundary"

export default function AIAssistantIsland() {
  return (
    <TrpcBoundary>
      <AIAssistantIslandInner />
    </TrpcBoundary>
  )
}

function AIAssistantIslandInner() {
  const { filters, updateFilters, filteredCount } = useCatalogSearch()
  const [isOpen, setIsOpen] = useState(false)

  useEffect(() => {
    const handleOpenAI = () => setIsOpen(true)
    window.addEventListener('openAIAssistant', handleOpenAI)
    return () => window.removeEventListener('openAIAssistant', handleOpenAI)
  }, [])

  const handleApplyFilters = (aiFilters: Partial<typeof filters>) => {
    updateFilters(aiFilters)
  }

  const handleToggle = () => setIsOpen(!isOpen)

  return (
    <AIAssistantDrawer
      isOpen={isOpen}
      onToggle={handleToggle}
      onApplyFilters={handleApplyFilters}
      currentFilters={filters}
      resultsCount={filteredCount}
    />
  )
}
