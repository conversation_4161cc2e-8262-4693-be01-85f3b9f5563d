"use client"

import { useMemo } from "react"
import { useQuery } from "@tanstack/react-query"
import { trpcClient } from "@/lib/clients"

// Хук для загрузки категорий
export function useCategories(options?: { rootOnly?: boolean; take?: number }) {
  const { data: categories, isLoading, error } = useQuery({
    queryKey: ["site.catalog.categories", options?.rootOnly ?? false, options?.take ?? 200],
    queryFn: () => trpcClient.site.catalog.categories.query({
      rootOnly: options?.rootOnly ?? false,
      take: options?.take ?? 200,
    }),
    staleTime: 10 * 60_000,
    gcTime: 60 * 60_000,
  })

  const formattedCategories = useMemo(() => {
    const list = Array.isArray(categories) ? categories : []
    return list.map(cat => ({
      id: cat.id,
      name: cat.name,
      slug: cat.slug,
      level: cat.level,
      path: cat.path
    }))
  }, [categories])

  return {
    categories: formattedCategories,
    isLoading,
    error
  }
}

// Хук для загрузки брендов
export function useBrands(options?: { take?: number }) {
  const { data: brands, isLoading, error } = useQuery({
    queryKey: ["site.catalog.brands", options?.take ?? 100],
    queryFn: () => trpcClient.site.catalog.brands.query({ take: options?.take ?? 100 }),
    staleTime: 10 * 60_000,
    gcTime: 60 * 60_000,
  })

  const formattedBrands = useMemo(() => {
    const list = Array.isArray(brands) ? brands : []
    return list.map(brand => ({
      id: brand.id,
      name: brand.name,
      slug: brand.slug,
      isOem: brand.isOem,
      country: brand.country
    }))
  }, [brands])

  return {
    brands: formattedBrands,
    isLoading,
    error
  }
}

// Хук для загрузки шаблонов атрибутов
export function useAttributeTemplates(options?: { take?: number }) {
  const { data: templates, isLoading, error } = useQuery({
    queryKey: ["site.attributes.templates", options?.take ?? 100],
    queryFn: () => trpcClient.site.attributes.templates.query({ take: options?.take ?? 100 }),
    staleTime: 15 * 60_000,
    gcTime: 60 * 60_000,
  })

  const formattedTemplates = useMemo(() => {
    const list = Array.isArray(templates) ? templates : []
    return list.map(template => ({
      id: template.id,
      name: template.name,
      title: template.title,
      dataType: template.dataType,
      unit: template.unit,
      isRequired: template.isRequired,
      allowedValues: template.allowedValues,
      tolerance: template.tolerance
    }))
  }, [templates])

  return {
    templates: formattedTemplates,
    isLoading,
    error
  }
}

// Хук для получения статистики атрибутов
export function useAttributeStats(templateId: number) {
  const { data: stats, isLoading, error } = useQuery({
    queryKey: ["site.attributes.stats", templateId],
    queryFn: () => trpcClient.site.attributes.stats.query({ templateId }),
    enabled: !!templateId,
  })

  return {
    stats,
    isLoading,
    error
  }
}

// Комбинированный хук для всех данных каталога
export function useCatalogMetadata() {
  const categories = useCategories()
  const brands = useBrands()
  const templates = useAttributeTemplates()

  const isLoading = categories.isLoading || brands.isLoading || templates.isLoading
  const hasError = categories.error || brands.error || templates.error

  return {
    categories: categories.categories,
    brands: brands.brands,
    templates: templates.templates,
    isLoading,
    hasError,
    errors: {
      categories: categories.error,
      brands: brands.error,
      templates: templates.error
    }
  }
}
