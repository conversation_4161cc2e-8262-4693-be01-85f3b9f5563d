/**
 * Утилиты для работы с фильтрами каталога
 */

export interface CatalogFilters {
  search?: string;
  categoryIds?: number[];
  brandIds?: number[];
  attributes?: AttributeFilter[];
  equipmentModelIds?: string[];
  priceRange?: [number, number];
}

export interface AttributeFilter {
  templateId: number;
  value?: string;
  minValue?: number;
  maxValue?: number;
  operator?: 'equals' | 'contains' | 'range' | 'in';
}

export interface ViewPreferences {
  mode: 'grid' | 'list' | 'table';
  itemsPerPage: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Преобразование фильтров в Prisma-совместимый input (findMany)
export const filtersToQuery = (filters: CatalogFilters) => {
  const and: any[] = [];

  if (filters.search) {
    and.push({ name: { contains: filters.search, mode: 'insensitive' } });
  }

  if (filters.categoryIds?.length) {
    and.push({ partCategoryId: { in: filters.categoryIds } });
  }

  // Фильтр по брендам через связку применимости к CatalogItem
  if (filters.brandIds?.length) {
    and.push({ applicabilities: { some: { catalogItem: { brandId: { in: filters.brandIds } } } } });
  }

  // Фильтр по моделям техники через EquipmentApplicability
  if (filters.equipmentModelIds?.length) {
    and.push({ equipmentApplicabilities: { some: { equipmentModelId: { in: filters.equipmentModelIds } } } });
  }

  // Простейшее отображение атрибутов (опционально): если указаны templateId и value — точное совпадение
  if (filters.attributes?.length) {
    const attrConds = filters.attributes
      .filter(a => !!a?.templateId)
      .map(a => {
        const cond: any = { templateId: a.templateId };
        if (a.value) cond.value = a.value;
        if (a.minValue != null || a.maxValue != null) {
          const range: any = {};
          if (a.minValue != null) range.gte = a.minValue;
          if (a.maxValue != null) range.lte = a.maxValue;
          cond.numericValue = range;
        }
        return cond;
      });
    if (attrConds.length) {
      and.push({ attributes: { some: { AND: attrConds } } });
    }
  }

  const where = and.length ? { AND: and } : {};
  return { where };
};

// Преобразование параметров запроса в фильтры
export const queryToFilters = (query: Record<string, any>): CatalogFilters => {
  const filters: CatalogFilters = {};
  
  if (query.search) {
    filters.search = query.search;
  }
  
  if (query.categoryIds) {
    filters.categoryIds = Array.isArray(query.categoryIds) 
      ? query.categoryIds.map(Number) 
      : [Number(query.categoryIds)];
  }
  
  if (query.brandIds) {
    filters.brandIds = Array.isArray(query.brandIds) 
      ? query.brandIds.map(Number) 
      : [Number(query.brandIds)];
  }
  
  if (query.equipmentModelIds) {
    filters.equipmentModelIds = Array.isArray(query.equipmentModelIds) 
      ? query.equipmentModelIds 
      : [query.equipmentModelIds];
  }
  
  if (query.minPrice !== undefined || query.maxPrice !== undefined) {
    filters.priceRange = [
      query.minPrice ? Number(query.minPrice) : 0,
      query.maxPrice ? Number(query.maxPrice) : Infinity
    ];
  }
  
  if (query.attributes) {
    filters.attributes = Array.isArray(query.attributes) 
      ? query.attributes 
      : [query.attributes];
  }
  
  return filters;
};

// Проверка, есть ли активные фильтры
export const hasActiveFilters = (filters: CatalogFilters): boolean => {
  return !!(
    filters.search ||
    filters.categoryIds?.length ||
    filters.brandIds?.length ||
    filters.equipmentModelIds?.length ||
    filters.priceRange ||
    filters.attributes?.length
  );
};

// Очистка фильтров
export const clearFilters = (): CatalogFilters => ({});

// Объединение фильтров
export const mergeFilters = (base: CatalogFilters, updates: Partial<CatalogFilters>): CatalogFilters => {
  return {
    ...base,
    ...updates,
    categoryIds: updates.categoryIds ?? base.categoryIds,
    brandIds: updates.brandIds ?? base.brandIds,
    equipmentModelIds: updates.equipmentModelIds ?? base.equipmentModelIds,
    attributes: updates.attributes ?? base.attributes,
  };
};