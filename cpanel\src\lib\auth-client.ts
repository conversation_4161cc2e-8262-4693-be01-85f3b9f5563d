/**
 * Централизованный auth клиент для интеграции с better-auth
 * Использует сгенерированные Zod типы из API
 */

import { createAuthClient } from "better-auth/vue"
import {
  adminClient,
  phoneNumberClient,
  usernameClient,
  anonymousClient
} from "better-auth/client/plugins"

// Импортируем сгенерированные типы из API
import { UserSchema } from "../../../api/generated/zod/models/User.schema"
import { RoleSchema } from "../../../api/generated/zod/enums/Role.schema"
import { z } from "zod"

// Типы на основе Zod схем
export type User = z.infer<typeof UserSchema>
export type Role = z.infer<typeof RoleSchema>

// Конфигурация клиента
const config = {
  // Используем API сервер напрямую или через прокси в Astro
  baseURL: import.meta.env.PUBLIC_API_URL || "http://localhost:3000",
  fetchOptions: {
    timeout: 10000,
    retries: 3
  }
}

// Создаем auth клиент с плагинами
export const authClient = createAuthClient({
  baseURL: config.baseURL,
  plugins: [
    adminClient(),
    phoneNumberClient(),
    usernameClient(),
    anonymousClient()
  ],
  fetchOptions: {
    timeout: config.fetchOptions?.timeout,
    retry: config.fetchOptions?.retries,

    onRequest: (ctx) => {
      if (process.env.NODE_ENV === 'development') {
        console.log('🔄 Auth request:', ctx.url)
      }
    },

    onResponse: (ctx) => {
      if (process.env.NODE_ENV === 'development') {
        console.log('✅ Auth response:', ctx.response.status)
      }
    },

    onError: (ctx) => {
      console.error('❌ Auth error:', ctx.error)

      if (ctx.response?.status === 401) {
        console.warn('🔒 Unauthorized - redirecting to login')
        // Можно добавить автоматическое перенаправление на страницу входа
        if (typeof window !== 'undefined' && !window.location.pathname.includes('/login')) {
          // window.location.href = '/admin/login'
        }
      } else if (ctx.response?.status === 403) {
        console.warn('🚫 Forbidden - insufficient permissions')
      } else if (ctx.response?.status >= 500) {
        console.error('🔥 Server error - check API status')
      }
    }
  }
})

// Экспорт типизированных методов для удобства
export const {
  signIn,
  signUp,
  signOut,
  useSession,
  getSession,
  $ERROR_CODES
} = authClient

// Плагин методы
export const adminMethods = authClient.admin
// Другие плагин методы будут доступны через authClient напрямую

// Хелперы для проверки ролей
export const hasRole = (user: User | null, role: Role): boolean => {
  return user?.role === role
}

export const isAdmin = (user: User | null): boolean => {
  return hasRole(user, 'ADMIN')
}

export const isShopOwner = (user: User | null): boolean => {
  return hasRole(user, 'SHOP')
}

export const isUser = (user: User | null): boolean => {
  return hasRole(user, 'USER')
}

export const canAccessAdmin = (user: User | null): boolean => {
  return isAdmin(user) || isShopOwner(user)
}

// Хелперы для работы с ошибками
export const getErrorMessage = (error: any, fallback = 'Произошла ошибка'): string => {
  if (typeof error === 'string') return error
  if (error?.message) return error.message
  if (error?.error?.message) return error.error.message
  return fallback
}

export const isAuthError = (error: any): boolean => {
  return error?.status === 401 || error?.code === 'UNAUTHORIZED'
}

export const isForbiddenError = (error: any): boolean => {
  return error?.status === 403 || error?.code === 'FORBIDDEN'
}

// Константы для использования в компонентах
export const AUTH_ROUTES = {
  LOGIN: '/admin/login',
  DASHBOARD: '/admin',
  PROFILE: '/admin/profile'
} as const

export const USER_ROLES = {
  GUEST: 'GUEST',
  USER: 'USER',
  SHOP: 'SHOP',
  ADMIN: 'ADMIN'
} as const

// Экспорт конфигурации для использования в других модулях
export { config as authConfig }

// Экспорт клиента по умолчанию
export default authClient
