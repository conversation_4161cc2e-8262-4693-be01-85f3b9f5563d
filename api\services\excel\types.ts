export type ImportMode = 'dry-run' | 'execute'
export type ConflictMode = 'upsert' | 'update_only' | 'skip' | 'error'

export interface ImportMeta {
  createMissingRefs: boolean
  onConflict: ConflictMode
  scope?: {
    brandSlug?: string
    categorySlug?: string
  }
}

export interface SheetParseResult<T = any> {
  sheetName: string
  rows: T[]
}

export interface ImportCounters {
  rowsSeen: number
  rowsValid: number
  created: number
  updated: number
  deleted: number
  skipped: number
  errorsCount: number
  warningsCount: number
}

export interface ImportResult {
  perSheet: Record<string, ImportCounters>
  errors: Array<{ sheet: string; rowIndex: number; message: string }>
  warnings: Array<{ sheet: string; rowIndex: number; message: string }>
  reportBase64?: string
}

