# Technology Stack

## Runtime & Build System
- **Bun** - Primary JavaScript runtime for high performance
- **Concurrently** - Run multiple development servers simultaneously
- **TypeScript** - Full type safety across the stack

## Backend (API)
- **Hono** - Modern web framework
- **tRPC v11** - Type-safe APIs with end-to-end typing
- **ZenStack** - Prisma extension with declarative access control
- **Prisma** - ORM with PostgreSQL
- **Better Auth** - Modern authentication system
- **Zod** - Data validation and type inference
- **SuperJSON** - JSON serialization with type preservation

## Frontend Applications

### Control Panel (cpanel/)
- **Astro** - Static site generator with server-side rendering
- **Vue 3** - Progressive framework with Composition API
- **PrimeVue** - Rich UI component library (unstyled mode)
- **Tailwind CSS v4+** - Utility-first CSS framework
- **Lucide Vue** - Icon library

### Public Site (site/)
- **Astro** - Static site generator
- **React** - UI library
- **Shadcn UI** - UI component library
- **Tailwind CSS v4+** - Utility-first CSS framework
- **TanStack Query** - Data fetching and caching

## Common Development Commands

```bash
# Install dependencies for entire project
npm install

# Development (runs API + Frontend concurrently)
npm run dev

# Individual services
npm run dev:api        # Backend only (port 3000)
npm run dev:frontend   # Control panel (port 4322)
cd site && npm run dev # Public site (port 4323)

# Build
npm run build          # Build frontend applications
npm run preview        # Preview built applications

# API specific (from api/ directory)
bun run --hot index.ts # Hot reload development server
```

## Database & Schema
- **PostgreSQL** - Primary database
- **ZModel** - ZenStack schema definition language
- **Prisma migrations** - Database schema versioning
- **Generated types** - Auto-generated TypeScript types from schema

## Code Quality
- **ESLint** - JavaScript/TypeScript linting
- **Prettier** - Code formatting
- **Biome** - Fast linter and formatter alternative