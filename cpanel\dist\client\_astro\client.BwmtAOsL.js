import{T as x}from"./index.BksvaNwr.js";import{P as At}from"./index.CzaMXvxd.js";import{P as Pt,C as tt}from"./index.CKgBWlpe.js";import{aW as $t,_ as pt,w as H,a0 as K,U as C,aX as Dt,a9 as et,ad as _,aw as T,aH as W,at as Ft,ay as Rt,aY as Qt,aZ as Lt,az as G,a_ as xt,a$ as Ht,ab as jt,as as M,a5 as it,aK as qt,d as It,J as U,b0 as Nt}from"./index.BglzLLgy.js";import{P as j,B as Ut,s as q}from"./index.CRcBj2l1.js";import{x as rt}from"./index.CLs7nh7g.js";import{C as Bt}from"./index.Tc5ZRw49.js";import{a as kt,r as vt,J as yt,a8 as Kt}from"./reactivity.esm-bundler.D5IypM4U.js";import{a as v}from"./index.BHZvt6Rq.js";import{d as Wt,e as Gt}from"./runtime-dom.esm-bundler.C-dfRCGi.js";function $(e){"@babel/helpers - typeof";return $=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},$(e)}function nt(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable})),i.push.apply(i,r)}return i}function I(e){for(var t=1;t<arguments.length;t++){var i=arguments[t]!=null?arguments[t]:{};t%2?nt(Object(i),!0).forEach(function(r){zt(e,r,i[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):nt(Object(i)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(i,r))})}return e}function zt(e,t,i){return(t=Vt(t))in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function Vt(e){var t=Yt(e,"string");return $(t)=="symbol"?t:t+""}function Yt(e,t){if($(e)!="object"||!e)return e;var i=e[Symbol.toPrimitive];if(i!==void 0){var r=i.call(e,t);if($(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Jt={ripple:!1,inputStyle:null,inputVariant:null,locale:{startsWith:"Starts with",contains:"Contains",notContains:"Not contains",endsWith:"Ends with",equals:"Equals",notEquals:"Not equals",noFilter:"No Filter",lt:"Less than",lte:"Less than or equal to",gt:"Greater than",gte:"Greater than or equal to",dateIs:"Date is",dateIsNot:"Date is not",dateBefore:"Date is before",dateAfter:"Date is after",clear:"Clear",apply:"Apply",matchAll:"Match All",matchAny:"Match Any",addRule:"Add Rule",removeRule:"Remove Rule",accept:"Yes",reject:"No",choose:"Choose",upload:"Upload",cancel:"Cancel",completed:"Completed",pending:"Pending",fileSizeTypes:["B","KB","MB","GB","TB","PB","EB","ZB","YB"],dayNames:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayNamesShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],dayNamesMin:["Su","Mo","Tu","We","Th","Fr","Sa"],monthNames:["January","February","March","April","May","June","July","August","September","October","November","December"],monthNamesShort:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],chooseYear:"Choose Year",chooseMonth:"Choose Month",chooseDate:"Choose Date",prevDecade:"Previous Decade",nextDecade:"Next Decade",prevYear:"Previous Year",nextYear:"Next Year",prevMonth:"Previous Month",nextMonth:"Next Month",prevHour:"Previous Hour",nextHour:"Next Hour",prevMinute:"Previous Minute",nextMinute:"Next Minute",prevSecond:"Previous Second",nextSecond:"Next Second",am:"am",pm:"pm",today:"Today",weekHeader:"Wk",firstDayOfWeek:0,showMonthAfterYear:!1,dateFormat:"mm/dd/yy",weak:"Weak",medium:"Medium",strong:"Strong",passwordPrompt:"Enter a password",emptyFilterMessage:"No results found",searchMessage:"{0} results are available",selectionMessage:"{0} items selected",emptySelectionMessage:"No selected item",emptySearchMessage:"No results found",fileChosenMessage:"{0} files",noFileChosenMessage:"No file chosen",emptyMessage:"No available options",aria:{trueLabel:"True",falseLabel:"False",nullLabel:"Not Selected",star:"1 star",stars:"{star} stars",selectAll:"All items selected",unselectAll:"All items unselected",close:"Close",previous:"Previous",next:"Next",navigation:"Navigation",scrollTop:"Scroll Top",moveTop:"Move Top",moveUp:"Move Up",moveDown:"Move Down",moveBottom:"Move Bottom",moveToTarget:"Move to Target",moveToSource:"Move to Source",moveAllToTarget:"Move All to Target",moveAllToSource:"Move All to Source",pageLabel:"Page {page}",firstPageLabel:"First Page",lastPageLabel:"Last Page",nextPageLabel:"Next Page",prevPageLabel:"Previous Page",rowsPerPageLabel:"Rows per page",jumpToPageDropdownLabel:"Jump to Page Dropdown",jumpToPageInputLabel:"Jump to Page Input",selectRow:"Row Selected",unselectRow:"Row Unselected",expandRow:"Row Expanded",collapseRow:"Row Collapsed",showFilterMenu:"Show Filter Menu",hideFilterMenu:"Hide Filter Menu",filterOperator:"Filter Operator",filterConstraint:"Filter Constraint",editRow:"Row Edit",saveEdit:"Save Edit",cancelEdit:"Cancel Edit",listView:"List View",gridView:"Grid View",slide:"Slide",slideNumber:"{slideNumber}",zoomImage:"Zoom Image",zoomIn:"Zoom In",zoomOut:"Zoom Out",rotateRight:"Rotate Right",rotateLeft:"Rotate Left",listLabel:"Option List"}},filterMatchModeOptions:{text:[v.STARTS_WITH,v.CONTAINS,v.NOT_CONTAINS,v.ENDS_WITH,v.EQUALS,v.NOT_EQUALS],numeric:[v.EQUALS,v.NOT_EQUALS,v.LESS_THAN,v.LESS_THAN_OR_EQUAL_TO,v.GREATER_THAN,v.GREATER_THAN_OR_EQUAL_TO],date:[v.DATE_IS,v.DATE_IS_NOT,v.DATE_BEFORE,v.DATE_AFTER]},zIndex:{modal:1100,overlay:1e3,menu:1e3,tooltip:1100},theme:void 0,unstyled:!1,pt:void 0,ptOptions:{mergeSections:!0,mergeProps:!1},csp:{nonce:void 0}},Zt=Symbol();function Xt(e,t){var i={config:kt(t)};return e.config.globalProperties.$primevue=i,e.provide(Zt,i),te(),ee(e,i),i}var A=[];function te(){pt.clear(),A.forEach(function(e){return e?.()}),A=[]}function ee(e,t){var i=vt(!1),r=function(){var l;if(((l=t.config)===null||l===void 0?void 0:l.theme)!=="none"&&!K.isStyleNameLoaded("common")){var d,h,w=((d=C.getCommonTheme)===null||d===void 0?void 0:d.call(C))||{},S=w.primitive,p=w.semantic,f=w.global,g=w.style,y={nonce:(h=t.config)===null||h===void 0||(h=h.csp)===null||h===void 0?void 0:h.nonce};C.load(S?.css,I({name:"primitive-variables"},y)),C.load(p?.css,I({name:"semantic-variables"},y)),C.load(f?.css,I({name:"global-variables"},y)),C.loadStyle(I({name:"global-style"},y),g),K.setLoadedStyleName("common")}};pt.on("theme:change",function(u){i.value||(e.config.globalProperties.$primevue.config.theme=u,i.value=!0)});var n=H(t.config,function(u,l){j.emit("config:change",{newValue:u,oldValue:l})},{immediate:!0,deep:!0}),s=H(function(){return t.config.ripple},function(u,l){j.emit("config:ripple:change",{newValue:u,oldValue:l})},{immediate:!0,deep:!0}),a=H(function(){return t.config.theme},function(u,l){i.value||K.setTheme(u),t.config.unstyled||r(),i.value=!1,j.emit("config:theme:change",{newValue:u,oldValue:l})},{immediate:!0,deep:!1}),o=H(function(){return t.config.unstyled},function(u,l){!u&&t.config.theme&&r(),j.emit("config:unstyled:change",{newValue:u,oldValue:l})},{immediate:!0,deep:!0});A.push(n),A.push(s),A.push(a),A.push(o)}var ie={install:function(t,i){var r=$t(Jt,i);Xt(t,r)}},re={install:function(t){var i={add:function(n){x.emit("add",n)},remove:function(n){x.emit("remove",n)},removeGroup:function(n){x.emit("remove-group",n)},removeAllGroups:function(){x.emit("remove-all-groups")}};t.config.globalProperties.$toast=i,t.provide(At,i)}},ne={install:function(t){var i={require:function(n){tt.emit("confirm",n)},close:function(){tt.emit("close")}};t.config.globalProperties.$confirm=i,t.provide(Pt,i)}},se=`
    .p-tooltip {
        position: absolute;
        display: none;
        max-width: dt('tooltip.max.width');
    }

    .p-tooltip-right,
    .p-tooltip-left {
        padding: 0 dt('tooltip.gutter');
    }

    .p-tooltip-top,
    .p-tooltip-bottom {
        padding: dt('tooltip.gutter') 0;
    }

    .p-tooltip-text {
        white-space: pre-line;
        word-break: break-word;
        background: dt('tooltip.background');
        color: dt('tooltip.color');
        padding: dt('tooltip.padding');
        box-shadow: dt('tooltip.shadow');
        border-radius: dt('tooltip.border.radius');
    }

    .p-tooltip-arrow {
        position: absolute;
        width: 0;
        height: 0;
        border-color: transparent;
        border-style: solid;
    }

    .p-tooltip-right .p-tooltip-arrow {
        margin-top: calc(-1 * dt('tooltip.gutter'));
        border-width: dt('tooltip.gutter') dt('tooltip.gutter') dt('tooltip.gutter') 0;
        border-right-color: dt('tooltip.background');
    }

    .p-tooltip-left .p-tooltip-arrow {
        margin-top: calc(-1 * dt('tooltip.gutter'));
        border-width: dt('tooltip.gutter') 0 dt('tooltip.gutter') dt('tooltip.gutter');
        border-left-color: dt('tooltip.background');
    }

    .p-tooltip-top .p-tooltip-arrow {
        margin-left: calc(-1 * dt('tooltip.gutter'));
        border-width: dt('tooltip.gutter') dt('tooltip.gutter') 0 dt('tooltip.gutter');
        border-top-color: dt('tooltip.background');
        border-bottom-color: dt('tooltip.background');
    }

    .p-tooltip-bottom .p-tooltip-arrow {
        margin-left: calc(-1 * dt('tooltip.gutter'));
        border-width: 0 dt('tooltip.gutter') dt('tooltip.gutter') dt('tooltip.gutter');
        border-top-color: dt('tooltip.background');
        border-bottom-color: dt('tooltip.background');
    }
`,oe={root:"p-tooltip p-component",arrow:"p-tooltip-arrow",text:"p-tooltip-text"},ae=C.extend({name:"tooltip-directive",style:se,classes:oe}),ue=Ut.extend({style:ae});function le(e,t){return fe(e)||he(e,t)||de(e,t)||ce()}function ce(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function de(e,t){if(e){if(typeof e=="string")return st(e,t);var i={}.toString.call(e).slice(8,-1);return i==="Object"&&e.constructor&&(i=e.constructor.name),i==="Map"||i==="Set"?Array.from(e):i==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?st(e,t):void 0}}function st(e,t){(t==null||t>e.length)&&(t=e.length);for(var i=0,r=Array(t);i<t;i++)r[i]=e[i];return r}function he(e,t){var i=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(i!=null){var r,n,s,a,o=[],u=!0,l=!1;try{if(s=(i=i.call(e)).next,t!==0)for(;!(u=(r=s.call(i)).done)&&(o.push(r.value),o.length!==t);u=!0);}catch(d){l=!0,n=d}finally{try{if(!u&&i.return!=null&&(a=i.return(),Object(a)!==a))return}finally{if(l)throw n}}return o}}function fe(e){if(Array.isArray(e))return e}function ot(e,t,i){return(t=pe(t))in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function pe(e){var t=ve(e,"string");return E(t)=="symbol"?t:t+""}function ve(e,t){if(E(e)!="object"||!e)return e;var i=e[Symbol.toPrimitive];if(i!==void 0){var r=i.call(e,t);if(E(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function E(e){"@babel/helpers - typeof";return E=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},E(e)}var ye=ue.extend("tooltip",{beforeMount:function(t,i){var r,n=this.getTarget(t);if(n.$_ptooltipModifiers=this.getModifiers(i),i.value){if(typeof i.value=="string")n.$_ptooltipValue=i.value,n.$_ptooltipDisabled=!1,n.$_ptooltipEscape=!0,n.$_ptooltipClass=null,n.$_ptooltipFitContent=!0,n.$_ptooltipIdAttr=q("pv_id")+"_tooltip",n.$_ptooltipShowDelay=0,n.$_ptooltipHideDelay=0,n.$_ptooltipAutoHide=!0;else if(E(i.value)==="object"&&i.value){if(it(i.value.value)||i.value.value.trim()==="")return;n.$_ptooltipValue=i.value.value,n.$_ptooltipDisabled=!!i.value.disabled===i.value.disabled?i.value.disabled:!1,n.$_ptooltipEscape=!!i.value.escape===i.value.escape?i.value.escape:!0,n.$_ptooltipClass=i.value.class||"",n.$_ptooltipFitContent=!!i.value.fitContent===i.value.fitContent?i.value.fitContent:!0,n.$_ptooltipIdAttr=i.value.id||q("pv_id")+"_tooltip",n.$_ptooltipShowDelay=i.value.showDelay||0,n.$_ptooltipHideDelay=i.value.hideDelay||0,n.$_ptooltipAutoHide=!!i.value.autoHide===i.value.autoHide?i.value.autoHide:!0}}else return;n.$_ptooltipZIndex=(r=i.instance.$primevue)===null||r===void 0||(r=r.config)===null||r===void 0||(r=r.zIndex)===null||r===void 0?void 0:r.tooltip,this.bindEvents(n,i),t.setAttribute("data-pd-tooltip",!0)},updated:function(t,i){var r=this.getTarget(t);if(r.$_ptooltipModifiers=this.getModifiers(i),this.unbindEvents(r),!!i.value){if(typeof i.value=="string")r.$_ptooltipValue=i.value,r.$_ptooltipDisabled=!1,r.$_ptooltipEscape=!0,r.$_ptooltipClass=null,r.$_ptooltipIdAttr=r.$_ptooltipIdAttr||q("pv_id")+"_tooltip",r.$_ptooltipShowDelay=0,r.$_ptooltipHideDelay=0,r.$_ptooltipAutoHide=!0,this.bindEvents(r,i);else if(E(i.value)==="object"&&i.value)if(it(i.value.value)||i.value.value.trim()===""){this.unbindEvents(r,i);return}else r.$_ptooltipValue=i.value.value,r.$_ptooltipDisabled=!!i.value.disabled===i.value.disabled?i.value.disabled:!1,r.$_ptooltipEscape=!!i.value.escape===i.value.escape?i.value.escape:!0,r.$_ptooltipClass=i.value.class||"",r.$_ptooltipFitContent=!!i.value.fitContent===i.value.fitContent?i.value.fitContent:!0,r.$_ptooltipIdAttr=i.value.id||r.$_ptooltipIdAttr||q("pv_id")+"_tooltip",r.$_ptooltipShowDelay=i.value.showDelay||0,r.$_ptooltipHideDelay=i.value.hideDelay||0,r.$_ptooltipAutoHide=!!i.value.autoHide===i.value.autoHide?i.value.autoHide:!0,this.bindEvents(r,i)}},unmounted:function(t,i){var r=this.getTarget(t);this.hide(t,0),this.remove(r),this.unbindEvents(r,i),r.$_ptooltipScrollHandler&&(r.$_ptooltipScrollHandler.destroy(),r.$_ptooltipScrollHandler=null)},timer:void 0,methods:{bindEvents:function(t,i){var r=this,n=t.$_ptooltipModifiers;n.focus?(t.$_ptooltipFocusEvent=function(s){return r.onFocus(s,i)},t.$_ptooltipBlurEvent=this.onBlur.bind(this),t.addEventListener("focus",t.$_ptooltipFocusEvent),t.addEventListener("blur",t.$_ptooltipBlurEvent)):(t.$_ptooltipMouseEnterEvent=function(s){return r.onMouseEnter(s,i)},t.$_ptooltipMouseLeaveEvent=this.onMouseLeave.bind(this),t.$_ptooltipClickEvent=this.onClick.bind(this),t.addEventListener("mouseenter",t.$_ptooltipMouseEnterEvent),t.addEventListener("mouseleave",t.$_ptooltipMouseLeaveEvent),t.addEventListener("click",t.$_ptooltipClickEvent)),t.$_ptooltipKeydownEvent=this.onKeydown.bind(this),t.addEventListener("keydown",t.$_ptooltipKeydownEvent),t.$_pWindowResizeEvent=this.onWindowResize.bind(this,t)},unbindEvents:function(t){var i=t.$_ptooltipModifiers;i.focus?(t.removeEventListener("focus",t.$_ptooltipFocusEvent),t.$_ptooltipFocusEvent=null,t.removeEventListener("blur",t.$_ptooltipBlurEvent),t.$_ptooltipBlurEvent=null):(t.removeEventListener("mouseenter",t.$_ptooltipMouseEnterEvent),t.$_ptooltipMouseEnterEvent=null,t.removeEventListener("mouseleave",t.$_ptooltipMouseLeaveEvent),t.$_ptooltipMouseLeaveEvent=null,t.removeEventListener("click",t.$_ptooltipClickEvent),t.$_ptooltipClickEvent=null),t.removeEventListener("keydown",t.$_ptooltipKeydownEvent),window.removeEventListener("resize",t.$_pWindowResizeEvent),t.$_ptooltipId&&this.remove(t)},bindScrollListener:function(t){var i=this;t.$_ptooltipScrollHandler||(t.$_ptooltipScrollHandler=new Bt(t,function(){i.hide(t)})),t.$_ptooltipScrollHandler.bindScrollListener()},unbindScrollListener:function(t){t.$_ptooltipScrollHandler&&t.$_ptooltipScrollHandler.unbindScrollListener()},onMouseEnter:function(t,i){var r=t.currentTarget,n=r.$_ptooltipShowDelay;this.show(r,i,n)},onMouseLeave:function(t){var i=t.currentTarget,r=i.$_ptooltipHideDelay,n=i.$_ptooltipAutoHide;if(n)this.hide(i,r);else{var s=M(t.target,"data-pc-name")==="tooltip"||M(t.target,"data-pc-section")==="arrow"||M(t.target,"data-pc-section")==="text"||M(t.relatedTarget,"data-pc-name")==="tooltip"||M(t.relatedTarget,"data-pc-section")==="arrow"||M(t.relatedTarget,"data-pc-section")==="text";!s&&this.hide(i,r)}},onFocus:function(t,i){var r=t.currentTarget,n=r.$_ptooltipShowDelay;this.show(r,i,n)},onBlur:function(t){var i=t.currentTarget,r=i.$_ptooltipHideDelay;this.hide(i,r)},onClick:function(t){var i=t.currentTarget,r=i.$_ptooltipHideDelay;this.hide(i,r)},onKeydown:function(t){var i=t.currentTarget,r=i.$_ptooltipHideDelay;t.code==="Escape"&&this.hide(t.currentTarget,r)},onWindowResize:function(t){jt()||this.hide(t),window.removeEventListener("resize",t.$_pWindowResizeEvent)},tooltipActions:function(t,i){if(!(t.$_ptooltipDisabled||!xt(t))){var r=this.create(t,i);this.align(t),!this.isUnstyled()&&Ht(r,250);var n=this;window.addEventListener("resize",t.$_pWindowResizeEvent),r.addEventListener("mouseleave",function s(){n.hide(t),r.removeEventListener("mouseleave",s),t.removeEventListener("mouseenter",t.$_ptooltipMouseEnterEvent),setTimeout(function(){return t.addEventListener("mouseenter",t.$_ptooltipMouseEnterEvent)},50)}),this.bindScrollListener(t),rt.set("tooltip",r,t.$_ptooltipZIndex)}},show:function(t,i,r){var n=this;r!==void 0?this.timer=setTimeout(function(){return n.tooltipActions(t,i)},r):this.tooltipActions(t,i)},tooltipRemoval:function(t){this.remove(t),this.unbindScrollListener(t),window.removeEventListener("resize",t.$_pWindowResizeEvent)},hide:function(t,i){var r=this;clearTimeout(this.timer),i!==void 0?setTimeout(function(){return r.tooltipRemoval(t)},i):this.tooltipRemoval(t)},getTooltipElement:function(t){return document.getElementById(t.$_ptooltipId)},getArrowElement:function(t){var i=this.getTooltipElement(t);return et(i,'[data-pc-section="arrow"]')},create:function(t){var i=t.$_ptooltipModifiers,r=G("div",{class:!this.isUnstyled()&&this.cx("arrow"),"p-bind":this.ptm("arrow",{context:i})}),n=G("div",{class:!this.isUnstyled()&&this.cx("text"),"p-bind":this.ptm("text",{context:i})});t.$_ptooltipEscape?(n.innerHTML="",n.appendChild(document.createTextNode(t.$_ptooltipValue))):n.innerHTML=t.$_ptooltipValue;var s=G("div",ot(ot({id:t.$_ptooltipIdAttr,role:"tooltip",style:{display:"inline-block",width:t.$_ptooltipFitContent?"fit-content":void 0,pointerEvents:!this.isUnstyled()&&t.$_ptooltipAutoHide&&"none"},class:[!this.isUnstyled()&&this.cx("root"),t.$_ptooltipClass]},this.$attrSelector,""),"p-bind",this.ptm("root",{context:i})),r,n);return document.body.appendChild(s),t.$_ptooltipId=s.id,this.$el=s,s},remove:function(t){if(t){var i=this.getTooltipElement(t);i&&i.parentElement&&(rt.clear(i),document.body.removeChild(i)),t.$_ptooltipId=null}},align:function(t){var i=t.$_ptooltipModifiers;i.top?(this.alignTop(t),this.isOutOfBounds(t)&&(this.alignBottom(t),this.isOutOfBounds(t)&&this.alignTop(t))):i.left?(this.alignLeft(t),this.isOutOfBounds(t)&&(this.alignRight(t),this.isOutOfBounds(t)&&(this.alignTop(t),this.isOutOfBounds(t)&&(this.alignBottom(t),this.isOutOfBounds(t)&&this.alignLeft(t))))):i.bottom?(this.alignBottom(t),this.isOutOfBounds(t)&&(this.alignTop(t),this.isOutOfBounds(t)&&this.alignBottom(t))):(this.alignRight(t),this.isOutOfBounds(t)&&(this.alignLeft(t),this.isOutOfBounds(t)&&(this.alignTop(t),this.isOutOfBounds(t)&&(this.alignBottom(t),this.isOutOfBounds(t)&&this.alignRight(t)))))},getHostOffset:function(t){var i=t.getBoundingClientRect(),r=i.left+Qt(),n=i.top+Lt();return{left:r,top:n}},alignRight:function(t){this.preAlign(t,"right");var i=this.getTooltipElement(t),r=this.getArrowElement(t),n=this.getHostOffset(t),s=n.left+_(t),a=n.top+(T(t)-T(i))/2;i.style.left=s+"px",i.style.top=a+"px",r.style.top="50%",r.style.right=null,r.style.bottom=null,r.style.left="0"},alignLeft:function(t){this.preAlign(t,"left");var i=this.getTooltipElement(t),r=this.getArrowElement(t),n=this.getHostOffset(t),s=n.left-_(i),a=n.top+(T(t)-T(i))/2;i.style.left=s+"px",i.style.top=a+"px",r.style.top="50%",r.style.right="0",r.style.bottom=null,r.style.left=null},alignTop:function(t){this.preAlign(t,"top");var i=this.getTooltipElement(t),r=this.getArrowElement(t),n=_(i),s=_(t),a=W(),o=a.width,u=this.getHostOffset(t),l=u.left+(s-n)/2,d=u.top-T(i);l<0?l=0:l+n>o&&(l=Math.floor(u.left+s-n)),i.style.left=l+"px",i.style.top=d+"px";var h=u.left-this.getHostOffset(i).left+s/2;r.style.top=null,r.style.right=null,r.style.bottom="0",r.style.left=h+"px"},alignBottom:function(t){this.preAlign(t,"bottom");var i=this.getTooltipElement(t),r=this.getArrowElement(t),n=_(i),s=_(t),a=W(),o=a.width,u=this.getHostOffset(t),l=u.left+(s-n)/2,d=u.top+T(t);l<0?l=0:l+n>o&&(l=Math.floor(u.left+s-n)),i.style.left=l+"px",i.style.top=d+"px";var h=u.left-this.getHostOffset(i).left+s/2;r.style.top="0",r.style.right=null,r.style.bottom=null,r.style.left=h+"px"},preAlign:function(t,i){var r=this.getTooltipElement(t);r.style.left="-999px",r.style.top="-999px",Ft(r,"p-tooltip-".concat(r.$_ptooltipPosition)),!this.isUnstyled()&&Rt(r,"p-tooltip-".concat(i)),r.$_ptooltipPosition=i,r.setAttribute("data-p-position",i)},isOutOfBounds:function(t){var i=this.getTooltipElement(t),r=i.getBoundingClientRect(),n=r.top,s=r.left,a=_(i),o=T(i),u=W();return s+a>u.width||s<0||n<0||n+o>u.height},getTarget:function(t){var i;return Dt(t,"p-inputwrapper")&&(i=et(t,"input"))!==null&&i!==void 0?i:t},getModifiers:function(t){return t.modifiers&&Object.keys(t.modifiers).length?t.modifiers:t.arg&&E(t.arg)==="object"?Object.entries(t.arg).reduce(function(i,r){var n=le(r,2),s=n[0],a=n[1];return(s==="event"||s==="position")&&(i[a]=!0),i},{}):{}}}}),k=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},R=typeof window>"u"||"Deno"in globalThis;function b(){}function me(e,t){return typeof e=="function"?e(t):e}function ge(e){return typeof e=="number"&&e>=0&&e!==1/0}function be(e,t){return Math.max(e+(t||0)-Date.now(),0)}function z(e,t){return typeof e=="function"?e(t):e}function we(e,t){return typeof e=="function"?e(t):e}function at(e,t){const{type:i="all",exact:r,fetchStatus:n,predicate:s,queryKey:a,stale:o}=e;if(a){if(r){if(t.queryHash!==J(a,t.options))return!1}else if(!F(t.queryKey,a))return!1}if(i!=="all"){const u=t.isActive();if(i==="active"&&!u||i==="inactive"&&u)return!1}return!(typeof o=="boolean"&&t.isStale()!==o||n&&n!==t.state.fetchStatus||s&&!s(t))}function ut(e,t){const{exact:i,status:r,predicate:n,mutationKey:s}=e;if(s){if(!t.options.mutationKey)return!1;if(i){if(D(t.options.mutationKey)!==D(s))return!1}else if(!F(t.options.mutationKey,s))return!1}return!(r&&t.state.status!==r||n&&!n(t))}function J(e,t){return(t?.queryKeyHashFn||D)(e)}function D(e){return JSON.stringify(e,(t,i)=>V(i)?Object.keys(i).sort().reduce((r,n)=>(r[n]=i[n],r),{}):i)}function F(e,t){return e===t?!0:typeof e!=typeof t?!1:e&&t&&typeof e=="object"&&typeof t=="object"?Object.keys(t).every(i=>F(e[i],t[i])):!1}function mt(e,t){if(e===t)return e;const i=lt(e)&&lt(t);if(i||V(e)&&V(t)){const r=i?e:Object.keys(e),n=r.length,s=i?t:Object.keys(t),a=s.length,o=i?[]:{},u=new Set(r);let l=0;for(let d=0;d<a;d++){const h=i?d:s[d];(!i&&u.has(h)||i)&&e[h]===void 0&&t[h]===void 0?(o[h]=void 0,l++):(o[h]=mt(e[h],t[h]),o[h]===e[h]&&e[h]!==void 0&&l++)}return n===a&&l===n?e:o}return t}function lt(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function V(e){if(!ct(e))return!1;const t=e.constructor;if(t===void 0)return!0;const i=t.prototype;return!(!ct(i)||!i.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(e)!==Object.prototype)}function ct(e){return Object.prototype.toString.call(e)==="[object Object]"}function Se(e){return new Promise(t=>{setTimeout(t,e)})}function Ee(e,t,i){return typeof i.structuralSharing=="function"?i.structuralSharing(e,t):i.structuralSharing!==!1?mt(e,t):t}function Oe(e,t,i=0){const r=[...e,t];return i&&r.length>i?r.slice(1):r}function _e(e,t,i=0){const r=[t,...e];return i&&r.length>i?r.slice(0,-1):r}var Z=Symbol();function gt(e,t){return!e.queryFn&&t?.initialPromise?()=>t.initialPromise:!e.queryFn||e.queryFn===Z?()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`)):e.queryFn}var Te=class extends k{#t;#e;#i;constructor(){super(),this.#i=e=>{if(!R&&window.addEventListener){const t=()=>e();return window.addEventListener("visibilitychange",t,!1),()=>{window.removeEventListener("visibilitychange",t)}}}}onSubscribe(){this.#e||this.setEventListener(this.#i)}onUnsubscribe(){this.hasListeners()||(this.#e?.(),this.#e=void 0)}setEventListener(e){this.#i=e,this.#e?.(),this.#e=e(t=>{typeof t=="boolean"?this.setFocused(t):this.onFocus()})}setFocused(e){this.#t!==e&&(this.#t=e,this.onFocus())}onFocus(){const e=this.isFocused();this.listeners.forEach(t=>{t(e)})}isFocused(){return typeof this.#t=="boolean"?this.#t:globalThis.document?.visibilityState!=="hidden"}},bt=new Te,Ce=class extends k{#t=!0;#e;#i;constructor(){super(),this.#i=e=>{if(!R&&window.addEventListener){const t=()=>e(!0),i=()=>e(!1);return window.addEventListener("online",t,!1),window.addEventListener("offline",i,!1),()=>{window.removeEventListener("online",t),window.removeEventListener("offline",i)}}}}onSubscribe(){this.#e||this.setEventListener(this.#i)}onUnsubscribe(){this.hasListeners()||(this.#e?.(),this.#e=void 0)}setEventListener(e){this.#i=e,this.#e?.(),this.#e=e(this.setOnline.bind(this))}setOnline(e){this.#t!==e&&(this.#t=e,this.listeners.forEach(i=>{i(e)}))}isOnline(){return this.#t}},B=new Ce;function Me(){let e,t;const i=new Promise((n,s)=>{e=n,t=s});i.status="pending",i.catch(()=>{});function r(n){Object.assign(i,n),delete i.resolve,delete i.reject}return i.resolve=n=>{r({status:"fulfilled",value:n}),e(n)},i.reject=n=>{r({status:"rejected",reason:n}),t(n)},i}function Ae(e){return Math.min(1e3*2**e,3e4)}function wt(e){return(e??"online")==="online"?B.isOnline():!0}var St=class extends Error{constructor(e){super("CancelledError"),this.revert=e?.revert,this.silent=e?.silent}};function Et(e){let t=!1,i=0,r;const n=Me(),s=()=>n.status!=="pending",a=f=>{s()||(w(new St(f)),e.abort?.())},o=()=>{t=!0},u=()=>{t=!1},l=()=>bt.isFocused()&&(e.networkMode==="always"||B.isOnline())&&e.canRun(),d=()=>wt(e.networkMode)&&e.canRun(),h=f=>{s()||(r?.(),n.resolve(f))},w=f=>{s()||(r?.(),n.reject(f))},S=()=>new Promise(f=>{r=g=>{(s()||l())&&f(g)},e.onPause?.()}).then(()=>{r=void 0,s()||e.onContinue?.()}),p=()=>{if(s())return;let f;const g=i===0?e.initialPromise:void 0;try{f=g??e.fn()}catch(y){f=Promise.reject(y)}Promise.resolve(f).then(h).catch(y=>{if(s())return;const O=e.retry??(R?0:3),P=e.retryDelay??Ae,Q=typeof P=="function"?P(i,y):P,L=O===!0||typeof O=="number"&&i<O||typeof O=="function"&&O(i,y);if(t||!L){w(y);return}i++,e.onFail?.(i,y),Se(Q).then(()=>l()?void 0:S()).then(()=>{t?w(y):p()})})};return{promise:n,status:()=>n.status,cancel:a,continue:()=>(r?.(),n),cancelRetry:o,continueRetry:u,canStart:d,start:()=>(d()?p():S().then(p),n)}}var Pe=e=>setTimeout(e,0);function $e(){let e=[],t=0,i=o=>{o()},r=o=>{o()},n=Pe;const s=o=>{t?e.push(o):n(()=>{i(o)})},a=()=>{const o=e;e=[],o.length&&n(()=>{r(()=>{o.forEach(u=>{i(u)})})})};return{batch:o=>{let u;t++;try{u=o()}finally{t--,t||a()}return u},batchCalls:o=>(...u)=>{s(()=>{o(...u)})},schedule:s,setNotifyFunction:o=>{i=o},setBatchNotifyFunction:o=>{r=o},setScheduler:o=>{n=o}}}var m=$e(),Ot=class{#t;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),ge(this.gcTime)&&(this.#t=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(R?1/0:300*1e3))}clearGcTimeout(){this.#t&&(clearTimeout(this.#t),this.#t=void 0)}},De=class extends Ot{#t;#e;#i;#n;#r;#o;#a;constructor(e){super(),this.#a=!1,this.#o=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.#n=e.client,this.#i=this.#n.getQueryCache(),this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.#t=Re(this.options),this.state=e.state??this.#t,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#r?.promise}setOptions(e){this.options={...this.#o,...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&this.#i.remove(this)}setData(e,t){const i=Ee(this.state.data,e,this.options);return this.#s({data:i,type:"success",dataUpdatedAt:t?.updatedAt,manual:t?.manual}),i}setState(e,t){this.#s({type:"setState",state:e,setStateOptions:t})}cancel(e){const t=this.#r?.promise;return this.#r?.cancel(e),t?t.then(b).catch(b):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#t)}isActive(){return this.observers.some(e=>we(e.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===Z||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0?this.observers.some(e=>z(e.options.staleTime,this)==="static"):!1}isStale(){return this.getObserversCount()>0?this.observers.some(e=>e.getCurrentResult().isStale):this.state.data===void 0||this.state.isInvalidated}isStaleByTime(e=0){return this.state.data===void 0?!0:e==="static"?!1:this.state.isInvalidated?!0:!be(this.state.dataUpdatedAt,e)}onFocus(){this.observers.find(t=>t.shouldFetchOnWindowFocus())?.refetch({cancelRefetch:!1}),this.#r?.continue()}onOnline(){this.observers.find(t=>t.shouldFetchOnReconnect())?.refetch({cancelRefetch:!1}),this.#r?.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.#i.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter(t=>t!==e),this.observers.length||(this.#r&&(this.#a?this.#r.cancel({revert:!0}):this.#r.cancelRetry()),this.scheduleGc()),this.#i.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#s({type:"invalidate"})}async fetch(e,t){if(this.state.fetchStatus!=="idle"&&this.#r?.status()!=="rejected"){if(this.state.data!==void 0&&t?.cancelRefetch)this.cancel({silent:!0});else if(this.#r)return this.#r.continueRetry(),this.#r.promise}if(e&&this.setOptions(e),!this.options.queryFn){const o=this.observers.find(u=>u.options.queryFn);o&&this.setOptions(o.options)}const i=new AbortController,r=o=>{Object.defineProperty(o,"signal",{enumerable:!0,get:()=>(this.#a=!0,i.signal)})},n=()=>{const o=gt(this.options,t),l=(()=>{const d={client:this.#n,queryKey:this.queryKey,meta:this.meta};return r(d),d})();return this.#a=!1,this.options.persister?this.options.persister(o,l,this):o(l)},a=(()=>{const o={fetchOptions:t,options:this.options,queryKey:this.queryKey,client:this.#n,state:this.state,fetchFn:n};return r(o),o})();this.options.behavior?.onFetch(a,this),this.#e=this.state,(this.state.fetchStatus==="idle"||this.state.fetchMeta!==a.fetchOptions?.meta)&&this.#s({type:"fetch",meta:a.fetchOptions?.meta}),this.#r=Et({initialPromise:t?.initialPromise,fn:a.fetchFn,abort:i.abort.bind(i),onFail:(o,u)=>{this.#s({type:"failed",failureCount:o,error:u})},onPause:()=>{this.#s({type:"pause"})},onContinue:()=>{this.#s({type:"continue"})},retry:a.options.retry,retryDelay:a.options.retryDelay,networkMode:a.options.networkMode,canRun:()=>!0});try{const o=await this.#r.start();if(o===void 0)throw new Error(`${this.queryHash} data is undefined`);return this.setData(o),this.#i.config.onSuccess?.(o,this),this.#i.config.onSettled?.(o,this.state.error,this),o}catch(o){if(o instanceof St){if(o.silent)return this.#r.promise;if(o.revert)return this.setState({...this.#e,fetchStatus:"idle"}),this.state.data}throw this.#s({type:"error",error:o}),this.#i.config.onError?.(o,this),this.#i.config.onSettled?.(this.state.data,o,this),o}finally{this.scheduleGc()}}#s(e){const t=i=>{switch(e.type){case"failed":return{...i,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...i,fetchStatus:"paused"};case"continue":return{...i,fetchStatus:"fetching"};case"fetch":return{...i,...Fe(i.data,this.options),fetchMeta:e.meta??null};case"success":const r={...i,data:e.data,dataUpdateCount:i.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};return this.#e=e.manual?r:void 0,r;case"error":const n=e.error;return{...i,error:n,errorUpdateCount:i.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:i.fetchFailureCount+1,fetchFailureReason:n,fetchStatus:"idle",status:"error"};case"invalidate":return{...i,isInvalidated:!0};case"setState":return{...i,...e.state}}};this.state=t(this.state),m.batch(()=>{this.observers.forEach(i=>{i.onQueryUpdate()}),this.#i.notify({query:this,type:"updated",action:e})})}};function Fe(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:wt(t.networkMode)?"fetching":"paused",...e===void 0&&{error:null,status:"pending"}}}function Re(e){const t=typeof e.initialData=="function"?e.initialData():e.initialData,i=t!==void 0,r=i?typeof e.initialDataUpdatedAt=="function"?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:i?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:i?"success":"pending",fetchStatus:"idle"}}var _t=class extends k{constructor(t={}){super(),this.config=t,this.#t=new Map}#t;build(t,i,r){const n=i.queryKey,s=i.queryHash??J(n,i);let a=this.get(s);return a||(a=new De({client:t,queryKey:n,queryHash:s,options:t.defaultQueryOptions(i),state:r,defaultOptions:t.getQueryDefaults(n)}),this.add(a)),a}add(t){this.#t.has(t.queryHash)||(this.#t.set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const i=this.#t.get(t.queryHash);i&&(t.destroy(),i===t&&this.#t.delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){m.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return this.#t.get(t)}getAll(){return[...this.#t.values()]}find(t){const i={exact:!0,...t};return this.getAll().find(r=>at(i,r))}findAll(t={}){const i=this.getAll();return Object.keys(t).length>0?i.filter(r=>at(t,r)):i}notify(t){m.batch(()=>{this.listeners.forEach(i=>{i(t)})})}onFocus(){m.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){m.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},Qe=class extends Ot{#t;#e;#i;constructor(e){super(),this.mutationId=e.mutationId,this.#e=e.mutationCache,this.#t=[],this.state=e.state||Le(),this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#t.includes(e)||(this.#t.push(e),this.clearGcTimeout(),this.#e.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#t=this.#t.filter(t=>t!==e),this.scheduleGc(),this.#e.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#t.length||(this.state.status==="pending"?this.scheduleGc():this.#e.remove(this))}continue(){return this.#i?.continue()??this.execute(this.state.variables)}async execute(e){const t=()=>{this.#n({type:"continue"})};this.#i=Et({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(new Error("No mutationFn found")),onFail:(n,s)=>{this.#n({type:"failed",failureCount:n,error:s})},onPause:()=>{this.#n({type:"pause"})},onContinue:t,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#e.canRun(this)});const i=this.state.status==="pending",r=!this.#i.canStart();try{if(i)t();else{this.#n({type:"pending",variables:e,isPaused:r}),await this.#e.config.onMutate?.(e,this);const s=await this.options.onMutate?.(e);s!==this.state.context&&this.#n({type:"pending",context:s,variables:e,isPaused:r})}const n=await this.#i.start();return await this.#e.config.onSuccess?.(n,e,this.state.context,this),await this.options.onSuccess?.(n,e,this.state.context),await this.#e.config.onSettled?.(n,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(n,null,e,this.state.context),this.#n({type:"success",data:n}),n}catch(n){try{throw await this.#e.config.onError?.(n,e,this.state.context,this),await this.options.onError?.(n,e,this.state.context),await this.#e.config.onSettled?.(void 0,n,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,n,e,this.state.context),n}finally{this.#n({type:"error",error:n})}}finally{this.#e.runNext(this)}}#n(e){const t=i=>{switch(e.type){case"failed":return{...i,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...i,isPaused:!0};case"continue":return{...i,isPaused:!1};case"pending":return{...i,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...i,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...i,data:void 0,error:e.error,failureCount:i.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}};this.state=t(this.state),m.batch(()=>{this.#t.forEach(i=>{i.onMutationUpdate(e)}),this.#e.notify({mutation:this,type:"updated",action:e})})}};function Le(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var Tt=class extends k{constructor(t={}){super(),this.config=t,this.#t=new Set,this.#e=new Map,this.#i=0}#t;#e;#i;build(t,i,r){const n=new Qe({mutationCache:this,mutationId:++this.#i,options:t.defaultMutationOptions(i),state:r});return this.add(n),n}add(t){this.#t.add(t);const i=N(t);if(typeof i=="string"){const r=this.#e.get(i);r?r.push(t):this.#e.set(i,[t])}this.notify({type:"added",mutation:t})}remove(t){if(this.#t.delete(t)){const i=N(t);if(typeof i=="string"){const r=this.#e.get(i);if(r)if(r.length>1){const n=r.indexOf(t);n!==-1&&r.splice(n,1)}else r[0]===t&&this.#e.delete(i)}}this.notify({type:"removed",mutation:t})}canRun(t){const i=N(t);if(typeof i=="string"){const n=this.#e.get(i)?.find(s=>s.state.status==="pending");return!n||n===t}else return!0}runNext(t){const i=N(t);return typeof i=="string"?this.#e.get(i)?.find(n=>n!==t&&n.state.isPaused)?.continue()??Promise.resolve():Promise.resolve()}clear(){m.batch(()=>{this.#t.forEach(t=>{this.notify({type:"removed",mutation:t})}),this.#t.clear(),this.#e.clear()})}getAll(){return Array.from(this.#t)}find(t){const i={exact:!0,...t};return this.getAll().find(r=>ut(i,r))}findAll(t={}){return this.getAll().filter(i=>ut(t,i))}notify(t){m.batch(()=>{this.listeners.forEach(i=>{i(t)})})}resumePausedMutations(){const t=this.getAll().filter(i=>i.state.isPaused);return m.batch(()=>Promise.all(t.map(i=>i.continue().catch(b))))}};function N(e){return e.options.scope?.id}function dt(e){return{onFetch:(t,i)=>{const r=t.options,n=t.fetchOptions?.meta?.fetchMore?.direction,s=t.state.data?.pages||[],a=t.state.data?.pageParams||[];let o={pages:[],pageParams:[]},u=0;const l=async()=>{let d=!1;const h=p=>{Object.defineProperty(p,"signal",{enumerable:!0,get:()=>(t.signal.aborted?d=!0:t.signal.addEventListener("abort",()=>{d=!0}),t.signal)})},w=gt(t.options,t.fetchOptions),S=async(p,f,g)=>{if(d)return Promise.reject();if(f==null&&p.pages.length)return Promise.resolve(p);const O=(()=>{const X={client:t.client,queryKey:t.queryKey,pageParam:f,direction:g?"backward":"forward",meta:t.options.meta};return h(X),X})(),P=await w(O),{maxPages:Q}=t.options,L=g?_e:Oe;return{pages:L(p.pages,P,Q),pageParams:L(p.pageParams,f,Q)}};if(n&&s.length){const p=n==="backward",f=p?xe:ht,g={pages:s,pageParams:a},y=f(r,g);o=await S(g,y,p)}else{const p=e??s.length;do{const f=u===0?a[0]??r.initialPageParam:ht(r,o);if(u>0&&f==null)break;o=await S(o,f),u++}while(u<p)}return o};t.options.persister?t.fetchFn=()=>t.options.persister?.(l,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},i):t.fetchFn=l}}}function ht(e,{pages:t,pageParams:i}){const r=t.length-1;return t.length>0?e.getNextPageParam(t[r],t,i[r],i):void 0}function xe(e,{pages:t,pageParams:i}){return t.length>0?e.getPreviousPageParam?.(t[0],t,i[0],i):void 0}var He=class{#t;#e;#i;#n;#r;#o;#a;#s;constructor(t={}){this.#t=t.queryCache||new _t,this.#e=t.mutationCache||new Tt,this.#i=t.defaultOptions||{},this.#n=new Map,this.#r=new Map,this.#o=0}mount(){this.#o++,this.#o===1&&(this.#a=bt.subscribe(async t=>{t&&(await this.resumePausedMutations(),this.#t.onFocus())}),this.#s=B.subscribe(async t=>{t&&(await this.resumePausedMutations(),this.#t.onOnline())}))}unmount(){this.#o--,this.#o===0&&(this.#a?.(),this.#a=void 0,this.#s?.(),this.#s=void 0)}isFetching(t){return this.#t.findAll({...t,fetchStatus:"fetching"}).length}isMutating(t){return this.#e.findAll({...t,status:"pending"}).length}getQueryData(t){const i=this.defaultQueryOptions({queryKey:t});return this.#t.get(i.queryHash)?.state.data}ensureQueryData(t){const i=this.defaultQueryOptions(t),r=this.#t.build(this,i),n=r.state.data;return n===void 0?this.fetchQuery(t):(t.revalidateIfStale&&r.isStaleByTime(z(i.staleTime,r))&&this.prefetchQuery(i),Promise.resolve(n))}getQueriesData(t){return this.#t.findAll(t).map(({queryKey:i,state:r})=>{const n=r.data;return[i,n]})}setQueryData(t,i,r){const n=this.defaultQueryOptions({queryKey:t}),a=this.#t.get(n.queryHash)?.state.data,o=me(i,a);if(o!==void 0)return this.#t.build(this,n).setData(o,{...r,manual:!0})}setQueriesData(t,i,r){return m.batch(()=>this.#t.findAll(t).map(({queryKey:n})=>[n,this.setQueryData(n,i,r)]))}getQueryState(t){const i=this.defaultQueryOptions({queryKey:t});return this.#t.get(i.queryHash)?.state}removeQueries(t){const i=this.#t;m.batch(()=>{i.findAll(t).forEach(r=>{i.remove(r)})})}resetQueries(t,i){const r=this.#t;return m.batch(()=>(r.findAll(t).forEach(n=>{n.reset()}),this.refetchQueries({type:"active",...t},i)))}cancelQueries(t,i={}){const r={revert:!0,...i},n=m.batch(()=>this.#t.findAll(t).map(s=>s.cancel(r)));return Promise.all(n).then(b).catch(b)}invalidateQueries(t,i={}){return m.batch(()=>(this.#t.findAll(t).forEach(r=>{r.invalidate()}),t?.refetchType==="none"?Promise.resolve():this.refetchQueries({...t,type:t?.refetchType??t?.type??"active"},i)))}refetchQueries(t,i={}){const r={...i,cancelRefetch:i.cancelRefetch??!0},n=m.batch(()=>this.#t.findAll(t).filter(s=>!s.isDisabled()&&!s.isStatic()).map(s=>{let a=s.fetch(void 0,r);return r.throwOnError||(a=a.catch(b)),s.state.fetchStatus==="paused"?Promise.resolve():a}));return Promise.all(n).then(b)}fetchQuery(t){const i=this.defaultQueryOptions(t);i.retry===void 0&&(i.retry=!1);const r=this.#t.build(this,i);return r.isStaleByTime(z(i.staleTime,r))?r.fetch(i):Promise.resolve(r.state.data)}prefetchQuery(t){return this.fetchQuery(t).then(b).catch(b)}fetchInfiniteQuery(t){return t.behavior=dt(t.pages),this.fetchQuery(t)}prefetchInfiniteQuery(t){return this.fetchInfiniteQuery(t).then(b).catch(b)}ensureInfiniteQueryData(t){return t.behavior=dt(t.pages),this.ensureQueryData(t)}resumePausedMutations(){return B.isOnline()?this.#e.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#t}getMutationCache(){return this.#e}getDefaultOptions(){return this.#i}setDefaultOptions(t){this.#i=t}setQueryDefaults(t,i){this.#n.set(D(t),{queryKey:t,defaultOptions:i})}getQueryDefaults(t){const i=[...this.#n.values()],r={};return i.forEach(n=>{F(t,n.queryKey)&&Object.assign(r,n.defaultOptions)}),r}setMutationDefaults(t,i){this.#r.set(D(t),{mutationKey:t,defaultOptions:i})}getMutationDefaults(t){const i=[...this.#r.values()],r={};return i.forEach(n=>{F(t,n.mutationKey)&&Object.assign(r,n.defaultOptions)}),r}defaultQueryOptions(t){if(t._defaulted)return t;const i={...this.#i.queries,...this.getQueryDefaults(t.queryKey),...t,_defaulted:!0};return i.queryHash||(i.queryHash=J(i.queryKey,i)),i.refetchOnReconnect===void 0&&(i.refetchOnReconnect=i.networkMode!=="always"),i.throwOnError===void 0&&(i.throwOnError=!!i.suspense),!i.networkMode&&i.persister&&(i.networkMode="offlineFirst"),i.queryFn===Z&&(i.enabled=!1),i}defaultMutationOptions(t){return t?._defaulted?t:{...this.#i.mutations,...t?.mutationKey&&this.getMutationDefaults(t.mutationKey),...t,_defaulted:!0}}clear(){this.#t.clear(),this.#e.clear()}},je="VUE_QUERY_CLIENT";function qe(e){const t=e?`:${e}`:"";return`${je}${t}`}function Y(e,t,i="",r=0){if(t){const n=t(e,i,r);if(n===void 0&&yt(e)||n!==void 0)return n}if(Array.isArray(e))return e.map((n,s)=>Y(n,t,String(s),r+1));if(typeof e=="object"&&Ne(e)){const n=Object.entries(e).map(([s,a])=>[s,Y(a,t,s,r+1)]);return Object.fromEntries(n)}return e}function Ie(e,t){return Y(e,t)}function c(e,t=!1){return Ie(e,(i,r,n)=>{if(n===1&&r==="queryKey")return c(i,!0);if(t&&Ue(i))return c(i(),t);if(yt(i))return c(Kt(i),t)})}function Ne(e){if(Object.prototype.toString.call(e)!=="[object Object]")return!1;const t=Object.getPrototypeOf(e);return t===null||t===Object.prototype}function Ue(e){return typeof e=="function"}var Be=class extends _t{find(e){return super.find(c(e))}findAll(e={}){return super.findAll(c(e))}},ke=class extends Tt{find(e){return super.find(c(e))}findAll(e={}){return super.findAll(c(e))}},Ct=class extends He{constructor(e={}){const t={defaultOptions:e.defaultOptions,queryCache:e.queryCache||new Be,mutationCache:e.mutationCache||new ke};super(t),this.isRestoring=vt(!1)}isFetching(e={}){return super.isFetching(c(e))}isMutating(e={}){return super.isMutating(c(e))}getQueryData(e){return super.getQueryData(c(e))}ensureQueryData(e){return super.ensureQueryData(c(e))}getQueriesData(e){return super.getQueriesData(c(e))}setQueryData(e,t,i={}){return super.setQueryData(c(e),t,c(i))}setQueriesData(e,t,i={}){return super.setQueriesData(c(e),t,c(i))}getQueryState(e){return super.getQueryState(c(e))}removeQueries(e={}){return super.removeQueries(c(e))}resetQueries(e={},t={}){return super.resetQueries(c(e),c(t))}cancelQueries(e={},t={}){return super.cancelQueries(c(e),c(t))}invalidateQueries(e={},t={}){const i=c(e),r=c(t);if(super.invalidateQueries({...i,refetchType:"none"},r),i.refetchType==="none")return Promise.resolve();const n={...i,type:i.refetchType??i.type??"active"};return qt().then(()=>super.refetchQueries(n,r))}refetchQueries(e={},t={}){return super.refetchQueries(c(e),c(t))}fetchQuery(e){return super.fetchQuery(c(e))}prefetchQuery(e){return super.prefetchQuery(c(e))}fetchInfiniteQuery(e){return super.fetchInfiniteQuery(c(e))}prefetchInfiniteQuery(e){return super.prefetchInfiniteQuery(c(e))}setDefaultOptions(e){super.setDefaultOptions(c(e))}setQueryDefaults(e,t){super.setQueryDefaults(c(e),c(t))}getQueryDefaults(e){return super.getQueryDefaults(c(e))}setMutationDefaults(e,t){super.setMutationDefaults(c(e),c(t))}getMutationDefaults(e){return super.getMutationDefaults(c(e))}},Ke={install:(e,t={})=>{const i=qe(t.queryClientKey);let r;if("queryClient"in t&&t.queryClient)r=t.queryClient;else{const a="queryClientConfig"in t?t.queryClientConfig:void 0;r=new Ct(a)}R||r.mount();let n=()=>{};if(t.clientPersister){r.isRestoring&&(r.isRestoring.value=!0);const[a,o]=t.clientPersister(r);n=a,o.then(()=>{r.isRestoring&&(r.isRestoring.value=!1),t.clientPersisterOnSuccess?.(r)})}const s=()=>{r.unmount(),n()};if(e.onUnmount)e.onUnmount(s);else{const a=e.unmount;e.unmount=function(){s(),a()}}e.provide(i,r)}};const Mt=e=>{e.use(ie,{unstyled:!0});const t=new Ct({defaultOptions:{queries:{staleTime:3e4,gcTime:5*6e4,retry:1,refetchOnWindowFocus:!1,refetchOnReconnect:!0},mutations:{retry:1}}});e.use(Ke,{queryClient:t}),e.use(re),e.use(ne),e.directive("tooltip",ye)},We=Object.freeze(Object.defineProperty({__proto__:null,default:Mt},Symbol.toStringTag,{value:"Module"})),Ge=async e=>{"default"in We&&await Mt(e)},ze=It({props:{value:String,name:String,hydrate:{type:Boolean,default:!0}},setup({name:e,value:t,hydrate:i}){if(!t)return()=>null;let r=i?"astro-slot":"astro-static-slot";return()=>U(r,{name:e,innerHTML:t})}});var Ve=ze;let ft=new WeakMap;var ci=e=>async(t,i,r,{client:n})=>{if(!e.hasAttribute("ssr"))return;const s=t.name?`${t.name} Host`:void 0,a={};for(const[d,h]of Object.entries(r))a[d]=()=>U(Ve,{value:h,name:d==="default"?void 0:d});const o=n!=="only",u=o?Wt:Gt;let l=ft.get(e);if(l)l.props=i,l.slots=a,l.component.$forceUpdate();else{l={props:i,slots:a};const d=u({name:s,render(){let h=U(t,l.props,l.slots);return l.component=this,Ye(t.setup)&&(h=U(Nt,null,h)),h}});d.config.idPrefix=e.getAttribute("prefix")??void 0,await Ge(d),d.mount(e,o),ft.set(e,l),e.addEventListener("astro:unmount",()=>d.unmount(),{once:!0})}};function Ye(e){const t=e?.constructor;return t&&t.name==="AsyncFunction"}export{ci as default};
