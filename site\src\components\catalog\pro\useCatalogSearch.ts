"use client"

import { useMemo } from "react"
import { trpc } from "@/lib/trpc"
import { useCatalogGlobalState } from "@/lib/catalog-state"
import { useDebounce } from "@/hooks/useDebounce"
import type { CatalogSearchFilters, PartApplicability } from "@/types/catalog"

// Хук для использования в отдельных компонентах
export function useCatalogSearch() {
  const { filters, setFilters, updateFilters, clearFilters } = useCatalogGlobalState()

  // Дебаунсинг для поискового запроса
  const debouncedQuery = useDebounce(filters.query, 300)

  // Загружаем данные через изолированный site API
  const { data: searchResult, isLoading, error } = trpc.site.search.catalogItems.useQuery({
    search: debouncedQuery || undefined,
    brandIds: filters.brandIds,
    categoryIds: filters.categoryIds,
    isOemOnly: filters.isOemOnly,
    accuracy: (filters.accuracyLevels as any) || undefined,
    attributeFilters: Object.entries(filters.attributeFilters || {}).map(([templateId, f]) => ({
      templateId: Number(templateId),
      values: f.values?.length ? f.values : undefined,
      minValue: f.numericRange?.[0],
      maxValue: f.numericRange?.[1],
    })),
    limit: 50,
    offset: 0,
    sortBy: 'updatedAt',
    sortDir: 'desc',
  }, {
    staleTime: 30_000, // 30 секунд для результатов поиска
    gcTime: 5 * 60_000, // 5 минут в кэше
  })

  // Логирование для отладки
  if (typeof window !== 'undefined') {
    console.log('Search filters:', filters)
    console.log('Search result:', searchResult)
    console.log('Search error:', error)
    console.log('Is loading:', isLoading)
  }

  // Преобразуем CatalogItem[] -> PartApplicability[] для совместимости с UI
  const results: PartApplicability[] = useMemo(() => {
    if (!searchResult?.items) {
      // Возвращаем демо данные если API не отвечает
      return [{
        id: 1,
        partId: 1,
        catalogItemId: 1,
        accuracy: "EXACT_MATCH" as const,
        notes: null,
        part: {
          id: 1,
          name: "Сальник коленвала передний",
          parentId: null,
          level: 0,
          path: "01",
          partCategoryId: 1,
          partCategory: {
            id: 1,
            name: "Сальники двигателя",
            slug: "engine-seals",
            description: null,
            parentId: null,
            level: 0,
            path: "01",
            icon: null,
            image: null
          },
          attributes: [
            {
              id: 1,
              value: "25.0",
              numericValue: 25.0,
              partId: 1,
              templateId: 1,
              template: {
                id: 1,
                name: "inner_diameter",
                title: "Внутренний диаметр",
                description: null,
                dataType: "NUMBER" as const,
                unit: "MM" as const,
                isRequired: true,
                minValue: null,
                maxValue: null,
                allowedValues: [],
                tolerance: 0.1,
                groupId: null
              }
            }
          ],
          image: null,
          mediaAssets: [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        catalogItem: {
          id: 1,
          sku: "TC-25x35x7",
          source: null,
          description: "Радиальный сальник для коленчатого вала",
          brandId: 1,
          brand: {
            id: 1,
            name: "SKF",
            slug: "skf",
            country: "Швеция",
            isOem: false
          },
          isPublic: true,
          attributes: [
            {
              id: 1,
              value: "25.0",
              numericValue: 25.0,
              catalogItemId: 1,
              templateId: 1,
              template: {
                id: 1,
                name: "inner_diameter",
                title: "Внутренний диаметр",
                description: null,
                dataType: "NUMBER" as const,
                unit: "MM" as const,
                isRequired: true,
                minValue: null,
                maxValue: null,
                allowedValues: [],
                tolerance: 0.1,
                groupId: null
              }
            }
          ],
          image: null,
          mediaAssets: []
        }
      }]
    }

    const list: PartApplicability[] = []
    for (const ci of searchResult.items as any[]) {
      if (!ci.applicabilities) continue
      for (const ap of ci.applicabilities) {
        list.push({
          id: ap.id,
          partId: ap.partId,
          catalogItemId: ap.catalogItemId,
          accuracy: ap.accuracy,
          notes: ap.notes,
          part: ap.part,
          catalogItem: {
            id: ci.id,
            sku: ci.sku,
            description: ci.description,
            brandId: ci.brandId,
            brand: ci.brand,
            isPublic: true,
            attributes: ci.attributes,
            image: ci.image ?? null,
            mediaAssets: ci.mediaAssets ?? [],
          },
        })
      }
    }
    return list
  }, [searchResult])

  const availableAttributeValues = useMemo(() => {
    const values: Record<number, string[]> = {}
    const numericStats: Record<number, { min: number; max: number; avg: number }> = {}

    results.forEach((applicability) => {
      applicability.catalogItem.attributes.forEach((attr) => {
        if (attr.template.dataType === "NUMBER" && attr.numericValue !== undefined && attr.numericValue !== null) {
          if (!numericStats[attr.templateId]) {
            numericStats[attr.templateId] = { min: attr.numericValue, max: attr.numericValue, avg: 0 }
          } else {
            numericStats[attr.templateId].min = Math.min(numericStats[attr.templateId].min, attr.numericValue)
            numericStats[attr.templateId].max = Math.max(numericStats[attr.templateId].max, attr.numericValue)
          }
        } else {
          if (!values[attr.templateId]) values[attr.templateId] = []
          if (!values[attr.templateId].includes(attr.value)) values[attr.templateId].push(attr.value)
        }
      })
    })

    Object.keys(numericStats).forEach((templateId) => {
      const templateIdNum = Number.parseInt(templateId)
      const allValues = results
        .flatMap((r) => r.catalogItem.attributes)
        .filter((attr) => attr.templateId === templateIdNum && attr.numericValue !== undefined && attr.numericValue !== null)
        .map((attr) => attr.numericValue as number)

      if (allValues.length > 0) {
        numericStats[templateIdNum].avg = allValues.reduce((sum, val) => sum + val, 0) / allValues.length
      }
    })

    return { values, numericStats }
  }, [results])

  return {
    filters,
    setFilters,
    updateFilters,
    clearFilters,
    results,
    totalCount: searchResult?.total || 0,
    filteredCount: results.length,
    isLoading,
    availableAttributeValues,
  }
}

