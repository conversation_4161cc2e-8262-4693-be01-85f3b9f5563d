# База данных
POSTGRES_PASSWORD=your_secure_postgres_password

# Аутентификация
BETTER_AUTH_SECRET=your_very_long_random_secret_key_here

# URL сервисов для development
API_URL=http://localhost:3000
CPANEL_URL=http://localhost:4322
SITE_URL=http://localhost:4323

# Домены для production (используются в docker-compose.prod.yml)
API_DOMAIN=api.yourdomain.com
CPANEL_DOMAIN=admin.yourdomain.com
SITE_DOMAIN=yourdomain.com

# URL для production
# API_URL=https://api.yourdomain.com
# CPANEL_URL=https://admin.yourdomain.com
# SITE_URL=https://yourdomain.com
