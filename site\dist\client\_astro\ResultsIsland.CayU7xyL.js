import{j as e}from"./jsx-runtime.D_zvdyIk.js";import{r as j}from"./index.0yr9KlQE.js";import{M as i,T as S}from"./TrpcBoundary.g7wmOpBw.js";import{M as y,e as k}from"./index.Di12BYGB.js";import{B as c}from"./badge.CrhVNNKw.js";import{C as _,S as A}from"./separator.1hI8CDE4.js";import{S as T}from"./status-badge.BsGsEBtY.js";import{c as v}from"./utils.CBfrqCZ4.js";import{I as b,F as M}from"./image.BtzVA0_o.js";import{c as l}from"./createLucideIcon.DdiNmGRb.js";import{Z as E}from"./zap.hS7937cF.js";import{u as $}from"./useCatalogSearch.3hHK2Zsb.js";import{P as F}from"./package.CV5wAU3g.js";import"./index.BWNh2K4G.js";import"./index.3rXK4OIH.js";import"./trpc.9HS4D061.js";import"./index.ViApDAiE.js";/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const D=[["path",{d:"M8 3 4 7l4 4",key:"9rb6wj"}],["path",{d:"M4 7h16",key:"6tx8e3"}],["path",{d:"m16 21 4-4-4-4",key:"siv7j2"}],["path",{d:"M20 17H4",key:"h6l3hr"}]],L=l("arrow-left-right",D);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const B=[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]],V=l("building",B);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const R=[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]],U=l("chart-column",R);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const P=[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]],O=l("clock",P);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Q=[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],Z=l("eye",Q);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const G=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]],H=l("grid-3x3",G);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const W=[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]],q=l("list",W);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const J=[["path",{d:"M11.017 2.814a1 1 0 0 1 1.966 0l1.051 5.558a2 2 0 0 0 1.594 1.594l5.558 1.051a1 1 0 0 1 0 1.966l-5.558 1.051a2 2 0 0 0-1.594 1.594l-1.051 5.558a1 1 0 0 1-1.966 0l-1.051-5.558a2 2 0 0 0-1.594-1.594l-5.558-1.051a1 1 0 0 1 0-1.966l5.558-1.051a2 2 0 0 0 1.594-1.594z",key:"1s2grr"}],["path",{d:"M20 2v4",key:"1rf3ol"}],["path",{d:"M22 4h-4",key:"gwowj6"}],["circle",{cx:"4",cy:"20",r:"2",key:"6kqj1y"}]],w=l("sparkles",J);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const K=[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]],I=l("trending-up",K);function X({mediaAsset:t,className:r,size:o="md",showBadge:f=!0}){const p={sm:"w-12 h-12",md:"w-16 h-16",lg:"w-24 h-24"};if(!t)return e.jsx("div",{className:v("flex items-center justify-center bg-muted/30 rounded-lg border border-border",p[o],r),children:e.jsx(b,{className:"h-1/2 w-1/2 text-muted-foreground"})});const m=t.mimeType.startsWith("image/"),h=t.mimeType==="application/pdf";return e.jsxs("div",{className:v("relative",r),children:[e.jsx("div",{className:v("rounded-lg overflow-hidden border border-border-strong",p[o]),children:m?e.jsx("img",{src:t.url||"/placeholder.svg",alt:t.fileName,className:"w-full h-full object-cover",loading:"lazy"}):e.jsx("div",{className:"w-full h-full flex items-center justify-center bg-muted/50",children:e.jsx(M,{className:"h-1/2 w-1/2 text-muted-foreground"})})}),f&&e.jsx(c,{variant:"secondary",className:"absolute -top-1 -right-1 text-xs h-5 px-1",children:h?"PDF":t.mimeType.split("/")[1].toUpperCase()})]})}function Y({query:t,resultsCount:r,onApplyFilters:o,onUpdateQuery:f}){const[p,m]=j.useState([]),[h,x]=j.useState(!1);j.useEffect(()=>{const u=setTimeout(()=>{const n=[];r===0&&t&&(n.push({id:"no-results-1",type:"search",title:"Попробуйте упростить запрос",description:"Используйте более общие термины",action:"Упростить"}),n.push({id:"no-results-2",type:"filter",title:"Расширить поиск по категориям",description:"Включить все категории уплотнений",action:"Расширить",filters:{categoryIds:[]}})),r>0&&r<5&&t&&n.push({id:"few-results-1",type:"search",title:"Найти похожие детали",description:"Поиск по синонимам и аналогам",action:"Найти похожие"}),r>50&&(n.push({id:"many-results-1",type:"filter",title:"Уточнить по бренду",description:"Выберите предпочтительного производителя",action:"Показать бренды"}),n.push({id:"many-results-2",type:"tip",title:"Добавьте технические параметры",description:"Укажите размеры или материал для точного поиска",action:"Добавить параметры"})),t.toLowerCase().includes("сальник")&&!t.toLowerCase().includes("двигател")&&n.push({id:"smart-1",type:"filter",title:"Сальники двигателя",description:"Самая популярная категория",action:"Показать",filters:{categoryIds:[1]}}),m(n),x(n.length>0)},300);return()=>clearTimeout(u)},[t,r]);const g=a=>{if(a.filters)o(a.filters);else if(a.type==="search"&&a.id==="no-results-1"){const u=t.split(" ").slice(0,2).join(" ");f(u)}};return h?e.jsx(y,{variant:"glass",className:"mb-4 border-primary/20 bg-primary/5",children:e.jsx(k,{className:"p-4",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"h-8 w-8 rounded-full bg-gradient-to-br from-primary to-accent flex items-center justify-center flex-shrink-0",children:e.jsx(w,{className:"h-4 w-4 text-primary-foreground"})}),e.jsxs("div",{className:"flex-1 space-y-3",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-sm mb-1",children:"AI предложения"}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Основано на вашем запросе и результатах поиска"})]}),e.jsx("div",{className:"space-y-2",children:p.map(a=>e.jsxs("div",{className:"flex items-center justify-between p-3 rounded-lg bg-background/50 border border-border/30 hover:border-primary/30 transition-colors",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("div",{className:"h-6 w-6 rounded-full bg-muted flex items-center justify-center",children:[a.type==="search"&&e.jsx(I,{className:"h-3 w-3"}),a.type==="filter"&&e.jsx(E,{className:"h-3 w-3"}),a.type==="tip"&&e.jsx(w,{className:"h-3 w-3"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-sm",children:a.title}),e.jsx("div",{className:"text-xs text-muted-foreground",children:a.description})]})]}),e.jsx(i,{variant:"outline",size:"sm",onClick:()=>g(a),className:"h-7 text-xs",children:a.action})]},a.id))})]})]})})}):null}function ge(){return e.jsx(S,{children:e.jsx(ee,{})})}function ee(){const{results:t,filters:r,updateFilters:o,filteredCount:f,clearFilters:p}=$(),[m,h]=j.useState("detailed"),[x,g]=j.useState([]),a=s=>{const N=new CustomEvent("openItemDetails",{detail:s});window.dispatchEvent(N)},u=s=>{o(s)},n=s=>{o({query:s})},C=()=>{const s=new CustomEvent("openAIAssistant");window.dispatchEvent(s)};return e.jsx("div",{className:"flex-1 overflow-y-auto",children:e.jsxs("div",{className:"container max-w-none p-4",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h2",{className:"text-xl font-bold tracking-tight",children:"Результаты поиска"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("div",{className:"flex items-center gap-1 p-1 bg-muted/50 rounded border border-border/40",children:[e.jsx(i,{variant:m==="detailed"?"default":"ghost",size:"sm",onClick:()=>h("detailed"),className:"h-7 w-7 p-0",children:e.jsx(q,{className:"h-3 w-3"})}),e.jsx(i,{variant:m==="grid"?"default":"ghost",size:"sm",onClick:()=>h("grid"),className:"h-7 w-7 p-0",children:e.jsx(H,{className:"h-3 w-3"})}),e.jsx(i,{variant:m==="table"?"default":"ghost",size:"sm",onClick:()=>h("table"),className:"h-7 w-7 p-0",children:e.jsx(U,{className:"h-3 w-3"})})]}),e.jsxs(i,{variant:"outline",size:"sm",className:"gap-1 h-7 px-2 text-xs",children:[e.jsx(L,{className:"h-3 w-3"}),"Сравнить",x.length>0&&e.jsx(c,{variant:"secondary",className:"ml-1 h-4 w-4 rounded-full p-0 text-xs",children:x.length})]})]})]}),e.jsx(Y,{query:r.query,resultsCount:f,onApplyFilters:u,onUpdateQuery:n}),t.length===0?e.jsx(y,{variant:"elevated",className:"text-center py-12 border-2 border-dashed border-border-strong",children:e.jsx(k,{children:e.jsxs("div",{className:"flex flex-col items-center gap-3",children:[e.jsx("div",{className:"h-12 w-12 rounded-full bg-muted/50 flex items-center justify-center",children:e.jsx(F,{className:"h-6 w-6 text-muted-foreground"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold mb-1",children:"Ничего не найдено"}),e.jsx("p",{className:"text-muted-foreground text-sm max-w-md",children:"Попробуйте изменить критерии поиска или воспользуйтесь AI ассистентом для помощи"})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(i,{variant:"outline",onClick:p,size:"sm",children:"Очистить фильтры"}),e.jsx(i,{variant:"gradient",onClick:C,size:"sm",children:"Спросить AI"})]})]})})}):e.jsx("div",{className:"space-y-3 animate-fade-in",children:t?.map((s,N)=>e.jsx(y,{variant:"elevated",className:"group hover:shadow-strong transition-all duration-200 animate-slide-up border hover:border-primary/20",style:{animationDelay:`${N*30}ms`},children:e.jsx(k,{className:"p-4",children:e.jsxs("div",{className:"flex gap-4",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx(X,{mediaAsset:s.catalogItem.image??void 0,size:"md",className:"cursor-pointer hover:scale-105 transition-transform"})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx(_,{checked:x.includes(s.id),onCheckedChange:d=>{g(d?[...x,s.id]:x.filter(z=>z!==s.id))},className:"data-[state=checked]:bg-primary data-[state=checked]:border-primary h-4 w-4"}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("h3",{className:"text-lg font-bold text-primary font-mono tracking-tight truncate",children:s.catalogItem?.sku}),e.jsxs("div",{className:"flex items-center gap-2 mt-1",children:[e.jsxs(c,{variant:"outline",className:"font-medium text-xs h-5",children:[e.jsx(V,{className:"h-3 w-3 mr-1"}),s?.catalogItem?.brand?.name]}),s?.catalogItem?.brand.isOem&&e.jsx(c,{variant:"secondary",className:"text-xs h-5",children:"OEM"}),s.catalogItem.mediaAssets.length>0&&e.jsxs(c,{variant:"outline",className:"text-xs h-5",children:[e.jsx(b,{className:"h-3 w-3 mr-1"}),s.catalogItem?.mediaAssets.length]})]})]})]}),e.jsxs("div",{className:"space-y-1 mb-3",children:[e.jsx("h4",{className:"font-semibold text-sm",children:s?.part?.name}),e.jsx("p",{className:"text-muted-foreground text-sm leading-relaxed line-clamp-2",children:s.catalogItem?.description})]}),s.notes&&e.jsx("div",{className:"p-2 rounded bg-info/10 border border-info/20 mb-3",children:e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx("div",{className:"h-4 w-4 rounded-full bg-info/20 flex items-center justify-center flex-shrink-0 mt-0.5",children:e.jsx("div",{className:"h-1.5 w-1.5 rounded-full bg-info"})}),e.jsx("p",{className:"text-xs text-info-foreground leading-relaxed",children:s.notes})]})})]}),e.jsxs("div",{className:"flex flex-col items-end gap-2 ml-4",children:[e.jsx(T,{status:s.accuracy,size:"sm"}),e.jsx(c,{variant:"outline",className:"text-xs",children:s.part?.partCategory?.name})]})]}),e.jsxs("div",{className:"flex flex-wrap gap-1 mb-3",children:[s.catalogItem.attributes.slice(0,6).map(d=>e.jsxs("div",{className:"inline-flex items-center gap-1 px-2 py-1 rounded-full bg-card border border-border-strong hover:border-primary/40 transition-colors text-xs",children:[e.jsxs("span",{className:"text-muted-foreground font-medium truncate max-w-[80px]",title:d.template.title,children:[d.template.title.split(" ")[0],":"]}),e.jsx("span",{className:"font-mono font-semibold",children:d.value}),d.template.unit&&e.jsx("span",{className:"text-muted-foreground",children:d.template.unit})]},d.id)),s.catalogItem.attributes.length>6&&e.jsxs("div",{className:"inline-flex items-center px-2 py-1 rounded-full bg-muted/50 border border-border text-xs text-muted-foreground",children:["+",s.catalogItem.attributes.length-6," еще"]})]}),e.jsxs("div",{className:"flex items-center justify-between pt-2 border-t border-border",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[e.jsx(O,{className:"h-3 w-3"}),e.jsx("span",{children:new Date(s.part.updatedAt).toLocaleDateString("ru-RU")})]}),e.jsx(A,{orientation:"vertical",className:"h-3"}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsxs(c,{variant:"outline",className:"text-xs font-mono h-4 px-1",children:["#",s.partId]}),e.jsxs(c,{variant:"outline",className:"text-xs font-mono h-4 px-1",children:["#",s.catalogItemId]})]})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsxs(i,{variant:"outline",size:"sm",onClick:()=>a(s),className:"gap-1 h-7 px-2 text-xs",children:[e.jsx(Z,{className:"h-3 w-3"}),"Подробности"]}),e.jsxs(i,{variant:"outline",size:"sm",className:"gap-1 h-7 px-2 text-xs",children:[e.jsx(M,{className:"h-3 w-3"}),"Техданные"]}),e.jsxs(i,{variant:"gradient",size:"sm",className:"gap-1 h-7 px-2 text-xs",children:[e.jsx(I,{className:"h-3 w-3"}),"В корзину"]})]})]})]})]})})},s?.id))})]})})}export{ge as default};
