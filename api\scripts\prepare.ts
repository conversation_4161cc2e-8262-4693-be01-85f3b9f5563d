import { readFileSync, writeFileSync } from 'node:fs';
import { dirname, resolve } from 'node:path';

async function main() {
    const inputPath = 'api/_temp/Книга1.cleaned.json';
    const outputPath = 'api/_temp/Книга1_prepared.json';

    const raw = readFileSync(inputPath, 'utf8');

    const json: unknown = JSON.parse(raw);

    const prepared = json.map((item: any) => {
        return {
            note: item['Наименование'],
            email: item['E-mail 1'],
            createdAt: '2025-08-14T12:00:00.000Z',
            isActive: true
        }
    });

    writeFileSync(outputPath, JSON.stringify(prepared, null, 2));
}

await main();
