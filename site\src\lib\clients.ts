import { QueryClient } from '@tanstack/react-query'
import { trpc } from '@/lib/trpc'
import { httpBatchLink, loggerLink } from '@trpc/client'
import superjson from 'superjson'

// Единый QueryClient на весь сайт (делится между островами)
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60_000,
      gcTime: 30 * 60_000,
      retry: 2,
      refetchOnWindowFocus: false,
      refetchOnReconnect: true,
      refetchOnMount: false,
      refetchInterval: false,
    },
    mutations: {
      retry: 1,
    },
  },
})

// Клиент для провайдера React Query (НЕ proxy)
const baseURL = import.meta.env.PUBLIC_API_URL || 'http://localhost:3000'
export const trpcClient = trpc.createClient({
  links: [
    loggerLink({ enabled: () => import.meta.env.DEV }),
    httpBatchLink({
      url: `${baseURL}/trpc`,
      transformer: superjson,
      fetch: (url, options) =>
        fetch(url, { ...(options as RequestInit), credentials: 'include' }),
    }),
  ],
})

