/**
 * Скрипт миграции для заполнения numericValue в существующих атрибутах
 * 
 * Запуск: bunx tsx scripts/migrate-numeric-values.ts
 */

import { PrismaClient } from '@prisma/client';
import { parseNumericValue } from '../lib/attribute-value-sync';

const prisma = new PrismaClient();

async function migrateAttributes<T extends { id: number; value: string; numericValue: number | null }>(
    modelName: string,
    findMany: () => Promise<T[]>,
    update: (id: number, numericValue: number | null) => Promise<any>
) {
    console.log(`\n📊 Мигрируем ${modelName}...`);
    
    const attributes = await findMany();
    console.log(`  Найдено записей: ${attributes.length}`);
    
    let updated = 0;
    let skipped = 0;
    let errors = 0;
    
    for (const attr of attributes) {
        try {
            // Пропускаем если numericValue уже установлено
            if (attr.numericValue !== null) {
                skipped++;
                continue;
            }
            
            // Парсим числовое значение из строки
            const numericValue = parseNumericValue(attr.value);
            
            // Обновляем только если value содержит число
            if (numericValue !== null) {
                await update(attr.id, numericValue);
                updated++;
                
                // Логируем прогресс каждые 100 записей
                if (updated % 100 === 0) {
                    console.log(`    ✅ Обновлено: ${updated}`);
                }
            } else {
                // value не является числом, numericValue остается null
                skipped++;
            }
        } catch (error) {
            console.error(`    ❌ Ошибка при обновлении ID ${attr.id}:`, error);
            errors++;
        }
    }
    
    console.log(`  ✅ Обновлено: ${updated}`);
    console.log(`  ⏭️  Пропущено: ${skipped}`);
    if (errors > 0) {
        console.log(`  ❌ Ошибок: ${errors}`);
    }
    
    return { updated, skipped, errors };
}

async function main() {
    console.log('🚀 Начинаем миграцию numericValue для существующих атрибутов...\n');
    
    const startTime = Date.now();
    let totalUpdated = 0;
    let totalSkipped = 0;
    let totalErrors = 0;
    
    try {
        // Мигрируем PartAttribute
        const partResult = await migrateAttributes(
            'PartAttribute',
            () => prisma.partAttribute.findMany({
                select: { id: true, value: true, numericValue: true }
            }),
            (id, numericValue) => prisma.partAttribute.update({
                where: { id },
                data: { numericValue }
            })
        );
        totalUpdated += partResult.updated;
        totalSkipped += partResult.skipped;
        totalErrors += partResult.errors;
        
        // Мигрируем CatalogItemAttribute
        const catalogResult = await migrateAttributes(
            'CatalogItemAttribute',
            () => prisma.catalogItemAttribute.findMany({
                select: { id: true, value: true, numericValue: true }
            }),
            (id, numericValue) => prisma.catalogItemAttribute.update({
                where: { id },
                data: { numericValue }
            })
        );
        totalUpdated += catalogResult.updated;
        totalSkipped += catalogResult.skipped;
        totalErrors += catalogResult.errors;
        
        // Мигрируем EquipmentModelAttribute
        const equipmentResult = await migrateAttributes(
            'EquipmentModelAttribute',
            () => prisma.equipmentModelAttribute.findMany({
                select: { id: true, value: true, numericValue: true }
            }),
            (id, numericValue) => prisma.equipmentModelAttribute.update({
                where: { id },
                data: { numericValue }
            })
        );
        totalUpdated += equipmentResult.updated;
        totalSkipped += equipmentResult.skipped;
        totalErrors += equipmentResult.errors;
        
        const duration = ((Date.now() - startTime) / 1000).toFixed(2);
        
        console.log('\n' + '='.repeat(50));
        console.log('📈 ИТОГИ МИГРАЦИИ:');
        console.log('='.repeat(50));
        console.log(`✅ Всего обновлено: ${totalUpdated}`);
        console.log(`⏭️  Всего пропущено: ${totalSkipped}`);
        if (totalErrors > 0) {
            console.log(`❌ Всего ошибок: ${totalErrors}`);
        }
        console.log(`⏱️  Время выполнения: ${duration} сек`);
        console.log('='.repeat(50));
        
        if (totalErrors > 0) {
            console.log('\n⚠️  Миграция завершена с ошибками. Проверьте логи выше.');
            process.exit(1);
        } else {
            console.log('\n✨ Миграция успешно завершена!');
        }
        
    } catch (error) {
        console.error('\n💥 Критическая ошибка при миграции:', error);
        process.exit(1);
    } finally {
        await prisma.$disconnect();
    }
}

// Запуск скрипта
main().catch((error) => {
    console.error('💥 Неожиданная ошибка:', error);
    process.exit(1);
});
