import{j as r}from"./jsx-runtime.D_zvdyIk.js";import{r as u}from"./index.0yr9KlQE.js";import{S as l}from"./index.BWNh2K4G.js";import{c as f}from"./index.3rXK4OIH.js";import{c as m}from"./utils.CBfrqCZ4.js";import{Q as p,t as o,l as h,h as g,S as v,a as x}from"./trpc.9HS4D061.js";const y=f("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-sm hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",gradient:"gradient-primary text-primary-foreground shadow-sm hover:opacity-90"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),b=u.forwardRef(({className:e,variant:t,size:s,asChild:a=!1,...i},c)=>{const d=a?l:"button";return r.jsx(d,{className:m(y({variant:t,size:s,className:e})),ref:c,...i})});b.displayName="ModernButton";const n=new p({defaultOptions:{queries:{staleTime:5*6e4,gcTime:30*6e4,retry:2,refetchOnWindowFocus:!1,refetchOnReconnect:!0,refetchOnMount:!1,refetchInterval:!1},mutations:{retry:1}}}),w="http://localhost:3000",j=o.createClient({links:[h({enabled:()=>!1}),g({url:`${w}/trpc`,transformer:v,fetch:(e,t)=>fetch(e,{...t,credentials:"include"})})]});function R({children:e}){return r.jsx(o.Provider,{client:j,queryClient:n,children:r.jsx(x,{client:n,children:e})})}export{b as M,R as T,j as t};
