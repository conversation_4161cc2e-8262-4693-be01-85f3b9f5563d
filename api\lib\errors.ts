export type DomainErrorCode =
  | 'NOT_FOUND'
  | 'BAD_REQUEST'
  | 'CONFLICT'
  | 'UNAUTHORIZED'
  | 'FORBIDDEN'
  | 'TOO_MANY_REQUESTS'
  | 'INTERNAL_SERVER_ERROR'

export class DomainError extends Error {
  public readonly code: DomainErrorCode

  constructor(code: DomainErrorCode, message: string) {
    super(message)
    this.code = code
    Object.setPrototypeOf(this, new.target.prototype)
  }
}

export function isDomainError(error: unknown): error is DomainError {
  return Boolean(error) && typeof error === 'object' && 'code' in (error as any) && 'message' in (error as any)
}



