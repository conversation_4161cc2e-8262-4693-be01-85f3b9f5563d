# MediaAsset структура файлов

## 📁 Организация директорий

```
data/media/
├── schema-images/         # Изображения схем атрибутов
├── part-images/           # Основные изображения деталей (Part.image)
├── category-images/       # Изображения категорий (PartCategory.image)
├── part-media/            # Галерея деталей (Part.mediaAssets) - изображения + PDF
├── catalogitem-images/    # Основные изображения позиций каталога (CatalogItem.image)
└── catalogitem-media/     # Галерея позиций каталога (CatalogItem.mediaAssets) - изображения + PDF
```

## 🔗 Связь с базой данных

### MediaAsset модель
```sql
model MediaAsset {
    id        Int      @id @default(autoincrement())
    fileName  String   -- Имя файла на диске
    mimeType  String   -- MIME тип (image/jpeg, image/png, application/pdf)
    fileSize  Int?     -- Размер в байтах
    url       String   -- URL для доступа: /api/uploads/{subdir}/{fileName}
    
    -- Связи с сущностями
    part         Part?         @relation("PartImage")           -- Основное изображение детали
    partCategory PartCategory? @relation("PartCategoryImage")   -- Изображение категории
    parts        Part[]        @relation("PartMediaAssets")     -- Галерея деталей
    catalogItems CatalogItem[] @relation("CatalogItemMediaAssets") -- Галерея позиций
    catalogItem  CatalogItem?  @relation("CatalogItemImage")    -- Основное изображение позиции
}
```

## 📂 Типы файлов по директориям

### schema-images/
- **Назначение**: Изображения для схем атрибутов
- **Типы файлов**: PNG, JPG, WebP
- **Создается**: Через UploadService.uploadSchemaImage()
- **Доступ**: `/api/uploads/schema-images/{fileName}`

### part-images/
- **Назначение**: Основные изображения деталей
- **Связь**: Part.image (1:1)
- **Типы файлов**: PNG, JPG, WebP, GIF, SVG
- **Создается**: Через UploadService.uploadPartImage()
- **Доступ**: `/api/uploads/part-images/{fileName}`

### category-images/
- **Назначение**: Изображения категорий деталей
- **Связь**: PartCategory.image (1:1)
- **Типы файлов**: PNG, JPG, WebP, GIF, SVG
- **Создается**: Через UploadService.uploadPartCategoryImage()
- **Доступ**: `/api/uploads/category-images/{fileName}`

### part-media/
- **Назначение**: Галерея медиа для деталей
- **Связь**: Part.mediaAssets (M:N)
- **Типы файлов**: PNG, JPG, WebP, GIF, SVG, PDF
- **Создается**: Через UploadService.uploadPartMedia()
- **Доступ**: `/api/uploads/part-media/{fileName}`

### catalogitem-images/
- **Назначение**: Основные изображения позиций каталога
- **Связь**: CatalogItem.image (1:1)
- **Типы файлов**: PNG, JPG, WebP, GIF, SVG
- **Создается**: Через UploadService.uploadCatalogItemImage()
- **Доступ**: `/api/uploads/catalogitem-images/{fileName}`

### catalogitem-media/
- **Назначение**: Галерея медиа для позиций каталога
- **Связь**: CatalogItem.mediaAssets (M:N)
- **Типы файлов**: PNG, JPG, WebP, GIF, SVG, PDF
- **Создается**: Через UploadService.uploadCatalogItemMedia()
- **Доступ**: `/api/uploads/catalogitem-media/{fileName}`

## 🔄 Жизненный цикл файлов

### Загрузка
1. Файл загружается через tRPC endpoint
2. Конвертируется из base64 в бинарный формат
3. Генерируется уникальное имя файла
4. Сохраняется в соответствующую поддиректорию
5. Создается запись MediaAsset в БД
6. Устанавливается связь с целевой сущностью

### Удаление
1. При удалении связи (например, Part.image = null)
2. Удаляется запись MediaAsset из БД
3. Автоматически удаляется файл с диска (через Prisma middleware)

### Замена
1. При замене изображения (например, новое Part.image)
2. Создается новый MediaAsset
3. Старый MediaAsset удаляется
4. Старый файл автоматически удаляется с диска

## 🛠️ Обслуживание

### Проверка целостности
```bash
# Проверить размеры директорий
du -sh data/media/*/

# Найти файлы без записей в БД (orphaned files)
# Требует написания скрипта для сравнения файлов и БД

# Найти записи БД без файлов
# Требует написания скрипта для проверки существования файлов
```

### Очистка
```bash
# Очистка конкретной категории
rm -rf data/media/schema-images/*

# Очистка всех медиа файлов (ОСТОРОЖНО!)
rm -rf data/media/*/*
```

### Бэкап
```bash
# Бэкап всех медиа файлов
tar -czf media-backup-$(date +%Y%m%d).tar.gz data/media/

# Бэкап конкретной категории
tar -czf part-images-backup-$(date +%Y%m%d).tar.gz data/media/part-images/
```

## 📊 Статистика использования

### Размеры по категориям
```bash
echo "=== MediaAsset Storage Usage ==="
for dir in data/media/*/; do
    if [ -d "$dir" ]; then
        size=$(du -sh "$dir" | cut -f1)
        count=$(find "$dir" -type f | wc -l)
        echo "$(basename "$dir"): $size ($count files)"
    fi
done
```

### Типы файлов
```bash
echo "=== File Types Distribution ==="
find data/media/ -type f -name "*.*" | sed 's/.*\.//' | sort | uniq -c | sort -nr
```

## ⚠️ Важные замечания

1. **Права доступа**: Файлы должны принадлежать пользователю контейнера (UID 1001)
2. **Размер файлов**: Нет ограничений на уровне файловой системы, но есть в коде API
3. **Безопасность**: Файлы доступны только через статический middleware Hono
4. **Кэширование**: Браузеры кэшируют файлы по URL, при замене файла URL меняется
5. **Бэкапы**: Обязательно включайте media директорию в регулярные бэкапы
