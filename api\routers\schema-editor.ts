import { z } from 'zod'
import { router, publicProcedure, authProcedure } from '../trpc'
import { SchemaEditorService } from '../services/schema-editor.service'

// Валидация SVG контента
const svgContentSchema = z.string().refine((value) => {
  if (!value) return true; // Пустое значение допустимо

  // Базовая проверка на SVG структуру
  const trimmed = value.trim();

  // Проверяем наличие SVG тега (может быть не в самом начале из-за XML декларации)
  if (!trimmed.includes('<svg') || !trimmed.includes('</svg>')) {
    return false;
  }

  // Проверка на потенциально опасные элементы
  const dangerousElements = ['script', 'object', 'embed', 'iframe'];
  const dangerousAttributes = ['onload', 'onclick', 'onerror', 'onmouseover', 'onmousedown', 'onmouseup'];

  for (const element of dangerousElements) {
    if (trimmed.includes(`<${element}`)) return false;
  }

  for (const attr of dangerousAttributes) {
    if (trimmed.includes(attr)) return false;
  }

  return true;
}, {
  message: "SVG содержит недопустимые элементы или атрибуты"
}).optional();

// Схемы валидации для аннотаций
const createAnnotationSchema = z.object({
  schemaId: z.string().min(1),
  annotationType: z.string().default('note'),
  x: z.number().min(0).max(100),
  y: z.number().min(0).max(100),
  width: z.number().min(0).max(100).optional(),
  height: z.number().min(0).max(100).optional(),
  text: z.string().min(1).optional(),
  description: z.string().optional(),
  color: z.string().default('#FF0000'),
  fontSize: z.number().int().min(8).max(72).default(12),
  strokeWidth: z.number().int().min(1).max(10).optional(),
  opacity: z.number().min(0).max(1).optional(),
  isVisible: z.boolean().default(true),
  sortOrder: z.number().int().default(0)
})

const updateAnnotationSchema = z.object({
  id: z.string().uuid(),
  annotationType: z.enum(['text', 'arrow', 'highlight', 'area', 'callout']).optional(),
  x: z.number().min(0).max(100).optional(),
  y: z.number().min(0).max(100).optional(),
  width: z.number().min(0).max(100).optional(),
  height: z.number().min(0).max(100).optional(),
  text: z.string().optional(),
  description: z.string().optional(),
  color: z.string().optional(),
  fontSize: z.number().int().min(8).max(72).optional(),
  strokeWidth: z.number().int().min(1).max(10).optional(),
  opacity: z.number().min(0).max(1).optional(),
  isVisible: z.boolean().optional(),
  displayOrder: z.number().int().optional()
})

// Схемы валидации для кросс-референсов
const createCrossReferenceSchema = z.object({
  sourcePartNumberId: z.string().uuid(),
  targetPartNumberId: z.string().uuid(),
  referenceType: z.enum(['DIRECT_REPLACEMENT', 'SUPERSEDED_BY', 'SUPERSEDES', 'EQUIVALENT', 'COMPATIBLE', 'ALTERNATIVE']),
  confidence: z.number().min(0).max(1).default(1.0),
  notes: z.string().optional(),
  restrictions: z.string().optional(),
  effectiveDate: z.date().optional(),
  source: z.string().optional()
})

export const schemaEditorRouter = router({
  // =====================================================
  // СХЕМЫ (CRUD)
  // =====================================================

  // Получить список схем (с пагинацией "cursor based")
  listSchemas: publicProcedure
    .input(
      z.object({
        limit: z.number().int().min(1).max(100).optional().default(50),
        cursor: z.string().uuid().optional(),
        unitId: z.string().uuid().optional(),
        searchQuery: z.string().optional(),
        orderBy: z.enum(['name', 'updatedAt', 'createdAt']).optional().default('updatedAt'),
        orderDirection: z.enum(['asc', 'desc']).optional().default('desc'),
      })
    )
    .query(async ({ input }) => SchemaEditorService.listSchemas(input)),

  // Получить одну схему по ID (с позициями и аннотациями)
  getSchema: publicProcedure
    .input(z.object({ id: z.string().uuid() }))
    .query(async ({ input }) => SchemaEditorService.getSchema(input)),

  // Создать новую схему
  createSchema: authProcedure
    .input(
      z.object({
        name: z.string().min(1),
        unitId: z.string().uuid().optional(),
        description: z.string().optional(),
        imageUrl: z.string().url().optional(),
        imageWidth: z.number().int().optional(),
        imageHeight: z.number().int().optional(),
        svgContent: svgContentSchema,
        isActive: z.boolean().default(true).optional(),
        sortOrder: z.number().int().default(0).optional(),
      })
    )
    .mutation(async ({ input }) => SchemaEditorService.createSchema(input)),

  // Обновить схему
  updateSchema: authProcedure
    .input(
      z.object({
        id: z.string().uuid(),
        name: z.string().min(1).optional(),
        unitId: z.string().uuid().optional(),
        description: z.string().optional(),
        imageUrl: z.string().url().optional(),
        imageWidth: z.number().int().optional(),
        imageHeight: z.number().int().optional(),
        svgContent: svgContentSchema,
        isActive: z.boolean().optional(),
        sortOrder: z.number().int().optional(),
      })
    )
    .mutation(async ({ input }) => SchemaEditorService.updateSchema(input)),

  // Удалить схему
  deleteSchema: authProcedure
    .input(z.object({ id: z.string().uuid() }))
    .mutation(async ({ input }) => SchemaEditorService.deleteSchema(input)),

  // =====================================================
  // АННОТАЦИИ
  // =====================================================
  
  // Получить все аннотации для схемы
  getAnnotations: publicProcedure
    .input(z.object({
      schemaId: z.string().min(1)
    }))
    .query(async ({ input }) => SchemaEditorService.getAnnotations(input)),

  // Создать аннотацию
  createAnnotation: authProcedure
    .input(createAnnotationSchema)
    .mutation(async ({ input }) => SchemaEditorService.createAnnotation(input)),

  // Обновить аннотацию
  updateAnnotation: authProcedure
    .input(updateAnnotationSchema)
    .mutation(async ({ input }) => SchemaEditorService.updateAnnotation(input)),

  // Удалить аннотацию
  deleteAnnotation: authProcedure
    .input(z.object({ id: z.string().uuid() }))
    .mutation(async ({ input }) => SchemaEditorService.deleteAnnotation(input)),

  // Массовое обновление порядка аннотаций
  updateAnnotationsOrder: authProcedure
    .input(z.object({
      annotations: z.array(z.object({
        id: z.string().uuid(),
        displayOrder: z.number().int()
      }))
    }))
    .mutation(async ({ input }) => SchemaEditorService.updateAnnotationsOrder(input)),

  // =====================================================
  // КРОСС-РЕФЕРЕНСЫ
  // =====================================================

  // Получить кросс-референсы для номера детали
  getCrossReferences: publicProcedure
    .input(z.object({
      partNumberId: z.string().uuid(),
      includeIncoming: z.boolean().default(true),
      includeOutgoing: z.boolean().default(true)
    }))
    .query(async ({ input }) => SchemaEditorService.getCrossReferences(input)),

  // Создать кросс-референс
  createCrossReference: authProcedure
    .input(createCrossReferenceSchema)
    .mutation(async ({ input }) => SchemaEditorService.createCrossReference(input)),

  // Удалить кросс-референс
  deleteCrossReference: authProcedure
    .input(z.object({ id: z.string().uuid() }))
    .mutation(async ({ input }) => SchemaEditorService.deleteCrossReference(input)),

  // =====================================================
  // ПРОВЕРКА СОВМЕСТИМОСТИ
  // =====================================================

  // Проверить совместимость детали с техникой
  checkPartCompatibility: publicProcedure
    .input(z.object({
      partId: z.string().uuid(),
      equipmentModelId: z.string().uuid().optional(),
      year: z.number().int().optional()
    }))
    .query(async ({ input }) => SchemaEditorService.checkPartCompatibility(input)),

  // =====================================================
  // ПРОВЕРКА ДОСТУПНОСТИ
  // =====================================================

  // Проверить доступность деталей на схеме
  checkSchemaPartsAvailability: publicProcedure
    .input(z.object({
      schemaId: z.string().min(1),
      variantId: z.string().uuid().optional()
    }))
    .query(async ({ input }) => SchemaEditorService.checkSchemaPartsAvailability(input)),

  // Получить стандартизированные атрибуты для детали
  getStandardizedAttributes: publicProcedure
    .input(z.object({ partId: z.string().uuid() }))
    .query(async ({ input }) => SchemaEditorService.getStandardizedAttributes(input)),

  // Создать стандартизированный атрибут
  createStandardizedAttribute: authProcedure
    .input(z.object({
      partId: z.string().uuid(),
      attributeId: z.string().uuid(),
      stringValue: z.string().optional(),
      numberValue: z.number().optional(),
      booleanValue: z.boolean().optional(),
      dateValue: z.date().optional(),
      jsonValue: z.any().optional(),
      unit: z.string().optional(),
      precision: z.number().int().optional(),
      tolerance: z.number().optional(),
      context: z.string().optional(),
      conditions: z.string().optional()
    }))
    .mutation(async ({ input }) => SchemaEditorService.createStandardizedAttribute(input)),

  // =====================================================
  // ПОЗИЦИИ СХЕМ
  // =====================================================

  // Создать позицию на схеме
  createPosition: authProcedure
    .input(z.object({
      schemaId: z.string().min(1),
      partId: z.string().min(1),
      positionNumber: z.string().min(1).max(10),
      x: z.number().min(0).max(100),
      y: z.number().min(0).max(100),
      quantity: z.number().int().min(1).default(1),
      isRequired: z.boolean().default(true),
      isHighlighted: z.boolean().default(false),
      notes: z.string().max(500).optional(),
      installationOrder: z.number().int().optional(),
      variantId: z.string().uuid().optional()
    }))
    .mutation(async ({ input }) => SchemaEditorService.createPosition(input)),

  // Обновить позицию на схеме
  updatePosition: authProcedure
    .input(z.object({
      id: z.string().uuid(),
      x: z.number().min(0).max(100).optional(),
      y: z.number().min(0).max(100).optional(),
      quantity: z.number().int().min(1).optional(),
      isRequired: z.boolean().optional(),
      isHighlighted: z.boolean().optional(),
      notes: z.string().max(500).optional(),
      installationOrder: z.number().int().optional()
    }))
    .mutation(async ({ input }) => SchemaEditorService.updatePosition(input)),

  // Удалить позицию
  deletePosition: authProcedure
    .input(z.object({ id: z.string().uuid() }))
    .mutation(async ({ input }) => SchemaEditorService.deletePosition(input))
})
