"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

export interface ModernInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  variant?: "default" | "ghost" | "filled"
}

const ModernInput = React.forwardRef<HTMLInputElement, ModernInputProps>(
  ({ className, type, variant = "default", ...props }, ref) => {
    return (
      <input
        type={type}
        className={cn(
          "flex h-11 w-full rounded-lg border border-input bg-input px-4 py-2 text-sm transition-all duration-200",
          "file:border-0 file:bg-transparent file:text-sm file:font-medium",
          "placeholder:text-muted-foreground",
          "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-0",
          "focus-visible:border-primary/50 focus-visible:bg-background/50",
          "disabled:cursor-not-allowed disabled:opacity-50",
          "hover:border-primary/30 hover:bg-background/30",
          {
            "border-transparent bg-muted/50": variant === "ghost",
            "bg-muted border-muted": variant === "filled",
          },
          className,
        )}
        ref={ref}
        {...props}
      />
    )
  },
)
ModernInput.displayName = "ModernInput"

export { ModernInput }

