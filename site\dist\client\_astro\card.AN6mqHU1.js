import{j as s}from"./jsx-runtime.D_zvdyIk.js";import{r as d}from"./index.0yr9KlQE.js";import{c as o}from"./utils.CBfrqCZ4.js";const t=d.forwardRef(({className:a,...r},e)=>s.jsx("div",{ref:e,className:o("rounded-xl border bg-card text-card-foreground shadow",a),...r}));t.displayName="Card";const i=d.forwardRef(({className:a,...r},e)=>s.jsx("div",{ref:e,className:o("flex flex-col space-y-1.5 p-6",a),...r}));i.displayName="CardHeader";const c=d.forwardRef(({className:a,...r},e)=>s.jsx("h3",{ref:e,className:o("font-semibold leading-none tracking-tight",a),...r}));c.displayName="CardTitle";const m=d.forwardRef(({className:a,...r},e)=>s.jsx("p",{ref:e,className:o("text-sm text-muted-foreground",a),...r}));m.displayName="CardDescription";const n=d.forwardRef(({className:a,...r},e)=>s.jsx("div",{ref:e,className:o("p-6 pt-0",a),...r}));n.displayName="CardContent";const f=d.forwardRef(({className:a,...r},e)=>s.jsx("div",{ref:e,className:o("flex items-center p-6 pt-0",a),...r}));f.displayName="CardFooter";export{t as C,n as a,i as b,c,m as d};
