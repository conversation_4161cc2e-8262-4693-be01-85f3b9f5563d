import { writeFile, mkdir } from 'fs/promises'
import { existsSync } from 'fs'
import { join } from 'path'
import { saveImageToLocalFS, resolveUrlToFilePath, deleteFileIfExists, saveMediaToLocalFS } from '../lib/media'

export class UploadService {
  static async uploadSchemaImage(input: { fileName: string; fileData: string; mimeType: string }) {
    const uploadsDir = join(process.cwd(), 'uploads', 'schema-images')
    if (!existsSync(uploadsDir)) await mkdir(uploadsDir, { recursive: true })
    const timestamp = Date.now()
    const extension = input.fileName.split('.').pop() || 'png'
    const uniqueFileName = `${timestamp}-${Math.random().toString(36).substring(2)}.${extension}`
    const filePath = join(uploadsDir, uniqueFileName)
    const base64Data = input.fileData.replace(/^data:image\/[^;]+;base64,/, '')
    const buffer = Buffer.from(base64Data, 'base64')
    await writeFile(filePath, buffer)
    const fileUrl = `/api/uploads/schema-images/${uniqueFileName}`
    return { success: true, url: fileUrl, fileName: uniqueFileName }
  }

  static async uploadPartImage(ctx: any, input: { partId: number; fileName: string; fileData: string; mimeType: string }) {
    const saved = await saveImageToLocalFS({ fileName: input.fileName, fileDataBase64: input.fileData, mimeType: input.mimeType, subdir: 'part-images' })
    const part = await ctx.db.part.findUnique({ where: { id: input.partId }, select: { id: true, imageId: true, image: { select: { url: true } } } })
    if (!part) throw new Error('Part not found')
    let oldUrl: string | null = null
    if (part.image) oldUrl = part.image.url
    const media = await ctx.db.mediaAsset.create({ data: { fileName: saved.fileName, mimeType: saved.mimeType, fileSize: saved.fileSize, url: saved.url } })
    await ctx.db.part.update({ where: { id: input.partId }, data: { imageId: media.id } })
    if (part.imageId && oldUrl) {
      try { await ctx.db.mediaAsset.delete({ where: { id: part.imageId } }) } catch {}
      const oldPath = resolveUrlToFilePath(oldUrl)
      await deleteFileIfExists(oldPath)
    }
    return media
  }

  static async deletePartImage(ctx: any, input: { partId: number }) {
    const part = await ctx.db.part.findUnique({ where: { id: input.partId }, include: { image: true } })
    if (!part) throw new Error('Part not found')
    if (!part.image) return { success: true }
    const mediaId = part.imageId as number
    const url = part.image.url as string
    await ctx.db.part.update({ where: { id: input.partId }, data: { imageId: null } })
    await ctx.db.mediaAsset.delete({ where: { id: mediaId } })
    const path = resolveUrlToFilePath(url)
    await deleteFileIfExists(path)
    return { success: true }
  }

  static async uploadPartCategoryImage(ctx: any, input: { partCategoryId: number; fileName: string; fileData: string; mimeType: string }) {
    const saved = await saveImageToLocalFS({ fileName: input.fileName, fileDataBase64: input.fileData, mimeType: input.mimeType, subdir: 'category-images' })
    const category = await ctx.db.partCategory.findUnique({ where: { id: input.partCategoryId }, include: { image: true } })
    if (!category) throw new Error('PartCategory not found')
    let oldUrl: string | null = null
    if (category.image) oldUrl = category.image.url
    const media = await ctx.db.mediaAsset.create({ data: { fileName: saved.fileName, mimeType: saved.mimeType, fileSize: saved.fileSize, url: saved.url } })
    await ctx.db.partCategory.update({ where: { id: category.id }, data: { imageId: media.id } })
    if (category.imageId && oldUrl) {
      try { await ctx.db.mediaAsset.delete({ where: { id: category.imageId } }) } catch {}
      const oldPath = resolveUrlToFilePath(oldUrl)
      await deleteFileIfExists(oldPath)
    }
    return media
  }

  static async deletePartCategoryImage(ctx: any, input: { partCategoryId: number }) {
    const category = await ctx.db.partCategory.findUnique({ where: { id: input.partCategoryId }, include: { image: true } })
    if (!category) throw new Error('PartCategory not found')
    if (!category.image) return { success: true }
    const mediaId = category.imageId as number
    const url = category.image.url as string
    await ctx.db.partCategory.update({ where: { id: input.partCategoryId }, data: { imageId: null } })
    await ctx.db.mediaAsset.delete({ where: { id: mediaId } })
    const path = resolveUrlToFilePath(url)
    await deleteFileIfExists(path)
    return { success: true }
  }

  // ========== Part: Галерея (изображения + PDF) ==========
  static async uploadPartMedia(ctx: any, input: { partId: number; fileName: string; fileData: string; mimeType: string }) {
    const saved = await saveMediaToLocalFS({ fileName: input.fileName, fileDataBase64: input.fileData, mimeType: input.mimeType, subdir: 'part-media' })
    const part = await ctx.db.part.findUnique({ where: { id: input.partId }, select: { id: true } })
    if (!part) throw new Error('Part not found')
    const media = await ctx.db.mediaAsset.create({ data: { fileName: saved.fileName, mimeType: saved.mimeType, fileSize: saved.fileSize, url: saved.url } })
    await ctx.db.part.update({ where: { id: input.partId }, data: { mediaAssets: { connect: { id: media.id } } } })
    return media
  }

  static async removePartMedia(ctx: any, input: { partId: number; mediaId: number }) {
    // Безопасно: только отключаем от части, сам MediaAsset не удаляем (может быть связан в других местах)
    await ctx.db.part.update({ where: { id: input.partId }, data: { mediaAssets: { disconnect: { id: input.mediaId } } } })
    return { success: true }
  }

  // ========== CatalogItem: основное изображение ==========
  static async uploadCatalogItemImage(ctx: any, input: { catalogItemId: number; fileName: string; fileData: string; mimeType: string }) {
    const saved = await saveImageToLocalFS({ fileName: input.fileName, fileDataBase64: input.fileData, mimeType: input.mimeType, subdir: 'catalogitem-images' })
    const item = await ctx.db.catalogItem.findUnique({ where: { id: input.catalogItemId }, select: { id: true, imageId: true, image: { select: { url: true } } } })
    if (!item) throw new Error('CatalogItem not found')
    let oldUrl: string | null = null
    if (item.image) oldUrl = item.image.url
    const media = await ctx.db.mediaAsset.create({ data: { fileName: saved.fileName, mimeType: saved.mimeType, fileSize: saved.fileSize, url: saved.url } })
    await ctx.db.catalogItem.update({ where: { id: input.catalogItemId }, data: { imageId: media.id } })
    if (item.imageId && oldUrl) {
      try { await ctx.db.mediaAsset.delete({ where: { id: item.imageId } }) } catch {}
      const oldPath = resolveUrlToFilePath(oldUrl)
      await deleteFileIfExists(oldPath)
    }
    return media
  }

  static async deleteCatalogItemImage(ctx: any, input: { catalogItemId: number }) {
    const item = await ctx.db.catalogItem.findUnique({ where: { id: input.catalogItemId }, include: { image: true } })
    if (!item) throw new Error('CatalogItem not found')
    if (!item.image) return { success: true }
    const mediaId = item.imageId as number
    const url = item.image.url as string
    await ctx.db.catalogItem.update({ where: { id: input.catalogItemId }, data: { imageId: null } })
    await ctx.db.mediaAsset.delete({ where: { id: mediaId } })
    const path = resolveUrlToFilePath(url)
    await deleteFileIfExists(path)
    return { success: true }
  }

  // ========== CatalogItem: галерея (изображения + PDF) ==========
  static async uploadCatalogItemMedia(ctx: any, input: { catalogItemId: number; fileName: string; fileData: string; mimeType: string }) {
    const saved = await saveMediaToLocalFS({ fileName: input.fileName, fileDataBase64: input.fileData, mimeType: input.mimeType, subdir: 'catalogitem-media' })
    const item = await ctx.db.catalogItem.findUnique({ where: { id: input.catalogItemId }, select: { id: true } })
    if (!item) throw new Error('CatalogItem not found')
    const media = await ctx.db.mediaAsset.create({ data: { fileName: saved.fileName, mimeType: saved.mimeType, fileSize: saved.fileSize, url: saved.url } })
    await ctx.db.catalogItem.update({ where: { id: input.catalogItemId }, data: { mediaAssets: { connect: { id: media.id } } } })
    return media
  }

  static async removeCatalogItemMedia(ctx: any, input: { catalogItemId: number; mediaId: number }) {
    await ctx.db.catalogItem.update({ where: { id: input.catalogItemId }, data: { mediaAssets: { disconnect: { id: input.mediaId } } } })
    return { success: true }
  }
}


