import { resolveComponent, createElement<PERSON><PERSON>, openBlock, mergeProps, createVN<PERSON>, renderSlot, createCommentVNode, createElementVNode, createBlock, resolveDynamicComponent, toDisplayString, withCtx, Transition, defineComponent, useSSRContext, ref, createSlots, renderList } from 'vue';
import EyeIcon from '@primevue/icons/eye';
import EyeSlashIcon from '@primevue/icons/eyeslash';
import { cn } from '@primeuix/utils';
import { isTouchDevice, relativePosition, getOuterWidth, absolutePosition, addStyle } from '@primeuix/utils/dom';
import { ZIndex } from '@primeuix/utils/zindex';
import { ConnectedOverlayScrollHandler } from '@primevue/core/utils';
import { s as script$3 } from './index_Cf2gUSb2.mjs';
import { s as script$2, O as OverlayEventBus, p as ptViewMerge, _ as _export_sfc } from './ClientRouter_B8Zzhk9G.mjs';
import BaseInput from '@primevue/core/baseinput';
import { style } from '@primeuix/styles/password';
import BaseStyle from '@primevue/core/base/style';
import { ssrRenderComponent, ssrRenderSlot } from 'vue/server-renderer';

var inlineStyles = {
  root: function root(_ref) {
    var props = _ref.props;
    return {
      position: props.appendTo === 'self' ? 'relative' : undefined
    };
  }
};
var classes = {
  root: function root(_ref2) {
    var instance = _ref2.instance;
    return ['p-password p-component p-inputwrapper', {
      'p-inputwrapper-filled': instance.$filled,
      'p-inputwrapper-focus': instance.focused,
      'p-password-fluid': instance.$fluid
    }];
  },
  pcInputText: 'p-password-input',
  maskIcon: 'p-password-toggle-mask-icon p-password-mask-icon',
  unmaskIcon: 'p-password-toggle-mask-icon p-password-unmask-icon',
  overlay: 'p-password-overlay p-component',
  content: 'p-password-content',
  meter: 'p-password-meter',
  meterLabel: function meterLabel(_ref3) {
    var instance = _ref3.instance;
    return "p-password-meter-label ".concat(instance.meter ? 'p-password-meter-' + instance.meter.strength : '');
  },
  meterText: 'p-password-meter-text'
};
var PasswordStyle = BaseStyle.extend({
  name: 'password',
  style: style,
  classes: classes,
  inlineStyles: inlineStyles
});

var script$1 = {
  name: 'BasePassword',
  "extends": BaseInput,
  props: {
    promptLabel: {
      type: String,
      "default": null
    },
    mediumRegex: {
      type: [String, RegExp],
      "default": '^(((?=.*[a-z])(?=.*[A-Z]))|((?=.*[a-z])(?=.*[0-9]))|((?=.*[A-Z])(?=.*[0-9])))(?=.{6,})' // eslint-disable-line
    },
    strongRegex: {
      type: [String, RegExp],
      "default": '^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.{8,})' // eslint-disable-line
    },
    weakLabel: {
      type: String,
      "default": null
    },
    mediumLabel: {
      type: String,
      "default": null
    },
    strongLabel: {
      type: String,
      "default": null
    },
    feedback: {
      type: Boolean,
      "default": true
    },
    appendTo: {
      type: [String, Object],
      "default": 'body'
    },
    toggleMask: {
      type: Boolean,
      "default": false
    },
    hideIcon: {
      type: String,
      "default": undefined
    },
    maskIcon: {
      type: String,
      "default": undefined
    },
    showIcon: {
      type: String,
      "default": undefined
    },
    unmaskIcon: {
      type: String,
      "default": undefined
    },
    disabled: {
      type: Boolean,
      "default": false
    },
    placeholder: {
      type: String,
      "default": null
    },
    required: {
      type: Boolean,
      "default": false
    },
    inputId: {
      type: String,
      "default": null
    },
    inputClass: {
      type: [String, Object],
      "default": null
    },
    inputStyle: {
      type: Object,
      "default": null
    },
    inputProps: {
      type: null,
      "default": null
    },
    panelId: {
      type: String,
      "default": null
    },
    panelClass: {
      type: [String, Object],
      "default": null
    },
    panelStyle: {
      type: Object,
      "default": null
    },
    panelProps: {
      type: null,
      "default": null
    },
    overlayId: {
      type: String,
      "default": null
    },
    overlayClass: {
      type: [String, Object],
      "default": null
    },
    overlayStyle: {
      type: Object,
      "default": null
    },
    overlayProps: {
      type: null,
      "default": null
    },
    ariaLabelledby: {
      type: String,
      "default": null
    },
    ariaLabel: {
      type: String,
      "default": null
    },
    autofocus: {
      type: Boolean,
      "default": null
    }
  },
  style: PasswordStyle,
  provide: function provide() {
    return {
      $pcPassword: this,
      $parentInstance: this
    };
  }
};

function _typeof$1(o) { "@babel/helpers - typeof"; return _typeof$1 = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof$1(o); }
function _defineProperty$1(e, r, t) { return (r = _toPropertyKey$1(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: true, configurable: true, writable: true }) : e[r] = t, e; }
function _toPropertyKey$1(t) { var i = _toPrimitive$1(t, "string"); return "symbol" == _typeof$1(i) ? i : i + ""; }
function _toPrimitive$1(t, r) { if ("object" != _typeof$1(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r); if ("object" != _typeof$1(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
var script = {
  name: 'Password',
  "extends": script$1,
  inheritAttrs: false,
  emits: ['change', 'focus', 'blur', 'invalid'],
  inject: {
    $pcFluid: {
      "default": null
    }
  },
  data: function data() {
    return {
      overlayVisible: false,
      meter: null,
      infoText: null,
      focused: false,
      unmasked: false
    };
  },
  mediumCheckRegExp: null,
  strongCheckRegExp: null,
  resizeListener: null,
  scrollHandler: null,
  overlay: null,
  mounted: function mounted() {
    this.infoText = this.promptText;
    this.mediumCheckRegExp = new RegExp(this.mediumRegex);
    this.strongCheckRegExp = new RegExp(this.strongRegex);
  },
  beforeUnmount: function beforeUnmount() {
    this.unbindResizeListener();
    if (this.scrollHandler) {
      this.scrollHandler.destroy();
      this.scrollHandler = null;
    }
    if (this.overlay) {
      ZIndex.clear(this.overlay);
      this.overlay = null;
    }
  },
  methods: {
    onOverlayEnter: function onOverlayEnter(el) {
      ZIndex.set('overlay', el, this.$primevue.config.zIndex.overlay);
      addStyle(el, {
        position: 'absolute',
        top: '0'
      });
      this.alignOverlay();
      this.bindScrollListener();
      this.bindResizeListener();

      // Issue: #7508
      this.$attrSelector && el.setAttribute(this.$attrSelector, '');
    },
    onOverlayLeave: function onOverlayLeave() {
      this.unbindScrollListener();
      this.unbindResizeListener();
      this.overlay = null;
    },
    onOverlayAfterLeave: function onOverlayAfterLeave(el) {
      ZIndex.clear(el);
    },
    alignOverlay: function alignOverlay() {
      if (this.appendTo === 'self') {
        relativePosition(this.overlay, this.$refs.input.$el);
      } else {
        this.overlay.style.minWidth = getOuterWidth(this.$refs.input.$el) + 'px';
        absolutePosition(this.overlay, this.$refs.input.$el);
      }
    },
    testStrength: function testStrength(str) {
      var level = 0;
      if (this.strongCheckRegExp.test(str)) level = 3;else if (this.mediumCheckRegExp.test(str)) level = 2;else if (str.length) level = 1;
      return level;
    },
    onInput: function onInput(event) {
      this.writeValue(event.target.value, event);
      this.$emit('change', event);
    },
    onFocus: function onFocus(event) {
      this.focused = true;
      if (this.feedback) {
        this.setPasswordMeter(this.d_value);
        this.overlayVisible = true;
      }
      this.$emit('focus', event);
    },
    onBlur: function onBlur(event) {
      this.focused = false;
      if (this.feedback) {
        this.overlayVisible = false;
      }
      this.$emit('blur', event);
    },
    onKeyUp: function onKeyUp(event) {
      if (this.feedback) {
        var value = event.target.value;
        var _this$checkPasswordSt = this.checkPasswordStrength(value),
          meter = _this$checkPasswordSt.meter,
          label = _this$checkPasswordSt.label;
        this.meter = meter;
        this.infoText = label;
        if (event.code === 'Escape') {
          this.overlayVisible && (this.overlayVisible = false);
          return;
        }
        if (!this.overlayVisible) {
          this.overlayVisible = true;
        }
      }
    },
    setPasswordMeter: function setPasswordMeter() {
      if (!this.d_value) {
        this.meter = null;
        this.infoText = this.promptText;
        return;
      }
      var _this$checkPasswordSt2 = this.checkPasswordStrength(this.d_value),
        meter = _this$checkPasswordSt2.meter,
        label = _this$checkPasswordSt2.label;
      this.meter = meter;
      this.infoText = label;
      if (!this.overlayVisible) {
        this.overlayVisible = true;
      }
    },
    checkPasswordStrength: function checkPasswordStrength(value) {
      var label = null;
      var meter = null;
      switch (this.testStrength(value)) {
        case 1:
          label = this.weakText;
          meter = {
            strength: 'weak',
            width: '33.33%'
          };
          break;
        case 2:
          label = this.mediumText;
          meter = {
            strength: 'medium',
            width: '66.66%'
          };
          break;
        case 3:
          label = this.strongText;
          meter = {
            strength: 'strong',
            width: '100%'
          };
          break;
        default:
          label = this.promptText;
          meter = null;
          break;
      }
      return {
        label: label,
        meter: meter
      };
    },
    onInvalid: function onInvalid(event) {
      this.$emit('invalid', event);
    },
    bindScrollListener: function bindScrollListener() {
      var _this = this;
      if (!this.scrollHandler) {
        this.scrollHandler = new ConnectedOverlayScrollHandler(this.$refs.input.$el, function () {
          if (_this.overlayVisible) {
            _this.overlayVisible = false;
          }
        });
      }
      this.scrollHandler.bindScrollListener();
    },
    unbindScrollListener: function unbindScrollListener() {
      if (this.scrollHandler) {
        this.scrollHandler.unbindScrollListener();
      }
    },
    bindResizeListener: function bindResizeListener() {
      var _this2 = this;
      if (!this.resizeListener) {
        this.resizeListener = function () {
          if (_this2.overlayVisible && !isTouchDevice()) {
            _this2.overlayVisible = false;
          }
        };
        window.addEventListener('resize', this.resizeListener);
      }
    },
    unbindResizeListener: function unbindResizeListener() {
      if (this.resizeListener) {
        window.removeEventListener('resize', this.resizeListener);
        this.resizeListener = null;
      }
    },
    overlayRef: function overlayRef(el) {
      this.overlay = el;
    },
    onMaskToggle: function onMaskToggle() {
      this.unmasked = !this.unmasked;
    },
    onOverlayClick: function onOverlayClick(event) {
      OverlayEventBus.emit('overlay-click', {
        originalEvent: event,
        target: this.$el
      });
    }
  },
  computed: {
    inputType: function inputType() {
      return this.unmasked ? 'text' : 'password';
    },
    weakText: function weakText() {
      return this.weakLabel || this.$primevue.config.locale.weak;
    },
    mediumText: function mediumText() {
      return this.mediumLabel || this.$primevue.config.locale.medium;
    },
    strongText: function strongText() {
      return this.strongLabel || this.$primevue.config.locale.strong;
    },
    promptText: function promptText() {
      return this.promptLabel || this.$primevue.config.locale.passwordPrompt;
    },
    overlayUniqueId: function overlayUniqueId() {
      return this.$id + '_overlay';
    },
    containerDataP: function containerDataP() {
      return cn({
        fluid: this.$fluid
      });
    },
    meterDataP: function meterDataP() {
      var _this$meter, _this$meter2;
      return cn(_defineProperty$1({}, (_this$meter = this.meter) === null || _this$meter === void 0 ? void 0 : _this$meter.strength, (_this$meter2 = this.meter) === null || _this$meter2 === void 0 ? void 0 : _this$meter2.strength));
    },
    overlayDataP: function overlayDataP() {
      return cn(_defineProperty$1({}, 'portal-' + this.appendTo, 'portal-' + this.appendTo));
    }
  },
  components: {
    InputText: script$3,
    Portal: script$2,
    EyeSlashIcon: EyeSlashIcon,
    EyeIcon: EyeIcon
  }
};

function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), true).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: true, configurable: true, writable: true }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
var _hoisted_1 = ["data-p"];
var _hoisted_2 = ["id", "data-p"];
var _hoisted_3 = ["data-p"];
function render(_ctx, _cache, $props, $setup, $data, $options) {
  var _component_InputText = resolveComponent("InputText");
  var _component_Portal = resolveComponent("Portal");
  return openBlock(), createElementBlock("div", mergeProps({
    "class": _ctx.cx('root'),
    style: _ctx.sx('root'),
    "data-p": $options.containerDataP
  }, _ctx.ptmi('root')), [createVNode(_component_InputText, mergeProps({
    ref: "input",
    id: _ctx.inputId,
    type: $options.inputType,
    "class": [_ctx.cx('pcInputText'), _ctx.inputClass],
    style: _ctx.inputStyle,
    defaultValue: _ctx.d_value,
    name: _ctx.$formName,
    "aria-labelledby": _ctx.ariaLabelledby,
    "aria-label": _ctx.ariaLabel,
    "aria-controls": _ctx.overlayProps && _ctx.overlayProps.id || _ctx.overlayId || _ctx.panelProps && _ctx.panelProps.id || _ctx.panelId || $options.overlayUniqueId,
    "aria-haspopup": true,
    placeholder: _ctx.placeholder,
    required: _ctx.required,
    fluid: _ctx.fluid,
    disabled: _ctx.disabled,
    variant: _ctx.variant,
    invalid: _ctx.invalid,
    size: _ctx.size,
    autofocus: _ctx.autofocus,
    onInput: $options.onInput,
    onFocus: $options.onFocus,
    onBlur: $options.onBlur,
    onKeyup: $options.onKeyUp,
    onInvalid: $options.onInvalid
  }, _ctx.inputProps, {
    "data-p-has-e-icon": _ctx.toggleMask,
    pt: _ctx.ptm('pcInputText'),
    unstyled: _ctx.unstyled
  }), null, 16, ["id", "type", "class", "style", "defaultValue", "name", "aria-labelledby", "aria-label", "aria-controls", "placeholder", "required", "fluid", "disabled", "variant", "invalid", "size", "autofocus", "onInput", "onFocus", "onBlur", "onKeyup", "onInvalid", "data-p-has-e-icon", "pt", "unstyled"]), _ctx.toggleMask && $data.unmasked ? renderSlot(_ctx.$slots, _ctx.$slots.maskicon ? 'maskicon' : 'hideicon', mergeProps({
    key: 0,
    toggleCallback: $options.onMaskToggle,
    "class": [_ctx.cx('maskIcon'), _ctx.maskIcon]
  }, _ctx.ptm('maskIcon')), function () {
    return [(openBlock(), createBlock(resolveDynamicComponent(_ctx.maskIcon ? 'i' : 'EyeSlashIcon'), mergeProps({
      "class": [_ctx.cx('maskIcon'), _ctx.maskIcon],
      onClick: $options.onMaskToggle
    }, _ctx.ptm('maskIcon')), null, 16, ["class", "onClick"]))];
  }) : createCommentVNode("", true), _ctx.toggleMask && !$data.unmasked ? renderSlot(_ctx.$slots, _ctx.$slots.unmaskicon ? 'unmaskicon' : 'showicon', mergeProps({
    key: 1,
    toggleCallback: $options.onMaskToggle,
    "class": [_ctx.cx('unmaskIcon')]
  }, _ctx.ptm('unmaskIcon')), function () {
    return [(openBlock(), createBlock(resolveDynamicComponent(_ctx.unmaskIcon ? 'i' : 'EyeIcon'), mergeProps({
      "class": [_ctx.cx('unmaskIcon'), _ctx.unmaskIcon],
      onClick: $options.onMaskToggle
    }, _ctx.ptm('unmaskIcon')), null, 16, ["class", "onClick"]))];
  }) : createCommentVNode("", true), createElementVNode("span", mergeProps({
    "class": "p-hidden-accessible",
    "aria-live": "polite"
  }, _ctx.ptm('hiddenAccesible'), {
    "data-p-hidden-accessible": true
  }), toDisplayString($data.infoText), 17), createVNode(_component_Portal, {
    appendTo: _ctx.appendTo
  }, {
    "default": withCtx(function () {
      return [createVNode(Transition, mergeProps({
        name: "p-connected-overlay",
        onEnter: $options.onOverlayEnter,
        onLeave: $options.onOverlayLeave,
        onAfterLeave: $options.onOverlayAfterLeave
      }, _ctx.ptm('transition')), {
        "default": withCtx(function () {
          return [$data.overlayVisible ? (openBlock(), createElementBlock("div", mergeProps({
            key: 0,
            ref: $options.overlayRef,
            id: _ctx.overlayId || _ctx.panelId || $options.overlayUniqueId,
            "class": [_ctx.cx('overlay'), _ctx.panelClass, _ctx.overlayClass],
            style: [_ctx.overlayStyle, _ctx.panelStyle],
            onClick: _cache[0] || (_cache[0] = function () {
              return $options.onOverlayClick && $options.onOverlayClick.apply($options, arguments);
            }),
            "data-p": $options.overlayDataP,
            role: "dialog",
            "aria-live": "polite"
          }, _objectSpread(_objectSpread(_objectSpread({}, _ctx.panelProps), _ctx.overlayProps), _ctx.ptm('overlay'))), [renderSlot(_ctx.$slots, "header"), renderSlot(_ctx.$slots, "content", {}, function () {
            return [createElementVNode("div", mergeProps({
              "class": _ctx.cx('content')
            }, _ctx.ptm('content')), [createElementVNode("div", mergeProps({
              "class": _ctx.cx('meter')
            }, _ctx.ptm('meter')), [createElementVNode("div", mergeProps({
              "class": _ctx.cx('meterLabel'),
              style: {
                width: $data.meter ? $data.meter.width : ''
              },
              "data-p": $options.meterDataP
            }, _ctx.ptm('meterLabel')), null, 16, _hoisted_3)], 16), createElementVNode("div", mergeProps({
              "class": _ctx.cx('meterText')
            }, _ctx.ptm('meterText')), toDisplayString($data.infoText), 17)], 16)];
          }), renderSlot(_ctx.$slots, "footer")], 16, _hoisted_2)) : createCommentVNode("", true)];
        }),
        _: 3
      }, 16, ["onEnter", "onLeave", "onAfterLeave"])];
    }),
    _: 3
  }, 8, ["appendTo"])], 16, _hoisted_1);
}

script.render = render;

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "Password",
  setup(__props, { expose: __expose }) {
    __expose();
    const theme = ref({
      root: `inline-flex relative p-fluid:flex`,
      pcInputText: {
        root: `appearance-none rounded-md outline-hidden
        bg-surface-0 dark:bg-surface-950
        p-filled:bg-surface-50 dark:p-filled:bg-surface-800
        text-surface-700 dark:text-surface-0
        placeholder:text-surface-500 dark:placeholder:text-surface-400
        border border-surface-300 dark:border-surface-700
        enabled:hover:border-surface-400 dark:enabled:hover:border-surface-600
        enabled:focus:border-primary
        disabled:bg-surface-200 disabled:text-surface-500
        dark:disabled:bg-surface-700 dark:disabled:text-surface-400
        p-invalid:border-red-400 dark:p-invalid:border-red-300
        p-invalid:placeholder:text-red-600 dark:p-invalid:placeholder:text-red-400
        px-3 py-2 p-fluid:w-full p-has-e-icon:pe-10
        p-small:text-sm p-small:px-[0.625rem] p-small:py-[0.375rem]
        p-large:text-lg p-large:px-[0.875rem] p-large:py-[0.625rem]
        transition-colors duration-200 shadow-[0_1px_2px_0_rgba(18,18,23,0.05)]`
      },
      overlay: `p-3 rounded-md p-portal-self:min-w-full
        bg-surface-0 dark:bg-surface-900
        border border-surface-200 dark:border-surface-700
        text-surface-700 dark:text-surface-0
        shadow-[0_4px_6px_-1px_rgba(0,0,0,0.1),0_2px_4px_-2px_rgba(0,0,0,0.1)]`,
      content: `flex flex-col gap-2`,
      meter: `h-3 bg-surface-200 dark:bg-surface-700 rounded-md`,
      meterLabel: `h-full w-0 transition-[width] duration-1000 ease-in-out rounded-md
        p-weak:bg-red-500 dark:p-weak:bg-red-400
        p-medium:bg-amber-500 dark:p-medium:bg-amber-400
        p-strong:bg-green-500 dark:p-strong:bg-green-400`,
      meterText: ``,
      transition: {
        enterFromClass: "opacity-0 scale-y-75",
        enterActiveClass: "transition duration-120 ease-[cubic-bezier(0,0,0.2,1)]",
        leaveActiveClass: "transition-opacity duration-100 ease-linear",
        leaveToClass: "opacity-0"
      }
    });
    const __returned__ = { theme, get EyeIcon() {
      return EyeIcon;
    }, get EyeSlashIcon() {
      return EyeSlashIcon;
    }, get Password() {
      return script;
    }, get ptViewMerge() {
      return ptViewMerge;
    } };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(ssrRenderComponent($setup["Password"], mergeProps({
    unstyled: "",
    pt: $setup.theme,
    ptOptions: {
      mergeProps: $setup.ptViewMerge
    }
  }, _attrs), createSlots({
    maskicon: withCtx(({ toggleCallback }, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["EyeSlashIcon"], {
          onClick: toggleCallback,
          class: "end-3 text-surface-500 dark:text-surface-400 absolute top-1/2 -mt-2 w-4 h-4"
        }, null, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["EyeSlashIcon"], {
            onClick: toggleCallback,
            class: "end-3 text-surface-500 dark:text-surface-400 absolute top-1/2 -mt-2 w-4 h-4"
          }, null, 8, ["onClick"])
        ];
      }
    }),
    unmaskicon: withCtx(({ toggleCallback }, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["EyeIcon"], {
          onClick: toggleCallback,
          class: "end-3 text-surface-500 dark:text-surface-400 absolute top-1/2 -mt-2 w-4 h-4"
        }, null, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["EyeIcon"], {
            onClick: toggleCallback,
            class: "end-3 text-surface-500 dark:text-surface-400 absolute top-1/2 -mt-2 w-4 h-4"
          }, null, 8, ["onClick"])
        ];
      }
    }),
    _: 2
  }, [
    renderList(_ctx.$slots, (_, slotName) => {
      return {
        name: slotName,
        fn: withCtx((slotProps, _push2, _parent2, _scopeId) => {
          if (_push2) {
            ssrRenderSlot(_ctx.$slots, slotName, slotProps ?? {}, null, _push2, _parent2, _scopeId);
          } else {
            return [
              renderSlot(_ctx.$slots, slotName, slotProps ?? {})
            ];
          }
        })
      };
    })
  ]), _parent));
}
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/volt/Password.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const Password = /* @__PURE__ */ _export_sfc(_sfc_main, [["ssrRender", _sfc_ssrRender]]);

export { Password as P };
