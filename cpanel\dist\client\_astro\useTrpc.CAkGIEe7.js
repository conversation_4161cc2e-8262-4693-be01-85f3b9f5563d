import{t as a}from"./trpc.BpyaUO08.js";import{u as G}from"./useToast.DbdIHNOo.js";import{r as M}from"./reactivity.esm-bundler.D5IypM4U.js";import{h as E}from"./index.BglzLLgy.js";const n=M([]),C=50,N=()=>{const i=G(),y=()=>`error_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,p=(s,r,c,u)=>({id:y(),code:s,message:r,details:c,field:u,timestamp:new Date}),m=s=>{n.value.unshift(s),n.value.length>C&&(n.value=n.value.slice(0,C))},I=(s,r=!0)=>{console.error("tRPC Error:",s);const c=s?.data?.code||s?.shape?.code||s?.name||"UNKNOWN_ERROR",u=s?.data?.zodError?.fieldErrors||s?.zodError?.fieldErrors;let o=s?.message;if(!o)switch(c){case"UNAUTHORIZED":o="Требуется авторизация";break;case"FORBIDDEN":o="Недостаточно прав для выполнения операции";break;case"NOT_FOUND":o="Ресурс не найден";break;case"BAD_REQUEST":o="Некорректный запрос";break;case"CONFLICT":o="Конфликт данных";break;case"PRECONDITION_FAILED":o="Нарушены условия выполнения операции";break;case"INTERNAL_SERVER_ERROR":o="Внутренняя ошибка сервера";break;case"TIMEOUT":o="Превышено время ожидания";break;default:o="Произошла ошибка при выполнении запроса"}if(u&&typeof u=="object"){const b=Object.entries(u).flatMap(([O,q])=>(Array.isArray(q)?q:[q]).filter(Boolean).map(S=>`${O}: ${S}`)).slice(0,5).join(`
`);b&&(o=`Ошибка валидации:
${b}`)}const d={...p(c,o,s),trpcCode:c,zodErrors:u};return m(d),r&&i.error("Ошибка",o),d},g=(s,r,c=!0)=>{console.error("Network Error:",s);const u=s?.status||s?.response?.status||0,o=s?.message||"Ошибка сети",d=u>=500||u===0||u===408||u===429,b={...p("NETWORK_ERROR",o,s),status:u,retryable:d,url:r};return m(b),c&&(u===0?i.error("Ошибка сети","Проверьте подключение к интернету"):u>=500?i.error("Ошибка сервера","Попробуйте повторить запрос позже"):i.error("Ошибка сети",o)),b},t=(s,r,c,u=!0)=>{const o=`Поле "${s}": ${c}`,d={...p("VALIDATION_ERROR",o,{value:r,constraint:c},s),field:s,value:r,constraint:c};return m(d),u&&i.warn("Ошибка валидации",o),d},e=(s,r,c=!0)=>{console.error("Generic Error:",s);const u=s?.message||"Произошла неизвестная ошибка",o=s?.code||s?.name||"GENERIC_ERROR",d=p(o,r?`${r}: ${u}`:u,s);return m(d),c&&i.error("Ошибка",d.message),d},l=(s,r={})=>{const{context:c,showToast:u=!0,url:o}=r;return s?.data?.code||s?.shape?.code?I(s,u):s?.status||s?.response?.status?g(s,o,u):e(s,c,u)},f=()=>{n.value=[]},R=s=>{const r=n.value.findIndex(c=>c.id===s);r!==-1&&n.value.splice(r,1)},h=s=>n.value.filter(r=>r.code===s),T=(s=10)=>n.value.slice(0,s),A=E(()=>{const s=["INTERNAL_SERVER_ERROR","UNAUTHORIZED","FORBIDDEN"];return n.value.some(r=>s.includes(r.code))}),v=E(()=>{const s={};return n.value.forEach(r=>{s[r.code]=(s[r.code]||0)+1}),s}),P=(s="Запись",r)=>{r?l(r,{context:`Сохранение ${s.toLowerCase()}`}):i.error("Ошибка сохранения",`Не удалось сохранить ${s.toLowerCase()}`)},w=(s="Запись",r)=>{r?l(r,{context:`Удаление ${s.toLowerCase()}`}):i.error("Ошибка удаления",`Не удалось удалить ${s.toLowerCase()}`)},x=(s="Данные",r)=>{r?l(r,{context:`Загрузка ${s.toLowerCase()}`}):i.error("Ошибка загрузки",`Не удалось загрузить ${s.toLowerCase()}`)};return{errors:E(()=>n.value),hasCriticalErrors:A,errorStats:v,handleError:l,handleTRPCError:I,handleNetworkError:g,handleValidationError:t,handleGenericError:e,clearErrors:f,removeError:R,getErrorsByType:h,getRecentErrors:T,showSaveError:P,showDeleteError:w,showLoadError:x}};function U(){const i=M(!1),y=M(null),p=N(),m=e=>{typeof window<"u"&&window.dispatchEvent(new CustomEvent("app:toast",{detail:e}))},I=e=>{const l=p.handleTRPCError(e,!1);y.value=l.message,m({severity:"error",summary:"Ошибка",detail:l.message})},g=()=>{y.value=null},t=async(e,l)=>{try{i.value=!0,g();const f=await e();return l?.success&&m({severity:"success",summary:l.success.title??"Успешно",detail:l.success.message}),f}catch(f){return I(f),null}finally{i.value=!1}};return{loading:E(()=>i.value),error:E(()=>y.value),clearError:g,execute:t,client:a,parts:{findMany:e=>t(()=>a.crud.part.findMany.query(e)),findUnique:e=>t(()=>a.crud.part.findUnique.query(e)),create:e=>t(()=>a.crud.part.create.mutate(e),{success:{title:"Сохранено",message:"Запчасть создана"}}),update:e=>t(()=>a.crud.part.update.mutate(e),{success:{title:"Сохранено",message:"Запчасть обновлена"}}),delete:e=>t(()=>a.crud.part.delete.mutate(e),{success:{title:"Удалено",message:"Запчасть удалена"}})},catalogItems:{findMany:e=>t(()=>a.crud.catalogItem.findMany.query(e)),findUnique:e=>t(()=>a.crud.catalogItem.findUnique.query(e)),create:e=>t(()=>a.crud.catalogItem.create.mutate(e),{success:{title:"Сохранено",message:"Позиция создана"}}),update:e=>t(()=>a.crud.catalogItem.update.mutate(e),{success:{title:"Сохранено",message:"Позиция обновлена"}}),delete:e=>t(()=>a.crud.catalogItem.delete.mutate(e),{success:{title:"Удалено",message:"Позиция удалена"}})},matching:{findMatchingParts:e=>t(()=>a.matching.findMatchingParts.query(e)),findMatchingCatalogItems:e=>t(()=>a.matching.findMatchingCatalogItems.query(e)),proposeLink:e=>t(()=>a.matching.proposeLink.mutate(e)),listProposals:e=>t(()=>a.matching.listProposals.query(e)),approveProposal:e=>t(()=>a.matching.approveProposal.mutate(e)),rejectProposal:e=>t(()=>a.matching.rejectProposal.mutate(e)),generateProposals:e=>t(()=>a.matching.generateProposals.mutate(e)),createPartFromItems:e=>t(()=>a.matching.createPartFromItems.mutate(e))},brands:{findMany:e=>t(()=>a.crud.brand.findMany.query(e)),create:e=>t(()=>a.crud.brand.create.mutate(e),{success:{title:"Сохранено",message:"Бренд создан"}}),update:e=>t(()=>a.crud.brand.update.mutate(e),{success:{title:"Сохранено",message:"Бренд обновлён"}}),delete:e=>t(()=>a.crud.brand.delete.mutate(e),{success:{title:"Удалено",message:"Бренд удалён"}})},partCategories:{findMany:e=>t(()=>a.crud.partCategory.findMany.query(e)),create:e=>t(()=>a.crud.partCategory.create.mutate(e),{success:{title:"Сохранено",message:"Категория создана"}}),update:e=>t(()=>a.crud.partCategory.update.mutate(e),{success:{title:"Сохранено",message:"Категория обновлена"}}),delete:e=>t(()=>a.crud.partCategory.delete.mutate(e),{success:{title:"Удалено",message:"Категория удалена"}})},media:{uploadPartImage:e=>t(()=>a.upload.uploadPartImage.mutate(e),{success:{title:"Загружено",message:"Изображение запчасти обновлено"}}),deletePartImage:e=>t(()=>a.upload.deletePartImage.mutate(e),{success:{title:"Удалено",message:"Изображение запчасти удалено"}}),uploadPartMedia:e=>t(()=>a.upload.uploadPartMedia.mutate(e),{success:{title:"Загружено",message:"Медиа добавлено в галерею запчасти"}}),removePartMedia:e=>t(()=>a.upload.removePartMedia.mutate(e),{success:{title:"Удалено",message:"Медиа удалено из галереи запчасти"}}),uploadPartCategoryImage:e=>t(()=>a.upload.uploadPartCategoryImage.mutate(e),{success:{title:"Загружено",message:"Изображение категории обновлено"}}),deletePartCategoryImage:e=>t(()=>a.upload.deletePartCategoryImage.mutate(e),{success:{title:"Удалено",message:"Изображение категории удалено"}}),uploadCatalogItemImage:e=>t(()=>a.upload.uploadCatalogItemImage.mutate(e),{success:{title:"Загружено",message:"Изображение позиции обновлено"}}),deleteCatalogItemImage:e=>t(()=>a.upload.deleteCatalogItemImage.mutate(e),{success:{title:"Удалено",message:"Изображение позиции удалено"}}),uploadCatalogItemMedia:e=>t(()=>a.upload.uploadCatalogItemMedia.mutate(e),{success:{title:"Загружено",message:"Медиа добавлено в галерею позиции"}}),removeCatalogItemMedia:e=>t(()=>a.upload.removeCatalogItemMedia.mutate(e),{success:{title:"Удалено",message:"Медиа удалено из галереи позиции"}})},equipmentModels:{findMany:e=>t(()=>a.crud.equipmentModel.findMany.query(e)),findUnique:e=>t(()=>a.crud.equipmentModel.findUnique.query(e)),create:e=>t(()=>a.crud.equipmentModel.create.mutate(e)),update:e=>t(()=>a.crud.equipmentModel.update.mutate(e)),delete:e=>t(()=>a.crud.equipmentModel.delete.mutate(e))},partAttributes:{findByPartId:e=>t(()=>a.partAttributes.findByPartId.query(e)),create:e=>t(()=>a.partAttributes.create.mutate(e),{success:{title:"Сохранено",message:"Атрибут добавлен"}}),update:e=>t(()=>a.partAttributes.update.mutate(e),{success:{title:"Сохранено",message:"Атрибут обновлён"}}),delete:e=>t(()=>a.partAttributes.delete.mutate(e),{success:{title:"Удалено",message:"Атрибут удалён"}}),bulkCreate:e=>t(()=>a.partAttributes.bulkCreate.mutate(e))},attributeTemplates:{findMany:e=>t(()=>a.attributeTemplates.findMany.query(e)),findById:e=>t(()=>a.attributeTemplates.findById.query(e)),create:e=>t(()=>a.attributeTemplates.create.mutate(e),{success:{title:"Сохранено",message:"Шаблон создан"}}),update:e=>t(()=>a.attributeTemplates.update.mutate(e),{success:{title:"Сохранено",message:"Шаблон обновлён"}}),delete:e=>t(()=>a.attributeTemplates.delete.mutate(e),{success:{title:"Удалено",message:"Шаблон удалён"}}),findAllGroups:()=>t(()=>a.attributeTemplates.findAllGroups.query({})),findGroupsHierarchy:()=>t(()=>a.attributeTemplates.findGroupsHierarchy.query({})),createGroup:e=>t(()=>a.attributeTemplates.createGroup.mutate(e),{success:{title:"Сохранено",message:"Группа создана"}}),updateGroup:e=>t(()=>a.attributeTemplates.updateGroup.mutate(e),{success:{title:"Сохранено",message:"Группа обновлена"}}),deleteGroup:e=>t(()=>a.attributeTemplates.deleteGroup.mutate(e),{success:{title:"Удалено",message:"Группа удалена"}})},attributeSynonyms:{groups:{findMany:e=>t(()=>a.attributeSynonyms.groups.findMany.query(e)),create:e=>t(()=>a.attributeSynonyms.groups.create.mutate(e),{success:{title:"Сохранено",message:"Группа синонимов создана"}}),update:e=>t(()=>a.attributeSynonyms.groups.update.mutate(e),{success:{title:"Сохранено",message:"Группа синонимов обновлена"}}),delete:e=>t(()=>a.attributeSynonyms.groups.delete.mutate(e),{success:{title:"Удалено",message:"Группа синонимов удалена"}})},synonyms:{findMany:e=>t(()=>a.attributeSynonyms.synonyms.findMany.query(e)),create:e=>t(()=>a.attributeSynonyms.synonyms.create.mutate(e),{success:{title:"Сохранено",message:"Синоним добавлен"}}),update:e=>t(()=>a.attributeSynonyms.synonyms.update.mutate(e),{success:{title:"Сохранено",message:"Синоним обновлён"}}),delete:e=>t(()=>a.attributeSynonyms.synonyms.delete.mutate(e),{success:{title:"Удалено",message:"Синоним удалён"}})},utils:{findGroupByValue:e=>t(()=>a.attributeSynonyms.utils.findGroupByValue.query(e)),findGroupSynonymsByValue:e=>t(()=>a.attributeSynonyms.utils.findGroupSynonymsByValue.query(e))}},partApplicability:{upsert:e=>t(()=>a.crud.partApplicability.upsert.mutate(e),{success:{title:"Сохранено",message:"Применимость обновлена"}}),findMany:e=>t(()=>a.crud.partApplicability.findMany.query(e)),create:e=>t(()=>a.crud.partApplicability.create.mutate(e)),update:e=>t(()=>a.crud.partApplicability.update.mutate(e)),findFirst:e=>t(()=>a.crud.partApplicability.findFirst.query(e)),delete:e=>t(()=>a.crud.partApplicability.delete.mutate(e),{success:{title:"Удалено",message:"Связь с группой удалена"}})},equipmentApplicability:{upsert:e=>t(()=>a.crud.equipmentApplicability.upsert.mutate(e)),findMany:e=>t(()=>a.crud.equipmentApplicability.findMany.query(e)),create:e=>t(()=>a.crud.equipmentApplicability.create.mutate(e),{success:{title:"Сохранено",message:"Применимость техники добавлена"}}),update:e=>t(()=>a.crud.equipmentApplicability.update.mutate(e),{success:{title:"Сохранено",message:"Применимость техники обновлена"}}),findFirst:e=>t(()=>a.crud.equipmentApplicability.findFirst.query(e))},importExport:{exportXlsx:e=>t(()=>a.importExport.exportXlsx.mutate(e)),exportTemplate:e=>t(()=>a.importExport.exportTemplate.mutate(e))},excelImport:{dryRun:e=>t(()=>a.import.dryRun.mutate(e)),execute:e=>t(()=>a.import.execute.mutate(e))}}}export{U as u};
