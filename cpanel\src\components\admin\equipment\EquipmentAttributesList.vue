<script setup lang="ts">
import { computed, ref } from 'vue'
import { PencilIcon, TrashIcon, CheckIcon, XIcon } from 'lucide-vue-next'
import Button from '@/volt/Button.vue'
import Tag from '@/volt/Tag.vue'
import DataTable from '@/volt/DataTable.vue'
import Column from 'primevue/column'
import AttributeValueInput from '@/components/admin/attributes/AttributeValueInput.vue'
import type { EquipmentModelAttributeWithTemplate } from '@/types/attributes'
import { formatAttributeValue, getUnitDisplayName, getDataTypeDisplayName, groupAttributes } from '@/utils/attributes'
import Icon from '@/components/ui/Icon.vue'

interface Props {
  attributes: EquipmentModelAttributeWithTemplate[]
  readonly?: boolean
  showGroupColumn?: boolean
  showDataTypeColumn?: boolean
  compact?: boolean
  groupByTemplate?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false,
  showGroupColumn: true,
  showDataTypeColumn: false,
  compact: false,
  groupByTemplate: false
})

const emit = defineEmits<{
  'edit-attribute': [attributeId: number]
  'delete-attribute': [attributeId: number]
  'update-value': [attributeId: number, value: any]
}>()

// Inline editing state
const editingAttributeId = ref<number | null>(null)
const editingValue = ref<any>(null)

// Computed properties
const tableData = computed(() => {
  return props.attributes.map(attribute => {
    const formatted = formatAttributeValue(attribute)
    
    return {
      id: attribute.id,
      name: attribute.template.title,
      value: formatted.displayValue,
      rawValue: formatted.rawValue,
      unit: formatted.unit,
      dataType: attribute.template.dataType,
      dataTypeDisplay: getDataTypeDisplayName(attribute.template.dataType),
      group: attribute.template.group?.name || 'Общие',
      description: attribute.template.description,
      isRequired: attribute.template.isRequired,
      template: attribute.template,
      attribute: attribute
    }
  })
})

const groupedAttributes = computed(() => {
  if (!props.groupByTemplate) return {}
  return groupAttributes(props.attributes)
})

// Remove the columns computed property since we'll use Column components directly

// Methods
function handleEditAttribute(attributeId: number) {
  emit('edit-attribute', attributeId)
}

function handleDeleteAttribute(attributeId: number) {
  emit('delete-attribute', attributeId)
}

function getValueSeverity(dataType: string) {
  switch (dataType) {
    case 'NUMBER':
      return 'info'
    case 'BOOLEAN':
      return 'success'
    case 'DATE':
      return 'warning'
    case 'JSON':
      return 'secondary'
    default:
      return undefined
  }
}

function formatValueForDisplay(item: any) {
  // For boolean values, show icons instead of text
  if (item.dataType === 'BOOLEAN') {
    return item.rawValue ? '✓' : '✗'
  }
  
  // For numbers with units, ensure proper formatting
  if (item.dataType === 'NUMBER' && item.unit) {
    const unitDisplay = getUnitDisplayName(item.unit)
    return `${item.rawValue.toLocaleString('ru-RU')} ${unitDisplay}`
  }
  
  return item.value
}

// Inline editing methods
function startEditing(attribute: any) {
  editingAttributeId.value = attribute.id
  editingValue.value = attribute.rawValue
}

function cancelEditing() {
  editingAttributeId.value = null
  editingValue.value = null
}

function saveEditing(attributeId: number) {
  if (editingValue.value !== null && editingValue.value !== undefined) {
    emit('update-value', attributeId, editingValue.value)
  }
  cancelEditing()
}

function getDataTypeIcon(dataType: string) {
  const icons: Record<string, string> = {
    STRING: 'pi pi-font',
    NUMBER: 'pi pi-hashtag',
    BOOLEAN: 'pi pi-check-square',
    DATE: 'pi pi-calendar',
    JSON: 'pi pi-code'
  }
  return icons[dataType] || 'pi pi-question'
}
</script>

<template>
  <div class="equipment-attributes-list">
    <!-- Grouped View -->
    <div v-if="groupByTemplate && Object.keys(groupedAttributes).length > 0" class="space-y-6">
      <div v-for="(groupAttrs, groupName) in groupedAttributes" :key="groupName" class="attribute-group">
        <div class="flex items-center gap-2 mb-3 pb-2 border-b border-surface-200 dark:border-surface-700">
          <i class="pi pi-folder text-blue-600"></i>
          <h4 class="font-medium text-surface-900 dark:text-surface-0">
            {{ groupName === 'undefined' ? 'Без группы' : groupName }}
          </h4>
          <Tag :value="`${groupAttrs.length} атр.`" severity="secondary" size="small" />
        </div>

        <div class="space-y-3">
          <div v-for="attribute in groupAttrs" :key="attribute.id" class="attribute-item">
            <div class="flex items-center gap-3 p-4 border rounded-lg transition-all duration-200 hover:shadow-sm"
                 :class="[
                   attribute.value && String(attribute.value).trim()
                     ? 'border-green-200 dark:border-green-700 bg-green-50 dark:bg-green-900/10'
                     : 'border-orange-200 dark:border-orange-700 bg-orange-50 dark:bg-orange-900/10'
                 ]">

              <!-- Icon and status -->
              <div class="flex-shrink-0 relative">
              <Icon :name="getDataTypeIcon(attribute.template.dataType)" class="text-lg text-primary w-5 h-5" />
                <div class="absolute -top-1 -right-1 w-3 h-3 rounded-full border-2 border-white dark:border-surface-900"
                     :class="attribute.value && String(attribute.value).trim() ? 'bg-green-500' : 'bg-orange-500'"
                     :title="attribute.value && String(attribute.value).trim() ? 'Заполнено' : 'Не заполнено'">
                </div>
              </div>

              <!-- Name and group -->
              <div class="flex-shrink-0 w-48">
                <div class="font-medium text-surface-900 dark:text-surface-0 text-sm">
                  {{ attribute.template.title }}
                  <Tag v-if="attribute.template.isRequired" severity="danger" class="text-xs ml-1">*</Tag>
                </div>
                <div v-if="attribute.template.description" class="text-xs text-surface-500 dark:text-surface-400 mt-1">
                  {{ attribute.template.description }}
                </div>
              </div>

              <!-- Value input/display -->
              <div class="flex-1">
                <!-- Editing mode -->
                <div v-if="editingAttributeId === attribute.id" class="flex items-center gap-2">
                  <AttributeValueInput
                    v-model="editingValue"
                    :template="attribute.template"
                    class="flex-1"
                    size="small"
                  />
                  
                  <!-- Save/Cancel buttons -->
                  <div class="flex gap-1">
                    <Button
                      @click="saveEditing(attribute.id)"
                      size="small"
                      class="p-1"
                    >
                      <CheckIcon class="w-3 h-3" />
                    </Button>
                    <Button
                      @click="cancelEditing"
                      severity="secondary"
                      size="small"
                      class="p-1"
                    >
                      <XIcon class="w-3 h-3" />
                    </Button>
                  </div>
                </div>
                
                <!-- Display mode -->
                <div v-else class="flex items-center gap-2 group">
                  <span class="text-surface-900 dark:text-surface-0">
                    {{ formatAttributeValue(attribute).displayValue || 'Не указано' }}
                  </span>
                  <Button
                    v-if="!readonly"
                    @click="startEditing(attribute)"
                    text
                    size="small"
                    class="p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    <PencilIcon class="w-3 h-3" />
                  </Button>
                </div>
              </div>

              <!-- Unit -->
              <div class="flex-shrink-0 w-16 text-center">
                <span v-if="attribute.template.unit" class="text-sm text-surface-500 font-medium">
                  {{ getUnitDisplayName(attribute.template.unit) }}
                </span>
              </div>

              <!-- Action buttons -->
              <div v-if="!readonly" class="flex-shrink-0 flex gap-2">
                <Button
                  @click="handleEditAttribute(attribute.id)"
                  severity="secondary"
                  size="small"
                  outlined
                  class="p-1"
                >
                  <PencilIcon class="w-3 h-3" />
                </Button>
                <Button
                  @click="handleDeleteAttribute(attribute.id)"
                  severity="danger"
                  size="small"
                  outlined
                  class="p-1"
                  :disabled="attribute.template.isRequired"
                >
                  <TrashIcon class="w-3 h-3" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Table View -->
    <DataTable
      v-else
      :value="tableData"
      :paginator="!compact && tableData.length > 10"
      :rows="compact ? 5 : 10"
      :rowsPerPageOptions="[5, 10, 25, 50]"
      sortMode="multiple"
      removableSort
      :loading="false"
      dataKey="id"
      :size="compact ? 'small' : 'normal'"
      class="p-datatable-sm"
      :class="{ 'compact-table': compact }"
    >
      <!-- Attribute Name Column -->
      <Column 
        field="name" 
        header="Атрибут" 
        sortable 
        :style="{ minWidth: '200px' }"
      >
        <template #body="{ data }">
          <div class="flex items-start gap-2">
            <div class="flex-1 min-w-0">
              <div class="flex items-center gap-2 mb-1">
                <span class="font-medium text-surface-900 dark:text-surface-0 truncate">
                  {{ data.name }}
                </span>
                <Tag 
                  v-if="data.isRequired" 
                  severity="danger" 
                  class="text-xs flex-shrink-0"
                >
                  Обязательный
                </Tag>
              </div>
              <div 
                v-if="data.description && !compact" 
                class="text-xs text-surface-500 dark:text-surface-400 line-clamp-2"
                :title="data.description"
              >
                {{ data.description }}
              </div>
            </div>
          </div>
        </template>
      </Column>

      <!-- Value Column -->
      <Column 
        field="value" 
        header="Значение" 
        sortable 
        :style="{ minWidth: '150px' }"
      >
        <template #body="{ data }">
          <!-- Editing mode -->
          <div v-if="editingAttributeId === data.id" class="flex items-center gap-2">
            <AttributeValueInput
              v-model="editingValue"
              :template="data.template"
              class="w-full"
              size="small"
            />
            
            <!-- Save/Cancel buttons -->
            <div class="flex gap-1">
              <Button
                @click="saveEditing(data.id)"
                size="small"
                class="p-1"
              >
                <CheckIcon class="w-3 h-3" />
              </Button>
              <Button
                @click="cancelEditing"
                severity="secondary"
                size="small"
                class="p-1"
              >
                <XIcon class="w-3 h-3" />
              </Button>
            </div>
          </div>
          
          <!-- Display mode -->
          <div v-else class="flex items-center gap-2 group">
            <Tag 
              :severity="getValueSeverity(data.dataType)"
              class="font-mono text-sm"
              :class="{
                'text-green-700 bg-green-50 dark:text-green-300 dark:bg-green-900/20': data.dataType === 'BOOLEAN' && data.rawValue,
                'text-red-700 bg-red-50 dark:text-red-300 dark:bg-red-900/20': data.dataType === 'BOOLEAN' && !data.rawValue
              }"
            >
              {{ formatValueForDisplay(data) }}
            </Tag>
            
            <!-- Quick edit button -->
            <Button
              v-if="!readonly"
              @click="startEditing(data)"
              text
              size="small"
              class="p-1 opacity-0 group-hover:opacity-100 transition-opacity"
            >
              <PencilIcon class="w-3 h-3" />
            </Button>
            
            <!-- Data type indicator for compact mode -->
            <span 
              v-if="compact && !showDataTypeColumn" 
              class="text-xs text-surface-400 dark:text-surface-500"
              :title="`Тип: ${data.dataTypeDisplay}`"
            >
              {{ data.dataType }}
            </span>
          </div>
        </template>
      </Column>

      <!-- Group Column -->
      <Column 
        v-if="showGroupColumn"
        field="group" 
        header="Группа" 
        sortable 
        :style="{ minWidth: '120px' }"
      >
        <template #body="{ data }">
          <Tag severity="secondary" class="text-xs">
            <i class="pi pi-folder text-xs mr-1"></i>
            {{ data.group }}
          </Tag>
        </template>
      </Column>

      <!-- Data Type Column -->
      <Column 
        v-if="showDataTypeColumn"
        field="dataTypeDisplay" 
        header="Тип" 
        sortable 
        :style="{ minWidth: '100px' }"
      >
        <template #body="{ data }">
          <Tag 
            :severity="getValueSeverity(data.dataType)"
            class="text-xs"
          >
            {{ data.dataTypeDisplay }}
          </Tag>
        </template>
      </Column>

      <!-- Actions Column -->
      <Column 
        v-if="!readonly"
        field="actions" 
        header="Действия" 
        :sortable="false" 
        :style="{ minWidth: '120px', width: '120px' }"
      >
        <template #body="{ data }">
          <div class="flex items-center gap-1">
            <Button
              @click="handleEditAttribute(data.id)"
              text
              size="small"
              class="p-2"
              :title="`Редактировать ${data.name}`"
            >
              <PencilIcon class="w-4 h-4" />
            </Button>
            <Button
              @click="handleDeleteAttribute(data.id)"
              text
              severity="danger"
              size="small"
              class="p-2"
              :title="`Удалить ${data.name}`"
              :disabled="data.isRequired"
            >
              <TrashIcon class="w-4 h-4" />
            </Button>
          </div>
        </template>
      </Column>

      <!-- Empty state -->
      <template #empty>
        <div class="text-center py-6 text-surface-500 dark:text-surface-400">
          <i class="pi pi-info-circle text-2xl mb-2 block"></i>
          <p class="text-sm">Нет атрибутов для отображения</p>
        </div>
      </template>
    </DataTable>
  </div>
</template>

<style scoped>
.equipment-attributes-list {
  /* Custom styles for the attributes list */
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.compact-table :deep(.p-datatable-tbody > tr > td) {
  padding: 0.5rem;
}

.compact-table :deep(.p-datatable-thead > tr > th) {
  padding: 0.5rem;
  font-size: 0.875rem;
}

/* Boolean value styling */
:deep(.p-tag) {
  font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .equipment-attributes-list :deep(.p-datatable-wrapper) {
    overflow-x: auto;
  }
  
  .equipment-attributes-list :deep(.p-datatable) {
    min-width: 600px;
  }
}
</style>