import { $prisma } from '../db'
import { hash } from 'bcryptjs'

async function createAdmin() {
  try {
    // Проверяем, есть ли уже админ
    const existingAdmin = await $prisma.user.findFirst({
      where: { role: 'ADMIN' }
    })

    if (existingAdmin) {
      console.log('Админ уже существует:', existingAdmin.email)
      return
    }

    // Хешируем пароль
    const hashedPassword = await hash('admin123', 10)

    // Создаем админа
    const admin = await $prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Администратор',
        role: 'ADMIN',
        emailVerified: true,
        // Добавляем хешированный пароль через raw SQL, так как better-auth управляет паролями
      }
    })

    // Создаем запись пароля для better-auth
    await $prisma.$executeRaw`
      INSERT INTO "account" ("id", "accountId", "providerId", "userId", "accessToken", "refreshToken", "idToken", "accessTokenExpiresAt", "refreshTokenExpiresAt", "scope", "password", "createdAt", "updatedAt")
      VALUES (gen_random_uuid(), ${admin.id}, 'credential', ${admin.id}, NULL, NULL, NULL, NULL, NULL, NULL, ${hashedPassword}, NOW(), NOW())
    `

    console.log('Админ создан успешно:')
    console.log('Email:', admin.email)
    console.log('Пароль: admin123')
    console.log('Роль:', admin.role)

  } catch (error) {
    console.error('Ошибка создания админа:', error)
  } finally {
    await $prisma.$disconnect()
  }
}

createAdmin()
