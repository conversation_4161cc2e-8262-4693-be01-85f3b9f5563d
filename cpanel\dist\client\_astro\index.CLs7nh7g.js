function i(){let l=[],v=(e,r,t=999)=>{let u=n(e,r,t),s=u.value+(u.key===e?0:t)+1;return l.push({key:e,value:s}),s},y=e=>{l=l.filter(r=>r.value!==e)},d=(e,r)=>n(e).value,n=(e,r,t=0)=>[...l].reverse().find(u=>!0)||{key:e,value:t},a=e=>e&&parseInt(e.style.zIndex,10)||0;return{get:a,set:(e,r,t)=>{r&&(r.style.zIndex=String(v(e,!0,t)))},clear:e=>{e&&(y(a(e)),e.style.zIndex="")},getCurrent:e=>d(e)}}var x=i();export{x};
