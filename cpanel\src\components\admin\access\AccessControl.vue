<template>
  <div class="w-full max-w-5xl">
    <h2 class="text-xl font-semibold mb-4">Контроль доступа</h2>

    <!-- Выбор пользователя -->
    <div class="bg-[--color-card] border border-[--color-border] rounded-[--radius-md] p-4 mb-4">
      <h3 class="font-medium mb-2">Пользователь</h3>
      <div class="flex gap-2 items-center">
        <InputText v-model="userQuery" placeholder="Поиск по email/имени" class="w-80" />
        <Button label="Найти" @click="searchUsers" />
      </div>
      <div class="mt-3 max-h-56 overflow-auto" v-if="userResults.length">
        <div
          v-for="u in userResults"
          :key="u.id"
          class="px-3 py-2 cursor-pointer hover:bg-[--color-muted]/10 rounded"
          :class="{ 'bg-[--color-primary]/10': selectedUser?.id === u.id }"
          @click="selectUser(u)"
        >
          <div class="text-sm">{{ u.email }} <span class="opacity-70">— {{ u.name || 'без имени' }}</span></div>
          <div class="text-xs opacity-70">role: {{ u.role }}</div>
        </div>
      </div>
    </div>

    <!-- Выбор роли -->
    <div class="bg-[--color-card] border border-[--color-border] rounded-[--radius-md] p-4 mb-4">
      <h3 class="font-medium mb-2">Роль (опционально)</h3>
      <div class="flex gap-2 items-center">
        <Select v-model="selectedRole" :options="roleOptions" class="w-60" />
        <Button label="Сбросить роль" severity="secondary" @click="selectedRole = ''" />
      </div>
      <div class="text-xs opacity-70 mt-2">Если указан user и роль одновременно — сервер может использовать приоритет/валидацию по необходимости.</div>
    </div>

    <!-- Конструктор permissions -->
    <div class="bg-[--color-card] border border-[--color-border] rounded-[--radius-md] p-4 mb-4">
      <h3 class="font-medium mb-2">Проверяемые permissions</h3>
      <div class="grid grid-cols-2 gap-3 mb-3">
        <InputText v-model="permissionResource" placeholder="Ресурс (например, project)" />
        <InputText v-model="permissionActions" placeholder='Действия через запятую (например, create,update)' />
      </div>
      <div class="flex gap-2">
        <Button label="Добавить" @click="addPermission" />
        <Button label="Очистить" severity="secondary" @click="clearPermissions" />
      </div>

      <div class="mt-4 text-sm" v-if="Object.keys(permissions).length">
        <div class="font-mono text-xs bg-[--color-muted]/10 p-3 rounded">{{ permissions }}</div>
      </div>
    </div>

    <!-- Проверка -->
    <div class="flex gap-2">
      <Button label="Проверить права" @click="check" />
      <Button label="Проверить роль локально" severity="secondary" @click="checkRoleLocal" />
    </div>

    <div class="mt-4" v-if="result">
      <div class="bg-[--color-card] border border-[--color-border] rounded-[--radius-md] p-4">
        <div class="font-mono text-xs whitespace-pre-wrap">{{ JSON.stringify(result, null, 2) }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import InputText from '@/volt/InputText.vue'
import Button from '@/volt/Button.vue'
import Select from '@/volt/Select.vue'
import { trpc } from '@/lib/trpc'
import { useToast } from '@/composables/useToast'

const toast = useToast()

// Пользователь
const userQuery = ref('')
const userResults = ref<any[]>([])
const selectedUser = ref<any | null>(null)

async function searchUsers() {
  try {
    const res = await trpc.crud.user.findMany.query({
      where: userQuery.value
        ? {
            OR: [
              { email: { contains: userQuery.value, mode: 'insensitive' } },
              { name: { contains: userQuery.value, mode: 'insensitive' } },
            ],
          }
        : undefined,
      take: 20,
      orderBy: { createdAt: 'desc' },
      select: { id: true, email: true, name: true, role: true },
    })
    userResults.value = res
  } catch (e: any) {
    toast.error(e?.message || 'Не удалось выполнить поиск пользователей')
  }
}

function selectUser(u: any) {
  selectedUser.value = u
}

// Роль
const roleOptions = [
  { label: 'Не выбрано', value: '' },
  { label: 'ADMIN', value: 'ADMIN' },
  { label: 'SHOP', value: 'SHOP' },
  { label: 'USER', value: 'USER' },
]
const selectedRole = ref('')

// Permissions
const permissions = reactive<Record<string, string[]>>({})
const permissionResource = ref('')
const permissionActions = ref('')

function addPermission() {
  const resource = permissionResource.value.trim()
  if (!resource) return
  const actions = permissionActions.value
    .split(',')
    .map((a) => a.trim())
    .filter(Boolean)
  if (!actions.length) return
  permissions[resource] = actions
  permissionResource.value = ''
  permissionActions.value = ''
}

function clearPermissions() {
  for (const key of Object.keys(permissions)) delete permissions[key]
}

// Проверки
const result = ref<any | null>(null)

async function check() {
  try {
    const res = await trpc.admin.hasPermission.query({
      permissions,
      userId: selectedUser.value?.id,
      role: selectedRole.value || undefined,
    })
    result.value = res
    toast.success('Проверка выполнена')
  } catch (e: any) {
    toast.error(e?.message || 'Ошибка проверки прав')
  }
}

function checkRoleLocal() {
  // Локальная проверка роли: в клиентском плагине есть checkRolePermission.
  // Здесь демонстрационный вызов через наш API уже покрывает сценарий прав.
  if (!selectedRole.value) {
    toast.warn('Укажите роль для локальной проверки')
    return
  }
  result.value = { role: selectedRole.value, permissions }
}
</script>

<style scoped>
</style>



