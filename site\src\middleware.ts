import { defineMiddleware } from 'astro:middleware'

const PROTECTED_ROUTES = [
  '/account',
] as const

const GUEST_ONLY_ROUTES = [
  '/login',
  '/register',
] as const

function isProtectedRoute(pathname: string): boolean {
  return PROTECTED_ROUTES.some(route => pathname === route || pathname.startsWith(route + '/'))
}

function isGuestOnlyRoute(pathname: string): boolean {
  return GUEST_ONLY_ROUTES.some(route => pathname === route || pathname.startsWith(route + '/'))
}

/**
 * Проверяет сессию пользователя через HTTP запрос к API
 */
async function getSession(request: Request) {
  try {
    const apiUrl = import.meta.env.PUBLIC_API_URL || 'http://localhost:3000'

    const response = await fetch(`${apiUrl}/api/auth/session`, {
      method: 'GET',
      headers: {
        'Cookie': request.headers.get('Cookie') || '',
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    })

    if (!response.ok) {
      return null
    }

    const data = await response.json()
    return data
  } catch (error) {
    console.error('Failed to fetch session:', error)
    return null
  }
}

export const onRequest = defineMiddleware(async (context, next) => {
  const { url, request, redirect } = context
  const pathname = url.pathname

  try {
    const session = await getSession(request)
    const user = session?.user
    const isAuthenticated = !!user

    if (import.meta.env.DEV) {
      console.log(`[/site] auth middleware: ${pathname}`, {
        isAuthenticated,
        userEmail: user?.email,
        role: (user as any)?.role,
      })
    }

    // Заполняем locals для серверного доступа в .astro
    context.locals.user = user ?? null
    context.locals.session = session?.session ?? null

    // Гостевые страницы: перенаправляем авторизованных в кабинет
    if (isGuestOnlyRoute(pathname)) {
      if (isAuthenticated) return redirect('/account')
      return next()
    }

    // Защищённые страницы
    if (isProtectedRoute(pathname)) {
      if (!isAuthenticated) return redirect('/login')
      return next()
    }

    return next()
  } catch (e) {
    console.error('Auth middleware error (/site):', e)
    if (isProtectedRoute(pathname)) return redirect('/login')
    return next()
  }
})

