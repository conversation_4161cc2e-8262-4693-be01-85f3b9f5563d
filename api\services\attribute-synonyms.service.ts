import { TRPCError } from '@trpc/server'
import { getSystemDB } from '../db'

export class AttributeSynonymsService {
  // Groups
  static async findGroups(input: any) {
    try {
      const db = getSystemDB()
      const where: any = { templateId: input.templateId }
      if (input.search && input.search.trim()) {
        const query = input.search.trim()
        where.OR = [
          { name: { contains: query, mode: 'insensitive' } },
          { description: { contains: query, mode: 'insensitive' } },
          { notes: { contains: query, mode: 'insensitive' } },
        ]
      }
      const [groups, total] = await Promise.all([
        db.attributeSynonymGroup.findMany({
          where,
          include: { _count: { select: { synonyms: true } } },
          orderBy: [{ compatibilityLevel: 'asc' }, { name: 'asc' }],
          take: input.limit,
          skip: input.offset,
        }),
        db.attributeSynonymGroup.count({ where }),
      ])
      return { groups, total, hasMore: input.offset + input.limit < total }
    } catch (error: any) {
      throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: error.message || 'Не удалось получить группы синонимов' })
    }
  }

  static async createGroup(input: any) {
    try {
      const db = getSystemDB()
      const conflict = await db.attributeSynonymGroup.findFirst({
        where: { templateId: input.templateId, name: { equals: input.name, mode: 'insensitive' } },
      })
      if (conflict) throw new TRPCError({ code: 'CONFLICT', message: 'Группа с таким названием уже существует для выбранного шаблона' })
      const group = await db.attributeSynonymGroup.create({
        data: {
          templateId: input.templateId,
          name: input.name.trim(),
          canonicalValue: input.canonicalValue ? String(input.canonicalValue).trim() : undefined,
          parentId: input.parentId ?? null,
          description: input.description || null,
          compatibilityLevel: input.compatibilityLevel,
          notes: input.notes || null,
        },
      })
      return group
    } catch (error: any) {
      if (error instanceof TRPCError) throw error
      throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: error.message || 'Не удалось создать группу' })
    }
  }

  static async updateGroup(input: any) {
    try {
      const db = getSystemDB()
      const existing = await db.attributeSynonymGroup.findUnique({ where: { id: input.id } })
      if (!existing) throw new TRPCError({ code: 'NOT_FOUND', message: 'Группа не найдена' })
      if (input.name && input.name.trim().toLowerCase() !== existing.name.toLowerCase()) {
        const conflict = await db.attributeSynonymGroup.findFirst({
          where: { templateId: existing.templateId, name: { equals: input.name, mode: 'insensitive' }, id: { not: input.id } },
        })
        if (conflict) throw new TRPCError({ code: 'CONFLICT', message: 'Группа с таким названием уже существует' })
      }
      const { id, ...data } = input
      // Нормализация канонического значения: пустая строка -> null, непустое — trim; отсутствует — не трогаем
      if (Object.prototype.hasOwnProperty.call(data, 'canonicalValue')) {
        if ((data as any).canonicalValue === '') (data as any).canonicalValue = null
        else if ((data as any).canonicalValue != null) (data as any).canonicalValue = String((data as any).canonicalValue).trim()
      }
      const updated = await db.attributeSynonymGroup.update({ where: { id }, data })
      return updated
    } catch (error: any) {
      if (error instanceof TRPCError) throw error
      throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: error.message || 'Не удалось обновить группу' })
    }
  }

  static async deleteGroup(input: { id: number }) {
    try {
      const db = getSystemDB()
      await db.attributeSynonymGroup.delete({ where: { id: input.id } })
      return { success: true }
    } catch (error: any) {
      throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: error.message || 'Не удалось удалить группу' })
    }
  }

  // Synonyms
  static async findSynonyms(input: any) {
    try {
      const db = getSystemDB()
      const synonyms = await db.attributeSynonym.findMany({ where: { groupId: input.groupId }, orderBy: { value: 'asc' } })
      return synonyms
    } catch (error: any) {
      throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: error.message || 'Не удалось получить синонимы' })
    }
  }

  static async createSynonym(input: any) {
    try {
      const db = getSystemDB()
      const trimmedValue = String(input.value || '').trim()
      const conflict = await db.attributeSynonym.findFirst({ where: { groupId: input.groupId, value: { equals: trimmedValue, mode: 'insensitive' } } })
      if (conflict) throw new TRPCError({ code: 'CONFLICT', message: 'Такое значение уже есть в группе' })
      const synonym = await db.attributeSynonym.create({ data: { groupId: input.groupId, value: trimmedValue, notes: input.notes || null, brandId: input.brandId ?? null, compatibilityLevel: input.compatibilityLevel ?? null } })
      return synonym
    } catch (error: any) {
      if (error instanceof TRPCError) throw error
      throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: error.message || 'Не удалось создать синоним' })
    }
  }

  static async updateSynonym(input: any) {
    try {
      const db = getSystemDB()
      const { id, ...data } = input
      const updated = await db.attributeSynonym.update({ where: { id }, data })
      return updated
    } catch (error: any) {
      if (error instanceof TRPCError) throw error
      throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: error.message || 'Не удалось обновить синоним' })
    }
  }
  static async deleteSynonym(input: { id: number }) {
    try {
      const db = getSystemDB()
      await db.attributeSynonym.delete({ where: { id: input.id } })
      return { success: true }
    } catch (error: any) {
      throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: error.message || 'Не удалось удалить синоним' })
    }
  }

  // Utils
  static async findGroupByValue(input: any) {
    try {
      const db = getSystemDB()
      const synonym = await db.attributeSynonym.findFirst({
        where: { value: { equals: input.value, mode: 'insensitive' }, group: { templateId: input.templateId } },
        include: { group: true },
      })
      if (!synonym) return null
      return { group: synonym.group, synonymId: synonym.id }
    } catch (error: any) {
      throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: error.message || 'Не удалось выполнить поиск' })
    }
  }

  static async findGroupSynonymsByValue(input: any) {
    try {
      const db = getSystemDB()
      const synonym = await db.attributeSynonym.findFirst({
        where: { value: { equals: input.value, mode: 'insensitive' }, group: { templateId: input.templateId } },
        include: {
          group: {
            include: {
              synonyms: {
                include: {
                  brand: true
                },
                orderBy: { value: 'asc' }
              }
            }
          }
        },
      })
      if (!synonym) return null
      return {
        group: synonym.group,
        synonyms: synonym.group.synonyms,
        currentSynonymId: synonym.id
      }
    } catch (error: any) {
      throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: error.message || 'Не удалось получить синонимы группы' })
    }
  }
}


