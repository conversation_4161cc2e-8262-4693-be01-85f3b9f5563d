#!/usr/bin/env bun

/**
 * Скрипт для создания тестовых пользователей
 * Создает пользователей с разными ролями для тестирования авторизации
 */

import { PrismaClient } from '@prisma/client'
import { auth } from './auth'

const prisma = new PrismaClient()

async function createTestUsers() {
  console.log('🔐 Создание тестовых пользователей...')

  try {
    // Создаем пользователей через better-auth API для правильного хеширования
    console.log('Создаем администратора...')
    const adminResult = await auth.api.createUser({
      body: {
        email: '<EMAIL>',
        name: 'Администратор PartTec',
        password: 'admin123',
        role: 'ADMIN'
      }
    })


    console.log('Создаем обычного пользователя...')
    const userResult = await auth.api.createUser({
      body: {
        email: '<EMAIL>',
        name: 'Обычный пользователь',
        password: 'user123',
        role: 'USER'
      }
    })

    // Проверяем результаты
    if (adminResult.user) {
      console.log('✅ Администратор создан:', adminResult.user.email)
    } else {
      console.log('❌ Ошибка создания администратора:', adminResult)
    }


    if (userResult.user) {
      console.log('✅ Обычный пользователь создан:', userResult.user.email)
    } else {
      console.log('❌ Ошибка создания пользователя:', userResult)
    }


    console.log('✅ Тестовые пользователи созданы:')
    console.log('👤 Администратор: <EMAIL> / admin123 (роль: ADMIN)')
    console.log('👥 Обычный пользователь: <EMAIL> / user123 (роль: USER)')
    console.log('')
    console.log('🔑 Для входа в админ-панель используйте: <EMAIL> / admin123')

  } catch (error) {
    console.error('❌ Ошибка при создании пользователей:', error)
    throw error
  }
}

async function main() {
  try {
    await createTestUsers()
  } catch (error) {
    console.error('Ошибка:', error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

// Запускаем скрипт
if (import.meta.main) {
  main()
}
