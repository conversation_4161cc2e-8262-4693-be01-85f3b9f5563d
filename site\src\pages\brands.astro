---
import MainLayout from "../layouts/MainLayout.astro";
import { trpcClient } from "@/lib/trpc";
import { TrpcProvider } from "@/components/providers/TrpcProvider";
import { BrandCard } from "@/components/catalog/BrandCard";

// Загружаем все бренды
let brands: any[] = [];

try {
  const brandsResponse = await trpcClient.crud.brand.findMany.query({
    include: {
      _count: { 
        select: { 
          catalogItems: true,
          equipmentModel: true 
        } 
      }
    },
    orderBy: { name: 'asc' }
  });
  brands = brandsResponse || [];
} catch (error) {
  console.error('Error loading brands:', error);
}

// Группируем бренды по типу
const oemBrands = brands.filter(brand => brand.isOem);
const aftermarketBrands = brands.filter(brand => !brand.isOem);
---

<MainLayout title="Бренды" description="Каталог производителей техники и запчастей">
  <TrpcProvider client:load>
    <div class="container mx-auto px-4 py-8">
      <!-- Заголовок -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold tracking-tight mb-2">Бренды</h1>
        <p class="text-muted-foreground">
          Производители техники и запчастей в нашем каталоге
        </p>
      </div>

      <!-- OEM бренды -->
      {oemBrands.length > 0 && (
        <div class="mb-12">
          <h2 class="text-2xl font-semibold mb-6">OEM производители</h2>
          <p class="text-muted-foreground mb-6">
            Оригинальные производители техники
          </p>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {oemBrands.map((brand) => (
              <BrandCard brand={brand} client:load />
            ))}
          </div>
        </div>
      )}

      <!-- Aftermarket бренды -->
      {aftermarketBrands.length > 0 && (
        <div class="mb-12">
          <h2 class="text-2xl font-semibold mb-6">Aftermarket производители</h2>
          <p class="text-muted-foreground mb-6">
            Производители запчастей и комплектующих
          </p>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {aftermarketBrands.map((brand) => (
              <BrandCard brand={brand} client:load />
            ))}
          </div>
        </div>
      )}

      <!-- Если нет брендов -->
      {brands.length === 0 && (
        <div class="text-center py-12">
          <h3 class="text-lg font-semibold mb-2">Бренды не найдены</h3>
          <p class="text-muted-foreground">
            Каталог брендов пуст или недоступен
          </p>
        </div>
      )}
    </div>
  </TrpcProvider>
</MainLayout>
