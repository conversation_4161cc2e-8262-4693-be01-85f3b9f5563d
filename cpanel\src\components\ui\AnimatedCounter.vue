<template>
  <Motion
    :initial="{ opacity: 0, scale: 0.5 }"
    :whileInView="{ opacity: 1, scale: 1 }"
    :transition="{ duration: 0.8, ease: 'easeOut' }"
    :viewport="{ once: true }"
    @inView="startCounting"
    class="text-4xl font-bold text-[--p-primary-color]"
  >
    {{ displayValue }}{{ suffix }}
  </Motion>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Motion } from 'motion-v'

interface Props {
  value: string | number
  suffix?: string
  duration?: number
}

const props = withDefaults(defineProps<Props>(), {
  suffix: '',
  duration: 2000
})

const currentValue = ref(0)
const isAnimating = ref(false)

const displayValue = computed(() => {
  if (typeof props.value === 'string') {
    return props.value
  }
  return Math.floor(currentValue.value).toLocaleString()
})

const startCounting = () => {
  if (isAnimating.value || typeof props.value === 'string') return
  
  isAnimating.value = true
  const target = typeof props.value === 'number' ? props.value : parseInt(props.value.replace(/\D/g, ''))
  const increment = target / (props.duration / 16) // 60fps
  
  const animate = () => {
    if (currentValue.value < target) {
      currentValue.value += increment
      requestAnimationFrame(animate)
    } else {
      currentValue.value = target
    }
  }
  
  animate()
}
</script>