# Parttec Monorepo

Монорепозиторий проекта Parttec, содержащий все компоненты системы.

## Структура проекта

- `/api/` - API сервер (Node.js/TypeScript, tRPC, Prisma)
- `/cpanel/` - Админ панель (Astro + Vue + Volt UI)
- `/site/` - Основной сайт, клиентская часть (Astro + React + shadcn/ui)

## Подмодули

Каждый компонент является отдельным Git-подмодулем:

- **API**: https://github.com/xferqr/parttec3-api.git
- **CPanel**: https://github.com/xferqr/parttec3-cpanel.git
- **Site**: https://github.com/xferqr/parttec3-site.git

## Установка

```bash
# Клонирование с подмодулями
git clone --recursive https://github.com/xferqr/parttec3.git

# Или если уже склонировали основной репозиторий
git submodule update --init --recursive
```

## Разработка

### Работа с подмодулями

```bash
# Обновление всех подмодулей до последних версий
git submodule update --remote

# Переход в конкретный подмодуль для работы
cd api
git checkout main
# делаем изменения
git add .
git commit -m "Update API"
git push

# Возвращаемся в основной репозиторий и обновляем ссылку на подмодуль
cd ..
git add api
git commit -m "Update API submodule"
git push
```

### Запуск проектов

```bash
# API сервер
cd api
npm install
npm run dev

# Админ панель
cd cpanel
npm install
npm run dev

# Основной сайт
cd site
npm install
npm run dev
```

## Архитектура

- **Чистая архитектура** - максимально прозрачный код
- **Типизация** - максимальное покрытие типами и контрактами
- **DRY принцип** - избегание дублирования кода
- **SOLID принципы** - соблюдение принципов объектно-ориентированного программирования

## Технологии

### API
- Node.js + TypeScript
- tRPC для типобезопасного API
- Prisma ORM
- ZenStack для генерации схем

### CPanel (Админ панель)
- Astro framework
- Vue.js компоненты
- Volt UI (unstyled PrimeVue) + Tailwind CSS
- Lucide иконки

### Site (Клиентская часть)
- Astro framework
- React компоненты
- shadcn/ui + Tailwind CSS
- Lucide иконки

## Правила разработки

1. Максимально простой код без лишнего функционала
2. Гибкая система, легко расширяемая и поддерживаемая
3. Автогенерация форм на базе Zod схем где возможно
4. Использование сгенерированных ZenStack схем и tRPC роутов
5. Единые компоненты, избегание дублирования
6. Astro View Transitions для навигации
7. Запрет на использование PrimeVue иконок, только Lucide
