import { useState } from 'react';
import { Search } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { navigate } from 'astro:transitions/client';

interface SearchFormProps {
  onSearch?: (query: string) => void;
  placeholder?: string;
  defaultValue?: string;
}

export function SearchForm({
  onSearch,
  placeholder = "Поиск запчастей...",
  defaultValue = ""
}: SearchFormProps) {
  const [query, setQuery] = useState(defaultValue);

  const runSearch = (q: string) => {
    const trimmed = q.trim();
    if (!trimmed) return;
    if (onSearch) {
      onSearch(trimmed);
    } else {
      navigate(`/catalog?search=${encodeURIComponent(trimmed)}`);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    runSearch(query);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSubmit(e);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="flex w-full max-w-sm items-center space-x-2">
      <div className="relative flex-1">
        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
        <Input
          type="text"
          placeholder={placeholder}
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onKeyDown={handleKeyDown}
          className="pl-10"
        />
      </div>
      <Button type="submit" size="sm">
        Найти
      </Button>
    </form>
  );
}
