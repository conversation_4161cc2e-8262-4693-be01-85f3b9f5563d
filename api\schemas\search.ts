import { z } from 'zod'
import { PartScalarFieldEnumSchema } from '../generated/zod/enums/PartScalarFieldEnum.schema'
import { CatalogItemScalarFieldEnumSchema } from '../generated/zod/enums/CatalogItemScalarFieldEnum.schema'

export const AttributeFilter = z.object({
  templateId: z.number(),
  value: z.string().optional(),
  values: z.array(z.string()).optional(),
  minValue: z.number().optional(),
  maxValue: z.number().optional(),
  matchType: z.enum(['exact', 'contains', 'range', 'in']).default('exact')
})

export const SearchPartsInput = z.object({
  name: z.string().optional(),
  attributeFilters: z.array(AttributeFilter).optional(),
  // было: categoryId (single), теперь поддерживаем и массив
  categoryId: z.number().optional(),
  categoryIds: z.array(z.number()).optional(),
  brandIds: z.array(z.number()).optional(),
  limit: z.number().min(1).max(100).default(20),
  offset: z.number().min(0).default(0),
  orderBy: PartScalarFieldEnumSchema.default('name')
    .refine((v) => ['name', 'createdAt', 'updatedAt'].includes(v), 'Недопустимое поле сортировки'),
  orderDir: z.enum(['asc', 'desc']).default('asc')
})

export const SearchCatalogItemsInput = z.object({
  search: z.string().optional(),
  sku: z.string().optional(),
  brandId: z.number().optional(),
  attributeFilters: z.array(AttributeFilter).optional(),
  limit: z.number().min(1).max(100).default(20),
  offset: z.number().min(0).default(0),
  // CatalogItem не имеет полей createdAt/updatedAt, поэтому разрешаем только существующие scalar поля
  orderBy: CatalogItemScalarFieldEnumSchema.default('sku')
    .refine((v) => ['id','sku','source','description','brandId','isPublic','imageId'].includes(v), 'Недопустимое поле сортировки'),
  orderDir: z.enum(['asc', 'desc']).default('asc')
})

export const GetAttributeStatsInput = z.object({
  templateId: z.number(),
  entityType: z.enum(['part', 'catalogItem', 'equipment'])
})


