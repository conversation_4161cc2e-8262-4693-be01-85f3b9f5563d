# Миграция на TanStack Vue Query

## Обзор

Проект успешно мигрирован с самодельных tRPC оберток на TanStack Vue Query для управления состоянием данных. Это обеспечивает:

- Автоматическое кеширование и инвалидацию
- Оптимистичные обновления
- Фоновую синхронизацию
- Лучший UX с placeholderData
- Централизованную обработку ошибок

## Архитектурный паттерн

### SSR + Islands + Vue Query

1. **SSR на Astro странице**: Загружаем initialData через tRPC
2. **Передача в Vue Island**: Пропсы с initialData
3. **Vue Query с initialData**: Мгновенный рендер + фоновые обновления
4. **Централизованные ключи и fetchers**: DRY принцип

## Структура файлов

```
frontend/src/lib/query/
├── keys.ts          # Стабильные query keys
├── fetchers.ts      # tRPC адаптеры
└── client.ts        # QueryClient фабрика

frontend/src/composables/
└── useQueryToastErrors.ts  # Обработка ошибок
```

## Использование

### 1. Query Keys (keys.ts)

```typescript
export const qk = {
  parts: {
    list: (input?: Record<string, any>) => ['trpc', 'crud.part.findMany', input ?? {}] as const,
    detail: (input: { where: { id: number }; include?: Record<string, any> }) => ['trpc', 'crud.part.findUnique', input] as const,
  },
  brands: {
    list: (input?: Record<string, any>) => ['trpc', 'crud.brand.findMany', input ?? {}] as const,
  },
  attributeGroups: {
    list: (input?: Record<string, any>) => ['trpc', 'crud.attributeGroup.findMany', input ?? {}] as const,
  },
  partApplicability: {
    list: (input?: Record<string, any>) => ['trpc', 'crud.partApplicability.findMany', input ?? {}] as const,
  },
  // ...
};
```

### 2. Fetchers (fetchers.ts)

```typescript
export const fetchers = {
  parts: {
    list: async (input?: Record<string, any>) => trpc.crud.part.findMany.query(input),
    detail: async (input: { where: { id: number }; include?: Record<string, any> }) => trpc.crud.part.findUnique.query(input),
  },
  attributeGroups: {
    list: async (input?: Record<string, any>) => trpc.crud.attributeGroup.findMany.query(input),
  },
  partApplicability: {
    list: async (input?: Record<string, any>) => trpc.crud.partApplicability.findMany.query(input),
  },
  // ...
};
```

### 3. В компонентах

```vue
<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query';
import { qk } from '@/lib/query/keys';
import { fetchers } from '@/lib/query/fetchers';
import { useQueryToastErrors } from '@/composables/useQueryToastErrors';

const { data: parts, error, isFetching } = useQuery({
  queryKey: qk.parts.list(filters.value),
  queryFn: () => fetchers.parts.list(filters.value),
  initialData: props.initialParts,
  placeholderData: (previousData) => previousData,
});

useQueryToastErrors(error);
</script>
```

### 4. SSR в Astro

```astro
---
import { trpc } from '@/lib/trpc';

const initialParts = await trpc.crud.part.findMany.query(baseQuery);
---

<Layout>
  <MyComponent client:load initialParts={initialParts} />
</Layout>
```

## Мигрированные страницы

### ✅ Завершено

- `/catalog` - Каталог запчастей с фильтрами и пагинацией
- `/search` - Поиск с фильтрами
- `/stats` - Статистика системы

### ✅ Мигрированные компоненты

- `AttributeFilter.vue` - Фильтры атрибутов с загрузкой групп и шаблонов
- `PartDetailView.vue` - Детальный просмотр запчасти с связанными данными
- `PartCompatibilitySection.vue` - Секция совместимости запчастей

### 🔄 Паттерн для новых страниц

1. Создать Astro страницу с SSR загрузкой
2. Создать Boundary компонент для ErrorBoundary
3. Создать основной компонент с useQuery
4. Добавить ключи в `keys.ts` и fetchers в `fetchers.ts`

## Лучшие практики

### ✅ Правильно

```typescript
// Стабильные ключи с параметрами
const queryKey = qk.parts.list(filters.value);

// Использование placeholderData для UX
placeholderData: (previousData) => previousData,

// Централизованная обработка ошибок
useQueryToastErrors(error);

// SSR initialData
initialData: props.initialParts,
```

### ❌ Неправильно

```typescript
// Нестабильные ключи
queryKey: ['parts', Math.random()],

// Игнорирование ошибок
// error не обрабатывается

// Дублирование логики
// Прямые tRPC вызовы в компонентах
```

## Настройки QueryClient

```typescript
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 30_000,        // 30 секунд
      gcTime: 5 * 60_000,       // 5 минут
      retry: 1,                 // 1 повтор
      refetchOnWindowFocus: false,
      refetchOnReconnect: true,
    },
    mutations: {
      retry: 1,
    },
  },
});
```

## Обработка ошибок

```typescript
// Автоматическая обработка с дедупликацией
export function useQueryToastErrors(errorRef: Ref<Error | null>) {
  const { handleError } = useErrorHandler();
  const shownErrors = new Set<string>();

  const stopWatcher = watch(errorRef, (err) => {
    if (err) {
      const errorKey = err.message + err.stack;
      if (!shownErrors.has(errorKey)) {
        shownErrors.add(errorKey);
        handleError(err);
      }
    }
  });

  onScopeDispose(() => {
    stopWatcher();
    shownErrors.clear();
  });
}
```

## Примеры мигрированных компонентов

### AttributeFilter.vue

```vue
<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query';
import { useQueryToastErrors } from '@/composables/useQueryToastErrors';
import { qk } from '@/lib/query/keys';
import { fetchers } from '@/lib/query/fetchers';

interface Props {
  initialGroups?: any[];
  initialTemplates?: AttributeTemplate[];
}

const props = defineProps<Props>();

// Vue Query для загрузки групп атрибутов
const groupsQuery = useQuery({
  queryKey: qk.attributeGroups.list({
    include: { _count: { select: { templates: true } } },
    orderBy: { name: 'asc' }
  }),
  queryFn: () => fetchers.attributeGroups.list({
    include: { _count: { select: { templates: true } } },
    orderBy: { name: 'asc' }
  }),
  initialData: props.initialGroups,
  placeholderData: (previousData) => previousData,
});
useQueryToastErrors(groupsQuery.error);

// Vue Query для загрузки шаблонов атрибутов
const templatesQuery = useQuery({
  queryKey: qk.attributeTemplates.list({
    include: { group: true },
    orderBy: { title: 'asc' }
  }),
  queryFn: () => fetchers.attributeTemplates.list({
    include: { group: true },
    orderBy: { title: 'asc' }
  }),
  initialData: props.initialTemplates,
  placeholderData: (previousData) => previousData,
});
useQueryToastErrors(templatesQuery.error);

// Computed данные из queries
const availableGroups = computed(() => groupsQuery.data.value || []);
const availableTemplates = computed(() => templatesQuery.data.value || []);
const loading = computed(() => groupsQuery.isFetching.value || templatesQuery.isFetching.value);
</script>
```

### PartDetailView.vue

```vue
<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query';
import { useQueryToastErrors } from '@/composables/useQueryToastErrors';
import { qk } from '@/lib/query/keys';
import { fetchers } from '@/lib/query/fetchers';

interface Props {
  partId: number;
  initialPart?: any;
}

const props = defineProps<Props>();

// Vue Query для загрузки основных данных запчасти
const partQuery = useQuery({
  queryKey: qk.parts.detail({
    where: { id: props.partId },
    include: {
      partCategory: true,
      attributes: { include: { template: { include: { unit: true, group: true } } } },
      equipmentApplicabilities: {
        include: {
          equipmentModel: {
            include: { brand: true, attributes: { include: { template: { include: { group: true } } } } }
          }
        }
      },
      image: true
    }
  }),
  queryFn: () => fetchers.parts.detail({
    where: { id: props.partId },
    include: {
      partCategory: true,
      attributes: { include: { template: { include: { unit: true, group: true } } } },
      equipmentApplicabilities: {
        include: {
          equipmentModel: {
            include: { brand: true, attributes: { include: { template: { include: { group: true } } } } }
          }
        }
      },
      image: true
    }
  }),
  initialData: props.initialPart,
  placeholderData: (previousData) => previousData,
  enabled: !!props.partId,
});
useQueryToastErrors(partQuery.error);

// Computed данные из queries
const part = computed(() => partQuery.data.value);
const loading = computed(() => partQuery.isFetching.value);
</script>
```

### PartCompatibilitySection.vue

```vue
<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query';
import { useQueryToastErrors } from '@/composables/useQueryToastErrors';
import { qk } from '@/lib/query/keys';
import { fetchers } from '@/lib/query/fetchers';

interface Props {
  partId: number;
  initialCompatibility?: any[];
}

const props = defineProps<Props>();

// Vue Query для загрузки совместимости
const compatibilityQuery = useQuery({
  queryKey: qk.partApplicability.list({
    where: { partId: props.partId },
    include: {
      catalogItem: {
        include: {
          brand: true,
          attributes: { include: { template: { include: { unit: true } } } }
        }
      }
    }
  }),
  queryFn: () => fetchers.partApplicability.list({
    where: { partId: props.partId },
    include: {
      catalogItem: {
        include: {
          brand: true,
          attributes: { include: { template: { include: { unit: true } } } }
        }
      }
    }
  }),
  initialData: props.initialCompatibility,
  placeholderData: (previousData) => previousData,
  enabled: !!props.partId,
});
useQueryToastErrors(compatibilityQuery.error);

// Computed данные из query
const items = computed(() => {
  const result = compatibilityQuery.data.value || [];
  return result.map((app: any) => ({
    id: app.catalogItem?.id,
    sku: app.catalogItem?.sku,
    description: app.catalogItem?.description,
    brand: app.catalogItem?.brand ? {
      id: app.catalogItem.brand.id,
      name: app.catalogItem.brand.name
    } : undefined,
    accuracy: app.accuracy,
    notes: app.notes ?? undefined,
    attributes: (app.catalogItem?.attributes ?? []).map((a: any) => ({
      id: a.id,
      value: a.value,
      template: {
        id: a.template.id,
        title: a.template.title ?? a.template.name,
        dataType: a.template.dataType,
        unit: a.template.unit ? { symbol: a.template.unit.symbol } : undefined
      }
    }))
  }));
});

const loading = computed(() => compatibilityQuery.isFetching.value);
</script>
```

## Мутации (для будущего)

```typescript
const createPartMutation = useMutation({
  mutationFn: (data) => trpc.crud.part.create.mutate(data),
  onSuccess: () => {
    // Инвалидация связанных запросов
    queryClient.invalidateQueries({ queryKey: qk.parts.list() });
  },
});
```

## Отличия от старого подхода

### Было (useTrpc)
- Ручное управление loading/error
- Нет кеширования
- Дублирование логики
- Императивные вызовы

### Стало (Vue Query)
- Автоматическое состояние
- Умное кеширование
- Декларативный подход
- Фоновые обновления
- Оптимистичные обновления

## Совместимость

- ✅ Админка не затронута (использует старый useTrpc)
- ✅ Существующие tRPC роуты не изменены
- ✅ Обратная совместимость с API
- ✅ Постепенная миграция возможна
