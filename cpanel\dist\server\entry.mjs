import { r as renderers } from './chunks/_@astro-renderers_CicWY1rm.mjs';
import { c as createExports, s as serverEntrypointModule } from './chunks/_@astrojs-ssr-adapter_BEwg9L7n.mjs';
import { manifest } from './manifest_DeRw2e73.mjs';

const serverIslandMap = new Map();;

const _page0 = () => import('./pages/_image.astro.mjs');
const _page1 = () => import('./pages/admin/access-control.astro.mjs');
const _page2 = () => import('./pages/admin/attributes.astro.mjs');
const _page3 = () => import('./pages/admin/brands.astro.mjs');
const _page4 = () => import('./pages/admin/catalogitems.astro.mjs');
const _page5 = () => import('./pages/admin/categories.astro.mjs');
const _page6 = () => import('./pages/admin/equipment.astro.mjs');
const _page7 = () => import('./pages/admin/forbidden.astro.mjs');
const _page8 = () => import('./pages/admin/import-export.astro.mjs');
const _page9 = () => import('./pages/admin/login.astro.mjs');
const _page10 = () => import('./pages/admin/logout.astro.mjs');
const _page11 = () => import('./pages/admin/parts/create.astro.mjs');
const _page12 = () => import('./pages/admin/parts/_id_/edit.astro.mjs');
const _page13 = () => import('./pages/admin/parts/_id_.astro.mjs');
const _page14 = () => import('./pages/admin/parts.astro.mjs');
const _page15 = () => import('./pages/admin/proposals.astro.mjs');
const _page16 = () => import('./pages/admin/register.astro.mjs');
const _page17 = () => import('./pages/admin/ui-demo.astro.mjs');
const _page18 = () => import('./pages/admin/users.astro.mjs');
const _page19 = () => import('./pages/admin.astro.mjs');
const _page20 = () => import('./pages/api/auth/_---all_.astro.mjs');
const _page21 = () => import('./pages/index.astro.mjs');
const pageMap = new Map([
    ["node_modules/astro/dist/assets/endpoint/node.js", _page0],
    ["src/pages/admin/access-control.astro", _page1],
    ["src/pages/admin/attributes.astro", _page2],
    ["src/pages/admin/brands/index.astro", _page3],
    ["src/pages/admin/catalogitems/index.astro", _page4],
    ["src/pages/admin/categories/index.astro", _page5],
    ["src/pages/admin/equipment/index.astro", _page6],
    ["src/pages/admin/forbidden.astro", _page7],
    ["src/pages/admin/import-export.astro", _page8],
    ["src/pages/admin/login.astro", _page9],
    ["src/pages/admin/logout.astro", _page10],
    ["src/pages/admin/parts/create.astro", _page11],
    ["src/pages/admin/parts/[id]/edit.astro", _page12],
    ["src/pages/admin/parts/[id].astro", _page13],
    ["src/pages/admin/parts/index.astro", _page14],
    ["src/pages/admin/proposals/index.astro", _page15],
    ["src/pages/admin/register.astro", _page16],
    ["src/pages/admin/ui-demo.astro", _page17],
    ["src/pages/admin/users/index.astro", _page18],
    ["src/pages/admin/index.astro", _page19],
    ["src/pages/api/auth/[...all].ts", _page20],
    ["src/pages/index.astro", _page21]
]);

const _manifest = Object.assign(manifest, {
    pageMap,
    serverIslandMap,
    renderers,
    actions: () => import('./_noop-actions.mjs'),
    middleware: () => import('./_astro-internal_middleware.mjs')
});
const _args = {
    "mode": "standalone",
    "client": "file:///D:/Dev/parttec/cpanel/dist/client/",
    "server": "file:///D:/Dev/parttec/cpanel/dist/server/",
    "host": false,
    "port": 4322,
    "assets": "_astro",
    "experimentalStaticHeaders": false
};
const _exports = createExports(_manifest, _args);
const handler = _exports['handler'];
const startServer = _exports['startServer'];
const options = _exports['options'];
const _start = 'start';
if (_start in serverEntrypointModule) {
	serverEntrypointModule[_start](_manifest, _args);
}

export { handler, options, pageMap, startServer };
