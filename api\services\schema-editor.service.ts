import { TRPCError } from '@trpc/server'
import { $prisma } from '../db'

export class SchemaEditorService {
  static async listSchemas(input: any) {
    const take = input.limit ?? 50
    const where: Record<string, any> = {}
    if (input.unitId) where.unitId = input.unitId
    if (input.searchQuery) where.OR = [
      { name: { contains: input.searchQuery, mode: 'insensitive' } },
      { description: { contains: input.searchQuery, mode: 'insensitive' } },
    ]
    const items = await $prisma.aggregateSchema.findMany({
      take: take + 1,
      skip: input.cursor ? 1 : 0,
      cursor: input.cursor ? { id: input.cursor } : undefined,
      where,
      orderBy: { [input.orderBy!]: input.orderDirection },
      include: { positions: { select: { id: true } }, annotations: { select: { id: true } } },
    })
    let nextCursor: string | undefined = undefined
    if (items.length > take) { const nextItem = items.pop()!; nextCursor = nextItem.id }
    return { items, nextCursor }
  }

  static async getSchema(input: { id: string }) {
    const schema = await $prisma.aggregateSchema.findUnique({
      where: { id: input.id },
      include: {
        positions: { include: { part: { include: { partNumbers: { include: { brand: true } } } } } },
        annotations: true,
      },
    })
    if (!schema) throw new TRPCError({ code: 'NOT_FOUND', message: 'Схема не найдена' })
    return schema
  }

  static async createSchema(input: any) {
    const schema = await $prisma.aggregateSchema.create({ data: {
      name: input.name, unitId: input.unitId, description: input.description, imageUrl: input.imageUrl,
      imageWidth: input.imageWidth, imageHeight: input.imageHeight, svgContent: input.svgContent,
      isActive: input.isActive ?? true, sortOrder: input.sortOrder ?? 0,
    }, include: { positions: true, annotations: true } })
    return schema
  }

  static async updateSchema(input: any) {
    const { id, ...data } = input
    const schema = await $prisma.aggregateSchema.update({ where: { id }, data, include: { positions: true, annotations: true } })
    return schema
  }

  static async deleteSchema(input: { id: string }) {
    await $prisma.aggregateSchema.delete({ where: { id: input.id } })
    return { success: true }
  }

  // Annotations
  static async getAnnotations(input: { schemaId: string }) {
    const annotations = await $prisma.schemaAnnotation.findMany({ where: { schemaId: input.schemaId }, orderBy: { sortOrder: 'asc' } })
    return annotations
  }
  static async createAnnotation(input: any) {
    const annotation = await $prisma.schemaAnnotation.create({ data: input })
    return annotation
  }
  static async updateAnnotation(input: any) {
    const { id, ...data } = input
    const annotation = await $prisma.schemaAnnotation.update({ where: { id }, data })
    return annotation
  }
  static async deleteAnnotation(input: { id: string }) {
    await $prisma.schemaAnnotation.delete({ where: { id: input.id } })
    return { success: true }
  }
  static async updateAnnotationsOrder(input: { annotations: Array<{ id: string; displayOrder: number }> }) {
    const updates = input.annotations.map((a) => $prisma.schemaAnnotation.update({ where: { id: a.id }, data: { displayOrder: a.displayOrder } }))
    await $prisma.$transaction(updates)
    return { success: true }
  }

  // Cross References
  static async getCrossReferences(input: any) {
    const crossReferences: any[] = []
    if (input.includeOutgoing) {
      const outgoing = await $prisma.partNumberCrossReference.findMany({
        where: { sourcePartNumberId: input.partNumberId },
        include: { targetPartNumber: { include: { brand: true, part: true } } },
      })
      crossReferences.push(...outgoing.map((ref) => ({ ...ref, direction: 'outgoing' })))
    }
    if (input.includeIncoming) {
      const incoming = await $prisma.partNumberCrossReference.findMany({
        where: { targetPartNumberId: input.partNumberId },
        include: { sourcePartNumber: { include: { brand: true, part: true } } },
      })
      crossReferences.push(...incoming.map((ref) => ({ ...ref, direction: 'incoming' })))
    }
    return crossReferences
  }
  static async createCrossReference(input: any) {
    const existing = await $prisma.partNumberCrossReference.findFirst({ where: { sourcePartNumberId: input.sourcePartNumberId, targetPartNumberId: input.targetPartNumberId, referenceType: input.referenceType } })
    if (existing) throw new TRPCError({ code: 'BAD_REQUEST', message: 'Такой кросс-референс уже существует' })
    const crossReference = await $prisma.partNumberCrossReference.create({ data: input, include: {
      sourcePartNumber: { include: { brand: true, part: true } },
      targetPartNumber: { include: { brand: true, part: true } },
    } })
    return crossReference
  }
  static async deleteCrossReference(input: { id: string }) {
    await $prisma.partNumberCrossReference.delete({ where: { id: input.id } })
    return { success: true }
  }

  // Compatibility
  static async checkPartCompatibility(input: any) {
    const compatibility = await $prisma.partCompatibility.findMany({
      where: {
        partId: input.partId,
        ...(input.equipmentModelId && { equipmentModelId: input.equipmentModelId }),
        ...(input.year && {
          OR: [
            { yearFrom: null, yearTo: null },
            { yearFrom: { lte: input.year }, yearTo: null },
            { yearFrom: null, yearTo: { gte: input.year } },
            { yearFrom: { lte: input.year }, yearTo: { gte: input.year } },
          ],
        }),
      },
      include: { equipmentModel: { include: { brand: true } } },
    })
    return compatibility
  }

  static async checkSchemaPartsAvailability(input: any) {
    const positions = await $prisma.schemaPosition.findMany({
      where: { schemaId: input.schemaId, variantId: input.variantId || null },
      include: {
        part: {
          include: {
            shopParts: { where: { isAvailable: true }, include: { shop: true } },
            partNumbers: { include: { brand: true } },
          },
        },
      },
    })
    const availability = positions.map((position) => {
      const availableShopParts = position.part?.shopParts.filter((sp: any) => sp.isAvailable) || []
      return {
        positionId: position.id,
        positionNumber: position.positionNumber,
        partId: position.partId,
        partName: position.part?.name,
        isAvailable: availableShopParts.length > 0,
        availableCount: availableShopParts.length,
        shops: availableShopParts.map((sp: any) => ({ shopId: sp.shopId, shopName: sp.shop.name, price: sp.price, quantity: sp.quantity })),
      }
    })
    return availability
  }

  static async getStandardizedAttributes(input: { partId: string }) {
    const attributes = await $prisma.standardizedPartAttribute.findMany({ where: { partId: input.partId }, include: { attribute: true } })
    return attributes
  }

  static async createStandardizedAttribute(input: any) {
    const attribute = await $prisma.standardizedPartAttribute.create({ data: input, include: { attribute: true } })
    return attribute
  }

  // Positions
  static async createPosition(input: any) {
    const { variantId, ...data } = input
    const position = await $prisma.schemaPosition.create({
      data,
      include: { part: { include: { partNumbers: { include: { brand: true } } } } },
    })
    return position
  }
  static async updatePosition(input: any) {
    const { id, ...data } = input
    const position = await $prisma.schemaPosition.update({
      where: { id },
      data,
      include: { part: { include: { partNumbers: { include: { brand: true } } } } },
    })
    return position
  }
  static async deletePosition(input: { id: string }) {
    await $prisma.schemaPosition.delete({ where: { id: input.id } })
    return { success: true }
  }
}


