import{r as o}from"./index.0yr9KlQE.js";import{t as E}from"./trpc.9HS4D061.js";class m{static STORAGE_KEY="catalog-search-filters";static EVENT_NAME="catalog-filters-changed";static getFilters(){if(typeof window>"u")return this.getDefaultFilters();try{const a=localStorage.getItem(this.STORAGE_KEY);if(a)return{...this.getDefaultFilters(),...JSON.parse(a)}}catch(a){console.warn("Failed to parse stored catalog filters:",a)}return this.getDefaultFilters()}static setFilters(a){if(!(typeof window>"u"))try{localStorage.setItem(this.STORAGE_KEY,JSON.stringify(a)),window.dispatchEvent(new CustomEvent(this.EVENT_NAME,{detail:a}))}catch(s){console.warn("Failed to store catalog filters:",s)}}static updateFilters(a){const s=this.getFilters();this.setFilters({...s,...a})}static clearFilters(){this.setFilters(this.getDefaultFilters())}static subscribe(a){if(typeof window>"u")return()=>{};const s=c=>{a(c.detail)};return window.addEventListener(this.EVENT_NAME,s),()=>{window.removeEventListener(this.EVENT_NAME,s)}}static getDefaultFilters(){return{query:"",categoryIds:[],brandIds:[],attributeFilters:{},accuracyLevels:[],isOemOnly:!1}}}function F(){const[l,a]=o.useState(()=>m.getFilters());return o.useEffect(()=>m.subscribe(a),[]),{filters:l,setFilters:i=>{m.setFilters(i)},updateFilters:i=>{m.updateFilters(i)},clearFilters:()=>{m.clearFilters()}}}function y(l,a){const[s,c]=o.useState(l);return o.useEffect(()=>{const p=setTimeout(()=>{c(l)},a);return()=>{clearTimeout(p)}},[l,a]),s}function S(){const{filters:l,setFilters:a,updateFilters:s,clearFilters:c}=F(),p=y(l.query,300),{data:i,isLoading:f,error:I}=E.site.search.catalogItems.useQuery({search:p||void 0,brandIds:l.brandIds,categoryIds:l.categoryIds,isOemOnly:l.isOemOnly,accuracy:l.accuracyLevels||void 0,attributeFilters:Object.entries(l.attributeFilters||{}).map(([r,e])=>({templateId:Number(r),values:e.values?.length?e.values:void 0,minValue:e.numericRange?.[0],maxValue:e.numericRange?.[1]})),limit:50,offset:0,sortBy:"updatedAt",sortDir:"desc"},{staleTime:3e4,gcTime:5*6e4});typeof window<"u"&&(console.log("Search filters:",l),console.log("Search result:",i),console.log("Search error:",I),console.log("Is loading:",f));const d=o.useMemo(()=>{if(!i?.items)return[{id:1,partId:1,catalogItemId:1,accuracy:"EXACT_MATCH",notes:null,part:{id:1,name:"Сальник коленвала передний",parentId:null,level:0,path:"01",partCategoryId:1,partCategory:{id:1,name:"Сальники двигателя",slug:"engine-seals",description:null,parentId:null,level:0,path:"01",icon:null,image:null},attributes:[{id:1,value:"25.0",numericValue:25,partId:1,templateId:1,template:{id:1,name:"inner_diameter",title:"Внутренний диаметр",description:null,dataType:"NUMBER",unit:"MM",isRequired:!0,minValue:null,maxValue:null,allowedValues:[],tolerance:.1,groupId:null}}],image:null,mediaAssets:[],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},catalogItem:{id:1,sku:"TC-25x35x7",source:null,description:"Радиальный сальник для коленчатого вала",brandId:1,brand:{id:1,name:"SKF",slug:"skf",country:"Швеция",isOem:!1},isPublic:!0,attributes:[{id:1,value:"25.0",numericValue:25,catalogItemId:1,templateId:1,template:{id:1,name:"inner_diameter",title:"Внутренний диаметр",description:null,dataType:"NUMBER",unit:"MM",isRequired:!0,minValue:null,maxValue:null,allowedValues:[],tolerance:.1,groupId:null}}],image:null,mediaAssets:[]}}];const r=[];for(const e of i.items)if(e.applicabilities)for(const n of e.applicabilities)r.push({id:n.id,partId:n.partId,catalogItemId:n.catalogItemId,accuracy:n.accuracy,notes:n.notes,part:n.part,catalogItem:{id:e.id,sku:e.sku,description:e.description,brandId:e.brandId,brand:e.brand,isPublic:!0,attributes:e.attributes,image:e.image??null,mediaAssets:e.mediaAssets??[]}});return r},[i]),b=o.useMemo(()=>{const r={},e={};return d.forEach(n=>{n.catalogItem.attributes.forEach(t=>{t.template.dataType==="NUMBER"&&t.numericValue!==void 0&&t.numericValue!==null?e[t.templateId]?(e[t.templateId].min=Math.min(e[t.templateId].min,t.numericValue),e[t.templateId].max=Math.max(e[t.templateId].max,t.numericValue)):e[t.templateId]={min:t.numericValue,max:t.numericValue,avg:0}:(r[t.templateId]||(r[t.templateId]=[]),r[t.templateId].includes(t.value)||r[t.templateId].push(t.value))})}),Object.keys(e).forEach(n=>{const t=Number.parseInt(n),g=d.flatMap(u=>u.catalogItem.attributes).filter(u=>u.templateId===t&&u.numericValue!==void 0&&u.numericValue!==null).map(u=>u.numericValue);g.length>0&&(e[t].avg=g.reduce((u,h)=>u+h,0)/g.length)}),{values:r,numericStats:e}},[d]);return{filters:l,setFilters:a,updateFilters:s,clearFilters:c,results:d,totalCount:i?.total||0,filteredCount:d.length,isLoading:f,availableAttributeValues:b}}export{S as u};
