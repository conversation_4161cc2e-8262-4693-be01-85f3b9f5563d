# Документ дизайна

## Обзор

Клиентский интерфейс PartTec3 представляет собой современное веб-приложение, построенное на архитектуре Vue Islands с использованием Astro как основного фреймворка. Система предоставляет профессиональный интерфейс для работы с каталогом взаимозаменяемых запчастей, поддерживая различные роли пользователей и сложную систему атрибутов.

## Правила

- Категорически нельзя использовать @apply атрибут в стилях.
- Vue компоненты импортируются как Astro islands  компоненты с атрибутом client:load или client:only="vue" 
- Не запускай  "Open in Browser", предлагай проверить мне.
- Следуй кристально чистой архитектуре проекта.
- Главный паттерн Vue Islands с client:load директивой.
- Volt UI как библиотека компонентов.
- МАКСИМАЛЬНО ПРОСТОЙ КОД, без лишнего функционала.
- Мы делаем максимально гибкую систему, которая будет легко расширяться и поддерживаться. 
- По возможности реализовывай автогенерацию форм на базе Zod схем.
- Используй по максимуму где это возможно уже сгенерированные Zenstack:  Zod схемы и tRPC роуты.
- Мы не создаем демо код, тестовый код! Мы сразу приступаем к реальному функционалу!
- Учти что категорически запрещено дублировать функционал, везде и всегда должны быть единые компоненты! 
- Категорически запрещено нарушать принцип DRY 
- Lucide-vue иконки
- Volt UI (unstyled primevue) + Tailwind 4
- Роутинг и навигация Astro View Transition (метод navigate(path:string)) https://docs.astro.build/en/reference/modules/astro-transitions/#navigate

## Архитектура

### Общая архитектура системы

```mermaid
graph TB
    subgraph "Frontend Layer"
        A[Astro Pages] --> B[Vue Islands]
        B --> C[Volt UI Components]
        C --> D[Composables]
    end

    subgraph "Data Layer"
        D --> E[tRPC Client]
        E --> F[Better Auth]
        E --> G[Superjson Transformer]
    end

    subgraph "Backend Layer"
        G --> H[tRPC Server]
        H --> I[ZenStack ORM]
        I --> J[PostgreSQL]
    end

    subgraph "State Management"
        D --> K[Vue Reactivity]
        K --> L[Local Storage]
        K --> M[Session Storage]
    end
```

### Архитектурные принципы

1. **Vue Islands Pattern**: Использование директивы `client:load` для гидратации только интерактивных компонентов
2. **Композиционная архитектура**: Все логика инкапсулирована в composables для переиспользования
3. **Типобезопасность**: End-to-end типизация через tRPC и Zod схемы
4. **Декларативные правила доступа**: Использование ZenStack для контроля доступа на уровне данных

### Структура проекта

```
frontend/src/
├── components/           # Vue компоненты
│   ├── catalog/         # Компоненты каталога
│   ├── search/          # Компоненты поиска
│   ├── parts/           # Компоненты запчастей
│   ├── equipment/       # Компоненты оборудования
│   ├── forms/           # Автогенерируемые формы
│   └── ui/              # Базовые UI компоненты
├── composables/         # Композиционные функции
├── layouts/             # Макеты страниц
├── pages/               # Astro страницы
├── types/               # TypeScript типы
├── utils/               # Утилиты
└── volt/                # Volt UI компоненты
```

## Компоненты и интерфейсы

### Основные компоненты каталога

#### CatalogBrowser.vue

Главный компонент для просмотра каталога запчастей.

**Интерфейс:**

```typescript
interface CatalogBrowserProps {
  initialFilters?: CatalogFilters;
  viewMode?: "grid" | "list" | "table";
  showFilters?: boolean;
}

interface CatalogFilters {
  search?: string;
  categoryIds?: number[];
  brandIds?: number[];
  attributes?: AttributeFilter[];
  equipmentModelIds?: string[];
  priceRange?: [number, number];
}
```

**Функциональность:**

- Отображение списка запчастей с пагинацией
- Фильтрация по категориям, брендам, атрибутам
- Переключение режимов отображения
- Сохранение состояния фильтров в URL

#### PartDetailView.vue

Детальная страница запчасти с полной информацией.

**Интерфейс:**

```typescript
interface PartDetailProps {
  partId: number;
  showRelated?: boolean;
  showCompatibility?: boolean;
}
```

**Функциональность:**

- Отображение всех атрибутов запчасти
- Список совместимых каталожных позиций
- Применимость к оборудованию
- Интерактивные схемы (если доступны)
- Галерея изображений

#### SearchInterface.vue

Расширенный интерфейс поиска с множественными фильтрами.

**Интерфейс:**

```typescript
interface SearchInterfaceProps {
  mode?: "simple" | "advanced";
  placeholder?: string;
  showSuggestions?: boolean;
}
```

**Функциональность:**

- Автодополнение поисковых запросов
- Фильтрация по атрибутам с поддержкой диапазонов
- Поиск по синонимам атрибутов
- Сохранение истории поиска

### Компоненты форм

#### DynamicForm.vue

Автогенерируемая форма на основе Zod схем.

**Интерфейс:**

```typescript
interface DynamicFormProps<T> {
  schema: ZodSchema<T>;
  initialData?: Partial<T>;
  onSubmit: (data: T) => Promise<void>;
  submitLabel?: string;
  showReset?: boolean;
}
```

**Функциональность:**

- Автоматическая генерация полей из Zod схемы
- Валидация в реальном времени
- Поддержка всех типов атрибутов
- Локализация сообщений об ошибках

#### AttributeEditor.vue

Специализированный редактор атрибутов с поддержкой шаблонов.

**Интерфейс:**

```typescript
interface AttributeEditorProps {
  entityType: "part" | "catalogItem" | "equipmentModel";
  entityId: number | string;
  templates: AttributeTemplate[];
  readonly?: boolean;
}
```

**Функциональность:**

- Динамическое добавление/удаление атрибутов
- Валидация по шаблонам атрибутов
- Поддержка единиц измерения
- Группировка атрибутов

### Компоненты визуализации

#### SchemaViewer.vue

Интерактивный просмотрщик SVG схем агрегатов.

**Интерфейс:**

```typescript
interface SchemaViewerProps {
  schemaId: number;
  interactive?: boolean;
  highlightPartId?: number;
  onPartClick?: (partId: number) => void;
}
```

**Функциональность:**

- Отображение SVG схем с позиционированием
- Интерактивные элементы с всплывающими подсказками
- Выделение выбранных деталей
- Масштабирование и панорамирование

#### CompatibilityMatrix.vue

Матрица совместимости запчастей с оборудованием.

**Интерфейс:**

```typescript
interface CompatibilityMatrixProps {
  partId?: number;
  equipmentModelId?: string;
  showAccuracyLevels?: boolean;
}
```

**Функциональность:**

- Табличное отображение совместимости
- Цветовое кодирование уровней точности
- Фильтрация по брендам и категориям
- Экспорт данных

## Модели данных

### Клиентские типы

```typescript
// Основные сущности
interface Part {
  id: number;
  name: string | null;
  parentId: number | null;
  level: number;
  path: string;
  partCategoryId: number;
  imageId: number | null;
  createdAt: Date;
  updatedAt: Date;

  // Связи
  category: PartCategory;
  attributes: PartAttribute[];
  applicabilities: PartApplicability[];
  equipmentApplicabilities: EquipmentApplicability[];
  image?: MediaAsset;
}

interface CatalogItem {
  id: number;
  sku: string;
  description: string | null;
  brandId: number;
  isPublic: boolean;

  // Связи
  brand: Brand;
  attributes: CatalogItemAttribute[];
  applicabilities: PartApplicability[];
}

interface AttributeTemplate {
  id: number;
  name: string;
  title: string;
  description: string | null;
  dataType: AttributeDataType;
  unit: AttributeUnit | null;
  isRequired: boolean;
  minValue: number | null;
  maxValue: number | null;
  allowedValues: string[];
  tolerance: number;
  groupId: number | null;

  // Связи
  group?: AttributeGroup;
  synonymGroups: AttributeSynonymGroup[];
}
```

### Состояние приложения

```typescript
interface AppState {
  // Аутентификация
  user: User | null;
  session: Session | null;

  // UI состояние
  theme: "light" | "dark";
  locale: "ru" | "en";
  sidebarCollapsed: boolean;

  // Каталог
  catalogFilters: CatalogFilters;
  searchHistory: SearchQuery[];
  viewPreferences: ViewPreferences;

  // Кэш
  cachedParts: Map<number, Part>;
  cachedCatalogItems: Map<number, CatalogItem>;
}
```

## Обработка ошибок

### Стратегия обработки ошибок

1. **tRPC ошибки**: Централизованная обработка в `useTrpc` composable
2. **Валидация форм**: Отображение ошибок рядом с полями
3. **Сетевые ошибки**: Глобальные уведомления через toast систему
4. **Ошибки доступа**: Перенаправление на страницы ошибок

### Типы ошибок

```typescript
interface AppError {
  code: string;
  message: string;
  details?: any;
  field?: string;
}

interface ValidationError extends AppError {
  field: string;
  value: any;
  constraint: string;
}

interface NetworkError extends AppError {
  status: number;
  retryable: boolean;
}
```

## Стратегия тестирования

### Уровни тестирования

1. **Unit тесты**: Composables и утилиты (Vitest)
2. **Component тесты**: Vue компоненты (Vue Test Utils + Vitest)
3. **Integration тесты**: Взаимодействие с API (Playwright)
4. **E2E тесты**: Пользовательские сценарии (Playwright)

### Тестовые данные

```typescript
// Фабрики для создания тестовых данных
const createMockPart = (overrides?: Partial<Part>): Part => ({
  id: 1,
  name: "Тестовая запчасть",
  parentId: null,
  level: 0,
  path: "01",
  partCategoryId: 1,
  imageId: null,
  createdAt: new Date(),
  updatedAt: new Date(),
  ...overrides,
});
```

## Производительность

### Оптимизации

1. **Ленивая загрузка**: Компоненты загружаются по требованию
2. **Виртуализация**: Для больших списков используется виртуальная прокрутка
3. **Кэширование**: tRPC запросы кэшируются на клиенте
4. **Оптимизация изображений**: Lazy loading и WebP формат
5. **Code splitting**: Разделение кода по маршрутам

### Метрики производительности

- Время загрузки первой страницы: < 2 секунды
- Время интерактивности: < 3 секунды
- Размер JavaScript бандла: < 500KB (gzipped)
- Lighthouse Score: > 90 для всех метрик

## Доступность

### Требования WCAG 2.1 AA

1. **Клавиатурная навигация**: Все элементы доступны с клавиатуры
2. **Скринридеры**: Правильная семантическая разметка
3. **Контрастность**: Минимум 4.5:1 для обычного текста
4. **Фокус**: Видимые индикаторы фокуса
5. **Альтернативный текст**: Для всех изображений

### Реализация

```typescript
// Composable для управления фокусом
export function useFocusManagement() {
  const trapFocus = (element: HTMLElement) => {
    // Реализация trap focus для модальных окон
  };

  const restoreFocus = (element: HTMLElement) => {
    // Восстановление фокуса после закрытия модалов
  };

  return { trapFocus, restoreFocus };
}
```

## Интернационализация

### Поддерживаемые языки

- Русский (основной)
- Английский

### Структура переводов

```typescript
interface Translations {
  common: {
    save: string;
    cancel: string;
    delete: string;
    edit: string;
    search: string;
  };
  catalog: {
    parts: string;
    categories: string;
    brands: string;
    filters: string;
  };
  attributes: {
    [key: string]: string;
  };
}
```

### Локализация данных

```typescript
// Форматирование чисел и дат
export function useLocalization() {
  const formatNumber = (value: number, unit?: AttributeUnit) => {
    return new Intl.NumberFormat(locale.value, {
      style: unit ? "unit" : "decimal",
      unit: unit ? getUnitCode(unit) : undefined,
    }).format(value);
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat(locale.value).format(date);
  };

  return { formatNumber, formatDate };
}
```

## Безопасность

### Клиентская безопасность

1. **XSS защита**: Санитизация пользовательского ввода
2. **CSRF защита**: Токены в формах
3. **Валидация**: Дублирование серверной валидации на клиенте
4. **Авторизация**: Проверка прав доступа в компонентах

### Реализация

```typescript
// Composable для проверки прав доступа
export function usePermissions() {
  const canEdit = (resource: string) => {
    return (
      user.value?.role === "ADMIN" ||
      (user.value?.role === "SHOP" && resource === "catalogItem")
    );
  };

  const canDelete = (resource: string) => {
    return user.value?.role === "ADMIN";
  };

  return { canEdit, canDelete };
}
```

## Развертывание

### Сборка продакшн версии

```bash
# Сборка статических файлов
npm run build

# Предварительный просмотр
npm run preview

# Анализ бандла
npm run analyze
```

### Переменные окружения

```typescript
interface EnvironmentConfig {
  API_URL: string;
  AUTH_SECRET: string;
  DATABASE_URL: string;
  UPLOAD_PATH: string;
  MAX_FILE_SIZE: number;
}
```

### CDN и кэширование

- Статические ресурсы кэшируются на 1 год
- HTML файлы кэшируются на 1 час
- API ответы кэшируются согласно заголовкам Cache-Control
