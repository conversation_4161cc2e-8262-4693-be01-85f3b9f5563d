import{c as e}from"./createLucideIcon.DdiNmGRb.js";import{r as s,a as c}from"./index.0yr9KlQE.js";import{d}from"./index.Di12BYGB.js";/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const u=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],v=e("x",u);var n=c[" useId ".trim().toString()]||(()=>{}),i=0;function x(t){const[o,r]=s.useState(n());return d(()=>{r(a=>a??String(i++))},[t]),t||(o?`radix-${o}`:"")}export{v as X,x as u};
