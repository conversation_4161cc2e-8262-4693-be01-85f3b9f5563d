import type { User as BetterAuthUser } from 'better-auth'

// Расширяем тип User из Better-Auth для нашей схемы
export interface ExtendedUser extends BetterAuthUser {
  role: 'USER' | 'SHOP' | 'ADMIN'
  // Admin plugin поля
  banned?: boolean
  banReason?: string | null
  banExpires?: Date | null
  shop?: {
    id: string
    name: string
    isActive: boolean
    isVerified: boolean
  } | null
}

// Контекст авторизации для ZenStack
export interface AuthContext {
  user: ExtendedUser | null
}

// Типы для проверки ролей
export type UserRole = 'USER' | 'SHOP' | 'ADMIN'

// Хелпер типы для TypeScript
export type AuthenticatedUser = NonNullable<ExtendedUser>
export type ShopOwner = ExtendedUser & { role: 'SHOP'; shop: NonNullable<ExtendedUser['shop']> }
export type Expert = ExtendedUser & { role: 'ADMIN' }
