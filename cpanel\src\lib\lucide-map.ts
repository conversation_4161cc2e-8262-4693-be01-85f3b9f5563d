import type { FunctionalComponent } from 'vue'
import {
  Alert<PERSON>riangle,
  Archive,
  ArrowLeft,
  Box,
  Calendar,
  CheckCircle,
  Check,
  ChevronDown,
  Circle,
  CircleHelp as Question,
  ChevronUp,
  Code2,
  Cog,
  Calculator,
  Folder,
  Globe,
  Hash,
  Home,
  Info,
  Inbox,
  Eye,
  Link,
  List,
  Loader2,
  LogOut,
  Network,
  Package,
  Pencil,
  Plus,
  PlusCircle,
  RefreshCcw,
  Save,
  Search,
  Send,
  SquareCheck as CheckSquare,
  Users,
  Building,
  Bolt,
  LineChart,
  Tag,
  Tags,
  Text as Font,
  Trash2 as Trash,
  User,
  Wrench,
  X
} from 'lucide-vue-next'

// Map of PrimeIcons token (without leading "pi pi-") to Lucide component
export const primeToLucideMap: Record<string, FunctionalComponent<any>> = {
  // common actions
  plus: Plus,
  refresh: RefreshCcw,
  pencil: Pencil,
  trash: Trash,
  link: Link,
  eye: Eye,
  times: X,
  check: Check,
  'check-circle': CheckCircle,
  send: Send,
  save: Save,
  home: Home,
  'exclamation-triangle': <PERSON><PERSON><PERSON><PERSON><PERSON>,
  users: Users,
  building: Building,
  bolt: Bolt,
  'plus-circle': PlusCircle,
  'chart-line': LineChart,

  // navigation / arrows
  'arrow-left': ArrowLeft,
  'chevron-down': ChevronDown,

  // objects
  box: Package || Box,
  inbox: Inbox,
  tag: Tag,
  tags: Tags,
  list: List,
  'info-circle': Info,
  circle: Circle,
  cog: Cog,
  sitemap: Network,
  calculator: Calculator,
  'chevron-up': ChevronUp,
  wrench: Wrench,
  search: Search,
  user: User,
  'sign-out': LogOut,
  folder: Folder,
  archive: Archive,
  globe: Globe,

  // data types
  font: Font,
  hashtag: Hash,
  'check-square': CheckSquare,
  calendar: Calendar,
  code: Code2,
  question: Question,

  // loaders
  spinner: Loader2
}

export function resolveLucideByPrimeIcon(primeIcon: string) {
  const normalized = primeIcon.replace(/^pi\s+/, '').replace(/^pi-/, '')
  return primeToLucideMap[normalized]
}


